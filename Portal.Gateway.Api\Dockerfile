# This stage is used when running from VS in fast mode (Default for Debug configuration)
FROM mcr.microsoft.com/dotnet/aspnet:8.0 AS base
RUN apt update && apt install -y iputils-ping telnet nano

# USER $APP_UID
USER root
WORKDIR /app
EXPOSE 8080

FROM mcr.microsoft.com/dotnet/sdk:8.0 AS build
RUN apt-get update && apt-get install -y bash
ARG BUILD_CONFIGURATION=Release
WORKDIR /src

# Restore as distinct layers
COPY ["Portal.Gateway.Api/Portal.Gateway.Api.csproj", "Portal.Gateway.Api/"]
RUN dotnet restore "Portal.Gateway.Api" --disable-build-servers --no-cache
COPY . .

# Build and publish a release
WORKDIR /src/Portal.Gateway.Api
RUN mkdir -p "/app/publish"
RUN dotnet publish "./Portal.Gateway.Api.csproj" --disable-build-servers -c $BUILD_CONFIGURATION -o "/app/publish" /p:UseAppHost=false
RUN ls -al "/app/publish"

# Build runtime image
FROM base AS final
ENV TZ=Turkey
WORKDIR /app
COPY --from=build "/app/publish" .
ENTRYPOINT ["dotnet", "Portal.Gateway.Api.dll"]