﻿using FluentValidation;
using Gateway.Extensions;
using Gateway.External.Application.Country.Dto;
using Gateway.External.Resources;

namespace Gateway.External.Application.Country.Validator
{
    internal class GetCountriesValidator : AbstractValidator<GetCountriesRequest>
    {
        public GetCountriesValidator()
        {
            RuleFor(p => p).Custom((item, context) =>
            {
                    if (item.To && item.From)
                        context.AddFailure(ServiceResources.METHOD_REQUIREMENT_ERROR + $" ({nameof(item.To)}: true, {nameof(item.From)} : true)");
            });
        }
    }

    internal class GetCityByCountryValidator : AbstractValidator<GetCityByCountryRequest>
    {
	    public GetCityByCountryValidator()
	    {
		    RuleFor(p => p).Custom((item, context) =>
		    {
			    if (!item.ResourceId.IsNumericAndGreaterThenZero())
				    context.AddFailure(string.Format(ServiceResources.PROPERTY_REQUIRED, nameof(item.ResourceId)));
		    });
		}
    }

    internal class GetVasTypeMessageByCountryValidator : AbstractValidator<GetVasTypeMessageByCountryRequest>
    {
	    public GetVasTypeMessageByCountryValidator()
	    {
		    RuleFor(p => p).Custom((item, context) =>
		    {
			    if (!item.CountryId.IsNumericAndGreaterThenZero())
				    context.AddFailure(string.Format(ServiceResources.PROPERTY_REQUIRED, nameof(item.CountryId)));
		    });
	    }
    }

    internal class GetChecklistByCountryValidator : AbstractValidator<GetChecklistByCountryRequest>
    {
        public GetChecklistByCountryValidator()
        {
            RuleFor(p => p).Custom((item, context) =>
            {
                if (!item.ResourceId.IsNumericAndGreaterThenZero())
                    context.AddFailure(string.Format(ServiceResources.PROPERTY_REQUIRED, nameof(item.ResourceId)));
            });
        }
    }
    internal class BranchesByCountryValidator : AbstractValidator<BranchesByCountryRequest>
    {
	    public BranchesByCountryValidator()
	    {
		    RuleFor(p => p).Custom((item, context) =>
		    {
			    if (!item.CountryId.IsNumericAndGreaterThenZero())
				    context.AddFailure(string.Format(ServiceResources.PROPERTY_REQUIRED, nameof(item.CountryId)));
		    });
	    }
    }
}
