﻿using Microsoft.EntityFrameworkCore.Metadata.Builders;
using Microsoft.EntityFrameworkCore;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;
using Portal.Gateway.Entity.Entities.Portal;

namespace Portal.Gateway.Entity.EntityConfigurations.Portal
{
    internal class CancellnsuranceOrderEntityConfiguration : IEntityTypeConfiguration<CancelInsuranceOrder>
    {
        public void Configure(EntityTypeBuilder<CancelInsuranceOrder> builder)
        {
            builder.ToTable("CancelInsuranceOrder");

            builder.HasIndex(e => e.ApplicationId, "IX_CancelInsuranceOrder_ApplicationId");
            builder.HasIndex(e => e.ApplicationInsuranceId, "IX_CancelInsuranceOrder_ApplicationInsuranceId");

            #region StaticAuditableEntityConfig

            builder.HasKey(e => e.Id);
            builder.Property(e => e.Id).IsRequired().ValueGeneratedOnAdd();
            builder.Property(e => e.IsActive).IsRequired();
            builder.Property(e => e.IsDeleted).IsRequired();
            builder.Property(e => e.CreatedBy);
            builder.Property(e => e.CreatedAt).HasColumnType("timestamp with time zone");
            builder.Property(e => e.UpdatedBy);
            builder.Property(e => e.UpdatedAt).HasColumnType("timestamp with time zone");
            builder.Property(e => e.DeletedBy);
            builder.Property(e => e.DeletedAt).HasColumnType("timestamp with time zone");

            #endregion

            builder.Property(e => e.ApplicationId).IsRequired();
            builder.Property(e => e.ApplicationInsuranceId).IsRequired();
            builder.Property(e => e.PolicyNumber).HasColumnType("citext").IsRequired();
            builder.Property(e => e.Status).IsRequired();
            builder.Property(e => e.RejectedPolicyType).IsRequired();

            builder.HasOne(d => d.Application)
                .WithMany(p => p.CancelInsuranceOrders)
                .HasForeignKey(d => d.ApplicationId)
                .OnDelete(DeleteBehavior.ClientSetNull);
            
            builder.HasOne(d => d.ApplicationInsurance)
                .WithMany(p => p.CancelInsuranceOrders)
                .HasForeignKey(d => d.ApplicationInsuranceId)
                .OnDelete(DeleteBehavior.ClientSetNull);
        }
    }
}
