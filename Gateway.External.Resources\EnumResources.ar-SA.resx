﻿<?xml version="1.0" encoding="utf-8"?>
<root>
  <!-- 
    Microsoft ResX Schema 
    
    Version 2.0
    
    The primary goals of this format is to allow a simple XML format 
    that is mostly human readable. The generation and parsing of the 
    various data types are done through the TypeConverter classes 
    associated with the data types.
    
    Example:
    
    ... ado.net/XML headers & schema ...
    <resheader name="resmimetype">text/microsoft-resx</resheader>
    <resheader name="version">2.0</resheader>
    <resheader name="reader">System.Resources.ResXResourceReader, System.Windows.Forms, ...</resheader>
    <resheader name="writer">System.Resources.ResXResourceWriter, System.Windows.Forms, ...</resheader>
    <data name="Name1"><value>this is my long string</value><comment>this is a comment</comment></data>
    <data name="Color1" type="System.Drawing.Color, System.Drawing">Blue</data>
    <data name="Bitmap1" mimetype="application/x-microsoft.net.object.binary.base64">
        <value>[base64 mime encoded serialized .NET Framework object]</value>
    </data>
    <data name="Icon1" type="System.Drawing.Icon, System.Drawing" mimetype="application/x-microsoft.net.object.bytearray.base64">
        <value>[base64 mime encoded string representing a byte array form of the .NET Framework object]</value>
        <comment>This is a comment</comment>
    </data>
                
    There are any number of "resheader" rows that contain simple 
    name/value pairs.
    
    Each data row contains a name, and value. The row also contains a 
    type or mimetype. Type corresponds to a .NET class that support 
    text/value conversion through the TypeConverter architecture. 
    Classes that don't support this are serialized and stored with the 
    mimetype set.
    
    The mimetype is used for serialized objects, and tells the 
    ResXResourceReader how to depersist the object. This is currently not 
    extensible. For a given mimetype the value must be set accordingly:
    
    Note - application/x-microsoft.net.object.binary.base64 is the format 
    that the ResXResourceWriter will generate, however the reader can 
    read any of the formats listed below.
    
    mimetype: application/x-microsoft.net.object.binary.base64
    value   : The object must be serialized with 
            : System.Runtime.Serialization.Formatters.Binary.BinaryFormatter
            : and then encoded with base64 encoding.
    
    mimetype: application/x-microsoft.net.object.soap.base64
    value   : The object must be serialized with 
            : System.Runtime.Serialization.Formatters.Soap.SoapFormatter
            : and then encoded with base64 encoding.

    mimetype: application/x-microsoft.net.object.bytearray.base64
    value   : The object must be serialized into a byte array 
            : using a System.ComponentModel.TypeConverter
            : and then encoded with base64 encoding.
    -->
  <xsd:schema id="root" xmlns="" xmlns:xsd="http://www.w3.org/2001/XMLSchema" xmlns:msdata="urn:schemas-microsoft-com:xml-msdata">
    <xsd:import namespace="http://www.w3.org/XML/1998/namespace" />
    <xsd:element name="root" msdata:IsDataSet="true">
      <xsd:complexType>
        <xsd:choice maxOccurs="unbounded">
          <xsd:element name="metadata">
            <xsd:complexType>
              <xsd:sequence>
                <xsd:element name="value" type="xsd:string" minOccurs="0" />
              </xsd:sequence>
              <xsd:attribute name="name" use="required" type="xsd:string" />
              <xsd:attribute name="type" type="xsd:string" />
              <xsd:attribute name="mimetype" type="xsd:string" />
              <xsd:attribute ref="xml:space" />
            </xsd:complexType>
          </xsd:element>
          <xsd:element name="assembly">
            <xsd:complexType>
              <xsd:attribute name="alias" type="xsd:string" />
              <xsd:attribute name="name" type="xsd:string" />
            </xsd:complexType>
          </xsd:element>
          <xsd:element name="data">
            <xsd:complexType>
              <xsd:sequence>
                <xsd:element name="value" type="xsd:string" minOccurs="0" msdata:Ordinal="1" />
                <xsd:element name="comment" type="xsd:string" minOccurs="0" msdata:Ordinal="2" />
              </xsd:sequence>
              <xsd:attribute name="name" type="xsd:string" use="required" msdata:Ordinal="1" />
              <xsd:attribute name="type" type="xsd:string" msdata:Ordinal="3" />
              <xsd:attribute name="mimetype" type="xsd:string" msdata:Ordinal="4" />
              <xsd:attribute ref="xml:space" />
            </xsd:complexType>
          </xsd:element>
          <xsd:element name="resheader">
            <xsd:complexType>
              <xsd:sequence>
                <xsd:element name="value" type="xsd:string" minOccurs="0" msdata:Ordinal="1" />
              </xsd:sequence>
              <xsd:attribute name="name" type="xsd:string" use="required" />
            </xsd:complexType>
          </xsd:element>
        </xsd:choice>
      </xsd:complexType>
    </xsd:element>
  </xsd:schema>
  <resheader name="resmimetype">
    <value>text/microsoft-resx</value>
  </resheader>
  <resheader name="version">
    <value>2.0</value>
  </resheader>
  <resheader name="reader">
    <value>System.Resources.ResXResourceReader, System.Windows.Forms, Version=4.0.0.0, Culture=neutral, PublicKeyToken=b77a5c561934e089</value>
  </resheader>
  <resheader name="writer">
    <value>System.Resources.ResXResourceWriter, System.Windows.Forms, Version=4.0.0.0, Culture=neutral, PublicKeyToken=b77a5c561934e089</value>
  </resheader>
  <data name="Agency" xml:space="preserve">
    <value>شركة سياحية</value>
  </data>
  <data name="ApplicantTypeFamily" xml:space="preserve">
    <value>نوع التقديم- عائلي</value>
  </data>
  <data name="ApplicantTypeGroup" xml:space="preserve">
    <value>نوع التقديم- جماعي</value>
  </data>
  <data name="ApplicantTypeIndividual" xml:space="preserve">
    <value>نوع التقديم- فردي</value>
  </data>
  <data name="ApplicantTypeRepresentative" xml:space="preserve">
    <value>نوع التقديم- تمثيلي</value>
  </data>
  <data name="ApplicationNumber" xml:space="preserve">
    <value>رقم الطلب</value>
  </data>
  <data name="ApplicationStatus" xml:space="preserve">
    <value>حالة التطبيق</value>
  </data>
  <data name="ApplicationStatusActive" xml:space="preserve">
    <value>وضع التقديم- فعال</value>
  </data>
  <data name="ApplicationStatusCancelled" xml:space="preserve">
    <value>وضع التقديم- تم الغائه</value>
  </data>
  <data name="ApplicationStatusPartiallyRefunded" xml:space="preserve">
    <value>وضع التقديم- تمت الإعادة الجزئية</value>
  </data>
  <data name="ApplicationTaken" xml:space="preserve">
    <value>تم استلام التقديم</value>
  </data>
  <data name="ApplicationTime" xml:space="preserve">
    <value>وقت التطبيق</value>
  </data>
  <data name="ApplicationTypeFree" xml:space="preserve">
    <value>نوع التقديم- مجاني</value>
  </data>
  <data name="ApplicationTypeNonApplicationInsurance" xml:space="preserve">
    <value>نوع التقديم- تأمين دون التقديم</value>
  </data>
  <data name="ApplicationTypeNonApplicationPcr" xml:space="preserve">
    <value>نوع التقديم- PCRدون التقديم</value>
  </data>
  <data name="ApplicationTypeNonApplicationPhotocopy" xml:space="preserve">
    <value>نوع التقديم-  استنساخ دون التقديم</value>
  </data>
  <data name="ApplicationTypeNonApplicationPhotograph" xml:space="preserve">
    <value>نوع التقديم-  تصوير دون التقديم</value>
  </data>
  <data name="ApplicationTypeNonApplicationPrintOut" xml:space="preserve">
    <value>نوع التقديم- طبع وثائق دون التقديم</value>
  </data>
  <data name="ApplicationTypeNormal" xml:space="preserve">
    <value>نوع التقديم- عادي</value>
  </data>
  <data name="ApplicationTypeTurquois" xml:space="preserve">
    <value>نوع التقديم- تركوازي</value>
  </data>
  <data name="ApplicationTypeTurquoisGratis" xml:space="preserve">
    <value>نوع التقديم- تركوازي كراتيز</value>
  </data>
  <data name="ApplicationTypeTurquoisPremium" xml:space="preserve">
    <value>نوع التقديم- تركوازي بريميوم</value>
  </data>
  <data name="ApplicationUnderEvaluation" xml:space="preserve">
    <value>يتم تقييم التقديم</value>
  </data>
  <data name="ApprovedQuota" xml:space="preserve">
    <value>الحصة المعتمدة</value>
  </data>
  <data name="ApprovedWorkPermit" xml:space="preserve">
    <value>اذن عمل حاصل على الموافقة</value>
  </data>
  <data name="Branch" xml:space="preserve">
    <value>فرع</value>
  </data>
  <data name="Business" xml:space="preserve">
    <value>عمل</value>
  </data>
  <data name="CallCenter" xml:space="preserve">
    <value>مركز الاتصال</value>
  </data>
  <data name="Child" xml:space="preserve">
    <value>طفل</value>
  </data>
  <data name="Companion" xml:space="preserve">
    <value>مرافق</value>
  </data>
  <data name="ConferenceFair" xml:space="preserve">
    <value>مؤتمر/ معرض</value>
  </data>
  <data name="Consenting" xml:space="preserve">
    <value>رخصة، موافقة</value>
  </data>
  <data name="Country" xml:space="preserve">
    <value>دولة</value>
  </data>
  <data name="CourierReturnedPassportToOffice" xml:space="preserve">
    <value>أعاد الساعي جواز السفر إلى المكتب</value>
  </data>
  <data name="CulturalSportive" xml:space="preserve">
    <value>رياضي ثقافي</value>
  </data>
  <data name="Date" xml:space="preserve">
    <value>تاريخ</value>
  </data>
  <data name="DeliveredToCargo" xml:space="preserve">
    <value>التسليم الى خدمة التوصيل</value>
  </data>
  <data name="DocumentEditingService" xml:space="preserve">
    <value>خدمة انشاء مستند</value>
  </data>
  <data name="Driver" xml:space="preserve">
    <value>سائق</value>
  </data>
  <data name="Email" xml:space="preserve">
    <value>بريد إلكتروني</value>
  </data>
  <data name="EntryBanned" xml:space="preserve">
    <value>حاصل على منع دخول</value>
  </data>
  <data name="EntryDate" xml:space="preserve">
    <value>موعد الدخول</value>
  </data>
  <data name="ExitDate" xml:space="preserve">
    <value>تاريخ الخروج</value>
  </data>
  <data name="FamilyAndFriendVisit" xml:space="preserve">
    <value>زيارة الأقارب والأصدقاء</value>
  </data>
  <data name="FamilyUnion" xml:space="preserve">
    <value>لم شمل الاسرة</value>
  </data>
  <data name="Female" xml:space="preserve">
    <value>انثى</value>
  </data>
  <data name="FileWithdrewAccordingtoCustomerRequest" xml:space="preserve">
    <value>تم سحب الملف بناءً على طلب المتقدم</value>
  </data>
  <data name="FlexibleAppointment" xml:space="preserve">
    <value>الموعد المرن</value>
  </data>
  <data name="HandDeliveredToTheApplicant" xml:space="preserve">
    <value>تم التسليم الى المتقدم باليد</value>
  </data>
  <data name="Health" xml:space="preserve">
    <value>علاج</value>
  </data>
  <data name="Id" xml:space="preserve">
    <value>بطاقة تعريف</value>
  </data>
  <data name="Individual" xml:space="preserve">
    <value>فردي</value>
  </data>
  <data name="LastUploadDate" xml:space="preserve">
    <value>تاريخ التحميل الأخير</value>
  </data>
  <data name="MainApplicant" xml:space="preserve">
    <value>مقدم الطلب الرئيسي</value>
  </data>
  <data name="Male" xml:space="preserve">
    <value>ذكر</value>
  </data>
  <data name="MBS" xml:space="preserve">
    <value>MBS</value>
  </data>
  <data name="MinistryOfHealthApplication" xml:space="preserve">
    <value>تقديم عن طريق وزارة الصحة</value>
  </data>
  <data name="Montage" xml:space="preserve">
    <value>تركيب</value>
  </data>
  <data name="Name" xml:space="preserve">
    <value>اسم</value>
  </data>
  <data name="NameSurname" xml:space="preserve">
    <value>اسم اللقب</value>
  </data>
  <data name="NonApplicationInsurance" xml:space="preserve">
    <value>تأمين بدون التقديم</value>
  </data>
  <data name="NotUploaded" xml:space="preserve">
    <value>لم يتم تحميلها</value>
  </data>
  <data name="PassportNumber" xml:space="preserve">
    <value>رقم جواز السفر</value>
  </data>
  <data name="PatientCompanion" xml:space="preserve">
    <value>مرافق مريض</value>
  </data>
  <data name="PaymentCompleted" xml:space="preserve">
    <value>تم التسديد</value>
  </data>
  <data name="PhoneNumber" xml:space="preserve">
    <value>رقم التليفون</value>
  </data>
  <data name="Platinum" xml:space="preserve">
    <value>Platinum</value>
  </data>
  <data name="PnlStatus" xml:space="preserve">
    <value>PNL حالة</value>
  </data>
  <data name="PrimeTime" xml:space="preserve">
    <value>Prime time</value>
  </data>
  <data name="Quota" xml:space="preserve">
    <value>حصة نسبية</value>
  </data>
  <data name="ReceivedAtMainVisaApplicationCenter" xml:space="preserve">
    <value>تم الاستلام من المركز الرئيسي للتأشيرة التركية</value>
  </data>
  <data name="ReceivedAtVisaCenter" xml:space="preserve">
    <value>تم الاستلام في مركز التأشيرة التركية</value>
  </data>
  <data name="Revision" xml:space="preserve">
    <value>مراجعة</value>
  </data>
  <data name="SentFromEmbassyToMainVisaApplicationCenter" xml:space="preserve">
    <value>تم الارسال من السفارة الى المركز الرئيسي للتأشيرة التركية</value>
  </data>
  <data name="SentFromMainVisaApplicationCenterToVisaApplicationCenter" xml:space="preserve">
    <value>تم الارسال من المركز الرئيسي للتأشيرة التركية الى المركز الفرعي للتأشيرة التركية</value>
  </data>
  <data name="SentToMainVisaApplicationCenter" xml:space="preserve">
    <value>تم الارسال الى المركز الرئيسي للتأشيرة التركية</value>
  </data>
  <data name="Servant" xml:space="preserve">
    <value>خادم/خادمة</value>
  </data>
  <data name="Slot" xml:space="preserve">
    <value>فتحة</value>
  </data>
  <data name="SlotTime" xml:space="preserve">
    <value>وقت الفتحة</value>
  </data>
  <data name="Status" xml:space="preserve">
    <value>حالة</value>
  </data>
  <data name="Student" xml:space="preserve">
    <value>طالب</value>
  </data>
  <data name="StudentCompanion" xml:space="preserve">
    <value>مرافق طالب</value>
  </data>
  <data name="Surname" xml:space="preserve">
    <value>اسم العائلة</value>
  </data>
  <data name="Touristic" xml:space="preserve">
    <value>سياحية</value>
  </data>
  <data name="Transit" xml:space="preserve">
    <value>عبور</value>
  </data>
  <data name="Unspecified" xml:space="preserve">
    <value>غير محدد</value>
  </data>
  <data name="Uploaded" xml:space="preserve">
    <value>تم الرفع</value>
  </data>
  <data name="User" xml:space="preserve">
    <value>مستخدم</value>
  </data>
  <data name="VIP" xml:space="preserve">
    <value>VIP</value>
  </data>
  <data name="VisaTransfer" xml:space="preserve">
    <value>نقل التأشيرة</value>
  </data>
  <data name="Wife" xml:space="preserve">
    <value>زوج/زوجة</value>
  </data>
  <data name="WorkPermit" xml:space="preserve">
    <value>رخصة عمل</value>
  </data>
  <data name="WorkPermitCompanion" xml:space="preserve">
    <value>مرافق رخصة عمل</value>
  </data>
  <data name="Ambulance" xml:space="preserve">
    <value>سيارة إسعاف</value>
  </data>
  <data name="AllInstitutions" xml:space="preserve">
    <value>جميع المؤسسات</value>
  </data>
  <data name="DentalClinic" xml:space="preserve">
    <value>عيادة الأسنان</value>
  </data>
  <data name="Pharmacy" xml:space="preserve">
    <value>صيدلية</value>
  </data>
  <data name="HomeHealthCare" xml:space="preserve">
    <value>مركز الرعاية المنزلية</value>
  </data>
  <data name="PhysicalTherapyCenter" xml:space="preserve">
    <value>مركز العلاج الطبيعي</value>
  </data>
  <data name="Hospital" xml:space="preserve">
    <value>مستشفى</value>
  </data>
  <data name="Laboratory" xml:space="preserve">
    <value>معمل</value>
  </data>
  <data name="MedicalMaterial" xml:space="preserve">
    <value>المواد الطبية</value>
  </data>
  <data name="Surgery" xml:space="preserve">
    <value>جراحة</value>
  </data>
  <data name="Optical" xml:space="preserve">
    <value>بصري</value>
  </data>
  <data name="Polyclinic" xml:space="preserve">
    <value>عيادة متعددة التخصصات</value>
  </data>
  <data name="Radiology" xml:space="preserve">
    <value>الأشعة</value>
  </data>
</root>