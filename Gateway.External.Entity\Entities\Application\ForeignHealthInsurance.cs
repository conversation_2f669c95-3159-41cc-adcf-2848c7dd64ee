﻿using System;

namespace Gateway.External.Entity.Entities.Application
{
    public class ForeignHealthInsurance
    {
        public ForeignHealthInsurance()
        {
            IsActive = true;
            IsDeleted = false;
        }
        public int Id { get; set; }
        public int ApplicationId { get; set; }
        public bool Status { get; set; }
        public DateTime PolicyDate { get; set; }
        public DateTime StartDate { get; set; }
        public DateTime EndDate { get; set; }
        public int? CreatedBy { get; set; }
        public DateTime? CreatedAt { get; set; }
        public int? UpdatedBy { get; set; }
        public DateTime? UpdatedAt { get; set; }
        public bool IsActive { get; set; }
        public bool IsDeleted { get; set; }
        public int? DeletedBy { get; set; }
        public DateTime? DeletedAt { get; set; }
    }
}
