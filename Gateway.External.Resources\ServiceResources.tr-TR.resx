﻿<?xml version="1.0" encoding="utf-8"?>
<root>
  <!-- 
    Microsoft ResX Schema 
    
    Version 2.0
    
    The primary goals of this format is to allow a simple XML format 
    that is mostly human readable. The generation and parsing of the 
    various data types are done through the TypeConverter classes 
    associated with the data types.
    
    Example:
    
    ... ado.net/XML headers & schema ...
    <resheader name="resmimetype">text/microsoft-resx</resheader>
    <resheader name="version">2.0</resheader>
    <resheader name="reader">System.Resources.ResXResourceReader, System.Windows.Forms, ...</resheader>
    <resheader name="writer">System.Resources.ResXResourceWriter, System.Windows.Forms, ...</resheader>
    <data name="Name1"><value>this is my long string</value><comment>this is a comment</comment></data>
    <data name="Color1" type="System.Drawing.Color, System.Drawing">Blue</data>
    <data name="Bitmap1" mimetype="application/x-microsoft.net.object.binary.base64">
        <value>[base64 mime encoded serialized .NET Framework object]</value>
    </data>
    <data name="Icon1" type="System.Drawing.Icon, System.Drawing" mimetype="application/x-microsoft.net.object.bytearray.base64">
        <value>[base64 mime encoded string representing a byte array form of the .NET Framework object]</value>
        <comment>This is a comment</comment>
    </data>
                
    There are any number of "resheader" rows that contain simple 
    name/value pairs.
    
    Each data row contains a name, and value. The row also contains a 
    type or mimetype. Type corresponds to a .NET class that support 
    text/value conversion through the TypeConverter architecture. 
    Classes that don't support this are serialized and stored with the 
    mimetype set.
    
    The mimetype is used for serialized objects, and tells the 
    ResXResourceReader how to depersist the object. This is currently not 
    extensible. For a given mimetype the value must be set accordingly:
    
    Note - application/x-microsoft.net.object.binary.base64 is the format 
    that the ResXResourceWriter will generate, however the reader can 
    read any of the formats listed below.
    
    mimetype: application/x-microsoft.net.object.binary.base64
    value   : The object must be serialized with 
            : System.Runtime.Serialization.Formatters.Binary.BinaryFormatter
            : and then encoded with base64 encoding.
    
    mimetype: application/x-microsoft.net.object.soap.base64
    value   : The object must be serialized with 
            : System.Runtime.Serialization.Formatters.Soap.SoapFormatter
            : and then encoded with base64 encoding.

    mimetype: application/x-microsoft.net.object.bytearray.base64
    value   : The object must be serialized into a byte array 
            : using a System.ComponentModel.TypeConverter
            : and then encoded with base64 encoding.
    -->
  <xsd:schema id="root" xmlns="" xmlns:xsd="http://www.w3.org/2001/XMLSchema" xmlns:msdata="urn:schemas-microsoft-com:xml-msdata">
    <xsd:import namespace="http://www.w3.org/XML/1998/namespace" />
    <xsd:element name="root" msdata:IsDataSet="true">
      <xsd:complexType>
        <xsd:choice maxOccurs="unbounded">
          <xsd:element name="metadata">
            <xsd:complexType>
              <xsd:sequence>
                <xsd:element name="value" type="xsd:string" minOccurs="0" />
              </xsd:sequence>
              <xsd:attribute name="name" use="required" type="xsd:string" />
              <xsd:attribute name="type" type="xsd:string" />
              <xsd:attribute name="mimetype" type="xsd:string" />
              <xsd:attribute ref="xml:space" />
            </xsd:complexType>
          </xsd:element>
          <xsd:element name="assembly">
            <xsd:complexType>
              <xsd:attribute name="alias" type="xsd:string" />
              <xsd:attribute name="name" type="xsd:string" />
            </xsd:complexType>
          </xsd:element>
          <xsd:element name="data">
            <xsd:complexType>
              <xsd:sequence>
                <xsd:element name="value" type="xsd:string" minOccurs="0" msdata:Ordinal="1" />
                <xsd:element name="comment" type="xsd:string" minOccurs="0" msdata:Ordinal="2" />
              </xsd:sequence>
              <xsd:attribute name="name" type="xsd:string" use="required" msdata:Ordinal="1" />
              <xsd:attribute name="type" type="xsd:string" msdata:Ordinal="3" />
              <xsd:attribute name="mimetype" type="xsd:string" msdata:Ordinal="4" />
              <xsd:attribute ref="xml:space" />
            </xsd:complexType>
          </xsd:element>
          <xsd:element name="resheader">
            <xsd:complexType>
              <xsd:sequence>
                <xsd:element name="value" type="xsd:string" minOccurs="0" msdata:Ordinal="1" />
              </xsd:sequence>
              <xsd:attribute name="name" type="xsd:string" use="required" />
            </xsd:complexType>
          </xsd:element>
        </xsd:choice>
      </xsd:complexType>
    </xsd:element>
  </xsd:schema>
  <resheader name="resmimetype">
    <value>text/microsoft-resx</value>
  </resheader>
  <resheader name="version">
    <value>2.0</value>
  </resheader>
  <resheader name="reader">
    <value>System.Resources.ResXResourceReader, System.Windows.Forms, Version=4.0.0.0, Culture=neutral, PublicKeyToken=b77a5c561934e089</value>
  </resheader>
  <resheader name="writer">
    <value>System.Resources.ResXResourceWriter, System.Windows.Forms, Version=4.0.0.0, Culture=neutral, PublicKeyToken=b77a5c561934e089</value>
  </resheader>
  <data name="BRANCH_DEPARTMENT_NOT_FOUND" xml:space="preserve">
    <value>Şube Departmanı bulunamadı</value>
  </data>
  <data name="APPLICANT_NOT_FOUND" xml:space="preserve">
    <value>Başvuran bulunamadı</value>
  </data>
  <data name="APPOINTMENT_NOT_FOUND" xml:space="preserve">
    <value>Randevu bulunamadı</value>
  </data>
  <data name="BRANCH_NOT_FOUND" xml:space="preserve">
    <value>Şube bulunamadı</value>
  </data>
  <data name="FAILED" xml:space="preserve">
    <value>FAILED</value>
  </data>
  <data name="FAMILY_APPLICANT_COUNT_ERROR" xml:space="preserve">
    <value>Aile başvuran sayısı birden fazla olmalıdır</value>
  </data>
  <data name="GROUP_APPLICANT_COUNT_ERROR" xml:space="preserve">
    <value>Grup başvuran sayısı birden fazla olmalıdır</value>
  </data>
  <data name="INDIVIDUAL_APPLICANT_COUNT_ERROR" xml:space="preserve">
    <value>Bireysel başvuran sayısı birden fazla olamaz</value>
  </data>
  <data name="INPUT_ERROR" xml:space="preserve">
    <value>INPUT_ERROR</value>
  </data>
  <data name="INTERNAL_SERVER_ERROR" xml:space="preserve">
    <value>INTERNAL_SERVER_ERROR</value>
  </data>
  <data name="INVALID_INPUT_ERROR" xml:space="preserve">
    <value>Geçersiz istek parametresi</value>
  </data>
  <data name="INVALID_REQUEST" xml:space="preserve">
    <value>Geçersiz istek</value>
  </data>
  <data name="PROPERTY_MAX_LENGTH_ERROR" xml:space="preserve">
    <value>{0} özelliği {1} karakterden fazla olamaz</value>
  </data>
  <data name="PROPERTY_REQUIRED" xml:space="preserve">
    <value>{0} gerekli</value>
  </data>
  <data name="RESOURCE_ALREADY_REGISTERED" xml:space="preserve">
    <value>Önceden kaydedilmiş</value>
  </data>
  <data name="RESOURCE_CREATED" xml:space="preserve">
    <value>Kayıt oluşturuldu</value>
  </data>
  <data name="RESOURCE_DELETED" xml:space="preserve">
    <value>Kayıt silindi</value>
  </data>
  <data name="RESOURCE_FOUND" xml:space="preserve">
    <value>Kayıt bulundu</value>
  </data>
  <data name="RESOURCE_NOT_FOUND" xml:space="preserve">
    <value>Kayıt bulunamadı</value>
  </data>
  <data name="RESOURCE_RETRIEVED" xml:space="preserve">
    <value>Kayıt getirildi</value>
  </data>
  <data name="RESOURCE_UPDATED" xml:space="preserve">
    <value>Kayıt güncellendi</value>
  </data>
  <data name="SUCCESS" xml:space="preserve">
    <value>SUCCESS</value>
  </data>
  <data name="SLOT_NOT_FOUND" xml:space="preserve">
    <value>Slot bulunamadı</value>
  </data>
  <data name="NO_AVAILABLE_SLOTS_FOUND" xml:space="preserve">
    <value>Mevcut slot bulunamadı</value>
  </data>
  <data name="FIRST_AVAILABLE_SLOT_FOUND" xml:space="preserve">
    <value>En yakında bulunan uygun slot zamanı bulundu</value>
  </data>
  <data name="COUNTRY_NOT_FOUND" xml:space="preserve">
    <value>Ülke bulunamadı</value>
  </data>
  <data name="INVALID_EMAIL_ADDRESS" xml:space="preserve">
    <value>Geçersiz mail adresi</value>
  </data>
  <data name="REPEATED_APPOINTMENT_FOUND" xml:space="preserve">
    <value>Pasaporta ait randevu bulunmaktadır</value>
  </data>
  <data name="SLOT_QUOTA_NOT_FOUND" xml:space="preserve">
    <value>Slot kotası bulunamadı</value>
  </data>
  <data name="APPLICATION_STATUS_NOT_FOUND" xml:space="preserve">
    <value>Başvuru statüsü bulunamadı</value>
  </data>
  <data name="APPLICANT_COUNT_VALIDATION_ERROR" xml:space="preserve">
    <value>Bireysel başvuru türünde başvuru sayısı 1'den fazla olamaz</value>
  </data>
  <data name="APPLICATION_NOT_FOUND" xml:space="preserve">
    <value>Başvuru bulunamadı</value>
  </data>
  <data name="PROPERTY_FORMAT_ERROR" xml:space="preserve">
    <value>{0} özelliği doğru formatta değil</value>
  </data>
  <data name="APPLICANT_ONLY_HAS_ONE_WİFEORHUSBAND" xml:space="preserve">
    <value>Başvuran kişinin sadece tek bir eşi olabilir</value>
  </data>
  <data name="ONLY_ONE_RECORD_CANBE_SET_AS_WIFEORHUSBAND_RECORD_FOR_APPOINTMENT" xml:space="preserve">
    <value>Randevu için eş veya koca kaydı olarak yalnızca bir kayıt ayarlanabilir</value>
  </data>
  <data name="METHOD_REQUIREMENT_ERROR" xml:space="preserve">
    <value>Bu method bu parametre değerleri ile çalışmaz</value>
  </data>
  <data name="APPOINTMENT_NOT_CONVERTED_APPLICATION" xml:space="preserve">
    <value>Randevunuz başvuruya dönüştürülmediği için başvuru durumunuzu görüntüleyemezsiniz</value>
  </data>
  <data name="APPOINTMENT_HAS_CONVERTED_APPLICATION_NOT_FOUND" xml:space="preserve">
    <value>Başvuruya dönüştürülmüş randevu bulunamadı</value>
  </data>
  <data name="FILE_OPERATION_FAILED" xml:space="preserve">
    <value>Dosya operasyonu başarısız oldu</value>
  </data>
  <data name="MAIL_FOOTER" xml:space="preserve">
    <value>&lt;string name="EmailFooter_Turkish"&gt;&amp;lt;div style=&amp;quot;font-family: Arial, sans-serif; font-size: 12px; color: #333;&amp;quot;&amp;gt;&amp;lt;p&amp;gt;Daha fazla bilgi için lütfen web sitemizi ziyaret edin: &amp;lt;a href=&amp;quot;https://gatewayinternational.com.tr&amp;quot; target=&amp;quot;_blank&amp;quot;&amp;gt;https://gatewayinternational.com.tr&amp;lt;/a&amp;gt;&amp;lt;/p&amp;gt;&amp;lt;p&amp;gt;Aşağıda belirtilen e-posta ve çağrı merkezi numaramızdan bize ulaşabilirsiniz.&amp;lt;/p&amp;gt;&amp;lt;p&amp;gt;E-posta: &amp;lt;a href=&amp;quot;mailto:<EMAIL>&amp;quot;&amp;gt;<EMAIL>&amp;lt;/a&amp;gt;&amp;lt;/p&amp;gt;&amp;lt;p&amp;gt;Çağrı Merkezi: &amp;lt;a href=&amp;quot;tel:+964662111919&amp;quot;&amp;gt;00964 ************&amp;lt;/a&amp;gt;&amp;lt;/p&amp;gt;&amp;lt;p&amp;gt;Teşekkürler.&amp;lt;/p&amp;gt;&amp;lt;p&amp;gt;Saygılarımızla,&amp;lt;/p&amp;gt;&amp;lt;p&amp;gt;Gateway International&amp;lt;/p&amp;gt;&amp;lt;p&amp;gt;Bu otomatik bir e-postadır. Lütfen bu e-postaya cevap vermeyiniz.&amp;lt;/p&amp;gt;&amp;lt;/div&amp;gt;&lt;/string&gt;</value>
  </data>
  <data name="NOTIFICATION_MAIL_NEWAPPOINTMENT" xml:space="preserve">
    <value>[DATE] tarihi ve [TIME] saat için [BRANCH] şubesine randevunuz oluşturulmuştur. Randevu numaranız [APPNUMBER].</value>
  </data>
  <data name="NOTIFICATION_MAIL_UPDATEAPPOINTMENT" xml:space="preserve">
    <value>[DATE] tarihi ve [TIME] saat için [BRANCH] şubesine randevunuz güncellenmiştir.</value>
  </data>
  <data name="APPLICANT_NOT_MATCH_WITH_APPOINTMENT" xml:space="preserve">
    <value>Başvuran randevu ile eşleşmemekte</value>
  </data>
  <data name="BRANCH_CHANGE_NOT_ALLOWED" xml:space="preserve">
    <value>Şube değişimi en fazla 1 kez yapılabilir</value>
  </data>
  <data name="NEW_APPLICANT_NOT_ALLOWED" xml:space="preserve">
    <value>Yeni kişi eklemeye izin verilmiyor</value>
  </data>
  <data name="SLOT_NOT_IN_THIS_BRANCH" xml:space="preserve">
    <value>Slot bu şubeye ait değil</value>
  </data>
  <data name="PASSPORT_VALIDITY_PERIOD_ERROR" xml:space="preserve">
    <value>Pasaport geçerlilik süresi 180 günden az olamaz</value>
  </data>
  <data name="BIRTHDATE_MUST_BE_PAST_TENSE" xml:space="preserve">
    <value>Doğum tarihi geçmiş zamanlı olmalı</value>
  </data>
  <data name="APPOINTMENT_SLOT_LIMIT" xml:space="preserve">
    <value>Gün ve tarih güncellemeleri en fazla 2 kez yapılabilir</value>
  </data>
  <data name="APPOINTMENT_UPDATE_NOT_ALLOWED" xml:space="preserve">
    <value>Randevu güncellemesi son 48 saat içinde yapılamaz</value>
  </data>
  <data name="DELETE_OPERATION_NOT_ALLOWED" xml:space="preserve">
    <value>24 saatten az kalan randevular silinemez</value>
  </data>
  <data name="REPEATED_MAIL_NOT_ALLOWED" xml:space="preserve">
    <value>Aynı mail adresi ile maksimum 2 defa randevu alınabilir</value>
  </data>
  <data name="REPEATED_PHONE_NUMBER_NOT_ALLOWED" xml:space="preserve">
    <value>Aynı telefon numarası ile en fazla 3 randevu alınabilir.</value>
  </data>
  <data name="VAST_TYPE_PRICE_MESSAGE" xml:space="preserve">
    <value>Servis bedeline ek olarak, bu hizmet için ekstra {price} hizmet bedeli bulunmaktadır.</value>
  </data>
  <data name="VAS_TYPE_NOT_ACTIVE" xml:space="preserve">
    <value>Vas türü aktif değil</value>
  </data>
  <data name="VISA_INFORMATION_NOT_ACTIVE" xml:space="preserve">
    <value>Vize bilgisi aktif değil</value>
  </data>
  <data name="USER_ALREADY_REGISTERED" xml:space="preserve">
    <value>Kullanıcı önceden kaydedilmiş</value>
  </data>
  <data name="PASSWORD_MISMATCH" xml:space="preserve">
    <value>Şifre uyuşmazlığı</value>
  </data>
  <data name="ALREADY_HAS_UPDATE_REQUEST" xml:space="preserve">
    <value>Oluşturulmuş güncelleme isteği bulunmakta</value>
  </data>
  <data name="USER_NOT_FOUND" xml:space="preserve">
    <value>Kullanıcı bulunamadı</value>
  </data>
  <data name="AUTHORIZED_USER_NOT_FOUND" xml:space="preserve">
    <value>Yetkili kullanıcı bulunamadı</value>
  </data>
  <data name="INVALID_PHONE_NUMBER" xml:space="preserve">
    <value>Geçersiz telefon numarası</value>
  </data>
  <data name="PROPERTY_MUST_NOT_HAVE_NUMERIC_CHARACTER" xml:space="preserve">
    <value>İçinde sayısal ifade olmamalıdır.</value>
  </data>
  <data name="PASSWORD_MUST_NOT_SAME" xml:space="preserve">
    <value>Şifre aynı olmamalı</value>
  </data>
  <data name="TRANSLATION_NOT_FOUND" xml:space="preserve">
    <value>Çeviri bulunamadı</value>
  </data>
  <data name="COMPANY_NOT_FOUND" xml:space="preserve">
    <value>Şirket bulunamadı</value>
  </data>
  <data name="EXTERNAL_REGISTER_NOTIFICATION" xml:space="preserve">
    <value>Kayıt işleminiz gerçekleşmiştir. Geçici şifreniz [PASSWORD] Geçici şifreniz ile giriş yaptıktan sonra sistemden değiştirebilirsiniz</value>
  </data>
  <data name="NO_DATA_FOUND_TO_REPORT" xml:space="preserve">
    <value>Raporlanıcak veri bulunamadı</value>
  </data>
  <data name="CREATED_BY" xml:space="preserve">
    <value>Yaratan kişi</value>
  </data>
  <data name="ORDER" xml:space="preserve">
    <value>Sıra</value>
  </data>
  <data name="REPORT_DATE" xml:space="preserve">
    <value>Rapor tarihi</value>
  </data>
  <data name="SAME_PASSPORT_USED_BETWEEN_APPLICANTS" xml:space="preserve">
    <value>Başvuranlar arasında aynı pasaport kullanıldı</value>
  </data>
  <data name="COMPANY_USER_REPORT" xml:space="preserve">
    <value>Kurum Kullanıcı Raporu</value>
  </data>
  <data name="COMPANY_SLOT_DEMAND_REPORT" xml:space="preserve">
    <value>Firma Slot Talep Raporu</value>
  </data>
  <data name="COMPANY_APPOINTMENT_DEMAND_REPORT" xml:space="preserve">
    <value>Firma Randevu Talep Formu</value>
  </data>
  <data name="APPOINTMENT_DEMAND_NOT_FOUND" xml:space="preserve">
    <value>Randevu talebi bulunamadı</value>
  </data>
  <data name="COMPANY_APPLICATION_REPORT" xml:space="preserve">
    <value>Firma Başvuru Raporu</value>
  </data>
  <data name="PNL_REPORT" xml:space="preserve">
    <value>PNL Raporu</value>
  </data>
  <data name="PNL_ALREADY_REGISTERED" xml:space="preserve">
    <value>PNL dosyası önceden yüklenmiş</value>
  </data>
  <data name="INVALID_PARAMETER" xml:space="preserve">
    <value>Geçersiz parametre {0}</value>
  </data>
  <data name="INVALID_PASSPORT_NUMBER" xml:space="preserve">
    <value>Geçersiz pasaport numarası</value>
  </data>
  <data name="DATE_PARAMETERS_MISMATCH" xml:space="preserve">
    <value>Tarih parametreleri uyumsuz</value>
  </data>
  <data name="FILE_SIZE_LIMIT_EXCEEDED" xml:space="preserve">
    <value>Dosya limit boyutu aşıldı</value>
  </data>
  <data name="INVALID_FILE_EXTENSION" xml:space="preserve">
    <value>Geçersiz dosya uzantısı</value>
  </data>
  <data name="EMAIL_MISMATCH_WITH_TOKEN" xml:space="preserve">
    <value>Email bilgisi giriş yaparken kullanılan mail ile aynı olmalıdır</value>
  </data>
  <data name="APPOINTMENT_FOUND_WITH_SAME_PASSPORT" xml:space="preserve">
    <value>Aynı pasaport numaralı randevu bulundu</value>
  </data>
  <data name="APPOINTMENT_CONFIRMATION_FILE_NAME" xml:space="preserve">
    <value>Randevu Onayı</value>
  </data>
  <data name="APPOINTMENT_UPDATE_CONFIRMATION_FILE_NAME" xml:space="preserve">
    <value>Randevu Güncelleme Onayı</value>
  </data>
  <data name="AppointmentDateTitle" xml:space="preserve">
    <value>Randevu Tarihi</value>
  </data>
  <data name="BAD_REQUEST" xml:space="preserve">
    <value>BAD_REQUEST</value>
  </data>
  <data name="CANNOT_CONTINUE_APPOINTMENT" xml:space="preserve">
    <value>Bu pasaport numarası ile randevuya devam edemezsiniz</value>
  </data>
  <data name="PRE_CONDITION_FAILED" xml:space="preserve">
    <value>Ön koşul başarısız</value>
  </data>
  <data name="PhotoBoothStatus_RANGE" xml:space="preserve">
    <value>PhotoBoothStatus 0 ile 6 arasında bir değer olmalıdır</value>
  </data>
  <data name="INVALID_PARAMETER_LOWER" xml:space="preserve">
    <value>Geçersiz parametre:{0} {1} değerinden düşük olmalıdır</value>
  </data>
  <data name="CANNOT_UPDATE_WALKIN" xml:space="preserve">
    <value>walkin randevular güncellenemiyor</value>
  </data>
  <data name="MOBILE_SLOT_NOT_FOUND" xml:space="preserve">
    <value>Seçtiğiniz tarihte uygun randevu bulunmamaktadır. Lütfen farklı bir tarih seçmeyi deneyiniz.</value>
  </data>
</root>