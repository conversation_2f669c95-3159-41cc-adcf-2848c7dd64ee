# Build and test ASP.NET Core projects targeting the full .NET Framework.
# Add steps that publish symbols, save build artifacts, and more:
# https://docs.microsoft.com/azure/devops/pipelines/languages/dotnet-core
trigger:
- master
pool:
  name: 'PortalPool'
variables:
  buildConfiguration: 'Release'
  artifactName: 'gatewayApiDrop'
  migrationScriptsArtifactName: 'migrationScripts'
  branchName: $[replace(variables['Build.SourceBranch'], 'refs/heads/', '')]
  seedFolderPath: '_GatewayPortals-API-K8S'

steps:
- task: UseDotNet@2
  displayName: 'Use .NET Core SDK 6.0.x'
  inputs:
    packageType: sdk
    version: 6.0.x
    installationPath: $(Agent.ToolsDirectory)/dotnet

- task: PowerShell@2
  displayName: 'Set ASPNETCORE_ENVIRONMENT variable based on branch'
  inputs:
    targetType: 'inline'
    script: |
      if ("$(branchName)" -eq "master") {
          Write-Output "Setting ASPNETCORE_ENVIRONMENT to Production"
          Write-Host "##vso[task.setvariable variable=ASPNETCORE_ENVIRONMENT]Production"
      }
      elseif ("$(branchName)" -eq "staging") {
          Write-Output "Setting ASPNETCORE_ENVIRONMENT to Staging"
          Write-Host "##vso[task.setvariable variable=ASPNETCORE_ENVIRONMENT]Staging"
      }
      else {
          Write-Output "Setting ASPNETCORE_ENVIRONMENT to Development"
          Write-Host "##vso[task.setvariable variable=ASPNETCORE_ENVIRONMENT]Development"
      }

- task: DotNetCoreCLI@2
  displayName: 'Install Dotnet EF Tool'
  inputs:
    command: custom
    custom: "tool"
    arguments: "update --global dotnet-ef --version 6.0.35"
    
- task: DotNetCoreCLI@2
  displayName: 'Restore dependencies'
  inputs:
    command: restore
    feedsToUse: config
    nugetConfigPath: NuGet.Config
    verbosityRestore: minimal
    arguments: '--no-cache'

- task: DotNetCoreCLI@2
  displayName: 'Build API solution'
  inputs:
    command: build
    projects: 'Portal.Gateway.Api/Portal.Gateway.Api.csproj'
    arguments: '--configuration $(buildConfiguration) -v q'

- powershell: |
    New-Item -ItemType Directory -Force -Path '$(Build.SourcesDirectory)/Migrations'
  displayName: "Create migrations folder"

- task: CopyFiles@2
  displayName: 'Copy Seed Files'
  inputs:
    SourceFolder: '$(Build.SourcesDirectory)/Portal.Gateway.Entity/$(seedFolderPath)/migrationScripts'
    Contents: 'Resources/**/*'
    TargetFolder: '$(Build.SourcesDirectory)/Migrations'
    OverWrite: true

- task: CopyFiles@2
  displayName: 'Copy appsettings.json'
  inputs:
    SourceFolder: '$(Build.SourcesDirectory)/Portal.Gateway.Api'
    Contents: 'appsettings.$(ASPNETCORE_ENVIRONMENT).json'
    TargetFolder: '$(Build.SourcesDirectory)/Migrations'
    OverWrite: true
    
- task: DotNetCoreCLI@2
  displayName: 'Generate db migration bundle'
  inputs:
    command: 'custom'
    custom: 'ef'
    arguments: 'migrations bundle --output $(Build.SourcesDirectory)/Migrations/migration_portal.exe --context MigrationPortalDbContext --project Portal.Gateway.Entity/Portal.Gateway.Entity.csproj'

- task: DotNetCoreCLI@2
  displayName: 'Publish solution'
  inputs:
    command: publish
    projects: 'Portal.Gateway.Api/Portal.Gateway.Api.csproj'
    publishWebProjects: false
    zipAfterPublish: false
    arguments: '--configuration $(buildConfiguration) --no-build --output $(build.artifactstagingdirectory)'

- task: PublishBuildArtifacts@1
  displayName: 'Publish artifact'
  inputs:
    PathtoPublish: '$(build.artifactstagingdirectory)'
    ArtifactName: '$(artifactName)'

- task: PublishBuildArtifacts@1
  displayName: 'Publish artifact: Publish migration bundles'
  inputs:
    PathtoPublish: '$(Build.SourcesDirectory)/Migrations'
    ArtifactName: '$(migrationScriptsArtifactName)'
