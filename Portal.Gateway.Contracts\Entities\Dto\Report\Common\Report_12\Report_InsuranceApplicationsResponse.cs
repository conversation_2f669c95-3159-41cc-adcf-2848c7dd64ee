﻿using System;
using System.Collections.Generic;

namespace Portal.Gateway.Contracts.Entities.Dto.Report.Common.Report_12
{
    public class Report_InsuranceApplicationsResponse
    {
        public IEnumerable<Application> Applications { get; set; }

        public class Application
        {
            public string ApplicationId { get; set; }

            public string PassportNumber { get; set; }

            public string PolicyNumber { get; set; }

            public string ProviderName { get; set; }

            public string ApplicantName { get; set; }

            public DateTime ApplicationDate { get; set; }

            public DateTime InsuranceStartDate { get; set; }

            public DateTime InsuranceEndDate { get; set; }

            public int InsuranceRemainingDays { get; set; }

            public decimal Price { get; set; }

            public int CurrencyId { get; set; }

            public string InsuranceStatus { get; set; }

			public int ApplicationExtraFeeId { get; set; }
			public decimal? CompanyPrice { get; set; }
            public string? CompanyPriceCurrency { get; set; }
            public decimal? Price2 { get; set; }
            public string? Price2Currency { get; set; }
            public int? ApplicationInsuranceCategory { get; set; }
		}
	}
}
