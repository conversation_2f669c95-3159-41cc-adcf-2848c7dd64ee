﻿using System.ComponentModel.DataAnnotations;
using System.ComponentModel.DataAnnotations.Schema;

namespace Gateway.QMS.Entity.Entities.VisaType
{
    //[NotMapped]
    public sealed class VisaTypeTranslation
    {
        public VisaTypeTranslation()
        {
            IsActive = true;
        }

        [Key]
        [DatabaseGenerated(DatabaseGeneratedOption.Identity)]
        public int Id { get; set; }

        [Required]
        public int VisaTypeId { get; set; }

       
        [Required]
        [Column(TypeName = "citext"), MaxLength(100)]
        public string Name { get; set; }

       
        [Required]
        public int LanguageId { get; set; }

        public bool IsActive { get; set; }

        public bool IsDeleted { get; set; }

        public VisaType VisaType { get; set; }
    }
}

