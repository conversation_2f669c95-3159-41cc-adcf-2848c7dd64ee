﻿using Gateway.EventBus.Publishers;
using Gateway.Notification.Application.Factories;
using Gateway.Notification.Application.Handlers.Dto;
using Gateway.Sms.Dto;
using Microsoft.Extensions.Configuration;
using System.Threading.Tasks;

namespace Gateway.Notification.Application.Sms
{
    public class SmsService : ISmsService
    {
        private readonly IConfiguration _configuration;
        private readonly SmsServiceProviderFactory _smsServiceProviderFactory;

        #region Ctor

        public SmsService(IConfiguration configuration, IMessagePublisher messagePublisher)
        {
            _configuration = configuration;

            _smsServiceProviderFactory = new SmsServiceProviderFactory(messagePublisher);
        }

        #endregion

        #region Public Methods

        public async Task<SendSmsResponse> SendSms(SmsCollectorRequest request)
        {
            var smsRequest = new SendSmsRequest
            {
                EndDate = request.SmsModel.EndDate,
                Message = request.SmsModel.Message,
                Msisdn = request.SmsModel.Msisdn,
                Originator = request.SmsModel.Originator,
                StartDate = request.SmsModel.StartDate
            };

            var smsProviderService = _smsServiceProviderFactory.GetInstance(request.SmsModel.ProviderId, _configuration);

            return await smsProviderService.SendSmsAsync(smsRequest);
        }

        #endregion
    }
}
