{"BaseImageUri": "http://***********:8063", "PortalApiUrl": "https://gateway-api-dev-k8s.gateway.com.tr", "PortalApiKey": "Gateway.ApiKey.2021", "Logging": {"LogLevel": {"Default": "Information", "Microsoft.AspNetCore": "Warning"}}, "ConnectionStrings": {"GatewayPortalDbConnection": "Server=**********; Database=GatewayPortalDbDev; Username=gw_portal_stg_app_usr;Password=*****************;"}, "RabbitMq": {"Host": "GWMQ01", "User": "test_user", "Password": "okmenn", "Port": 5672, "Exchange": "log.exchange"}, "Redis": {"Url": "***********", "Port": "6379", "ConnectTimeout": 10000, "ConnectRetry": 3, "DefaultDatabase": 7}, "AppSettings": {"MinioConfiguration": {"EndPoint": "visacdn.gateway.com.tr:443", "AccessKey": "eGBBZ2ZVzNWCFW5kvmKr", "SecretKey": "tnczwQAiKkNpEHIGwXwztZtFZCaT1i2JziBUZXY3", "BucketPrefix": "test-"}}}