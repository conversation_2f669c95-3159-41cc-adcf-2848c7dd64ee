﻿var exportFlag = false;

$(function () {
    $("#filterGridApplication").click(function (e) {
        refreshGridApplication();
    });

    $("#clearGridApplicationFilter").click(function (e) {
        $("#FilterApplicationNumber").val('');
        $("#FilterCountryId").data("kendoDropDownList").select(null);
        $("#FilterWaitingTimeForDocument").data("kendoDropDownList").select(null);
        $("#FilterAgencyId").data("kendoDropDownList").select(null);
        $("#FilterApplicantTypeId").data("kendoDropDownList").select(null);
        $("#FilterLocalAuthorityStatusId").data("kendoDropDownList").select(null);
        $("#FilterName").val('');
        $("#FilterSurname").val('');
        $("#FilterPassportNumber").val('');
        $("#FilterNationalityId").data("kendoDropDownList").select(null);
        $("#FilterEmail").val('');
        $("#FilterPhoneNumber").val('');
        $("#FilterVisaCategoryId").data("kendoDropDownList").select(null);
        $("#FilterAllowPassiveDeletedApplications").prop('checked', false);
        $("#FilterStartDate").data("kendoDropDownList").select(null);
        $("#FilterEndDate").data("kendoDropDownList").select(null);
        $("#FilterEvaluationDate").data("kendoDropDownList").select(null);
        $("#FilterAllList").data("kendoDropDownList").select(null);
        $("#FilterSuitable").data("kendoDropDownList").select(null);
        $("#FilterNotSuitable").data("kendoDropDownList").select(null);
        $("#FilterWaiting").data("kendoDropDownList").select(null);
        refreshGridApplication();
    });

    $("#filterGridApplicationCancellation").click(function (e) {
        refreshGridApplicationCancellation();
    });

    $("#clearGridApplicationCancellationFilter").click(function (e) {
        $("#FilterCancellationTypeId").data("kendoDropDownList").select(null);
        $("#FilterCancellationReasonId").data("kendoDropDownList").select(null);
        $("#FilterCancellationStatusId").data("kendoDropDownList").select(null);
        refreshGridApplicationCancellation();
    });
});

function refreshGridApplication() {
    $('#gridApplication').data('kendoGrid').dataSource.read();
    $('#gridApplication').data('kendoGrid').refresh();
}

function gridApplicationFilterData() {
    var list = [];
    for (var i = 0; i < 2; i++) {
        list.push($('#FilterApplicationStatusIds_' + i + "_").val());
    }
    var postData = { values: list };
    return {
        FilterApplicationNumber: $("#FilterApplicationNumber").val(),
        FilterCountryId: $("#FilterCountryId").val(),
        FilterAgencyId: $("#FilterAgencyId").val(),
        FilterApplicantTypeId: $("#FilterApplicantTypeId").val(),
        FilterApplicationTypeId: $("#FilterApplicationTypeId").val(),
        FilterName: $("#FilterName").val(),
        FilterSurname: $("#FilterSurname").val(),
        FilterPassportNumber: $("#FilterPassportNumber").val(),
        FilterNationalityId: $("#FilterNationalityId").val(),
        FilterEmail: $("#FilterEmail").val(),
        FilterPhoneNumber: $("#FilterPhoneNumber").val(),
        FilterVisaCategoryId: $("#FilterVisaCategoryId").val(),
        FilterAllBranchs: $("#FilterAllBranchs").val(),
        FilterAllowPassiveDeletedApplications: $('#FilterAllowPassiveDeletedApplications').is(":checked"),
        FilterStartDate: $("#FilterStartDate").val(),
        FilterEndDate: $("#FilterEndDate").val(),
        FilterEvaluationDate: $("#FilterEvaluationDate").val(),
        FilterLocalAuthorityStatusId: $("#FilterLocalAuthorityStatusId").val(),
        FilterBranchId: 37,
        FilterWaitingTimeForDocument: $("#FilterWaitingTimeForDocument").val(),
        FilterApplicationStatusIds: postData.values,
        FilterShowLocalAuthorityRows: true,
        FilterIsTurkmenistanPage:true,
        FilterAllList: $("#FilterAllList").prop("checked"),
        FilterSuitable: $("#FilterSuitable").prop("checked"),
        FilterNotSuitable: $("#FilterNotSuitable").prop("checked"),
        FilterWaiting: $("#FilterWaiting").prop("checked")
    };
}

function partialAddApplicationCancellation(EncryptedApplicationId, ApplicationCancellationTypeId) {
    $.post('/Appointment/ApplicationCancellation/PartialAddApplicationCancellation', { encryptedApplicationId: EncryptedApplicationId, applicationCancellationTypeId: ApplicationCancellationTypeId },
        function (data) {
            $('#divPartialAddApplicationCancellation').html(data);
        }, 'html');
}

$("#formAddApplicationCancellation").submit(function (e) {

    e.preventDefault();
    var form = $(this);
    var validator = form.kendoValidator().data("kendoValidator");

    if (!validator.validate()) return;

    $.ajax({
        type: "POST",
        url: "/Appointment/ApplicationCancellation/AddApplicationCancellation",
        data: form.serialize(),
        success: function (data) {
            showNotification(data.Type, data.Message);
            if (data.Type === "success") {
                $('#modalAddApplicationCancellation').modal('hide');
                showNotification("success", jsResources.ApplicationCancellationRequestSaved);
            }
        },
        error: function (data) {
            showNotification('danger', jsResources.ErrorOccurred);
        }
    });

});

function refreshGridApplicationCancellation() {
    $('#gridApplicationCancellation').data('kendoGrid').dataSource.read();
    $('#gridApplicationCancellation').data('kendoGrid').refresh();
}

function gridApplicationCancellationFilterData() {
    return {
        FilterCancellationTypeId: $("#FilterCancellationTypeId").val(),
        FilterCancellationReasonId: $("#FilterCancellationReasonId").val(),
        FilterCancellationStatusId: $("#FilterCancellationStatusId").val()
    };
}

function updateApplicationCancellationStatus(EncryptedApplicationCancellationId, ApplicationCancellationStatusId) {
    bootbox.confirm(jsResources.AreYouSureToDoThisAction, function (result) {
        if (result) {
            $.ajax({
                type: "PUT",
                url: "/Appointment/ApplicationCancellation/UpdateApplicationCancellationStatus",
                data: { encryptedApplicationCancellationId: EncryptedApplicationCancellationId, applicationCancellationStatusId: ApplicationCancellationStatusId },
                success: function (data) {
                    showNotification(data.Type, data.Message);
                    if (data.Type === "success") {
                        refreshGridApplicationCancellation();
                    }
                },
                error: function (data) {
                    showNotification('danger', jsResources.ErrorOccurred);
                }
            });
        }
    });
}

function partialApplicationEasyUse(EncryptedApplicationId) {
    $.post('/Appointment/Application/EasyUse', { encryptedApplicationId: EncryptedApplicationId },
        function (data) {
            $('#divPartialApplicationEasyUse').html(data);
        }, 'html');
}

$("#formApplicationEasyUse").submit(function (e) {

    e.preventDefault();
    var form = $(this);
    var validator = form.kendoValidator().data("kendoValidator");

    if (!validator.validate()) return;

    $.ajax({
        type: "PUT",
        url: "/Appointment/Application/UpdateApplicationEasyUse",
        data: form.serialize(),
        success: function (data) {
            showNotification(data.Type, data.Message);
            if (data.Type === "success") {
                $('#modalApplicationEasyUse').modal('hide');
                refreshGridApplication();
            }
        },
        error: function (data) {
            showNotification('danger', jsResources.ErrorOccurred);
        }
    });

});

