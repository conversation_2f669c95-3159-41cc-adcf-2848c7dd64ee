﻿using Gateway.QMS.Application.Action.Dto;
using Gateway.QMS.Application.WhiteList.Dto;
using System;
using System.Collections.Generic;
using System.Threading.Tasks;
using Gateway.QMS.Application.BlackList.Dto;
using Gateway.QMS.Application.Ticket.Dto;
using StackExchange.Redis;
using RedLockNet;

namespace Gateway.QMS.Application
{
    public interface IRedisRepository
    {
        #region Transaction

        ITransaction BeginTransaction();
        Task ExecuteTransactionAsync(ITransaction trans);

        #endregion

        #region Lock

        bool IsLock(string key, string value, TimeSpan expireTime);
        Task RemoveLock(int branchId, int lineId, int lineDepartmentId);

        Task<bool> TryAcquireLock(string resource);
        void ReleaseLock();
        Task<IRedLock> LockInstance(string key);

        #endregion

        #region Token

        Task<RedisToken> GetTokenCache(int branchId, int lineId, string token);
        Task AddTokenCache(ITransaction trans, RedisToken redisModel, int branchId, int lineId, string token);
        Task AddTokenCacheWithoutTransaction(RedisToken redisModel, int branchId, int lineId, string token);

        #endregion

        #region Active Token

        Task<List<RedisLineActiveToken>> GetRedisActiveTokens(int branchId, int lineId);
        Task<RedisToken.TokenApplicant> GetApplicantInfo(int branchId, int lineId, string token, int applicantNumber);
        Task AddActiveToken(ITransaction trans, RedisLineActiveToken activeToken, int branchId, int lineId);
        Task UpdateActiveToken(ITransaction trans, RedisLineActiveToken activeToken, int branchId, int lineId);
        Task UpdateActiveTokens(ITransaction trans, List<RedisLineActiveToken> activeTokens, int branchId, int lineId);
        Task UpdateSelectedActiveToken(ITransaction trans, RedisLineActiveToken activeToken, int branchId, int lineId);
        Task DeleteCompletedActiveToken(ITransaction trans, RedisLineActiveToken activeToken, int branchId, int lineId);
        Task<bool> UpdateActiveTokenWithoutTransaction(RedisLineActiveToken activeToken, int branchId, int lineId);

        #endregion

        #region User

        Task<RedisUserToken> GetUserCache(int branchId, int userId);
        Task DeleteUserCache(int branchId, int userId);
        Task<List<RedisUserToken>> GetUserWaitingCache(int branchId, int userId);
        Task AddUserCache(ITransaction trans, RedisUserToken userCache, int branchId, int userId);
        Task AddUserWaitingCache(ITransaction trans, List<RedisUserToken> userWaitingCache, int branchId, int userId);
        Task AddUserWaitingCacheWithoutTransaction(List<RedisUserToken> userWaitingCache, int branchId, int userId);

        #endregion

        #region LineDepartment

        Task<RedisLineDepartment> GetLineDepartmentCache(int branchId, int lineId, int lineDepartmentId);
        Task AddLineDepartmentCache(ITransaction trans, RedisLineDepartment lineDepartmentCache, int branchId, int lineId, int lineDepartmentId);
        Task<bool> IsCounterBusy(int branchId, int lineId, int counterId, int lineDepartmentId);

        #endregion

        #region TokenDepartment

        Task<RedisTokenDepartmentList> GetDepartments(int branchId, int lineId, string token, Guid id);
        Task<RedisTokenDepartmentList> GetTokenDepartmentCache(int branchId, int lineId, string tokenNumber, Guid id);
        Task AddTokenDepartments(ITransaction trans, RedisTokenDepartmentList tokenDepartment, int branchId, int lineId, string token, Guid id);
        Task AddTokenDepartmentsWithoutTransaction(RedisTokenDepartmentList tokenDepartment, int branchId, int lineId, string token, Guid id);
        Task AddTokenCache(ITransaction trans, RedisToken tokenCache, int branchId, int lineId);

        #endregion

        #region PassportDeliveryApplication

        Task<PassportDeliveryApplication> GetPassportDeliveryApplicationCache(int branchId, int lineId);
        Task AddPassportDeliveryApplications(ITransaction trans, int branchId, int lineId, List<PassportDeliveryApplication.Application> newApplications);

        #endregion

        #region Whitelist

        Task<List<RedisWhitelistModel>> GetWhitelistMembers(int branchId);
        Task<bool> GetWhitelistMember(RedisWhitelistModel person);
        Task AddWhitelist(RedisWhitelistModel redisWhitelistModel);
        Task UpdateWhitelist(RedisWhitelistModel redisWhitelistModel);
        Task DeleteWhitelist(int branchId, int id);

        #endregion

        #region Blacklist

        Task<bool> GetBlacklistMember(RedisWhitelistModel person);
        Task AddBlacklist(RedisBlacklistModel redisBlacklistModel);
        Task UpdateBlacklist(RedisBlacklistModel redisBlacklistModel);
        Task DeleteBlacklist(int id);

        #endregion

        #region Number Of Waiting

        Task<RedisNumberOfWaitingModel> GetWaitingCount(int branchId, int lineId, int lineDepartmentId);
        Task<RedisNumberOfWaitingModel> AddWaitingCount(ITransaction trans, int branchId, int lineId, int lineDepartmentId, int number);
        Task RemoveWaitingCountWithoutTransaction(int branchId, int lineId, int lineDepartmentId, int number);
        Task<RedisNumberOfWaitingModel> RemoveWaitingCount(ITransaction trans, int branchId, int lineId, int lineDepartmentId, int number);

        #endregion

        #region Number Of Total Waitings (Dashboard)

        Task<RedisTotalNumberOfWaitingModel> GetTotalWaitingCount(int branchId);
        Task AddTotalWaitingCount(ITransaction trans, int branchId, int callNextCount, int holdOnCount);
        Task RemoveTotalWaitingCountWithoutTransaction(int branchId, int callNextCount, int holdOnCount);
        Task RemoveTotalWaitingCount(ITransaction trans, int branchId, int callNextCount, int holdOnCount);

        #endregion
    }
}