﻿using System.Collections.Generic;

namespace Portal.Gateway.Entity.Entities.Portal
{
    public class CountryChecklist : AuditableEntity
    {
        public CountryChecklist()
        {
            IsActive = true;
            IsDeleted = false;
            CountryChecklistTranslations = new HashSet<CountryChecklistTranslation>();
        }
        public int Id { get; set; }
        public int CountryId { get; set; }
        public bool MustCheck { get; set; }
        public Country Country { get; set; }
        public virtual ICollection<CountryChecklistTranslation> CountryChecklistTranslations { get; set; }
    }
}
