﻿namespace Portal.Gateway.Contracts.Settings.AppSettings
{
    public class AppSettings
    {
        public string ApiKey { get; set; }
        public string CorporateId { get; set; }
        public string PortalDbConnectionString { get; set; }
        public PublicKey[] PublicKey { get; set; }
        public string ActiveDirectoryUrl { get; set; }
        public string EmaaInsuranceConfirmationUrl { get; set; }
        public string DigitalInsuranceUrl { get; set; }
        public string EsimBucket { get; set; }
        public bool EnableRejectedApplicationInsuranceCase { get; set; }
        public TinyUrl TinyUrl { get; set; }
        public B2B B2B { get; set; }
        public SftpConfiguration SftpConfiguration { get; set; }
        public FileEncryptDecrypt FileEncryptDecrypt { get; set; }
        public Cargo Cargo { get; set; }
    }

    public class TinyUrl
    {
        public string TokenKey { get; set; }
        public string ApiUrl { get; set; }
        public bool Enabled { get; set; }
        public string Domain { get; set; }
    }
    public class PublicKey
    {
        public string Token { get; set; }

        public string Username { get; set; }

        public string[] Urls { get; set; }
    }

    public class B2B
    {
        public string BaseUrl { get; set; }

        public string ForgetPasswordUrl { get; set; }
    }

    public class SftpConfiguration
    {
        public string Host { get; set; }

        public int Port { get; set; }

        public string Username { get; set; }

        public string Password { get; set; }

        public string Fingerprint { get; set; }

        public string RootFolder { get; set; }
    }

    public class FileEncryptDecrypt
    {
        public string CipherKey { get; set; }

        public string CipherIV { get; set; }
    }

    public class Cargo
    {
        public string BaseApiUrl { get; set; }
        public string Track { get; set; }
        public string ListCargoToCheckStatus { get; set; }
        public string UpdateCargoStatus { get; set; }
        public bool IsEnabled { get; set; }
    }
}
