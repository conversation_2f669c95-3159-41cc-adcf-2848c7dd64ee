﻿using FluentValidation;
using Microsoft.AspNetCore.Mvc.Rendering;
using Portal.Gateway.Resources;
using System.Collections.Generic;

namespace Portal.Gateway.UI.Areas.Management.ViewModels.BranchApplicationCountryExtraFee
{
    public class UpdateBranchApplicationCountryExtraFeeViewModel
    {
        public string EncryptedId { get; set; }

        public int BranchApplicationCountryId { get; set; }

        public int ExtraFeeId { get; set; }

        public bool IsAutoChecked { get; set; }

        public decimal Price { get; set; }

        public decimal Tax { get; set; }

        public decimal TaxRatio { get; set; }

        public decimal ServiceTax { get; set; }

        public decimal BasePrice { get; set; }

        public int CurrencyId { get; set; }

        public string SapExtraFeeId { get; set; }

        public bool IsActive { get; set; }

        public string BranchName { get; set; }

        public string CountryName { get; set; }

        public bool ShowInICR { get; set; }

        public bool ShowInSummary { get; set; }
        public bool IsShowInReport { get; set; }
        public bool IsGroupInIcr { get; set; }
        public bool IsShowInRejectionList { get; set; }
		public bool IsShowInAllApplicationsReport { get; set; }
        public bool IsShowInApplicationAfterRejection { get; set; }

        public List<SelectListItem> CurrencyList { get; set; }
    }

    public class UpdateBranchApplicationCountryExtraFeeViewModelValidator : AbstractValidator<UpdateBranchApplicationCountryExtraFeeViewModel>
    {
        public UpdateBranchApplicationCountryExtraFeeViewModelValidator()
        {
            RuleFor(x => x.EncryptedId)
                .NotEmpty().WithMessage(SiteResources.RequiredField);

            RuleFor(x => x.ExtraFeeId)
                .NotEmpty().WithMessage(SiteResources.RequiredField)
                .GreaterThan(0).WithMessage(SiteResources.RequiredField);

            RuleFor(x => x.BranchApplicationCountryId)
                .NotEmpty().WithMessage(SiteResources.RequiredField)
                .GreaterThan(0).WithMessage(SiteResources.RequiredField);

            RuleFor(x => x.CurrencyId)
                .NotEmpty().WithMessage(SiteResources.RequiredField)
                .GreaterThan(0).WithMessage(SiteResources.RequiredField);
        }
    }
}
