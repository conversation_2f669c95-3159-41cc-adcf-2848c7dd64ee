﻿using Gateway.Extensions;
using Gateway.QMS.Application.Action.Dto;
using Gateway.QMS.Application.Action.Event;
using Gateway.QMS.Application.BlackList.Dto;
using Gateway.QMS.Application.Ticket.Dto;
using Gateway.QMS.Application.WhiteList.Dto;
using Gateway.Redis;
using RedLockNet;
using StackExchange.Redis;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Threading.Tasks;

namespace Gateway.QMS.Application
{
    public class RedisRepository : IRedisRepository
    {
        private readonly IRedisClient _redisClient;

        public RedisRepository(IRedisClient redisClient)
        {
            _redisClient = redisClient;
        }

        #region Public Methods

        #region Transaction

        public ITransaction BeginTransaction()
        {
            return _redisClient.BeginTransaction();
        }

        public async Task ExecuteTransactionAsync(ITransaction trans)
        {
            await _redisClient.ExecuteTransactionAsync(trans);
        }

        #endregion

        #region Lock

        public bool IsLock(string key, string value, TimeSpan expireTime)
        {
            return _redisClient.IsLock(key, value, expireTime);
        }

        public async Task RemoveLock(int branchId, int lineId, int lineDepartmentId)
        {

            var key = RedisKeysFactory.GetKey(RedisKeyType.InternalLockKey,
                new RedisActionEvent
                {
                    BranchId = branchId,
                    LineId = lineId,
                    LineDepartmentId = lineDepartmentId
                });

            var lockedKey = await _redisClient.ExistsAsync(key);

            if (lockedKey)
                await _redisClient.RemoveLock(key);
        }

        public async Task<bool> TryAcquireLock(string resource)
        {
            return await _redisClient.TryAcquireLock(resource);
        }

        public void ReleaseLock()
        {
            _redisClient.ReleaseLock();
        }

        public async Task<IRedLock> LockInstance(string key)
        {
            return await _redisClient.LockInstance(key);
        }

        #endregion

        #region Token

        public async Task<RedisToken> GetTokenCache(int branchId, int lineId, string token)
        {
            return await _redisClient.GetAsync<RedisToken>(RedisKeysFactory.GetKey(
                RedisKeyType.Token,
                new RedisActionEvent
                {
                    BranchId = branchId,
                    LineId = lineId,
                    TokenNumber = token
                }));
        }

        public Task AddTokenCache(ITransaction trans, RedisToken redisModel, int branchId, int lineId, string token)
        {
            _ = trans.StringSetAsync(RedisKeysFactory.GetKey(RedisKeyType.Token,
                new RedisActionEvent
                {
                    BranchId = branchId,
                    LineId = lineId,
                    TokenNumber = token
                }), redisModel.ToJson(), GetTtl());

            return Task.CompletedTask;
        }

        public async Task AddTokenCacheWithoutTransaction(RedisToken redisModel, int branchId, int lineId, string token)
        {
            await _redisClient.AddStringAsync(RedisKeysFactory.GetKey(RedisKeyType.Token,
                new RedisActionEvent
                {
                    BranchId = branchId,
                    LineId = lineId,
                    TokenNumber = token
                }), redisModel, GetTtlHours());
        }

        #endregion

        #region Active Token

        public async Task<List<RedisLineActiveToken>> GetRedisActiveTokens(int branchId, int lineId)
        {
            return await _redisClient.GetAsync<List<RedisLineActiveToken>>(RedisKeysFactory.GetKey(RedisKeyType.ActiveToken,
                new RedisActionEvent
                {
                    BranchId = branchId,
                    LineId = lineId
                }));
        }

        public async Task<RedisToken.TokenApplicant> GetApplicantInfo(int branchId, int lineId, string token, int applicantNumber)
        {
            var activeToken = await _redisClient.GetAsync<RedisToken>(RedisKeysFactory.GetKey(RedisKeyType.Token, new RedisActionEvent
            {
                BranchId = branchId,
                LineId = lineId,
                TokenNumber = token
            }));

            var activeApplicant = activeToken?.Applicants?.Find(r => r.Id == applicantNumber);

            return activeApplicant ?? new RedisToken.TokenApplicant();
        }

        public async Task AddActiveToken(ITransaction trans, RedisLineActiveToken activeToken, int branchId, int lineId)
        {
            var activeTokens = await _redisClient.GetAsync<List<RedisLineActiveToken>>(RedisKeysFactory.GetKey(
                RedisKeyType.ActiveToken,
                new RedisActionEvent
                {
                    BranchId = branchId,
                    LineId = lineId
                }));

            if (activeTokens is null)
                activeTokens = new List<RedisLineActiveToken> { activeToken };
            else
                activeTokens.Add(activeToken);

            _ = trans.StringSetAsync(RedisKeysFactory.GetKey(RedisKeyType.ActiveToken,
                new RedisActionEvent
                {
                    BranchId = branchId,
                    LineId = lineId
                }), activeTokens.ToJson(), GetTtl());
        }

        public async Task UpdateActiveToken(ITransaction trans, RedisLineActiveToken activeToken, int branchId, int lineId)
        {
            var activeTokens = await _redisClient.GetAsync<List<RedisLineActiveToken>>(RedisKeysFactory.GetKey(
                RedisKeyType.ActiveToken,
                new RedisActionEvent
                {
                    BranchId = branchId,
                    LineId = lineId
                })) ?? throw new Exception("activeTokens not found");

            for (var i = 0; i < activeTokens.Count; i++)
            {
                if (activeTokens[i].Token == activeToken?.Token && activeTokens[i].Id == activeToken?.Id)
                {
                    activeTokens[i] = activeToken;
                }
            }

            _ = trans.StringSetAsync(RedisKeysFactory.GetKey(RedisKeyType.ActiveToken,
                new RedisActionEvent
                {
                    BranchId = branchId,
                    LineId = lineId
                }), activeTokens.ToJson(), GetTtl());
        }

        public Task UpdateActiveTokens(ITransaction trans, List<RedisLineActiveToken> activeTokens, int branchId, int lineId)
        {
            _ = trans.StringSetAsync(RedisKeysFactory.GetKey(RedisKeyType.ActiveToken,
                new RedisActionEvent
                {
                    BranchId = branchId,
                    LineId = lineId
                }), activeTokens.ToJson(), GetTtl());

            return Task.CompletedTask;
        }


        public async Task UpdateSelectedActiveToken(ITransaction trans, RedisLineActiveToken activeToken, int branchId, int lineId)
        {
            var activeTokens = await _redisClient.GetAsync<List<RedisLineActiveToken>>(RedisKeysFactory.GetKey(
                RedisKeyType.ActiveToken,
                new RedisActionEvent
                {
                    BranchId = branchId,
                    LineId = lineId
                })) ?? throw new Exception("activeTokens not found");

            for (var i = 0; i < activeTokens.Count; i++)
            {
                if (activeTokens[i].Id == activeToken?.Id)
                {
                    activeTokens[i] = activeToken;
                }
            }

            _ = trans.StringSetAsync(RedisKeysFactory.GetKey(RedisKeyType.ActiveToken,
                new RedisActionEvent
                {
                    BranchId = branchId,
                    LineId = lineId
                }), activeTokens.ToJson(), GetTtl());
        }

        public async Task DeleteCompletedActiveToken(ITransaction trans, RedisLineActiveToken activeToken, int branchId, int lineId)
        {
            var activeTokens = await _redisClient.GetAsync<List<RedisLineActiveToken>>(RedisKeysFactory.GetKey(
                RedisKeyType.ActiveToken,
                new RedisActionEvent
                {
                    BranchId = branchId,
                    LineId = lineId
                })) ?? throw new Exception("activeTokens not found");

            for (var i = 0; i < activeTokens.Count; i++)
            {
                if (activeTokens[i].Id == activeToken?.Id)
                {
                    activeTokens.Remove(activeTokens[i]);
                }
            }
            
            _ = trans.StringSetAsync(RedisKeysFactory.GetKey(RedisKeyType.ActiveToken,
                new RedisActionEvent
                {
                    BranchId = branchId,
                    LineId = lineId
                }), activeTokens.ToJson(), GetTtl());
        }

        public async Task<bool> UpdateActiveTokenWithoutTransaction(RedisLineActiveToken activeToken, int branchId, int lineId)
        {
            var activeTokens = await _redisClient.GetAsync<List<RedisLineActiveToken>>(RedisKeysFactory.GetKey(
                RedisKeyType.ActiveToken,
                new RedisActionEvent
                {
                    BranchId = branchId,
                    LineId = lineId
                }));

            if (activeTokens is null)
                return false;

            for (var i = 0; i < activeTokens.Count; i++)
            {
                if (activeTokens[i].Token == activeToken?.Token && activeTokens[i].Id == activeToken?.Id)
                {
                    activeTokens[i] = activeToken;
                }
            }

            await _redisClient.AddAsync(RedisKeysFactory.GetKey(RedisKeyType.ActiveToken,
                new RedisActionEvent
                {
                    BranchId = branchId,
                    LineId = lineId
                }), activeTokens);

            return true;
        }

        #endregion

        #region User

        public async Task<RedisUserToken> GetUserCache(int branchId, int userId)
        {
            return await _redisClient.GetAsync<RedisUserToken>(RedisKeysFactory.GetKey(
                RedisKeyType.User,
                new RedisActionEvent
                {
                    BranchId = branchId,
                    UserId = userId
                }));
        }

        public async Task DeleteUserCache(int branchId, int userId)
        {
            var token = await _redisClient.GetAsync<RedisUserToken>(RedisKeysFactory.GetKey(
                RedisKeyType.User,
                new RedisActionEvent
                {
                    BranchId = branchId,
                    UserId = userId
                }));

            if(token != null)
                _ = _redisClient.DeleteAsync(RedisKeysFactory.GetKey(RedisKeyType.User,
                    new RedisActionEvent
                    {
                        BranchId = branchId,
                        UserId = userId
                    }));
        }


        public async Task<List<RedisUserToken>>GetUserWaitingCache(int branchId, int userId)
        {
            return await _redisClient.GetAsync<List<RedisUserToken>>(RedisKeysFactory.GetKey(
                RedisKeyType.UserWaiting,
                new RedisActionEvent
                {
                    BranchId = branchId,
                    UserId = userId
                }));
        }

        public Task AddUserCache(ITransaction trans, RedisUserToken userCache, int branchId, int userId)
        {
            _ = trans.StringSetAsync(RedisKeysFactory.GetKey(RedisKeyType.User,
                new RedisActionEvent
                {
                    BranchId = branchId,
                    UserId = userId
                }), userCache.ToJson(), GetTtl());

            return Task.CompletedTask;
        }

        public Task AddUserWaitingCache(ITransaction trans, List<RedisUserToken> userWaitingCache, int branchId, int userId)
        {
            userWaitingCache = userWaitingCache.Where(p => !p.IsCalled).ToList();

            _ = trans.StringSetAsync(RedisKeysFactory.GetKey(RedisKeyType.UserWaiting,
                new RedisActionEvent
                {
                    BranchId = branchId,
                    UserId = userId
                }), userWaitingCache.ToJson(), GetTtl());

            return Task.CompletedTask;
        }

        public async Task AddUserWaitingCacheWithoutTransaction(List<RedisUserToken> userWaitingCache, int branchId, int userId)
        {
            userWaitingCache = userWaitingCache.Where(p => !p.IsCalled).ToList();

            await _redisClient.AddStringAsync(RedisKeysFactory.GetKey(RedisKeyType.UserWaiting,
                new RedisActionEvent
                {
                    BranchId = branchId,
                    UserId = userId
                }), userWaitingCache, GetTtlHours());
        }


        #endregion

        #region LineDepartment

        public async Task<RedisLineDepartment> GetLineDepartmentCache(int branchId, int lineId, int lineDepartmentId)
        {
            return await _redisClient.GetAsync<RedisLineDepartment>(RedisKeysFactory.GetKey(
                RedisKeyType.LineDepartment,
                new RedisActionEvent
                {
                    BranchId = branchId,
                    LineId = lineId,
                    LineDepartmentId = lineDepartmentId
                }));
        }

        public Task AddLineDepartmentCache(ITransaction trans, RedisLineDepartment lineDepartmentCache, int branchId, int lineId, int lineDepartmentId)
        {
            _ = trans.StringSetAsync(RedisKeysFactory.GetKey(RedisKeyType.LineDepartment,
                new RedisActionEvent
                {
                    BranchId = branchId,
                    LineId = lineId,
                    LineDepartmentId = lineDepartmentId
                }), lineDepartmentCache.ToJson(), GetTtl());

            return Task.CompletedTask;
        }

        public async Task<bool> IsCounterBusy(int branchId, int lineId, int counterId, int lineDepartmentId)
        {
            var lineDepartmentCache = await _redisClient.GetAsync<RedisLineDepartment>(RedisKeysFactory.GetKey(
                RedisKeyType.LineDepartment,
                new RedisActionEvent
                {
                    BranchId = branchId,
                    LineId = lineId,
                    LineDepartmentId = lineDepartmentId
                }));

            var counterIsBusy = lineDepartmentCache?.Tokens?.Exists(p => p.CounterId == counterId && p.Status == (byte)Enums.Enums.TokenStatus.Created);

            return counterIsBusy.GetValueOrDefault();
        }

        #endregion

        #region TokenDepartment

        public Task AddTokenDepartments(ITransaction trans, RedisTokenDepartmentList tokenDepartment, int branchId, int lineId, string token, Guid id)
        {
            _ = trans.StringSetAsync(RedisKeysFactory.GetKey(RedisKeyType.TokenDepartments,
                new RedisActionEvent
                {
                    BranchId = branchId,
                    LineId = lineId,
                    TokenNumber = token,
                    Id = id
                }), tokenDepartment.ToJson(), GetTtl());
            return Task.CompletedTask;
        }

        public async Task AddTokenDepartmentsWithoutTransaction(RedisTokenDepartmentList tokenDepartment, int branchId, int lineId, string token, Guid id)
        {
            await _redisClient.AddAsync(RedisKeysFactory.GetKey(RedisKeyType.TokenDepartments,
                new RedisActionEvent
                {
                    BranchId = branchId,
                    LineId = lineId,
                    TokenNumber = token,
                    Id = id
                }), tokenDepartment);
        }

        public Task AddTokenCache(ITransaction trans, RedisToken tokenCache, int branchId, int lineId)
        {
            _ = trans.StringSetAsync(RedisKeysFactory.GetKey(RedisKeyType.Token,
                new RedisActionEvent
                {
                    BranchId = branchId,
                    LineId = lineId,
                    TokenNumber = tokenCache.Token
                }), tokenCache.ToJson(), GetTtl());

            return Task.CompletedTask;
        }

        public async Task<RedisTokenDepartmentList> GetDepartments(int branchId, int lineId, string token, Guid id)
        {
            var activeDepartment = await _redisClient.GetAsync<RedisTokenDepartmentList>(RedisKeysFactory.GetKey(
                RedisKeyType.TokenDepartments,
                new RedisActionEvent
                {
                    BranchId = branchId,
                    LineId = lineId,
                    TokenNumber = token,
                    Id = id
                }));

            return activeDepartment;
        }

        public async Task<RedisTokenDepartmentList> GetTokenDepartmentCache(int branchId, int lineId, string tokenNumber, Guid id)
        {
            var activeTokenDepartment = await _redisClient.GetAsync<RedisTokenDepartmentList>(RedisKeysFactory.GetKey(
                RedisKeyType.TokenDepartments,
                new RedisActionEvent
                {
                    BranchId = branchId,
                    LineId = lineId,
                    TokenNumber = tokenNumber,
                    Id = id
                }));

            return activeTokenDepartment;
        }

        #endregion

        #region PassportDeliveryApplication

        public async Task<PassportDeliveryApplication> GetPassportDeliveryApplicationCache(int branchId, int lineId)
        {
            return await _redisClient.GetAsync<PassportDeliveryApplication>(RedisKeysFactory.GetKey(
                RedisKeyType.PassportDeliveryApplication,
                new RedisActionEvent
                {
                    BranchId = branchId,
                    LineId = lineId
                }));
        }

        public async Task AddPassportDeliveryApplications(ITransaction trans, int branchId, int lineId, List<PassportDeliveryApplication.Application> newApplications)
        {
            var applications = await _redisClient.GetAsync<PassportDeliveryApplication>(RedisKeysFactory.GetKey(
                RedisKeyType.PassportDeliveryApplication,
                new RedisActionEvent
                {
                    BranchId = branchId,
                    LineId = lineId
                }));

            if (applications == null)
                applications = new PassportDeliveryApplication { Applications = newApplications };           

            else
            {
                var changedApplicants = applications.Applications
                    .Where(p => newApplications.Select(q => q.PreApplicationApplicantId).Contains(p.PreApplicationApplicantId))
                .ToList();

                if (changedApplicants.Count > 0)
                {
                    foreach (var applicant in changedApplicants)
                    {
                        var changedApplicant = newApplications.Find(p => p.PreApplicationApplicantId == applicant.PreApplicationApplicantId);

                        applicant.ApplicationId = changedApplicant.ApplicationId;
                        applicant.PassportNumber = changedApplicant.PassportNumber;
                        applicant.ApplicationTime = changedApplicant.ApplicationTime;

                        newApplications.Remove(changedApplicant);
                    }
                }
                else
                    applications.Applications.AddRange(newApplications);
            }
            
            _ = trans.StringSetAsync(RedisKeysFactory.GetKey(RedisKeyType.PassportDeliveryApplication,
                new RedisActionEvent
                {
                    BranchId = branchId,
                    LineId = lineId
                }), applications.ToJson(), GetTtl());
        }

        #endregion

        #region Whitelist

        public async Task<bool> GetWhitelistMember(RedisWhitelistModel person)
        {
            var whiteListMembers = await _redisClient.GetAsync<List<RedisWhitelistModel>>(RedisKeysFactory.GetKey(RedisKeyType.Whitelist,
                new RedisActionEvent { BranchId = person.BranchId }));

            return whiteListMembers != null && whiteListMembers.Exists(w => w.PassportNumber == person.PassportNumber && w.NationalityId == person.NationalityId);
        }

        public async Task<List<RedisWhitelistModel>> GetWhitelistMembers(int branchId)
        {
            return await _redisClient.GetAsync<List<RedisWhitelistModel>>(RedisKeysFactory.GetKey(RedisKeyType.Whitelist,
                new RedisActionEvent { BranchId = branchId }));
        }

        public async Task AddWhitelist(RedisWhitelistModel redisWhitelistModel)
        {
            var whiteList = await _redisClient.GetAsync<List<RedisWhitelistModel>>(RedisKeysFactory.GetKey(RedisKeyType.Whitelist,
                new RedisActionEvent { BranchId = redisWhitelistModel.BranchId }));

            if (whiteList is null)
                whiteList = new List<RedisWhitelistModel> { redisWhitelistModel };
            else
                whiteList.Add(redisWhitelistModel);

            await _redisClient.AddTimelessAsync(RedisKeysFactory.GetKey(RedisKeyType.Whitelist,
                new RedisActionEvent
                {
                    BranchId = redisWhitelistModel.BranchId,
                    NationalityId = redisWhitelistModel.NationalityId
                }), whiteList);
        }

        public async Task UpdateWhitelist(RedisWhitelistModel redisWhitelistModel)
        {
            var whiteList = await _redisClient.GetAsync<List<RedisWhitelistModel>>(RedisKeysFactory.GetKey(RedisKeyType.Whitelist, new RedisActionEvent { BranchId = redisWhitelistModel.BranchId }));

            if (whiteList is null)
                return;

            for (var i = 0; i < whiteList.Count; i++)
            {
                if (whiteList[i].Id == redisWhitelistModel.Id)
                {
                    whiteList[i] = redisWhitelistModel;
                }
            }

            await _redisClient.AddTimelessAsync(RedisKeysFactory.GetKey(RedisKeyType.Whitelist, new RedisActionEvent { BranchId = redisWhitelistModel.BranchId }), whiteList);
        }

        public async Task DeleteWhitelist(int branchId, int id)
        {
            var whiteList = await _redisClient.GetAsync<List<RedisWhitelistModel>>(RedisKeysFactory.GetKey(RedisKeyType.Whitelist, new RedisActionEvent { BranchId = branchId }));

            if (whiteList is null)
                return;

            for (var i = 0; i < whiteList.Count; i++)
            {
                if (whiteList[i].Id == id)
                {
                    whiteList.Remove(whiteList[i]);
                }
            }

            await _redisClient.AddTimelessAsync(RedisKeysFactory.GetKey(RedisKeyType.Whitelist, new RedisActionEvent { BranchId = branchId }), whiteList);
        }

        #endregion

        #region Blacklist

        public async Task<bool> GetBlacklistMember(RedisWhitelistModel person)
        {
            var blackListMembers = await _redisClient.GetAsync<List<RedisWhitelistModel>>(RedisKeysFactory.GetKey(RedisKeyType.BlackList,
                new RedisActionEvent()));

            return blackListMembers != null && blackListMembers.Exists(b => b.PassportNumber == person.PassportNumber && b.NationalityId == person.NationalityId);
        }

        public async Task AddBlacklist(RedisBlacklistModel redisBlacklistModel)
        {
            var blackList = await _redisClient.GetAsync<List<RedisBlacklistModel>>(RedisKeysFactory.GetKey(RedisKeyType.BlackList, new RedisActionEvent()));

            if (blackList is null)
                blackList = new List<RedisBlacklistModel> { redisBlacklistModel };
            else
                blackList.Add(redisBlacklistModel);

            await _redisClient.AddTimelessAsync(RedisKeysFactory.GetKey(RedisKeyType.BlackList,
                new RedisActionEvent()), blackList);
        }

        public async Task UpdateBlacklist(RedisBlacklistModel redisBlacklistModel)
        {
            var blackList = await _redisClient.GetAsync<List<RedisBlacklistModel>>(RedisKeysFactory.GetKey(RedisKeyType.BlackList, new RedisActionEvent()));

            if (blackList is null)
                return;

            for (var i = 0; i < blackList.Count; i++)
            {
                if (blackList[i].Id == redisBlacklistModel.Id)
                    blackList[i] = redisBlacklistModel;
            }

            await _redisClient.AddTimelessAsync(RedisKeysFactory.GetKey(RedisKeyType.BlackList, new RedisActionEvent()), blackList);
        }

        public async Task DeleteBlacklist(int id)
        {
            var blackList = await _redisClient.GetAsync<List<RedisBlacklistModel>>(RedisKeysFactory.GetKey(RedisKeyType.BlackList, new RedisActionEvent()));

            if (blackList is null)
                return;

            for (var i = 0; i < blackList.Count; i++)
            {
                if (blackList[i].Id == id)
                    blackList.Remove(blackList[i]);
            }

            await _redisClient.AddTimelessAsync(RedisKeysFactory.GetKey(RedisKeyType.BlackList, new RedisActionEvent()), blackList);
        }

        #endregion

        #region Number Of Waiting

        public async Task<RedisNumberOfWaitingModel> GetWaitingCount(int branchId, int lineId, int lineDepartmentId)
        {
            return await _redisClient.GetAsync<RedisNumberOfWaitingModel>(RedisKeysFactory.GetKey(RedisKeyType.NumberOfWaiting,
                new RedisActionEvent { BranchId = branchId, LineId = lineId, LineDepartmentId = lineDepartmentId }));
        }

        public async Task<RedisNumberOfWaitingModel> AddWaitingCount(ITransaction trans, int branchId, int lineId, int lineDepartmentId, int number)
        {
            var key = RedisKeysFactory.GetKey(RedisKeyType.NumberOfWaiting,
                new RedisActionEvent { BranchId = branchId, LineId = lineId, LineDepartmentId = lineDepartmentId });

            var numberOfWaitingModel = await _redisClient.GetAsync<RedisNumberOfWaitingModel>(key);

            if (numberOfWaitingModel?.Count < 0)
                return new RedisNumberOfWaitingModel();

            if (numberOfWaitingModel == null)
                numberOfWaitingModel = new RedisNumberOfWaitingModel { Count = number };

            else
                numberOfWaitingModel.Count += number;


            _ = trans.StringSetAsync(key, numberOfWaitingModel.ToJson(), GetTtl());

            return numberOfWaitingModel;
        }

        public async Task RemoveWaitingCountWithoutTransaction(int branchId, int lineId, int lineDepartmentId, int number)
        {
            var numberOfWaitingModel = await _redisClient.GetAsync<RedisNumberOfWaitingModel>(RedisKeysFactory.GetKey(RedisKeyType.NumberOfWaiting,
                new RedisActionEvent { BranchId = branchId, LineId = lineId, LineDepartmentId = lineDepartmentId }));

            if (numberOfWaitingModel?.Count < 0 || numberOfWaitingModel == null || numberOfWaitingModel.Count < number)
                return;

            numberOfWaitingModel.Count -= number;

            await _redisClient.AddAsync(RedisKeysFactory.GetKey(RedisKeyType.NumberOfWaiting,
                new RedisActionEvent { BranchId = branchId, LineId = lineId, LineDepartmentId = lineDepartmentId }), numberOfWaitingModel);
        }

        public async Task<RedisNumberOfWaitingModel> RemoveWaitingCount(ITransaction trans, int branchId, int lineId, int lineDepartmentId, int number)
        {
            var key = RedisKeysFactory.GetKey(RedisKeyType.NumberOfWaiting,
                new RedisActionEvent { BranchId = branchId, LineId = lineId, LineDepartmentId = lineDepartmentId });

            var numberOfWaitingModel = await _redisClient.GetAsync<RedisNumberOfWaitingModel>(key);

            if (numberOfWaitingModel?.Count < 0 || numberOfWaitingModel == null || numberOfWaitingModel.Count < number)
                return new RedisNumberOfWaitingModel { Count = -1 };

            numberOfWaitingModel.Count -= number;

            _ = trans.StringSetAsync(key, numberOfWaitingModel.ToJson(), GetTtl());

            return new RedisNumberOfWaitingModel { Count = numberOfWaitingModel.Count };
        }

        #endregion

        #region Number Of Total Waiting

        public async Task<RedisTotalNumberOfWaitingModel> GetTotalWaitingCount(int branchId)
        {
            return await _redisClient.GetAsync<RedisTotalNumberOfWaitingModel>(RedisKeysFactory.GetKey(RedisKeyType.TotalNumberOfWaitingToken,
                new RedisActionEvent { BranchId = branchId}));
        }

        public async Task AddTotalWaitingCount(ITransaction trans, int branchId, int callNextCount, int holdOnCount)
        {
            var key = RedisKeysFactory.GetKey(RedisKeyType.TotalNumberOfWaitingToken,
                new RedisActionEvent { BranchId = branchId });

            var numberOfWaitingModel = await _redisClient.GetAsync<RedisTotalNumberOfWaitingModel>(key);

            if (numberOfWaitingModel?.CallNextCount < 0 || numberOfWaitingModel?.HoldOnCount < 0)
                return;

            if (numberOfWaitingModel == null)
                numberOfWaitingModel = new RedisTotalNumberOfWaitingModel
                {
                    CallNextCount = callNextCount,
                    HoldOnCount = holdOnCount
                };
            else
            {
                numberOfWaitingModel.CallNextCount += callNextCount;
                numberOfWaitingModel.HoldOnCount += holdOnCount;
            }
            _ = trans.StringSetAsync(key, numberOfWaitingModel.ToJson(), GetTtl());
        }

        public async Task RemoveTotalWaitingCountWithoutTransaction(int branchId, int callNextCount, int holdOnCount)
        {
            var numberOfWaitingModel = await _redisClient.GetAsync<RedisTotalNumberOfWaitingModel>(RedisKeysFactory.GetKey(RedisKeyType.TotalNumberOfWaitingToken,
                new RedisActionEvent { BranchId = branchId }));

            if (numberOfWaitingModel == null || numberOfWaitingModel.CallNextCount < 0 ||
                numberOfWaitingModel.HoldOnCount < 0 || numberOfWaitingModel.CallNextCount < callNextCount ||
                numberOfWaitingModel.HoldOnCount < holdOnCount)
                return;

            numberOfWaitingModel.CallNextCount -= callNextCount;
            numberOfWaitingModel.HoldOnCount -= holdOnCount;

            await _redisClient.AddAsync(RedisKeysFactory.GetKey(RedisKeyType.TotalNumberOfWaitingToken,
                new RedisActionEvent { BranchId = branchId }), numberOfWaitingModel);
        }

        public async Task RemoveTotalWaitingCount(ITransaction trans, int branchId, int callNextCount, int holdOnCount)
        {
            var key = RedisKeysFactory.GetKey(RedisKeyType.TotalNumberOfWaitingToken,
                new RedisActionEvent { BranchId = branchId });
            var numberOfWaitingModel = await _redisClient.GetAsync<RedisTotalNumberOfWaitingModel>(key);

            if (numberOfWaitingModel == null 
                || numberOfWaitingModel.CallNextCount < 0 
                || numberOfWaitingModel.HoldOnCount < 0 
                || numberOfWaitingModel.CallNextCount < callNextCount 
                || numberOfWaitingModel.HoldOnCount < holdOnCount)
                return;

            numberOfWaitingModel.CallNextCount -= callNextCount;
            numberOfWaitingModel.HoldOnCount -= holdOnCount;

            _ = trans.StringSetAsync(key, numberOfWaitingModel.ToJson(), GetTtl());
        }


        #endregion

        #endregion

        #region Private Methods

        private static TimeSpan GetTtl() => TimeSpan.FromHours(24);
        private static int GetTtlHours() => (int)TimeSpan.FromHours(24).TotalHours;

        #endregion
    }
}