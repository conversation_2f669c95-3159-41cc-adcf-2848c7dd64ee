﻿using System;
using System.Globalization;
using System.Resources;
using System.Text.RegularExpressions;
using Gateway.External.Resources;
using Portal.Gateway.Common.Utility.Extensions;

namespace Gateway.External.Application.Extensions
{
    public static class StringExtensions
    {
        public static string ToSiteResourcesValue(this string value, int languageId = (int)Enums.Enums.Language.English)
        {
            var cultureInfo = new CultureInfo(((Enums.Enums.Culture)languageId).ToDescription());
            var rm = new ResourceManager(typeof(ServiceResources));
            return rm.GetString(value, cultureInfo);
        }

        public static string ToEnumResourcesValue(this string value, int languageId = (int)Enums.Enums.Language.English)
        {
            var cultureInfo = new CultureInfo(((Enums.Enums.Culture)languageId).ToDescription());
            var rm = new ResourceManager(typeof(EnumResources));
            return rm.GetString(value, cultureInfo);
        }

        public static bool IsPassportNumberValid(this string passportNumber)
        {
            return new Regex(@"^(?!^0+$)[A-Za-z0-9]{6,9}$").IsMatch(passportNumber);
        }
    }
}
