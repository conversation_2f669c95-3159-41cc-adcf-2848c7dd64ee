﻿using Serilog;
using Serilog.Formatting.Json;
using System;
using Gateway.Logger.Core.Models;
using Serilog.Core;
using Serilog.Events;
using Serilog.Sinks.RabbitMQ;
using System.Reflection;
using System.Linq;
using Gateway.Extensions;

namespace Gateway.Logger.Core.Configuration
{
    public static class LoggerConfigurationBuilder
    {
        public static Serilog.Core.Logger GetLoggerConfiguration(LogConfiguration model)
        {
            return new LoggerConfiguration()
                .Enrich.With<SuppressAuthSchemeLogs>()
                .Filter.ByExcluding(e => e.Properties.ContainsKey("ShouldSkipLog"))
                .Enrich.FromLogContext()
                .Enrich.WithProperty(LogConstants.Instance, model.Instance)
                .Enrich.WithProperty(LogConstants.DateTime, DateTime.UtcNow)
                .Enrich.WithProperty(LogConstants.Application, model.Application)
                .Enrich.WithEnvironmentName()
                .Enrich.With(new RemoveProperties())
                .MinimumLevel.Override("Microsoft", LogConfiguration.MicrosoftLogLevel)
                .MinimumLevel.Override("System", LogConfiguration.SystemLogLevel)
                .MinimumLevel.Information()
                .WriteTo.Console()
                .WriteTo.RabbitMQ((clientConfiguration, sinkConfiguration) =>
                {
                    clientConfiguration.Username = model.Configuration["RabbitMq:User"];
                    clientConfiguration.Password = model.Configuration["RabbitMq:Password"];
                    clientConfiguration.Exchange = model.Configuration["RabbitMq:Exchange"].AddEnvironmentSuffix();
                    clientConfiguration.ExchangeType = "direct";
                    clientConfiguration.DeliveryMode = RabbitMQDeliveryMode.Durable;
                    clientConfiguration.Port = Convert.ToInt32(model.Configuration["RabbitMq:Port"]);
                    clientConfiguration.VHost = "/";
                    clientConfiguration.Hostnames.Add(model.Configuration["RabbitMq:Host"]);
                    clientConfiguration.AutoCreateExchange = true;
                    sinkConfiguration.RestrictedToMinimumLevel = LogConfiguration.DefaultLogLevel;
                    sinkConfiguration.TextFormatter = new JsonFormatter(renderMessage: true);
                })
                .CreateLogger();
        }
    }

    internal class RemoveProperties : ILogEventEnricher
    {
        public void Enrich(LogEvent logEvent, ILogEventPropertyFactory logEventPropertyFactory)
        {
            logEvent.RemovePropertyIfPresent("ConnectionId");
        }
    }

    internal class SuppressAuthSchemeLogs : ILogEventEnricher
    {
        public void Enrich(LogEvent logEvent, ILogEventPropertyFactory propertyFactory)
        {
            if (logEvent.Properties.ContainsKey("AuthenticationScheme") && logEvent.Properties.ContainsKey("FailureMessage"))
            {
                logEvent.AddPropertyIfAbsent(propertyFactory.CreateProperty("ShouldSkipLog", true));
            }
        }
    }
}