﻿using FluentValidation;
using Gateway.Extensions;
using Gateway.External.Application.Application.Dto;
using Gateway.External.Application.Application.Dto.Request;
using Gateway.External.Resources;
using System;
using System.Globalization;
using System.Linq;

namespace Gateway.External.Application.Application.Validators
{
    internal class GetApplicationStatusValidator : AbstractValidator<GetApplicationStatusRequest>
    {
        public GetApplicationStatusValidator()
        {
            RuleFor(p => p).Custom((item, context) =>
            {
                if (item.BirthDate.StringToDateTime("ddMMyyyy", CultureInfo.InvariantCulture) == DateTime.MinValue)
                    context.AddFailure(string.Format(ServiceResources.PROPERTY_FORMAT_ERROR, nameof(item.BirthDate)));
            });

            RuleFor(p => p).Custom((item, context) =>
            {
                if (!item.ApplicationNumber.IsNumericAndGreaterThenZero())
                    context.AddFailure(string.Format(ServiceResources.PROPERTY_REQUIRED, nameof(item.ApplicationNumber)));
            });
        }
    }

    internal class GetApplicationStatusByIdValidator : AbstractValidator<GetApplicationStatusByIdRequest>
    {
        public GetApplicationStatusByIdValidator()
        {
            RuleFor(p => p).Custom((item, context) =>
            {
                if (!item.ApplicationNumber.IsNumericAndGreaterThenZero())
                    context.AddFailure(string.Format(ServiceResources.PROPERTY_REQUIRED, nameof(item.ApplicationNumber)));
            });
        }
    }

    internal class GetApplicationStatusByAppointmentApplicantValidator : AbstractValidator<GetApplicationStatusByAppointmentRequest>
    {
        public GetApplicationStatusByAppointmentApplicantValidator()
        {
            RuleFor(p => p).Custom((item, context) =>
            {
                if (!item.AppointmentId.IsNumericAndGreaterThenZero())
                    context.AddFailure(string.Format(ServiceResources.PROPERTY_REQUIRED, nameof(item.AppointmentId)));
            });
        }
    }

    internal class GetYssApplicationValidator : AbstractValidator<GetYssApplicationRequest>
    {
        public GetYssApplicationValidator()
        {
            RuleFor(p => p).Custom((item, context) =>
            {
                if (string.IsNullOrWhiteSpace(item.PassportNumber))
                {
                    context.AddFailure(string.Format(ServiceResources.PROPERTY_REQUIRED, nameof(item.PassportNumber)));
                }
            });

            RuleFor(p => p).Custom((item, context) =>
            {
                if (string.IsNullOrWhiteSpace(item.BirthDate))
                {
                    context.AddFailure(string.Format(ServiceResources.PROPERTY_REQUIRED, nameof(item.BirthDate)));
                }
                else if (item.BirthDate.Length != 8)
                {
                    context.AddFailure(string.Format(ServiceResources.PROPERTY_MAX_LENGTH_ERROR, nameof(item.BirthDate)));
                }
                else if(item.BirthDate != null && !item.BirthDate.All(char.IsDigit))
                {
                    context.AddFailure(string.Format(ServiceResources.PROPERTY_FORMAT_ERROR, nameof(item.BirthDate)));
                }
                else if (item.BirthDate.StringToDateTime("ddMMyyyy", CultureInfo.InvariantCulture) == DateTime.MinValue)
                {
                    context.AddFailure(string.Format(ServiceResources.PROPERTY_FORMAT_ERROR, nameof(item.BirthDate)));
                }
            });
        }
    }
}
