﻿<Project Sdk="Microsoft.NET.Sdk.Worker">

  <PropertyGroup>
    <TargetFramework>net8.0</TargetFramework>
    <Nullable>enable</Nullable>
    <ImplicitUsings>enable</ImplicitUsings>
    <UserSecretsId>dotnet-Portal.Gateway.BackgroundWorkers-cf95326f-5ef3-4d51-9fb3-9c990174309b</UserSecretsId>
	<ErrorOnDuplicatePublishOutputFiles>false</ErrorOnDuplicatePublishOutputFiles>
  </PropertyGroup>

  <ItemGroup>
    <PackageReference Include="Microsoft.Extensions.Hosting" Version="8.0.1" />
	<PackageReference Include="Quartz" Version="3.14.0" />
	<PackageReference Include="Quartz.Extensions.Hosting" Version="3.14.0" />
	<PackageReference Include="Serilog.AspNetCore" Version="9.0.0" />
	<PackageReference Include="Microsoft.Extensions.Caching.StackExchangeRedis" Version="8.0.14" />
	<PackageReference Include="AutoMapper" Version="14.0.0" />
  </ItemGroup>
  <ItemGroup>
	<ProjectReference Include="..\Gateway.Cargo.Application\Gateway.Cargo.Application.csproj" />
	<ProjectReference Include="..\Gateway.EventBus\Gateway.EventBus.csproj" />
	<ProjectReference Include="..\Gateway.Logger.Core\Gateway.Logger.Core.csproj" />
	<ProjectReference Include="..\Gateway.Logger.Worker\Gateway.Logger.Application.csproj" />
	<ProjectReference Include="..\Gateway.Notification.Worker\Gateway.Notification.Application.csproj" />
	<ProjectReference Include="..\Portal.Gateway.Contracts\Portal.Gateway.Contracts.csproj" />
	<ProjectReference Include="..\Portal.Gateway.ExternalServices\Portal.Gateway.ExternalServices.csproj" />
	<ProjectReference Include="..\Portal.Gateway.Services\Portal.Gateway.Services.csproj" />
  </ItemGroup>
  <ItemGroup>
    <Content Update="appsettings.Development-K8S.json">
      <CopyToOutputDirectory>Always</CopyToOutputDirectory>
    </Content>
    <Content Update="appsettings.json">
      <CopyToOutputDirectory>Always</CopyToOutputDirectory>
      <ExcludeFromSingleFile>true</ExcludeFromSingleFile>
      <CopyToPublishDirectory>PreserveNewest</CopyToPublishDirectory>
    </Content>
    <Content Update="appsettings.Production-K8S.json">
      <CopyToOutputDirectory>Always</CopyToOutputDirectory>
      <ExcludeFromSingleFile>true</ExcludeFromSingleFile>
      <CopyToPublishDirectory>PreserveNewest</CopyToPublishDirectory>
    </Content>
  </ItemGroup>
  <ItemGroup>
    <None Update="Assets\documents\Fonts\arial.ttf">
      <CopyToOutputDirectory>PreserveNewest</CopyToOutputDirectory>
    </None>
  </ItemGroup>
</Project>
