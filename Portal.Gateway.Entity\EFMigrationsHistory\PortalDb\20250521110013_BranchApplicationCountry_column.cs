﻿using Microsoft.EntityFrameworkCore.Migrations;

#nullable disable

namespace Portal.Gateway.Entity.EFMigrationsHistory.PortalDb
{
    public partial class BranchApplicationCountry_column : Migration
    {
        protected override void Up(MigrationBuilder migrationBuilder)
        {
            migrationBuilder.AddColumn<bool>(
                name: "IsShowInApplicationAfterRejection",
                table: "BranchApplicationCountryExtraFee",
                type: "boolean",
                nullable: false,
                defaultValueSql: "false");

            migrationBuilder.CreateIndex(
                name: "IX_BranchApplicationCountryVisaCategory_VisaCategoryId",
                table: "BranchApplicationCountryVisaCategory",
                column: "VisaCategoryId");

            migrationBuilder.AddForeignKey(
                name: "FK_BranchApplicationCountryVisaCategory_VisaType_VisaCategoryId",
                table: "BranchApplicationCountryVisaCategory",
                column: "VisaCategoryId",
                principalTable: "VisaType",
                principalColumn: "Id");
        }

        protected override void Down(MigrationBuilder migrationBuilder)
        {
            migrationBuilder.DropForeignKey(
                name: "FK_BranchApplicationCountryVisaCategory_VisaType_VisaCategoryId",
                table: "BranchApplicationCountryVisaCategory");

            migrationBuilder.DropIndex(
                name: "IX_BranchApplicationCountryVisaCategory_VisaCategoryId",
                table: "BranchApplicationCountryVisaCategory");

            migrationBuilder.DropColumn(
                name: "IsShowInApplicationAfterRejection",
                table: "BranchApplicationCountryExtraFee");
        }
    }
}
