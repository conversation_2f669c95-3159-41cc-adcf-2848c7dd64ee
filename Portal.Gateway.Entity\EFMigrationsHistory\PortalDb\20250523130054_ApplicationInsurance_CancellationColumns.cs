﻿using System;
using Microsoft.EntityFrameworkCore.Migrations;
using Npgsql.EntityFrameworkCore.PostgreSQL.Metadata;

#nullable disable

namespace Portal.Gateway.Entity.EFMigrationsHistory.PortalDb
{
    public partial class ApplicationInsurance_CancellationColumns : Migration
    {
        protected override void Up(MigrationBuilder migrationBuilder)
        {
            migrationBuilder.AddColumn<int>(
                name: "CancellationReasonId",
                table: "ApplicationInsurance",
                type: "integer",
                nullable: true);

            migrationBuilder.AddColumn<int>(
                name: "CancelledBy",
                table: "ApplicationInsurance",
                type: "integer",
                nullable: true);

            migrationBuilder.AddColumn<DateTime>(
                name: "CancelledDate",
                table: "ApplicationInsurance",
                type: "timestamp without time zone",
                nullable: true);

            migrationBuilder.AddColumn<bool>(
                name: "IsCancelled",
                table: "ApplicationInsurance",
                type: "boolean",
                nullable: false,
                defaultValueSql: "false");
        }

        protected override void Down(MigrationBuilder migrationBuilder)
        {
            migrationBuilder.DropColumn(
                name: "CancellationReasonId",
                table: "ApplicationInsurance");

            migrationBuilder.DropColumn(
                name: "CancelledBy",
                table: "ApplicationInsurance");

            migrationBuilder.DropColumn(
                name: "CancelledDate",
                table: "ApplicationInsurance");

            migrationBuilder.DropColumn(
                name: "IsCancelled",
                table: "ApplicationInsurance");
        }
    }
}
