﻿using Microsoft.EntityFrameworkCore.Metadata.Builders;
using Microsoft.EntityFrameworkCore;
using Portal.Gateway.Entity.Entities.Portal;
using System;

namespace Portal.Gateway.Entity.EntityConfigurations.Portal
{
    public class ApplicationInsuranceEntityConfiguration : IEntityTypeConfiguration<ApplicationInsurance>
    {
        public void Configure(EntityTypeBuilder<ApplicationInsurance> builder)
        {
            builder.ToTable("ApplicationInsurance");

            builder.HasIndex(e => e.ApplicationId, "IX_ApplicationInsurance_ApplicationId");

            builder.HasIndex(e => e.ProviderId, "IX_ApplicationInsurance_ProviderId");

            #region StaticAuditableEntityConfig

            builder.HasKey(e => e.Id);
            builder.Property(e => e.Id).IsRequired().ValueGeneratedOnAdd();
            builder.Property(e => e.IsActive).IsRequired();
            builder.Property(e => e.IsDeleted).IsRequired();
            builder.Property(e => e.CreatedBy);
            builder.Property(e => e.CreatedAt).HasColumnType("timestamp with time zone");
            builder.Property(e => e.UpdatedBy);
            builder.Property(e => e.UpdatedAt).HasColumnType("timestamp with time zone");
            builder.Property(e => e.DeletedBy);
            builder.Property(e => e.DeletedAt).HasColumnType("timestamp with time zone");

            #endregion

            builder.Property(e => e.ApplicationExtraFeeId).HasDefaultValueSql("1");
            builder.Property(e => e.Number).HasColumnType("citext");
            builder.Property(e => e.ApplicationId).IsRequired();
            builder.Property(e => e.ProviderId).IsRequired();
            builder.Property(e => e.StartDate).IsRequired().HasColumnType("timestamp without time zone");
            builder.Property(e => e.EndDate).IsRequired().HasColumnType("timestamp without time zone");
            builder.Property(e => e.DocumentId).HasColumnType("citext");
            builder.Property(e => e.RelatedIndividualInsuraneId);
            builder.Property(e => e.OldRelatedIndividualInsuraneId);
            builder.Property(e => e.IsCancelled).IsRequired().HasDefaultValueSql("false");

            builder.HasOne(d => d.Application)
                .WithMany(p => p.ApplicationInsurance)
                .HasForeignKey(d => d.ApplicationId)
                .OnDelete(DeleteBehavior.ClientSetNull);

            builder.HasOne(d => d.Provider)
                .WithMany(p => p.ApplicationInsurances)
                .HasForeignKey(d => d.ProviderId)
                .OnDelete(DeleteBehavior.ClientSetNull);
        }
    }
}
