﻿@model IEnumerable<ApplicationViewModel>  

@functions {
    private Dictionary<string, bool> GetLatestApplications(IEnumerable<ApplicationViewModel> applications)
    {
        if (applications == null) return new();

        var excludedTypes = new HashSet<int>
        {
            (int)ApplicationType.NonApplicationInsurance,
            (int)ApplicationType.NonApplication,
            (int)ApplicationType.NonApplicationPcr,
            (int)ApplicationType.NonApplicationPhotocopy,
            (int)ApplicationType.NonApplicationPrintOut,
            (int)ApplicationType.NonApplicationPhotograph
        };

        var relevantApps = applications.Where(app => !excludedTypes.Contains(app.ApplicationTypeId)).ToList();

        var latestByBranchAppCountryId = relevantApps
            .GroupBy(app => app.BranchApplicationCountryId)
            .ToDictionary(
                g => g.Key,
                g => g.OrderByDescending(app => app.ApplicationTime)
                    .Select(app => app.ApplicationNumber)
                    .FirstOrDefault()
            );

        return relevantApps.ToDictionary(
            app => app.ApplicationNumber,
            app => latestByBranchAppCountryId.TryGetValue(app.BranchApplicationCountryId, out var latestNumber)
                   && latestNumber == app.ApplicationNumber
        );
    }
}

@{
    var latestApplications = GetLatestApplications(Model);
}


<div class="card-body" style="max-height: 400px; overflow:auto;">  
   <div class="row">  
       <div class="col-xl-12">  
           @if (Model != null)  
           {  
               <table class="table">  
                   <thead>  
                   <tr>  
                       <th scope="col">#</th>  
                       <th scope="col">@SiteResources.ApplicationNumber.ToTitleCase()</th>  
                       <th scope="col">@SiteResources.PassportNumber.ToTitleCase()</th>  
                       <th scope="col">@SiteResources.BranchName.ToTitleCase()</th>  
                       <th scope="col">@SiteResources.ApplicationTime.ToTitleCase()</th>  
                       <th scope="col">@SiteResources.ApplicationStatus.ToTitleCase()</th>  
                       <th scope="col">@SiteResources.Status.ToTitleCase()</th>  
                       <th scope="col"></th>  
                   </tr>  
                   </thead>  
                   <tbody>  
                   @foreach (var item in Model.Select((value, index) => new { index, value }))  
                   {
                            var isLatestApplication = latestApplications.TryGetValue(item.value.ApplicationNumber, out var isLatest) && isLatest;
                       <tr style="@(item.value.HasApplicationNotes ? "background-color: #ffffb2;" : "")">  
                           <td scope="row">@(item.index + 1)</td>  
                           <td><a class="font-weight-bolder btn btn-link-info" href="/Appointment/Application/DetailApplicationSummary?encryptedApplicationId=@item.value.EncryptedId" target="_blank">@item.value.EncryptedId.ToDecryptInt().ToApplicationNumber()</a></td>  
                           <td>@item.value.PassportNumber</td>  
                           <td>  
                               <span>@item.value.BranchName</span>  
                               <span>(@item.value.CountryName)</span>  
                           </td>  
                           <td>@item.value.ApplicationTime</td>  
                           <td>@item.value.ApplicationStatus</td>  
                           <td>@item.value.Status</td>  
                           <td>
                               @if (isLatestApplication)
                               {
                                   <button type="button" id="importData" class="btn btn-primary font-weight-bold px-3 py-2" onclick="importApplicationDataFromHistory('@item.value.EncryptedId');">@SiteResources.ImportApplicationHistoryData</button>
                               }
                           </td>  
                       </tr>  
                   }  
                   </tbody>  
               </table>  
           }  
       </div>  
   </div>  
</div>  

<script></script>
