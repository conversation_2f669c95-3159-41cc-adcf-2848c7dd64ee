﻿using System;
using Microsoft.EntityFrameworkCore.Migrations;

#nullable disable

namespace Portal.Gateway.Entity.EFMigrationsHistory.PortalDb
{
    /// <inheritdoc />
    public partial class AddCloumnsClaimLossEntryLog : Migration
    {
        /// <inheritdoc />
        protected override void Up(MigrationBuilder migrationBuilder)
        {
            migrationBuilder.AddColumn<DateTime>(
                name: "NextAttemptDateTime",
                table: "ClaimLossEntryLog",
                type: "timestamp without time zone",
                nullable: true);

            migrationBuilder.AddColumn<int>(
                name: "RetryCount",
                table: "ClaimLossEntryLog",
                type: "integer",
                nullable: true);

            migrationBuilder.AddColumn<int>(
                name: "Status",
                table: "ClaimLossEntryLog",
                type: "integer",
                nullable: true);

            migrationBuilder.CreateIndex(
                name: "ClaimLossEntryLog_Status_Idx",
                table: "ClaimLossEntryLog",
                column: "Status");
        }

        /// <inheritdoc />
        protected override void Down(MigrationBuilder migrationBuilder)
        {
            migrationBuilder.DropIndex(
                name: "ClaimLossEntryLog_Status_Idx",
                table: "ClaimLossEntryLog");

            migrationBuilder.DropColumn(
                name: "NextAttemptDateTime",
                table: "ClaimLossEntryLog");

            migrationBuilder.DropColumn(
                name: "RetryCount",
                table: "ClaimLossEntryLog");

            migrationBuilder.DropColumn(
                name: "Status",
                table: "ClaimLossEntryLog");
        }
    }
}
