﻿
jQuery(document).ready(function () {
    GeneralPicture.init();
});

var GeneralPicture = function () {

    var _chairmanGeneralStats = function () {
        $.ajax({
            type: "GET",
            url: "/Dashboard/GetChairmanStatsFromBeginning",
            success: function (data) {
                if (data !== null && data.Type === "success") {
                    document.getElementById("chairman_entire_count").innerHTML = data.Data.AllApplications;
                    document.getElementById("chairman_declined_ratio").innerHTML = data.Data.DeclinedRatio + "%";
                    document.getElementById("chairman_declined_ratio_progress").style.width = data.Data.DeclinedRatio + "%";
                    document.getElementById("chairman_pending_approval_count").innerHTML = data.Data.PendingApproval;
                    document.getElementById("chairman_completed_appications_count").innerHTML = data.Data.Completed;
                    //document.getElementById("chairman_total_stats_base_on_country_text").innerHTML = data.Data.PieChartName;
                    document.getElementById("chairman_entry_banned_count").innerHTML = data.Data.EntryBanned;
                }
                else {
                    document.getElementById("chairman_entire_count").innerHTML = "0";
                    document.getElementById("chairman_declined_ratio").innerHTML = "0%";
                    document.getElementById("chairman_declined_ratio_progress").style.width = "0%";
                    document.getElementById("chairman_pending_approval_count").innerHTML = "0";
                    document.getElementById("chairman_completed_appications_count").innerHTML = "0";
                    document.getElementById("chairman_entry_banned_count").innerHTML = "0";
                }
            },
            error: function (data) { }
        });
    }

    var _chairmanOfficerCount = function () {
        $.ajax({
            type: "GET",
            url: "/Dashboard/GetOfficerCount",
            success: function (data) {
                if (data !== null && data.Type === "success") {
                    document.getElementById("chairman_all_registered_users").innerHTML = data.Data.TotalCount;
                }
                else {
                    document.getElementById("chairman_all_registered_users").innerHTML = "0";
                }
            },
            error: function (data) { }
        });
    }

    return {
        init: function () {

            //general
			_chairmanGeneralStats();
            _chairmanOfficerCount();

        }
    };
}();
