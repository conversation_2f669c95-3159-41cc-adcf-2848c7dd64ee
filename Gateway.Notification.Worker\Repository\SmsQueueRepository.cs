﻿using Dapper;
using Gateway.Notification.Application.Context;
using Gateway.Notification.Application.Entities;
using Microsoft.EntityFrameworkCore;
using Portal.Gateway.Infrastructure.Dapper;
using System;
using System.Collections.Generic;
using System.Data;
using System.Linq;
using System.Threading.Tasks;

namespace Gateway.Notification.Application.Repository
{
    public class SmsQueueRepository : ISmsQueueRepository
    {
        private readonly NotificationWorkerDbContext _dbContext;

        public SmsQueueRepository(NotificationWorkerDbContext dbContext)
        {
            _dbContext = dbContext;
        }

        public async Task<int> SaveSmsQueue(SmsQueue smsQueue)
        {
            smsQueue.CreatedAt = DateTime.Now;
            smsQueue.IsDeleted = false;

            await _dbContext.SmsQueue.AddAsync(smsQueue);
            await _dbContext.SaveChangesAsync();

            return smsQueue.Id;
        }

        public void UpdateSmsQueue(SmsQueue smsQueue)
        {
            smsQueue.UpdatedAt = DateTime.Now;

            _dbContext.Update(smsQueue);
            _dbContext.SaveChanges();
        }

        public async Task<List<SmsQueue>> GetFailedSms()
        {
            var existingSmsQueue = await _dbContext.SmsQueue
                .Where(p => p.IsSendSuccessful == false && p.RetryCount < 5 &&
                            p.SendDate >= DateTime.UtcNow.Date && p.SendDate < DateTime.Now.Date.AddDays(1))
                .OrderBy(q => q.Id).Take(10)
                .ToListAsync();

            if (existingSmsQueue.Count == 0) return new List<SmsQueue>();

            var queueList = existingSmsQueue.Select(item => new SmsQueue
            {
                Id = item.Id,
                ExternalTransactionId = item.ExternalTransactionId,
                Sender = item.Sender,
                Receiver = item.Receiver,
                SmsType = item.SmsType,
                RetryCount = item.RetryCount,
                NumberOfParts = item.NumberOfParts,
                SendDate = item.SendDate,
                MsgTxt = item.MsgTxt,
                IsSendSuccessful = item.IsSendSuccessful,
                ProviderId = item.ProviderId,
                IsProcessing = item.IsProcessing,
                ProviderTransactionId = item.ProviderTransactionId
            }).ToList();

            return queueList;
        }
    }
}
