﻿using System;
using System.Collections.Generic;

namespace Gateway.External.Entity.Entities.Country
{
    public class CountryChecklist
    {
        public CountryChecklist()
        {
            IsActive = true;
            IsDeleted = false;
            CountryChecklistTranslations = new HashSet<CountryChecklistTranslation>();
        }

        public int Id { get; set; }

        public int CountryId { get; set; }

        public bool MustCheck { get; set; }

        public int? CreatedBy { get; set; }

        public DateTime? CreatedAt { get; set; }

        public int? UpdatedBy { get; set; }

        public DateTime? UpdatedAt { get; set; }

        public bool IsActive { get; set; }

        public bool IsDeleted { get; set; }

        public int? DeletedBy { get; set; }

        public DateTime? DeletedAt { get; set; }

        public Country Country { get; set; }

        public virtual ICollection<CountryChecklistTranslation> CountryChecklistTranslations { get; set; }
    }
}
