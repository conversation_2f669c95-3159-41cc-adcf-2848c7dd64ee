﻿using Microsoft.AspNetCore.Http;
using Microsoft.Extensions.Logging;
using Microsoft.Extensions.Primitives;
using Nest;
using Newtonsoft.Json;
using System;
using System.Collections.Generic;
using System.IO;
using System.Linq;
using System.Net;
using System.Text;
using System.Text.RegularExpressions;
using System.Threading.Tasks;

namespace Portal.Gateway.Common.LoggerMiddleware
{
    internal class LogMiddleware
    {
        private static readonly IReadOnlyList<string> _invalidTextList = new List<string>
        {
            "/healthcheck",
            ".js",
            ".css",
            ".scss",
            ".txt",
            ".png",
            ".ico",
            ".jpg",
            ".jpeg",
            ".gif",
            ".map",
            ".woff",
            ".ttf",
            ".otf",
            ".oet",
            ".svg",
            "swagger",
            "javascriptresources"
        };

        private readonly PortalLoggerConfig _config;
        private readonly bool _isProduction;
        private readonly Regex _isSuccessFalseRegex;
        private readonly ILogger<LogMiddleware> _logger;
        private readonly RequestDelegate _next;
        private readonly IElasticClient _elasticClient;

        public LogMiddleware(ILogger<LogMiddleware> logger, RequestDelegate next, PortalLoggerConfig config, IElasticClient elasticClient)
        {
            _logger = logger;
            _next = next;
            _config = config;
            _isSuccessFalseRegex = new Regex(@"\""?[iI]s[sS]uccess\""?\s*\:\s*[fF]alse");
            _isProduction = Environment.GetEnvironmentVariable("ASPNETCORE_ENVIRONMENT")?.ToLowerInvariant() == "production"
                || Environment.GetEnvironmentVariable("ASPNETCORE_ENVIRONMENT")?.ToLowerInvariant() == "production-k8s";
            _elasticClient = elasticClient;
        }

        public async Task Invoke(HttpContext context)
        {
            if (!IsRequestLoggable(context))
            {
                await _next(context);

                return;
            }

            var traceData = new TraceData(context);

            var requestLog = await WriteRequestLogAsync(context, traceData);

            var originalBodyStream = context.Response.Body;

            using (var responseBody = new MemoryStream())
            {
                context.Response.Body = responseBody;

                try
                {
                    await _next(context);
                    SetResponseHeaders(context, traceData);
                    await WriteResponseLogAsync(context.Response, responseBody, requestLog);
                }
                catch (Exception exception)
                {
                    await WriteErrorLogAsync(context.Response, exception, requestLog);
                }

                try
                {
                    await responseBody.CopyToAsync(originalBodyStream);
                }
                catch (Exception exception)
                {
                    _logger.LogCritical(exception, "responseBody.CopyToAsync");
                }
            }
        }

        private static void SetResponseHeaders(HttpContext context, TraceData traceData)
        {
            if (!context.Response.Headers.ContainsKey("correlationId"))
                context.Response.Headers.Remove("correlationId");

            context.Response.Headers.Add("correlationId", traceData.CorrelationId.ToString());
        }

        private async Task<LogMessage> WriteRequestLogAsync(HttpContext context, TraceData traceData)
        {
            context.Request.EnableBuffering();

            var message = await new StreamReader(context.Request.Body).ReadToEndAsync();

            context.Request.Body.Position = 0;

            var requestLog = new LogMessage
            {
                Caller = System.Reflection.Assembly.GetEntryAssembly().GetName().Name,
                LogLevel = Microsoft.Extensions.Logging.LogLevel.Information,
                LogType = LogType.Request,
                CorrelationId = traceData.CorrelationId,
                SessionId = traceData.SessionId,
                UserId = traceData.UserId,
                Message = message,
                Headers = context.Request.Headers?.ToDictionary(q => q.Key, q => string.Join(", ", q.Value)),
                UrlHost = context.Request.Host.Value,
                UrlMethod = context.Request.Method,
                UrlPath = context.Request.Path.Value,
                UrlQueryString = new StringValues(context.Request.Query?.Select(q => $"{q.Key}: {q.Value}").ToArray())
            };

            //await _elasticClient.IndexDocumentAsync(requestLog);

            return requestLog;
        }

        private async Task WriteResponseLogAsync(HttpResponse response, Stream bodyStream, LogMessage requestLog)
        {
            if (response.ContentType == null)
                return;

            string message;

            var logLevel = Microsoft.Extensions.Logging.LogLevel.Information;

            if (response.ContentType.Contains("application/json"))
            {
                bodyStream.Seek(0, SeekOrigin.Begin);
                message = await new StreamReader(bodyStream).ReadToEndAsync();

                if (_isSuccessFalseRegex.IsMatch(message))
                    logLevel = Microsoft.Extensions.Logging.LogLevel.Error;
            }
            else
            {
                message = $"Non JSON content: {response.ContentType}";
            }

            bodyStream.Seek(0, SeekOrigin.Begin);

            LogResponse(
                response.Headers,
                requestLog,
                logLevel,
                message,
                response.StatusCode);
        }

        private async Task WriteErrorLogAsync(HttpResponse response, Exception exception, LogMessage requestLog)
        {
            LogResponse(
                response.Headers,
                requestLog,
                Microsoft.Extensions.Logging.LogLevel.Error,
                JsonConvert.SerializeObject(exception, JsonSettings.JsonSerializerSettings),
                (int)HttpStatusCode.InternalServerError);


            var buffer = Encoding.UTF8.GetBytes(
                JsonConvert.SerializeObject(
                    new ApiResponse
                    {
                        IsSuccess = false,
                        Code = "INTERNAL_ERROR",
                        Message = exception.ToString()
                    }));

            response.StatusCode = (int)HttpStatusCode.OK;
            response.ContentType = "application/json";

            await response.Body.WriteAsync(buffer, 0, buffer.Length);

            response.Body.Seek(0, SeekOrigin.Begin);
        }

        private void LogResponse(IHeaderDictionary header, LogMessage requestLog, Microsoft.Extensions.Logging.LogLevel logLevel, string message, int statusCode)
        {
            var logMessage = new LogMessage
            {
                Caller = requestLog.Caller,
                ParentLogId = requestLog.Id,
                LogLevel = logLevel,
                LogType = LogType.Response,
                SessionId = requestLog.SessionId,
                CorrelationId = requestLog.CorrelationId,
                UserId = requestLog.UserId,
                Message = message,
                Headers = header?.ToDictionary(q => q.Key, q => string.Join(", ", q.Value)),
                UrlHost = requestLog.UrlHost,
                UrlMethod = requestLog.UrlMethod,
                UrlPath = requestLog.UrlPath,
                UrlQueryString = requestLog.UrlQueryString,
                ResponseStatusCode = statusCode,
                ProcessCost = (DateTime.UtcNow - requestLog.LogDate).TotalMilliseconds
            };

            //_elasticClient.IndexDocumentAsync(logMessage);
        }

        private static bool IsRequestLoggable(HttpContext context)
        {
            var path = context.Request.Path.Value.ToLowerInvariant();

            if (_invalidTextList.Any(invalidText => path.Contains(invalidText)))
                return false;

            return !context.Request.Headers.ContainsKey("Content-Type")
                || context.Request.ContentType.Contains("application/json");
        }

        private class TraceData
        {
            public TraceData(HttpContext context)
            {
                if (!context.Request.Headers.ContainsKey("correlationId"))
                {
                    CorrelationId = Guid.NewGuid();

                    context.Request.Headers["correlationId"] = CorrelationId.ToString();
                }
                else
                {
                    if (!Guid.TryParse(context.Request.Headers["correlationId"], out var correlationId))
                        correlationId = Guid.NewGuid();

                    CorrelationId = correlationId;
                }

                SessionId = context.Request.Headers.ContainsKey("sessionId")
                    ? context.Request.Headers["sessionId"].ToString()
                    : null;

                UserId = context.Request.Headers.ContainsKey("userId")
                    ? context.Request.Headers["userId"].ToString()
                    : null;
            }

            public string SessionId { get; }
            public string UserId { get; }
            public Guid CorrelationId { get; }
        }
    }
}
