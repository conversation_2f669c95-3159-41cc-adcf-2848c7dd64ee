﻿using Gateway.Notification.Application.Entities;
using Microsoft.EntityFrameworkCore;
using System;
using System.Threading.Tasks;
using System.Threading;

namespace Gateway.Notification.Application.Context
{
    public class NotificationWorkerDbContext : DbContext
    {
        public NotificationWorkerDbContext(DbContextOptions<NotificationWorkerDbContext> options) : base(options) { }

        public DbSet<SmsQueue> SmsQueue { get; set; }
        public DbSet<MailQueue> MailQueue { get; set; }

        protected override void OnModelCreating(ModelBuilder modelBuilder)
        {
            modelBuilder.Entity<SmsQueue>().Property(l => l.IsDeleted).HasDefaultValue(false);
            modelBuilder.Entity<MailQueue>().Property(l => l.IsDeleted).HasDefaultValue(false);

            modelBuilder.ApplyConfiguration(new SmsQueueConfiguration());
            modelBuilder.ApplyConfiguration(new MailQueueConfiguration());

            base.OnModelCreating(modelBuilder);
        }

        public override async Task<int> SaveChangesAsync(bool acceptAllChangesOnSuccess,
            CancellationToken cancellationToken = new CancellationToken())
        {
            OnBeforeSaving();
            return await base.SaveChangesAsync(acceptAllChangesOnSuccess, cancellationToken);
        }


        protected virtual void OnBeforeSaving()
        {
            foreach (var entry in ChangeTracker.Entries())
                switch (entry.State)
                {
                    case EntityState.Added:
                        if (entry?.GetType().GetProperty("IsDeleted") != null)
                            entry.Entity.GetType().GetProperty("IsDeleted")?.SetValue(entry.Entity, false);
                        if (entry?.Entity.GetType().GetProperty("CreatedAt") != null)
                            entry.Entity.GetType().GetProperty("CreatedAt")?.SetValue(entry.Entity, DateTime.Now);
                        break;
                    case EntityState.Deleted:
                        if (entry.Entity.GetType().GetProperty("IsDeleted") != null)
                            entry.Entity.GetType().GetProperty("IsDeleted")?.SetValue(entry.Entity, true);
                        if (entry.Entity.GetType().GetProperty("DeletedAt") != null)
                            entry.Entity.GetType().GetProperty("DeletedAt")?.SetValue(entry.Entity, DateTime.Now);
                        break;
                    case EntityState.Modified:
                        if (entry.Entity.GetType().GetProperty("UpdatedAt") != null)
                            entry.Entity.GetType().GetProperty("UpdatedAt")?.SetValue(entry.Entity, DateTime.Now);
                        break;
                }
        }
    }
}
