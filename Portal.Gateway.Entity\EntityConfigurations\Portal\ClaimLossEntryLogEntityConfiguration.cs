﻿using Microsoft.EntityFrameworkCore.Metadata.Builders;
using Microsoft.EntityFrameworkCore;
using Portal.Gateway.Entity.Entities.Portal;
using System;

namespace Portal.Gateway.Entity.EntityConfigurations.Portal
{
    public class ClaimLossEntryLogEntityConfiguration : IEntityTypeConfiguration<ClaimLossEntryLog>
    {
        public void Configure(EntityTypeBuilder<ClaimLossEntryLog> builder)
        {
            builder.ToTable("ClaimLossEntryLog");

            builder.HasIndex(e => e.ApplicationId, "ClaimLossEntryLog_ApplicationId_Idx");

            builder.HasIndex(e => e.Id, "ClaimLossEntryLog_Id_Idx");

            builder.HasIndex(e => e.Status, "ClaimLossEntryLog_Status_Idx");

            #region StaticAuditableEntityConfig

            builder.HasKey(e => e.Id);
            builder.Property(e => e.Id).IsRequired().ValueGeneratedOnAdd();
            builder.Property(e => e.IsActive).IsRequired();
            builder.Property(e => e.IsDeleted).IsRequired();
            builder.Property(e => e.CreatedBy).IsRequired();
            builder.Property(e => e.CreatedAt).IsRequired().HasColumnType("timestamp with time zone");
            builder.Property(e => e.UpdatedBy);
            builder.Property(e => e.UpdatedAt).HasColumnType("timestamp with time zone");
            builder.Property(e => e.DeletedBy);
            builder.Property(e => e.DeletedAt).HasColumnType("timestamp with time zone");

            #endregion

            builder.Property(e => e.ApplicationId).IsRequired();
            builder.Property(e => e.ClaimAmount).HasColumnType("character varying");
            builder.Property(e => e.ClaimNo).HasColumnType("citext");
            builder.Property(e => e.ServiceResponse).HasColumnType("citext");

            builder.HasOne(d => d.Application)
                .WithMany(p => p.ClaimLossEntryLogs)
                .HasForeignKey(d => d.ApplicationId)
                .OnDelete(DeleteBehavior.ClientSetNull)
                .HasConstraintName("FK_ClaimLossEntryLog_ApplicationId");

            builder.HasOne(d => d.User)
                .WithMany(p => p.ClaimLossEntryLogs)
                .HasForeignKey(d => d.CreatedBy)
                .OnDelete(DeleteBehavior.ClientSetNull)
                .HasConstraintName("FK_ClaimLossEntryLog_CreatedBy");
        }
    }
}
