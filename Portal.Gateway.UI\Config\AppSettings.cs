﻿namespace Portal.Gateway.UI.Config
{
    public class AppSettings
    {
        public string PortalGatewayApiUrl { get; set; }
        public string PortalGatewayApiKey { get; set; }
        public string CorporateId { get; set; }
        public string AppointmentReturnUrl { get; set; }
        public string GoogleCloudProjectId { get; set; }
        public string GoogleCloudCredentialsUrl { get; set; }
        public bool Notification { get; set; }
        public string IcrReturnUrl { get; set; }
        public string PortalGatewayUiUrl { get; set; }
        public Qms Qms { get; set; }
        public Biometrics Biometrics { get; set; }
        public Cargo Cargo { get; set; }
        public PrintData PrintData { get; set; }
        public bool EnableQuarterCategoryStatsGraph { get; set; }
        public bool EnableRejectedApplicationInsuranceCase { get; set; }
    }

    public class Qms
    {
        public string BaseApiUrl { get; set; }
    }
    
    public class Biometrics
    {
        public string BaseApiUrl { get; set; }
    }

    public class Cargo
    {
        public string BaseApiUrl { get; set; }
    }

    public class PrintData
    {
        public string BaseApiUrl { get; set; }
    }
}
