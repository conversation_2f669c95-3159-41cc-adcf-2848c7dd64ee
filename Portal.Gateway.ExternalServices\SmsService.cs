﻿using Gateway.EventBus.Publishers;
using Portal.Gateway.Common.Utility.Extensions;
using Portal.Gateway.ExternalServices.Contracts;
using Portal.Gateway.ExternalServices.Models;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Threading.Tasks;
using Portal.Gateway.Contracts.Entities.Dto.Notification;
using Microsoft.Extensions.Configuration;
using Gateway.Extensions;

namespace Portal.Gateway.ExternalServices
{
    public class SmsService : BaseExternalService, ISmsService
    {
        private readonly IMessagePublisher _messagePublisher;
        private readonly string _queueName;

        public SmsService(IMessagePublisher messagePublisher, IConfiguration configuration)
        {
            _messagePublisher = messagePublisher;
            _queueName = configuration["RabbitMq:SMSQueue"].AddEnvironmentSuffix();
        }

        public async Task SendSms(List<SendNotificationServiceRequest.Notification> request)
        {
            await request.ForEachAsync(async p =>
            {
                var smsTo = p.ApplicantList.DistinctBy(r => r.Contact).Select(r => new SendNotificationServiceRequest.Applicant
                {
                    Contact = r.Contact,
                    ProviderId = r.ProviderId,
                    Sender = r.Sender
                }).ToList();

                for (var i = 0; i < smsTo.Count; i++)
                {
                    var transactionArray = p.TransactionId.Split(',');
                    var message = new SendSmsRequest
                    {
                        TransactionId = p.NotificationType == 0 ? transactionArray[i] : p.TransactionId,
                        SmsType = p.NotificationType,
                        ProviderId = smsTo[i].ProviderId,
                        Msisdn = smsTo[i].Contact,
                        EndDate = null,
                        Message = p.Text,
                        Originator = smsTo[i].Sender,
                        StartDate = DateTime.Now,
                        IsResend = p.ResendParentId != null,
                        NotificationType = p.NotificationType,
                    };

                    await _messagePublisher.PublishAsync(_queueName, message, true, p.IsPriorityNotification ? 10 : 0);
                }
            });
        }
    }
}
