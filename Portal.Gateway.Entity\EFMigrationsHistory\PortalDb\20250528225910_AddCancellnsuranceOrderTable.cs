﻿using System;
using Microsoft.EntityFrameworkCore.Migrations;
using Npgsql.EntityFrameworkCore.PostgreSQL.Metadata;

#nullable disable

namespace Portal.Gateway.Entity.EFMigrationsHistory.PortalDb
{
    public partial class AddCancellnsuranceOrderTable : Migration
    {
        protected override void Up(MigrationBuilder migrationBuilder)
        {
            migrationBuilder.CreateTable(
                name: "CancelInsuranceOrder",
                columns: table => new
                {
                    Id = table.Column<int>(type: "integer", nullable: false)
                        .Annotation("Npgsql:ValueGenerationStrategy", NpgsqlValueGenerationStrategy.SerialColumn),
                    ApplicationId = table.Column<int>(type: "integer", nullable: false),
                    ApplicationInsuranceId = table.Column<int>(type: "integer", nullable: false),
                    Status = table.Column<byte>(type: "smallint", nullable: false),
                    ProviderId = table.Column<int>(type: "integer", nullable: false),
                    PolicyNumber = table.Column<string>(type: "text", nullable: true),
                    IsActive = table.Column<bool>(type: "boolean", nullable: false),
                    IsDeleted = table.Column<bool>(type: "boolean", nullable: false),
                    DeletedBy = table.Column<int>(type: "integer", nullable: true),
                    DeletedAt = table.Column<DateTime>(type: "timestamp without time zone", nullable: true),
                    CreatedBy = table.Column<int>(type: "integer", nullable: true),
                    CreatedAt = table.Column<DateTime>(type: "timestamp without time zone", nullable: true),
                    UpdatedBy = table.Column<int>(type: "integer", nullable: true),
                    UpdatedAt = table.Column<DateTime>(type: "timestamp without time zone", nullable: true)
                },
                constraints: table =>
                {
                    table.PrimaryKey("PK_CancelInsuranceOrder", x => x.Id);
                    table.ForeignKey(
                        name: "FK_CancelInsuranceOrder_Application_ApplicationId",
                        column: x => x.ApplicationId,
                        principalTable: "Application",
                        principalColumn: "Id",
                        onDelete: ReferentialAction.Cascade);
                    table.ForeignKey(
                        name: "FK_CancelInsuranceOrder_ApplicationInsurance_ApplicationInsura~",
                        column: x => x.ApplicationInsuranceId,
                        principalTable: "ApplicationInsurance",
                        principalColumn: "Id",
                        onDelete: ReferentialAction.Cascade);
                });

            migrationBuilder.CreateIndex(
                name: "IX_CancelInsuranceOrder_ApplicationId",
                table: "CancelInsuranceOrder",
                column: "ApplicationId");

            migrationBuilder.CreateIndex(
                name: "IX_CancelInsuranceOrder_ApplicationInsuranceId",
                table: "CancelInsuranceOrder",
                column: "ApplicationInsuranceId");

        }

        protected override void Down(MigrationBuilder migrationBuilder)
        {
            migrationBuilder.DropTable(
                name: "CancelInsuranceOrder");
        }
    }
}
