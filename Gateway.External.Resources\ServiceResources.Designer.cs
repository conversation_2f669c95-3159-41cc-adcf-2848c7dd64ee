﻿//------------------------------------------------------------------------------
// <auto-generated>
//     This code was generated by a tool.
//     Runtime Version:4.0.30319.42000
//
//     Changes to this file may cause incorrect behavior and will be lost if
//     the code is regenerated.
// </auto-generated>
//------------------------------------------------------------------------------

namespace Gateway.External.Resources {
    using System;
    
    
    /// <summary>
    ///   A strongly-typed resource class, for looking up localized strings, etc.
    /// </summary>
    // This class was auto-generated by the StronglyTypedResourceBuilder
    // class via a tool like ResGen or Visual Studio.
    // To add or remove a member, edit your .ResX file then rerun ResGen
    // with the /str option, or rebuild your VS project.
    [global::System.CodeDom.Compiler.GeneratedCodeAttribute("System.Resources.Tools.StronglyTypedResourceBuilder", "17.0.0.0")]
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute()]
    [global::System.Runtime.CompilerServices.CompilerGeneratedAttribute()]
    public class ServiceResources {
        
        private static global::System.Resources.ResourceManager resourceMan;
        
        private static global::System.Globalization.CultureInfo resourceCulture;
        
        [global::System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1811:AvoidUncalledPrivateCode")]
        internal ServiceResources() {
        }
        
        /// <summary>
        ///   Returns the cached ResourceManager instance used by this class.
        /// </summary>
        [global::System.ComponentModel.EditorBrowsableAttribute(global::System.ComponentModel.EditorBrowsableState.Advanced)]
        public static global::System.Resources.ResourceManager ResourceManager {
            get {
                if (object.ReferenceEquals(resourceMan, null)) {
                    global::System.Resources.ResourceManager temp = new global::System.Resources.ResourceManager("Gateway.External.Resources.ServiceResources", typeof(ServiceResources).Assembly);
                    resourceMan = temp;
                }
                return resourceMan;
            }
        }
        
        /// <summary>
        ///   Overrides the current thread's CurrentUICulture property for all
        ///   resource lookups using this strongly typed resource class.
        /// </summary>
        [global::System.ComponentModel.EditorBrowsableAttribute(global::System.ComponentModel.EditorBrowsableState.Advanced)]
        public static global::System.Globalization.CultureInfo Culture {
            get {
                return resourceCulture;
            }
            set {
                resourceCulture = value;
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Already has update request.
        /// </summary>
        public static string ALREADY_HAS_UPDATE_REQUEST {
            get {
                return ResourceManager.GetString("ALREADY_HAS_UPDATE_REQUEST", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to The applicant count can not be more than 1 on indvidual applicant type.
        /// </summary>
        public static string APPLICANT_COUNT_VALIDATION_ERROR {
            get {
                return ResourceManager.GetString("APPLICANT_COUNT_VALIDATION_ERROR", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Applicant not found.
        /// </summary>
        public static string APPLICANT_NOT_FOUND {
            get {
                return ResourceManager.GetString("APPLICANT_NOT_FOUND", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Applicant not match with appointment.
        /// </summary>
        public static string APPLICANT_NOT_MATCH_WITH_APPOINTMENT {
            get {
                return ResourceManager.GetString("APPLICANT_NOT_MATCH_WITH_APPOINTMENT", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Applicant only has one wife or husband.
        /// </summary>
        public static string APPLICANT_ONLY_HAS_ONE_WİFEORHUSBAND {
            get {
                return ResourceManager.GetString("APPLICANT_ONLY_HAS_ONE_WİFEORHUSBAND", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Application not found.
        /// </summary>
        public static string APPLICATION_NOT_FOUND {
            get {
                return ResourceManager.GetString("APPLICATION_NOT_FOUND", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Application status not found.
        /// </summary>
        public static string APPLICATION_STATUS_NOT_FOUND {
            get {
                return ResourceManager.GetString("APPLICATION_STATUS_NOT_FOUND", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Appointment Confirmation.
        /// </summary>
        public static string APPOINTMENT_CONFIRMATION_FILE_NAME {
            get {
                return ResourceManager.GetString("APPOINTMENT_CONFIRMATION_FILE_NAME", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Appointment demand not found.
        /// </summary>
        public static string APPOINTMENT_DEMAND_NOT_FOUND {
            get {
                return ResourceManager.GetString("APPOINTMENT_DEMAND_NOT_FOUND", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Appointment found with same passport number.
        /// </summary>
        public static string APPOINTMENT_FOUND_WITH_SAME_PASSPORT {
            get {
                return ResourceManager.GetString("APPOINTMENT_FOUND_WITH_SAME_PASSPORT", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Appointment converted to application not found.
        /// </summary>
        public static string APPOINTMENT_HAS_CONVERTED_APPLICATION_NOT_FOUND {
            get {
                return ResourceManager.GetString("APPOINTMENT_HAS_CONVERTED_APPLICATION_NOT_FOUND", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Appointment has not been converted to an application.
        /// </summary>
        public static string APPOINTMENT_NOT_CONVERTED_APPLICATION {
            get {
                return ResourceManager.GetString("APPOINTMENT_NOT_CONVERTED_APPLICATION", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Appointment not found.
        /// </summary>
        public static string APPOINTMENT_NOT_FOUND {
            get {
                return ResourceManager.GetString("APPOINTMENT_NOT_FOUND", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Day and date updates can be made at most 2 times.
        /// </summary>
        public static string APPOINTMENT_SLOT_LIMIT {
            get {
                return ResourceManager.GetString("APPOINTMENT_SLOT_LIMIT", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Appointment Update Confirmation.
        /// </summary>
        public static string APPOINTMENT_UPDATE_CONFIRMATION_FILE_NAME {
            get {
                return ResourceManager.GetString("APPOINTMENT_UPDATE_CONFIRMATION_FILE_NAME", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Appointment update cannot be made within the last 48 hours.
        /// </summary>
        public static string APPOINTMENT_UPDATE_NOT_ALLOWED {
            get {
                return ResourceManager.GetString("APPOINTMENT_UPDATE_NOT_ALLOWED", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Appointment Date.
        /// </summary>
        public static string AppointmentDateTitle {
            get {
                return ResourceManager.GetString("AppointmentDateTitle", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Authorized user not found.
        /// </summary>
        public static string AUTHORIZED_USER_NOT_FOUND {
            get {
                return ResourceManager.GetString("AUTHORIZED_USER_NOT_FOUND", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to BAD_REQUEST.
        /// </summary>
        public static string BAD_REQUEST {
            get {
                return ResourceManager.GetString("BAD_REQUEST", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Birth date must be past tense.
        /// </summary>
        public static string BIRTHDATE_MUST_BE_PAST_TENSE {
            get {
                return ResourceManager.GetString("BIRTHDATE_MUST_BE_PAST_TENSE", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Branch change can be done only once..
        /// </summary>
        public static string BRANCH_CHANGE_NOT_ALLOWED {
            get {
                return ResourceManager.GetString("BRANCH_CHANGE_NOT_ALLOWED", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Branch Department not found.
        /// </summary>
        public static string BRANCH_DEPARTMENT_NOT_FOUND {
            get {
                return ResourceManager.GetString("BRANCH_DEPARTMENT_NOT_FOUND", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Branch not found.
        /// </summary>
        public static string BRANCH_NOT_FOUND {
            get {
                return ResourceManager.GetString("BRANCH_NOT_FOUND", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to You cannot continue the appointment process with this passport number.
        /// </summary>
        public static string CANNOT_CONTINUE_APPOINTMENT {
            get {
                return ResourceManager.GetString("CANNOT_CONTINUE_APPOINTMENT", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Cannot update walkin appointments.
        /// </summary>
        public static string CANNOT_UPDATE_WALKIN {
            get {
                return ResourceManager.GetString("CANNOT_UPDATE_WALKIN", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Company Application Report.
        /// </summary>
        public static string COMPANY_APPLICATION_REPORT {
            get {
                return ResourceManager.GetString("COMPANY_APPLICATION_REPORT", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Company Appointment Demand Report.
        /// </summary>
        public static string COMPANY_APPOINTMENT_DEMAND_REPORT {
            get {
                return ResourceManager.GetString("COMPANY_APPOINTMENT_DEMAND_REPORT", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Company not found.
        /// </summary>
        public static string COMPANY_NOT_FOUND {
            get {
                return ResourceManager.GetString("COMPANY_NOT_FOUND", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Company Slot Demand Report.
        /// </summary>
        public static string COMPANY_SLOT_DEMAND_REPORT {
            get {
                return ResourceManager.GetString("COMPANY_SLOT_DEMAND_REPORT", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Company User Report.
        /// </summary>
        public static string COMPANY_USER_REPORT {
            get {
                return ResourceManager.GetString("COMPANY_USER_REPORT", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Country not found.
        /// </summary>
        public static string COUNTRY_NOT_FOUND {
            get {
                return ResourceManager.GetString("COUNTRY_NOT_FOUND", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Created by.
        /// </summary>
        public static string CREATED_BY {
            get {
                return ResourceManager.GetString("CREATED_BY", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Date parameters mismatch.
        /// </summary>
        public static string DATE_PARAMETERS_MISMATCH {
            get {
                return ResourceManager.GetString("DATE_PARAMETERS_MISMATCH", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Appointments with less than 24 hours left cannot be deleted.
        /// </summary>
        public static string DELETE_OPERATION_NOT_ALLOWED {
            get {
                return ResourceManager.GetString("DELETE_OPERATION_NOT_ALLOWED", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Email must be same with email that used in login.
        /// </summary>
        public static string EMAIL_MISMATCH_WITH_TOKEN {
            get {
                return ResourceManager.GetString("EMAIL_MISMATCH_WITH_TOKEN", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Your registration has been completed. Your temporary password is [PASSWORD] You can change it from the system after logging in with your temporary password..
        /// </summary>
        public static string EXTERNAL_REGISTER_NOTIFICATION {
            get {
                return ResourceManager.GetString("EXTERNAL_REGISTER_NOTIFICATION", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to FAILED.
        /// </summary>
        public static string FAILED {
            get {
                return ResourceManager.GetString("FAILED", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to The family applicant counts can be more than 1 item.
        /// </summary>
        public static string FAMILY_APPLICANT_COUNT_ERROR {
            get {
                return ResourceManager.GetString("FAMILY_APPLICANT_COUNT_ERROR", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to File operation failed.
        /// </summary>
        public static string FILE_OPERATION_FAILED {
            get {
                return ResourceManager.GetString("FILE_OPERATION_FAILED", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to File size limit exceeded.
        /// </summary>
        public static string FILE_SIZE_LIMIT_EXCEEDED {
            get {
                return ResourceManager.GetString("FILE_SIZE_LIMIT_EXCEEDED", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to First available slot found.
        /// </summary>
        public static string FIRST_AVAILABLE_SLOT_FOUND {
            get {
                return ResourceManager.GetString("FIRST_AVAILABLE_SLOT_FOUND", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to The group applicant counts can be more than 1 item.
        /// </summary>
        public static string GROUP_APPLICANT_COUNT_ERROR {
            get {
                return ResourceManager.GetString("GROUP_APPLICANT_COUNT_ERROR", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to The individual applicant counts can not be more than 1 item.
        /// </summary>
        public static string INDIVIDUAL_APPLICANT_COUNT_ERROR {
            get {
                return ResourceManager.GetString("INDIVIDUAL_APPLICANT_COUNT_ERROR", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to INPUT_ERROR.
        /// </summary>
        public static string INPUT_ERROR {
            get {
                return ResourceManager.GetString("INPUT_ERROR", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to INTERNAL_SERVER_ERROR.
        /// </summary>
        public static string INTERNAL_SERVER_ERROR {
            get {
                return ResourceManager.GetString("INTERNAL_SERVER_ERROR", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Invalid email address.
        /// </summary>
        public static string INVALID_EMAIL_ADDRESS {
            get {
                return ResourceManager.GetString("INVALID_EMAIL_ADDRESS", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Invalid file extension.
        /// </summary>
        public static string INVALID_FILE_EXTENSION {
            get {
                return ResourceManager.GetString("INVALID_FILE_EXTENSION", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Invalid input error.
        /// </summary>
        public static string INVALID_INPUT_ERROR {
            get {
                return ResourceManager.GetString("INVALID_INPUT_ERROR", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Invalid parameter {0}.
        /// </summary>
        public static string INVALID_PARAMETER {
            get {
                return ResourceManager.GetString("INVALID_PARAMETER", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Invalid parameter:{0} must be lower than {1}.
        /// </summary>
        public static string INVALID_PARAMETER_LOWER {
            get {
                return ResourceManager.GetString("INVALID_PARAMETER_LOWER", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Invalid passport number.
        /// </summary>
        public static string INVALID_PASSPORT_NUMBER {
            get {
                return ResourceManager.GetString("INVALID_PASSPORT_NUMBER", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Invalid phone number.
        /// </summary>
        public static string INVALID_PHONE_NUMBER {
            get {
                return ResourceManager.GetString("INVALID_PHONE_NUMBER", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Invalid request.
        /// </summary>
        public static string INVALID_REQUEST {
            get {
                return ResourceManager.GetString("INVALID_REQUEST", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to &lt;string name=&quot;EmailFooter_English&quot;&gt;&amp;lt;div style=&amp;quot;font-family: Arial, sans-serif; font-size: 12px; color: #333;&amp;quot;&amp;gt;&amp;lt;p&amp;gt;For more information, please visit our website: &amp;lt;a href=&amp;quot;https://gatewayinternational.com.tr&amp;quot; target=&amp;quot;_blank&amp;quot;&amp;gt;https://gatewayinternational.com.tr&amp;lt;/a&amp;gt;&amp;lt;/p&amp;gt;&amp;lt;p&amp;gt;You can reach us by e-mail and call center number at the below-mentioned details:&amp;lt;/p&amp;gt;&amp;lt;p&amp;gt;E-mail: &amp;lt;a href=&amp;quot;mailto:<EMAIL>&amp;quot;&amp;gt;helpline@gat [rest of string was truncated]&quot;;.
        /// </summary>
        public static string MAIL_FOOTER {
            get {
                return ResourceManager.GetString("MAIL_FOOTER", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to This method is not valid for this parameter combinations.
        /// </summary>
        public static string METHOD_REQUIREMENT_ERROR {
            get {
                return ResourceManager.GetString("METHOD_REQUIREMENT_ERROR", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to No available appointment for the selected date. Please try a different date..
        /// </summary>
        public static string MOBILE_SLOT_NOT_FOUND {
            get {
                return ResourceManager.GetString("MOBILE_SLOT_NOT_FOUND", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to New applicant not allowed .
        /// </summary>
        public static string NEW_APPLICANT_NOT_ALLOWED {
            get {
                return ResourceManager.GetString("NEW_APPLICANT_NOT_ALLOWED", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to No available slots found.
        /// </summary>
        public static string NO_AVAILABLE_SLOTS_FOUND {
            get {
                return ResourceManager.GetString("NO_AVAILABLE_SLOTS_FOUND", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to No data found to report.
        /// </summary>
        public static string NO_DATA_FOUND_TO_REPORT {
            get {
                return ResourceManager.GetString("NO_DATA_FOUND_TO_REPORT", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Dear Applicant, &lt;br/&gt;&lt;br/&gt; Your pre-appointment has been scheduled to the &lt;span style=\&quot;color: black;\&quot;&gt; [BRANCH] branch &lt;/span&gt; for the &lt;span style=\&quot;color: black;\&quot;&gt; [DATE]  date, [TIME] hour. &lt;/span&gt; Your appointment number is &lt;span style=\&quot;color: black;\&quot;&gt; [APPNUMBER] &lt;/span&gt; &lt;br/&gt;&lt;br/&gt; Please Kindly find the appointment letter in the attachment.
        /// </summary>
        public static string NOTIFICATION_MAIL_NEWAPPOINTMENT {
            get {
                return ResourceManager.GetString("NOTIFICATION_MAIL_NEWAPPOINTMENT", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Dear Applicant, &lt;br/&gt;&lt;br/&gt; Your pre-appointment has been updated to the &lt;span style=\&quot;color: black;\&quot;&gt; [BRANCH] branch &lt;/span&gt; for the &lt;span style=\&quot;color: black;\&quot;&gt; [DATE]  date, [TIME] hour. &lt;/span&gt;  Please Kindly find the appointment letter in the attachment.
        /// </summary>
        public static string NOTIFICATION_MAIL_UPDATEAPPOINTMENT {
            get {
                return ResourceManager.GetString("NOTIFICATION_MAIL_UPDATEAPPOINTMENT", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Only one record can be set as wife or husband record for appointment.
        /// </summary>
        public static string ONLY_ONE_RECORD_CANBE_SET_AS_WIFEORHUSBAND_RECORD_FOR_APPOINTMENT {
            get {
                return ResourceManager.GetString("ONLY_ONE_RECORD_CANBE_SET_AS_WIFEORHUSBAND_RECORD_FOR_APPOINTMENT", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Order.
        /// </summary>
        public static string ORDER {
            get {
                return ResourceManager.GetString("ORDER", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Passport validity period cannot be less than 180 days.
        /// </summary>
        public static string PASSPORT_VALIDITY_PERIOD_ERROR {
            get {
                return ResourceManager.GetString("PASSPORT_VALIDITY_PERIOD_ERROR", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Password mismatch.
        /// </summary>
        public static string PASSWORD_MISMATCH {
            get {
                return ResourceManager.GetString("PASSWORD_MISMATCH", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Password must not same.
        /// </summary>
        public static string PASSWORD_MUST_NOT_SAME {
            get {
                return ResourceManager.GetString("PASSWORD_MUST_NOT_SAME", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to PhotoBoothStatus must be a value between 0 and 6.
        /// </summary>
        public static string PhotoBoothStatus_RANGE {
            get {
                return ResourceManager.GetString("PhotoBoothStatus_RANGE", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to PNL already registered.
        /// </summary>
        public static string PNL_ALREADY_REGISTERED {
            get {
                return ResourceManager.GetString("PNL_ALREADY_REGISTERED", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to PNL Report.
        /// </summary>
        public static string PNL_REPORT {
            get {
                return ResourceManager.GetString("PNL_REPORT", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Pre condition failed.
        /// </summary>
        public static string PRE_CONDITION_FAILED {
            get {
                return ResourceManager.GetString("PRE_CONDITION_FAILED", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to The property {0} format not valid.
        /// </summary>
        public static string PROPERTY_FORMAT_ERROR {
            get {
                return ResourceManager.GetString("PROPERTY_FORMAT_ERROR", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to The property {0} can not be more than {1} characters.
        /// </summary>
        public static string PROPERTY_MAX_LENGTH_ERROR {
            get {
                return ResourceManager.GetString("PROPERTY_MAX_LENGTH_ERROR", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Must not have numeric characters.
        /// </summary>
        public static string PROPERTY_MUST_NOT_HAVE_NUMERIC_CHARACTER {
            get {
                return ResourceManager.GetString("PROPERTY_MUST_NOT_HAVE_NUMERIC_CHARACTER", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to {0} is required.
        /// </summary>
        public static string PROPERTY_REQUIRED {
            get {
                return ResourceManager.GetString("PROPERTY_REQUIRED", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to There is an appointment with the passport.
        /// </summary>
        public static string REPEATED_APPOINTMENT_FOUND {
            get {
                return ResourceManager.GetString("REPEATED_APPOINTMENT_FOUND", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Maximum 2 appointments can be made with the same e-mail address..
        /// </summary>
        public static string REPEATED_MAIL_NOT_ALLOWED {
            get {
                return ResourceManager.GetString("REPEATED_MAIL_NOT_ALLOWED", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Maximum 3 appointments can be made with the same phone number..
        /// </summary>
        public static string REPEATED_PHONE_NUMBER_NOT_ALLOWED {
            get {
                return ResourceManager.GetString("REPEATED_PHONE_NUMBER_NOT_ALLOWED", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Report date.
        /// </summary>
        public static string REPORT_DATE {
            get {
                return ResourceManager.GetString("REPORT_DATE", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Resource is already registered.
        /// </summary>
        public static string RESOURCE_ALREADY_REGISTERED {
            get {
                return ResourceManager.GetString("RESOURCE_ALREADY_REGISTERED", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Resource created.
        /// </summary>
        public static string RESOURCE_CREATED {
            get {
                return ResourceManager.GetString("RESOURCE_CREATED", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Resource deleted.
        /// </summary>
        public static string RESOURCE_DELETED {
            get {
                return ResourceManager.GetString("RESOURCE_DELETED", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Resource found.
        /// </summary>
        public static string RESOURCE_FOUND {
            get {
                return ResourceManager.GetString("RESOURCE_FOUND", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Resource not found.
        /// </summary>
        public static string RESOURCE_NOT_FOUND {
            get {
                return ResourceManager.GetString("RESOURCE_NOT_FOUND", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Resource retrieved.
        /// </summary>
        public static string RESOURCE_RETRIEVED {
            get {
                return ResourceManager.GetString("RESOURCE_RETRIEVED", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Resource updated.
        /// </summary>
        public static string RESOURCE_UPDATED {
            get {
                return ResourceManager.GetString("RESOURCE_UPDATED", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Same passport used between applicants.
        /// </summary>
        public static string SAME_PASSPORT_USED_BETWEEN_APPLICANTS {
            get {
                return ResourceManager.GetString("SAME_PASSPORT_USED_BETWEEN_APPLICANTS", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Slot not found.
        /// </summary>
        public static string SLOT_NOT_FOUND {
            get {
                return ResourceManager.GetString("SLOT_NOT_FOUND", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Slot not in this branch.
        /// </summary>
        public static string SLOT_NOT_IN_THIS_BRANCH {
            get {
                return ResourceManager.GetString("SLOT_NOT_IN_THIS_BRANCH", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Slot quota not found.
        /// </summary>
        public static string SLOT_QUOTA_NOT_FOUND {
            get {
                return ResourceManager.GetString("SLOT_QUOTA_NOT_FOUND", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to SUCCESS.
        /// </summary>
        public static string SUCCESS {
            get {
                return ResourceManager.GetString("SUCCESS", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Translation not found.
        /// </summary>
        public static string TRANSLATION_NOT_FOUND {
            get {
                return ResourceManager.GetString("TRANSLATION_NOT_FOUND", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to User already registered.
        /// </summary>
        public static string USER_ALREADY_REGISTERED {
            get {
                return ResourceManager.GetString("USER_ALREADY_REGISTERED", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to User not found.
        /// </summary>
        public static string USER_NOT_FOUND {
            get {
                return ResourceManager.GetString("USER_NOT_FOUND", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Vas type not active.
        /// </summary>
        public static string VAS_TYPE_NOT_ACTIVE {
            get {
                return ResourceManager.GetString("VAS_TYPE_NOT_ACTIVE", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to In addition to the service fee, there is an extra {price} service fee for this service..
        /// </summary>
        public static string VAST_TYPE_PRICE_MESSAGE {
            get {
                return ResourceManager.GetString("VAST_TYPE_PRICE_MESSAGE", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Visa information not active.
        /// </summary>
        public static string VISA_INFORMATION_NOT_ACTIVE {
            get {
                return ResourceManager.GetString("VISA_INFORMATION_NOT_ACTIVE", resourceCulture);
            }
        }
    }
}
