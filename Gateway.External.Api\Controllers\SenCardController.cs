﻿using Gateway.External.Api.Factories.ResponseFactory;
using Gateway.External.Application.SenCard;
using Gateway.External.Application.SenCard.Dto.Request;
using Gateway.External.Core.Context;
using Microsoft.AspNetCore.Mvc;
using System.Threading.Tasks;
using Microsoft.AspNetCore.Authorization;

namespace Gateway.External.Api.Controllers
{
    [Authorize]
    [Route("api")]
    [ApiController]
    public class SenCardController(IContext context, ISenCardService senCardService) : Controller
    {
        #region Public Methods

        [HttpGet]
        [Route("sencard/province")]
        public async Task<IActionResult> GetSenCardProvince()
        {
            var serviceRequest = new SenCardProvinceRequest()
            {
                Context = context
            };

            var result = await senCardService.GetSenCardProvince(serviceRequest);

            return BaseResponseFactory.CreateResponse(result, result?.Provinces);
        }

        [HttpGet]
        [Route("sencard/institution")]
        public async Task<IActionResult> GetSenCardInstitution()
        {
            var serviceRequest = new SenCardProvinceRequest
            {
                Context = context
            };

            var result = await senCardService.GetSenCardInstitution(serviceRequest);

            return BaseResponseFactory.CreateResponse(result, result?.Institutions);
        }

        [HttpGet]
        [Route("sencard/province/{provinceId}/district")]
        public async Task<IActionResult> GetSenCardDistrict(string provinceId)
        {
            var serviceRequest = new SenCardDistrictRequest
            {
                ProvinceId = provinceId,
                Context = context
            };

            var result = await senCardService.GetSenCardDistrict(serviceRequest);

            return BaseResponseFactory.CreateResponse(result, result?.Districts);
        }

        [HttpGet]
        [Route("sencard/province/{provinceId}/district/{districtId}/institution/{institutionId}")]
        public async Task<IActionResult> GetSenCardOrganization(string provinceId, string districtId, string institutionId)
        {
            var serviceRequest = new SenCardOrganizationRequest
            {
                ProvinceId = provinceId,
                DistrictId = districtId,
                InstitutionId = institutionId,
                Context = context
            };

            var result = await senCardService.GetSenCardOrganization(serviceRequest);

            return BaseResponseFactory.CreateResponse(result, result?.Organizations);
        }

        #endregion
    }
}
