﻿@using Microsoft.AspNetCore.Builder
@using Microsoft.Extensions.Options
@using Microsoft.AspNetCore.Http
@using Microsoft.AspNetCore.Mvc.TagHelpers
@using Portal.Gateway.UI.Constants
@using Portal.Gateway.UI.Models
@inject IHttpContextAccessor HttpContextAccessor
@inject IOptions<RequestLocalizationOptions> RequestLocalizationOptions
@{
    var currentUser = HttpContextAccessor.HttpContext.Session.Get<UserModel>(SessionKeys.UserSession);
    var languageId = Html.CurrentLanguageId().ToString().ToInt();
    var roleListForTurkmenistanLocal = new int[] { 28 };
    var isLocalAuthorized = currentUser.RoleIds.Any(p => roleListForTurkmenistanLocal.Contains(p));
    ViewData["Title"] = @SiteResources.ApplicationList.ToTitleCase();
}

@model FilterApplicationViewModel

<style>
    .small-font-grid {
        font-size: 12px !important;
    }

    .k-grid-header th.k-header .k-checkbox {
        visibility: hidden;
    }
</style>

<form id="report-form" class="card card-custom card-stretch" method="post">
    @Html.HiddenFor(p => p.FilterAllBranchs)
    @for (var i = 0; i < Model.FilterApplicationStatusIds.Count; i++)
    {
        @Html.HiddenFor(model => Model.FilterApplicationStatusIds[i])
    }
    <div class="card card-custom card-stretch">
        <div class="card-header">
            <div class="card-title">
                <h3 class="card-label">
                    @SiteResources.ApplicationList.ToTitleCase()
                </h3>
            </div>
            <div class="card-toolbar">
                <div class="btn-group">
                    <button type="submit" id="report-button-" style="display: none" class="btn btn-outline-primary font-weight-bold mr-2" asp-action="GetApplicationReport" asp-area="Appointment" asp-controller="Application">
                        <i class="la la-list-alt"></i> @SiteResources.GenerateReport.ToTitleCase()
                    </button>
                </div>
            </div>
        </div>
        <div class="card-body">
            <div class="mb-5">
                <span class='label label-xl label-dot label-success ml-1'></span>
                <span>@SiteResources.Main @EnumResources.ApplicantTypeGroup.ToTitleCase()</span>
                <span class='label label-xl label-dot label-info ml-1'></span>
                <span>@SiteResources.Main @EnumResources.ApplicantTypeFamily.ToTitleCase()</span>
                <span class='label label-xl label-dot label-warning ml-1'></span>
                <span>@SiteResources.ApplicationNotesExist.ToTitleCase()</span>
            </div>
            <div class="accordion accordion-toggle-arrow mb-3">
                <div class="card">
                    <div class="card-header">
                        <div class="card-title" data-toggle="collapse" data-target="#filtering-options">
                            <i class="la la-filter"></i> @SiteResources.FilteringOptions.ToTitleCase()
                        </div>
                    </div>
                    <div id="filtering-options" class="collapse">
                        <div class="card-body">
                            <div class="form-group row">
                                <div class="col-lg-3 col-md-6">
                                    <label class="font-weight-bold">@SiteResources.ApplicationNumber.ToTitleCase()</label>
                                    <input type="text" asp-for="FilterApplicationNumber" class="form-control" />
                                </div>
                                <div class="col-lg-3 col-md-6">
                                    <label class="font-weight-bold">@SiteResources.Name.ToTitleCase()</label>
                                    <input type="text" asp-for="FilterName" class="form-control" />
                                </div>
                                <div class="col-lg-3 col-md-6">
                                    <label class="font-weight-bold">@SiteResources.Surname.ToTitleCase()</label>
                                    <input type="text" asp-for="FilterSurname" class="form-control" />
                                </div>
                                <div class="col-lg-3 col-md-6">
                                    <label class="font-weight-bold">@SiteResources.WaitingTimeLength.ToTitleCase()</label>
                                    @(Html.Kendo().DropDownListFor(m => m.FilterWaitingTimeForDocument)
                                        .HtmlAttributes(new { @class = "form-control" })
                                        .Filter(FilterType.Contains)
                                        .OptionLabel(SiteResources.Select)
                                        .DataTextField("Text")
                                        .DataValueField("Value")
                                        .DataSource(source =>
                                        {
                                            source.Read(read =>
                                            {
                                                read.Action("GetWaitingTimeForDocumentSelectList", "Parameter", new { Area = "" });
                                            });
                                        }))
                                </div>
                            </div>
                            <div class="form-group row">
                                <div class="col-lg-3 col-md-6">
                                    <label class="font-weight-bold">@SiteResources.PassportNumber.ToTitleCase()</label>
                                    <input type="text" asp-for="FilterPassportNumber" class="form-control" />
                                </div>
                                <div class="col-lg-3 col-md-6">
                                    <label class="font-weight-bold">@SiteResources.StartDate.ToTitleCase()</label>
                                    @(Html.Kendo().DatePickerFor(m => m.FilterStartDate).Format(SiteResources.DatePickerFormatView))
                                </div>
                                <div class="col-lg-3 col-md-6">
                                    <label class="font-weight-bold">@SiteResources.EndDate.ToTitleCase()</label>
                                    @(Html.Kendo().DatePickerFor(m => m.FilterEndDate).Format(SiteResources.DatePickerFormatView))
                                </div>
                                <div class="col-lg-3 col-md-6">
                                    <label class="font-weight-bold">@SiteResources.EvaluationDate.ToTitleCase()</label>
                                    @(Html.Kendo().DatePickerFor(m => m.FilterEvaluationDate).Format(SiteResources.DatePickerFormatView))
                                </div>
                            </div>
                            <div class="form-group row">
                                <div class="col-lg-3">
                                    <div>
                                        <button id="filterGridApplication" type="button" class="btn btn-primary mr-1"><i class="la la-search"></i>@SiteResources.Filter</button>
                                        <button id="clearGridApplicationFilter" type="reset" class="btn btn-secondary mr-1"><i class="la la-times"></i>@SiteResources.Clear</button>
                                    </div>
                                </div>
                                <div class="col-lg-4 col-md-6" style="align-self: flex-end;">
                                    @(Html.Kendo().CheckBoxFor(m => m.FilterAllList).HtmlAttributes(new { @class = "checkbox-square", onchange = "onChangeFilterAllList()" }).Label(SiteResources.FilterAllList))
                                    @(Html.Kendo().CheckBoxFor(m => m.FilterSuitable).HtmlAttributes(new { @class = "checkbox-square", onchange = "onChangeFilterSuitable()" }).Label(SiteResources.Suitable))
                                    @(Html.Kendo().CheckBoxFor(m => m.FilterNotSuitable).HtmlAttributes(new { @class = "checkbox-square", onchange = "onChangeFilterNotSuitable()" }).Label(SiteResources.NotSuitable))
                                    @(Html.Kendo().CheckBoxFor(m => m.FilterWaiting).HtmlAttributes(new { @class = "checkbox-square", onchange = "onChangeFilterWaiting()" }).Label(SiteResources.FilterWaiting))
                                </div>

                            </div>
                        </div>
                    </div>
                </div>
            </div>
            <div class="form-group row">
                <div class="col-lg-12">
                    @(
                        Html.Kendo().Grid<ApplicationViewModel>()
                        .Name("gridApplication")
                        .Columns(columns =>
                        {
                            columns.Select().HtmlAttributes(new { data_encryptedid = "#=EncryptedId#", data_ihbstatusid = "#=IhbStatus.Id#", onchange = "onCheckboxChange()" }).Width(50);
                            columns.Bound(m => m.IhbOrderErrorMessage).Hidden();
                            columns.Bound(m => m.EncryptedIhbOrderId).Hidden();
                            columns.Bound(o => o.HasApplicationNotes).Visible(false);
                            columns.Bound(o => o.EncryptedId).Visible(false);
                            columns.Bound(o => o.RelationalApplicationId)
                            .ClientTemplate(
                            "<a class='font-weight-bolder btn btn-link-info' href='/Appointment/Application/DetailTurkmenistan?encryptedApplicationId=#=EncryptedId#' target='_blank'>#=getItemTextValue(EncryptedId,'ToDecryptInt').padStart(13, '0')#</a>" +
                            "# if (RelationalApplicationId == null && ApplicantTypeId == " + ((int)ApplicantType.Group).ToString() + ") { #" +
                            "<span class='label label-xl label-dot label-success ml-1'></span>" +
                            "# } #" +
                            "# if (RelationalApplicationId == null && ApplicantTypeId == " + ((int)ApplicantType.Family).ToString() + ") { #" +
                            "<span class='label label-xl label-dot label-info ml-1'></span>" +
                            "# } #" +
                            "# if (RelationalApplicationId != null) { #" +
                            "<span class='text-muted font-weight-bold mr-2' style='font-size:10px'>#=RelationalApplicationId.toString().padStart(13, '0')#</span>" +
                            "# } #"
                            )
                            .Title(SiteResources.ApplicationNumber.ToTitleCase()).Width(150);
                            columns.Bound(o => o.ApplicationNumber).Hidden().Title(SiteResources.ApplicationNumber.ToTitleCase());
                            columns.Bound(o => o.PassportNumber).Title(SiteResources.PassportNumber.ToTitleCase()).Media("lg");
                            columns.Bound(o => o.ApplicationTimeText).Title(SiteResources.ApplicationTime.ToTitleCase()).Media("lg");
                            if (!isLocalAuthorized)
                            {
                                columns.Bound(o => o.ApplicantType).Title(SiteResources.ApplicantType.ToTitleCase()).Media("lg");
                                columns.Bound(o => o.ApplicationType).Title(SiteResources.ApplicationType.ToTitleCase()).Media("lg");
                                columns.Bound(o => o.ApplicationStatus).Title(SiteResources.ApplicationStatus.ToTitleCase()).Media("lg");
                                columns.Bound(o => o.Status).Title(SiteResources.Status.ToTitleCase()).Media("lg");
                            }
                            else
                            {
                                columns.Bound(o => o.ApplicantType).Hidden().Title(SiteResources.ApplicantType.ToTitleCase()).Media("lg");
                                columns.Bound(o => o.ApplicationType).Hidden().Title(SiteResources.ApplicationType.ToTitleCase()).Media("lg");
                                columns.Bound(o => o.ApplicationStatus).Hidden().Title(SiteResources.ApplicationStatus.ToTitleCase()).Media("lg");
                                columns.Bound(o => o.Status).Hidden().Title(SiteResources.Status.ToTitleCase()).Media("lg");
                            }
                            columns.Bound(o => o.Name).Title(SiteResources.Name.ToTitleCase()).Media("lg");
                            columns.Bound(o => o.Surname).Title(SiteResources.Surname.ToTitleCase()).Media("lg");
                            if (!isLocalAuthorized)
                            {
                                columns.Bound(o => o.CountryName).Title(SiteResources.Country.ToTitleCase()).Media("lg");
                                columns.Bound(o => o.Email).Title(SiteResources.ContactInformation.ToTitleCase()).Media("lg");
                            }
                            else
                            {
                                columns.Bound(o => o.CountryName).Hidden().Title(SiteResources.Country.ToTitleCase()).Media("lg");
                                columns.Bound(o => o.Email).Hidden().Title(SiteResources.ContactInformation.ToTitleCase()).Media("lg");
                            }
                            columns.Bound(o => o.IhbDocumentNumber).Title(SiteResources.IHBDocumentNumber.ToTitleCase()).Media("lg").ClientTemplate("#= getIhbDocumentNumber(IhbDocumentNumber) #");
                            columns.Bound(o => o.IhbNumber).Title(SiteResources.IHBNumber.ToTitleCase()).Media("lg").ClientTemplate("#= formatIhbNumber(IhbNumber) #").EditorTemplateName("String");
                            columns.Bound(o => o.EvaluationTime).Title(SiteResources.EvaluationTime.ToTitleCase()).Media("lg");
                            columns.Bound(o => o.IhbStatus).Title(SiteResources.IHBStatus.ToTitleCase()).ClientTemplate("#=IhbStatus.Name#").Sortable(false).Width(150);
                            columns.Bound(o => o.EncryptedIhbDocumentId).ClientTemplateId("gridApplicationFileOperationsTemplate").Title(SiteResources.FileOperations.ToTitleCase()).Width(150);
                            columns.Bound(o => o.FatherName).Hidden().Title(SiteResources.FatherName.ToTitleCase()).Media("lg");
                            columns.Bound(o => o.BirthDateText).Hidden().Title(SiteResources.BirthDate.ToTitleCase()).Media("lg");
                            columns.Bound(o => o.Gender).Hidden().Title(SiteResources.Gender.ToTitleCase()).Media("lg");
                        }).ToolBar(tools => tools.Excel())
                        .Excel(excel => excel
                        .FileName("turkmenistan-applications.xlsx")
                        .AllPages(true)
                        .Filterable(true)
                        .ProxyURL(Url.Action("ExcelExport", "Grid"))
                        )
                        .ToolBar(toolBar =>
                        {
                            toolBar.Save();
                        })
                        .Events(events => events.DataBound("onDataBound"))
                        .DataSource(dataSource => dataSource
                        .Ajax()
                        .Batch(true)
                        .Events(events => events.RequestEnd("onGridRequestEnd"))
                        .Read(read => read.Action("GetSanitizedPaginatedApplications", "Application", new { Area = "Appointment" }).Data("gridApplicationFilterData"))
                        .Update(update => update.Action("AddIhbOrder", "Application", new { Area = "Appointment" }))
                        .Model(model =>
                        {
                            model.Id(p => p.EncryptedId);
                            model.Field(p => p.HasApplicationNotes).Editable(false);
                            model.Field(p => p.EncryptedId).Editable(false);
                            model.Field(p => p.RelationalApplicationId).Editable(false);
                            model.Field(p => p.ApplicationNumber).Editable(false);
                            model.Field(p => p.PassportNumber).Editable(false);
                            model.Field(p => p.ApplicationTime).Editable(false);
                            model.Field(p => p.ApplicationTimeText).Editable(false);
                            model.Field(p => p.ApplicantType).Editable(false);
                            model.Field(p => p.ApplicationType).Editable(false);
                            model.Field(p => p.ApplicationStatus).Editable(false);
                            model.Field(p => p.Status).Editable(false);
                            model.Field(p => p.Name).Editable(false);
                            model.Field(p => p.Surname).Editable(false);
                            model.Field(p => p.CountryName).Editable(false);
                            model.Field(p => p.Email).Editable(false);
                            model.Field(p => p.IhbDocumentNumber).DefaultValue("1").Editable(false);
                            model.Field(p => p.IhbNumber).Editable(true);
                            model.Field(p => p.EvaluationTime).Editable(false);
                            model.Field(p => p.IhbStatus).Editable(true);
                            model.Field(p => p.EncryptedIhbDocumentId).Editable(false);

                        })
                        .PageSize(10)
                        )
                        .Editable(editable => editable
                        .Mode(GridEditMode.InCell)
                        .DisplayDeleteConfirmation(false)
                        .Enabled(true)
                        ).Events(events => events
                        .Edit("onGridEdit")
                        )
                        .Navigatable()
                        .Scrollable(s => s.Height("auto"))
                        .Pageable(pager => pager
                        .Refresh(true)
                        .PageSizes(new[] { 10, 20, 50 }))
                        )
                </div>
            </div>
        </div>
    </div>
</form>

<div class="modal fade" id="modalAddApplicationCancellation" tabindex="-1" role="dialog" aria-labelledby="staticBackdrop" aria-hidden="true">
    <div class="modal-dialog modal-dialog-scrollable modal-xl" role="document">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title">@SiteResources.ApplicationCancellationDetails.ToTitleCase()</h5>
                <button type="button" class="close" data-dismiss="modal" aria-label="Close">
                    <i aria-hidden="true" class="ki ki-close"></i>
                </button>
            </div>
            <div class="modal-body">
                <div id="divPartialAddApplicationCancellation"></div>
            </div>
        </div>
    </div>
</div>

<div class="modal fade" id="modalApplicationEasyUse" tabindex="-1" role="dialog" aria-labelledby="staticBackdrop" aria-hidden="true">
    <div class="modal-dialog modal-dialog-scrollable modal-xl" role="document">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title">@SiteResources.EasyUse.ToTitleCase()</h5>
                <button type="button" class="close" data-dismiss="modal" aria-label="Close">
                    <i aria-hidden="true" class="ki ki-close"></i>
                </button>
            </div>
            <div class="modal-body">
                <div id="divPartialApplicationEasyUse"></div>
            </div>
        </div>
    </div>
</div>

@section Scripts {
    <script src="~/js/Appointment/Application/list-turkmenistan.js"></script>

    <script id="contactInformationTemplate" type="text/x-kendo-tmpl">
        <div>
            <i class="la la-envelope"></i>
            <span>#=Email#</span>
        </div>
        <div>
            <i class="la la-phone"></i>
            <span>#=PhoneNumber1#</span>
        </div>
    </script>

    <script>
        $('#filtering-options').on('keyup',
            function (e) {
                if (e.key == "Enter") $('#filterGridApplication').click();
                if (e.key == "Escape") $('#clearGridApplicationFilter').click();
            });

        function formatIhbNumber(ihbNumber) {
            return ihbNumber ? `No: 102/2 ${ihbNumber}` : "";
        }

        function getIhbDocumentNumber(IhbDocumentNumber) {
            if (IhbDocumentNumber && IhbDocumentNumber > 0) {
                return `${IhbDocumentNumber}`;
            }
            return "1";
        }

        function checkPassiveDeletedApplicationsAllowed() {
            var allowPassiveDeletedApplications = $('#FilterAllowPassiveDeletedApplications').is(":checked");
            $("#FilterAllowPassiveDeletedApplications").prop("checked", false);

            if (allowPassiveDeletedApplications) {
                return new Promise(function (resolve, reject) {
                    $.ajax({
                        type: "GET",
                        url: "/Appointment/Application/IsPassiveDeletedApplicationsAllowed",
                        data: {
                        },
                        success: function (data) {
                            if (data) {
                                if (data.Result === true) {
                                    $("#FilterAllowPassiveDeletedApplications").prop("checked", true);
                                } else if (data.Message) {
                                    bootbox.dialog({
                                        message: '<p style="color:#FFFFFF;"><b>' + data.Message + '</b></p>',
                                        size: 'extra-large',
                                        onEscape: true,
                                        backdrop: true
                                    });
                                    $('.modal-content').css("background-color", "#FFA800");
                                } else {
                                    showNotification('danger', jsResources.ErrorOccurred);
                                }
                            }
                        },
                        error: function () {
                            showNotification('danger', jsResources.ErrorOccurred);
                        },
                        complete: function () {
                            resolve();
                        }
                    });
                });
            }
        }

         function getApplicationIhbStatusId() {
            var grid = $('#gridApplication').data('kendoGrid');
            var dataItem = grid.dataItem(grid.table.find('.k-edit-cell').parents('tr'));
            return { IhbStatusId: dataItem.IhbStatusId };
        }

        function onGridEdit(e) {
            var grid = $("#gridApplication").data("kendoGrid");
            if (e.model.EncryptedIhbDocumentId) {
                grid.closeCell();

                showNotification('warning', jsResources.CannotEditWithIhbFile);
            }
            else if (e.model.EncryptedIhbOrderId){
                grid.closeCell();

                showNotification('warning', jsResources.AlreadyHasIhbOrder);
            }
        }

        function onDataBound(e) {
            var grid = e.sender;
              grid.tbody.find("tr").each(function() {
                  var row = $(this);
                  var dataItem = grid.dataItem(row);

                  if (dataItem.EncryptedIhbDocumentId || dataItem.EncryptedIhbOrderId) {
                      row.find(".k-checkbox").hide();

                  } else {
                      row.find(".k-checkbox").show();
                  }
              });
        }

        function onCheckboxChange() {

            if ($("#gridApplication").data("kendoGrid").tbody.find("input:checked").closest("td").length > 1) {
                $('.div-update-selected-rows').show();
            }
            else {
                $('.div-update-selected-rows').hide();
            }
        }


        function ErrorHandler(e) {
            showNotification("error", jsResources.ErrorOccurred);
        }

        function onGridRequestEnd(e) {
            if (e.type === "update") {
                // Check direct response first
                if (e.response && e.response.IhbOrderErrorMessage) {
                    showNotification("warning", e.response.IhbOrderErrorMessage);
                    return;
                }

                // Check all items in the response
                let didWork = 0;
                if (e.response && e.response.Data) {
                    $.each(e.response.Data, function(index, item) {
                        if (item.IhbOrderErrorMessage) {
                            showNotification("warning", item.IhbOrderErrorMessage);
                            didWork++;
                        }
                    });
                }

                if (didWork === 0) {
                    showNotification("success", jsResources.OperationIsSuccessful);
                }

                this.read();
            }
        }

    </script>

    <script>
        $(document).ready(function () {
            $(window).keydown(function (event) {
                if (event.keyCode == 13) {
                    event.preventDefault();
                    return false;
                }
            });

            var exportFlag = false;
            $("#gridApplication").data("kendoGrid").bind("excelExport", function (e) {
                if (!exportFlag) {
                    for (var i = 0; i < e.sender.columns.length; i++) {
                        if (e.sender.columns[i].hidden) {
                            e.sender.showColumn(i);
                        }
                    }
                    e.sender.hideColumn(0);
                    e.sender.hideColumn(15);
                    e.preventDefault();
                    exportFlag = true;
                    setTimeout(function () {
                        e.sender.saveAsExcel();
                    });
                } else {
                    for (var i = 0; i < e.sender.columns.length; i++) {
                        if (e.sender.columns[i].hidden === false)
                            e.sender.hideColumn(i);
                    }
                    e.sender.showColumn(0);
                    e.sender.showColumn(15);
                    exportFlag = false;
                }
            });
        });

        function onChangeFilterAllList() {
            var allList = document.getElementById("FilterAllList");
            var suitable = document.getElementById("FilterSuitable");
            var notSuitable = document.getElementById("FilterNotSuitable");
            var filterWaiting = document.getElementById("FilterWaiting");

            if (allList.checked) {
                suitable.checked = false;
                notSuitable.checked = false;
                filterWaiting.checked = false;
            }
        }

        function onChangeFilterSuitable() {
            var allList = document.getElementById("FilterAllList");
            var suitable = document.getElementById("FilterSuitable");
            var notSuitable = document.getElementById("FilterNotSuitable");
            var filterWaiting = document.getElementById("FilterWaiting");

            if (suitable.checked) {
                allList.checked = false;
                notSuitable.checked = false;
                filterWaiting.checked = false;
            }
        }

        function onChangeFilterNotSuitable() {
            var allList = document.getElementById("FilterAllList");
            var suitable = document.getElementById("FilterSuitable");
            var notSuitable = document.getElementById("FilterNotSuitable");
            var filterWaiting = document.getElementById("FilterWaiting");

            if (notSuitable.checked) {
                allList.checked = false;
                suitable.checked = false;
                filterWaiting.checked = false;
            }
        }

        function onChangeFilterWaiting() {
            var allList = document.getElementById("FilterAllList");
            var suitable = document.getElementById("FilterSuitable");
            var notSuitable = document.getElementById("FilterNotSuitable");
            var filterWaiting = document.getElementById("FilterWaiting");

            if (filterWaiting.checked) {
                allList.checked = false;
                suitable.checked = false;
                notSuitable.checked = false;
            }
        }
   
    </script>

    <script id="gridApplicationFileOperationsTemplate" type="text/x-kendo-tmpl">
        <div class="btn-group">
            #if(EncryptedIhbDocumentId != null)
            {#
             <a class='font-weight-bold btn btn-link-info' href='/Appointment/Application/DownloadFile?encryptedDocumentId=#=EncryptedIhbDocumentId#' target='_blank'>@Html.Raw(SiteResources.Download)</a>
            #}#
        </div>
    </script>
}