﻿@using Gateway.Extensions
@model AddUpdateApplicationViewModel

<div id="divStep4ExtraPackages" class="pb-5" data-wizard-type="step-content">
    @Html.HiddenFor(m => Model.ShowPaymentMethods)
    <h4 class="mb-10 font-weight-bold text-dark">@SiteResources.ApplicationStep4ExtraPackages.ToTitleCase()</h4>
    <div class="row">
        <div class="col-xl-12">
            @{
                if (Model.IsExistingApplication)
                {
                    <div class="form-group row">
                        <div class="col-3">
                            <span class="switch switch-outline switch-icon switch-success">
                                <label class="pr-2">@SiteResources.AllowUpdate.ToTitleCase()</label>
                                <label>
                                    <input id="AllowUpdate" type="checkbox" 
                                    @(Model.ApplicationTypeId == (int)ApplicationType.NonApplicationRelatedInsurance ? "disabled" : "") onchange="checkExtraPackageUpdateAllowed();" />
                                    <span></span>
                                </label>
                            </span>
                        </div>
                    </div>
                    <div class="separator separator-dashed my-5"></div>
                }
            }
            <div id="divExtraPackageDetails">
                @{
                    if (Model.ExtraFees != null)
                    {
                        <h3 class="font-size-lg text-dark font-weight-bold mb-6">@EnumHelper.GetEnumDescription(typeof(ExtraFeeCategoryType), 6.ToString()).ToTitleCase()</h3>
                        foreach (var item in Model.ExtraFees.Where(g => g.CategoryTypeId == 6).Select((value, groupIndex) => new { groupIndex, value }))
                        {
                            var index = Model.ExtraFees.IndexOf(item.value);
                            var typeId = item.value.TypeId;
                            var minimumItem = item.value.MinimumItem.HasValue ? item.value.MinimumItem.Value : 0;
                            var categoryTypeId = 6;

                            @Html.HiddenFor(m => Model.ExtraFees[index].ApplicationExtraFeeId)
                            @Html.HiddenFor(m => Model.ExtraFees[index].ExtraFeeId)
                            @Html.HiddenFor(m => Model.ExtraFees[index].TypeId)
                            @Html.HiddenFor(m => Model.ExtraFees[index].MinimumItem)
                            @Html.HiddenFor(m => Model.ExtraFees[index].CategoryTypeId)
                            @Html.HiddenFor(m => Model.ExtraFees[index].IsPreChecked)
                            @Html.HiddenFor(m => Model.ExtraFees[index].IsPreQuantity)
                            @Html.HiddenFor(m => Model.ExtraFees[index].ExtraFeeName)
                            @Html.HiddenFor(m => Model.ExtraFees[index].IsForRejectedApplications)
                            @Html.HiddenFor(m => Model.ExtraFees[index].RejectionFeePolicyType)

                            <div class="fee_@categoryTypeId-@index form-group row">
                                <div class="col-lg-3 col-md-6">
                                    @(Html.Kendo().CheckBoxFor(m => Model.ExtraFees[index].IsChecked)
                                    .Checked(Model.IsExistingApplication == true ? (item.value.ApplicationExtraFeeId != null && item.value.IsChecked) : item.value.IsChecked)
                                    .HtmlAttributes(new { @class = "checkbox-square", onchange = "onChangeExtraFee(" + index + "," + minimumItem + "," + typeId + "," + categoryTypeId + ");" }))
                                    @item.value.ExtraFeeName.ToTitleCase()
                                </div>
                                <div class="col-lg-3 col-md-6">
                                    (
                                    @item.value.Price @EnumHelper.GetEnumDescription(typeof(CurrencyType), item.value.CurrencyId.ToString())
                                    @if (item.value.TypeId == (int)ExtraFeeType.Multiple)
                                    {
                                        <span>/</span>
                                        @SiteResources.Item.ToLower()
                                    }
                                    )
                                    @if (item.value.TypeId == (int)ExtraFeeType.Multiple)
                                    {
                                        <input style="width: 50px;" asp-for="@Model.ExtraFees[index].Quantity" type="number" min="@minimumItem" />
                                        @SiteResources.Item.ToLower()
                                        <span asp-validation-for="@Model.ExtraFees[index].Quantity"></span>
                                    }
                                    else
                                    {
                                        <input hidden style="width: 50px;" asp-for="@Model.ExtraFees[index].Quantity" type="number" value="1" />
                                    }
                                </div>
                                <div class="paymentShowHide_@index col-lg-3 col-md-6">
                                    @(Html.Kendo().CheckBoxFor(m => Model.ExtraFees[index].Cash).Checked(Model.IsExistingApplication == true ? (item.value.ApplicationExtraFeeId != null && Model.ExtraFees[index].Cash) : Model.ExtraFees[index].Cash).HtmlAttributes(new { @class = "checkbox-square", onchange = "onChangeExtraFeePaymentMethods(" + index + ");" })) @SiteResources.Cash
                                    @(Html.Kendo().CheckBoxFor(m => Model.ExtraFees[index].Pos).Checked(Model.IsExistingApplication == true ? (item.value.ApplicationExtraFeeId != null && Model.ExtraFees[index].Pos) : Model.ExtraFees[index].Pos).HtmlAttributes(new { @class = "checkbox-square", onchange = "onChangeExtraFeePaymentMethods(" + index + ");" })) Pos
                                    @(Html.Kendo().CheckBoxFor(m => Model.ExtraFees[index].Online).Checked(Model.IsExistingApplication == true ? (item.value.ApplicationExtraFeeId != null && Model.ExtraFees[index].Online) : Model.ExtraFees[index].Online).HtmlAttributes(new { @class = "checkbox-square", onchange = "onChangeExtraFeePaymentMethods(" + index + ");" })) Online
                                </div>
                            </div>
                        }
                        <div class="separator separator-dashed my-5"></div>

                        <h3 class="font-size-lg text-dark font-weight-bold mb-6">@EnumHelper.GetEnumDescription(typeof(ExtraFeeCategoryType), 4.ToString()).ToTitleCase()</h3>
                        foreach (var item in Model.ExtraFees.Where(g => g.CategoryTypeId == 4).Select((value, groupIndex) => new { groupIndex, value }))
                        {
                            var index = Model.ExtraFees.IndexOf(item.value);
                            var typeId = item.value.TypeId;
                            var minimumItem = item.value.MinimumItem.HasValue ? item.value.MinimumItem.Value : 0;
                            var categoryTypeId = 4;

                            @Html.HiddenFor(m => Model.ExtraFees[index].ApplicationExtraFeeId)
                            @Html.HiddenFor(m => Model.ExtraFees[index].ExtraFeeId)
                            @Html.HiddenFor(m => Model.ExtraFees[index].TypeId)
                            @Html.HiddenFor(m => Model.ExtraFees[index].MinimumItem)
                            @Html.HiddenFor(m => Model.ExtraFees[index].CategoryTypeId)
                            @Html.HiddenFor(m => Model.ExtraFees[index].IsPreChecked)
                            @Html.HiddenFor(m => Model.ExtraFees[index].IsPreQuantity)
                            @Html.HiddenFor(m => Model.ExtraFees[index].ExtraFeeName)
                            @Html.HiddenFor(m => Model.ExtraFees[index].IsForRejectedApplications)
                            @Html.HiddenFor(m => Model.ExtraFees[index].RejectionFeePolicyType)

                            <div class="fee_@categoryTypeId-@index form-group row">
                                <div class="col-lg-3 col-md-6">
                                    @(Html.Kendo().CheckBoxFor(m => Model.ExtraFees[index].IsChecked)
                                    .Checked(item.value.IsChecked)
                                    .HtmlAttributes(new { @class = "checkbox-square", onchange = "onChangeExtraFee(" + index + "," + minimumItem + "," + typeId + "," + categoryTypeId + ");" }))
                                    @item.value.ExtraFeeName.ToTitleCase()
                                </div>
                                <div class="col-lg-3 col-md-6">
                                    (
                                    @item.value.Price @EnumHelper.GetEnumDescription(typeof(CurrencyType), item.value.CurrencyId.ToString())
                                    @if (item.value.TypeId == (int)ExtraFeeType.Multiple)
                                    {
                                        <span>/</span>
                                        @SiteResources.Item.ToLower()
                                    }
                                    )
                                    @if (item.value.TypeId == (int)ExtraFeeType.Multiple)
                                    {
                                        <input style="width: 50px;" asp-for="@Model.ExtraFees[index].Quantity" type="number" min="@minimumItem" />
                                        @SiteResources.Item.ToLower()
                                        <span asp-validation-for="@Model.ExtraFees[index].Quantity"></span>
                                    }
                                    else
                                    {
                                        <input hidden style="width: 50px;" asp-for="@Model.ExtraFees[index].Quantity" type="number" value="1" />
                                    }
                                </div>
                                <div class="paymentShowHide_@index col-lg-3 col-md-6">
                                    @(Html.Kendo().CheckBoxFor(m => Model.ExtraFees[index].Cash).Checked(Model.ExtraFees[index].Cash).HtmlAttributes(new { @class = "checkbox-square", onchange = "onChangeExtraFeePaymentMethods(" + index + ");" })) @SiteResources.Cash
                                    @(Html.Kendo().CheckBoxFor(m => Model.ExtraFees[index].Pos).Checked(Model.ExtraFees[index].Pos).HtmlAttributes(new { @class = "checkbox-square", onchange = "onChangeExtraFeePaymentMethods(" + index + ");" })) Pos
                                    @(Html.Kendo().CheckBoxFor(m => Model.ExtraFees[index].Online).Checked(Model.ExtraFees[index].Online).HtmlAttributes(new { @class = "checkbox-square", onchange = "onChangeExtraFeePaymentMethods(" + index + ");" })) Online
                                </div>
                            </div>
                        }
                        <div class="separator separator-dashed my-5"></div>

                        <h3 class="font-size-lg text-dark font-weight-bold mb-6">@EnumHelper.GetEnumDescription(typeof(ExtraFeeCategoryType), 1.ToString()).ToTitleCase()</h3>
                        foreach (var item in Model.ExtraFees.Where(g => g.CategoryTypeId == 1).OrderBy(o => o.MainFeeId).Select((value, groupIndex) => new { groupIndex, value }))
                        {
                            var index = Model.ExtraFees.IndexOf(item.value);
                            var typeId = item.value.TypeId;
                            var minimumItem = item.value.MinimumItem.HasValue ? item.value.MinimumItem.Value : 0;
                            var categoryTypeId = 1;
                            var lastExtraFee = Model.ExtraFees
                            .LastOrDefault(g => g.MainFeeId == item.value.MainFeeId);
                            var lastExtraFeeIndex = Model.ExtraFees.IndexOf(lastExtraFee);
                            var isAnyMainFeeCheecked = Model.ExtraFees.Any(a => a.MainFeeId == item.value.MainFeeId && a.IsChecked);

                            @Html.HiddenFor(m => Model.ExtraFees[index].ApplicationExtraFeeId)
                            @Html.HiddenFor(m => Model.ExtraFees[index].ExtraFeeId)
                            @Html.HiddenFor(m => Model.ExtraFees[index].TypeId)
                            @Html.HiddenFor(m => Model.ExtraFees[index].MinimumItem)
                            @Html.HiddenFor(m => Model.ExtraFees[index].CategoryTypeId)
                            @Html.HiddenFor(m => Model.ExtraFees[index].IsPreChecked)
                            @Html.HiddenFor(m => Model.ExtraFees[index].IsPreQuantity)
                            @Html.HiddenFor(m => Model.ExtraFees[index].ExtraFeeName)
                            @Html.HiddenFor(m => Model.ExtraFees[index].Price)
                            @Html.HiddenFor(m => Model.ExtraFees[index].CurrencyId)
                            @Html.HiddenFor(m => Model.ExtraFees[index].IsForRejectedApplications)
                            @Html.HiddenFor(m => Model.ExtraFees[index].RejectionFeePolicyType)


                            if (item.value.IsMainFee)
                            {
                                <div class="fee_@categoryTypeId-@index other_@index form-group row">
                                    <div class="col-lg-3 col-md-6">
                                        @(Html.Kendo().CheckBoxFor(m => Model.ExtraFees[index].IsChecked)
                                        .Checked(item.value.IsChecked)
                                        .HtmlAttributes(new { @class = "main-fee_" + index + " checkbox-square main-id_" + item.value.MainFeeId, onchange = "toggleSubFees(" + index + "," + lastExtraFeeIndex + "," + item.value.MainFeeId + ");" }))
                                        @item.value.ExtraFeeName.ToTitleCase()
                                    </div>
                                    <div class="col-lg-3 col-md-6">
                                        (@item.value.Price @EnumHelper.GetEnumDescription(typeof(CurrencyType), item.value.CurrencyId.ToString())
                                        @if (item.value.TypeId == (int)ExtraFeeType.Multiple)
                                        {
                                            <span>/</span>
                                            @SiteResources.Item.ToLower()
                                        }
                                        )
                                        @if (item.value.TypeId == (int)ExtraFeeType.Multiple)
                                        {
                                            <input style="width: 50px;" asp-for="@Model.ExtraFees[index].Quantity" type="number" min="@minimumItem" />
                                            @SiteResources.Item.ToLower()
                                            <span asp-validation-for="@Model.ExtraFees[index].Quantity"></span>
                                        }
                                        else
                                        {
                                            <input hidden style="width: 50px;" asp-for="@Model.ExtraFees[index].Quantity" type="number" value="1" />
                                        }
                                    </div>
                                    <div class="paymentShowHide_@index col-lg-3 col-md-6">
                                        @(Html.Kendo().CheckBoxFor(m => Model.ExtraFees[index].Cash).Checked(Model.ExtraFees[index].Cash).HtmlAttributes(new { @class = "checkbox-square", onchange = "onChangeExtraFeePaymentMethods(" + index + ");" })) @SiteResources.Cash
                                        @(Html.Kendo().CheckBoxFor(m => Model.ExtraFees[index].Pos).Checked(Model.ExtraFees[index].Pos).HtmlAttributes(new { @class = "checkbox-square", onchange = "onChangeExtraFeePaymentMethods(" + index + ");" })) Pos
                                        @(Html.Kendo().CheckBoxFor(m => Model.ExtraFees[index].Online).Checked(Model.ExtraFees[index].Online).HtmlAttributes(new { @class = "checkbox-square", onchange = "onChangeExtraFeePaymentMethods(" + index + ");" })) Online
                                    </div>
                                </div>
                                if (index == lastExtraFeeIndex)
                                {
                                    <div class="fee_@categoryTypeId-@index" id="sub-fees_@<EMAIL>" style="@(isAnyMainFeeCheecked ? "" : "display:none;")">
                                        @foreach (var subFee in Model.ExtraFees.Where(g => g.CategoryTypeId == 1 && g.IsSubFee && g.RelatedMainFeeId == item.value.MainFeeId))
                                        {
                                            var subFeeIndex = Model.ExtraFees.IndexOf(subFee);
                                            var subFeeTypeId = subFee.TypeId;
                                            var subFeeMinimumItem = subFee.MinimumItem.HasValue ? subFee.MinimumItem.Value : 0;

                                            <div class="other_@subFeeIndex form-group row">
                                                <div class="col-lg-3 col-md-6">
                                                    @(Html.Kendo().CheckBoxFor(m => Model.ExtraFees[subFeeIndex].IsChecked)
                                                    .Checked(subFee.IsChecked)
                                                    .HtmlAttributes(new { @class = "checkbox-square ml-4", onchange = "onChangeExtraFee(" + subFeeIndex + "," + subFeeMinimumItem + "," + subFeeTypeId + "," + categoryTypeId + ");" }))
                                                    @subFee.ExtraFeeName.ToTitleCase()
                                                </div>
                                                <div class="col-lg-3 col-md-6">
                                                    (
                                                    @subFee.Price @EnumHelper.GetEnumDescription(typeof(CurrencyType), subFee.CurrencyId.ToString())
                                                    @if (subFee.TypeId == (int)ExtraFeeType.Multiple)
                                                    {
                                                        <span>/</span>
                                                        @SiteResources.Item.ToLower()
                                                    }
                                                    )
                                                    @if (subFee.TypeId == (int)ExtraFeeType.Multiple)
                                                    {
                                                        <input style="width: 50px;" asp-for="@Model.ExtraFees[subFeeIndex].Quantity" type="number" min="@subFeeMinimumItem" />
                                                        @SiteResources.Item.ToLower()
                                                        <span asp-validation-for="@Model.ExtraFees[subFeeIndex].Quantity"></span>
                                                    }
                                                    else
                                                    {
                                                        <input hidden style="width: 50px;" asp-for="@Model.ExtraFees[subFeeIndex].Quantity" type="number" value="1" />
                                                    }
                                                </div>
                                                <div class="paymentShowHide_@subFeeIndex col-lg-3 col-md-6">
                                                    @(Html.Kendo().CheckBoxFor(m => Model.ExtraFees[subFeeIndex].Cash).Checked(Model.ExtraFees[subFeeIndex].Cash).HtmlAttributes(new { @class = "checkbox-square", onchange = "onChangeExtraFeePaymentMethods(" + subFeeIndex + ");" })) @SiteResources.Cash
                                                    @(Html.Kendo().CheckBoxFor(m => Model.ExtraFees[subFeeIndex].Pos).Checked(Model.ExtraFees[subFeeIndex].Pos).HtmlAttributes(new { @class = "checkbox-square", onchange = "onChangeExtraFeePaymentMethods(" + subFeeIndex + ");" })) Pos
                                                    @(Html.Kendo().CheckBoxFor(m => Model.ExtraFees[subFeeIndex].Online).Checked(Model.ExtraFees[subFeeIndex].Online).HtmlAttributes(new { @class = "checkbox-square", onchange = "onChangeExtraFeePaymentMethods(" + subFeeIndex + ");" })) Online
                                                </div>
                                            </div>
                                        }
                                    </div>
                                }
                            }

                            if (!item.value.IsMainFee && !item.value.IsSubFee)
                            {
                                <div class="fee_@categoryTypeId-@index other_@index form-group row">
                                    <div class="col-lg-3 col-md-6">
                                        @(Html.Kendo().CheckBoxFor(m => Model.ExtraFees[index].IsChecked)
                                        .Checked(item.value.IsChecked)
                                        .HtmlAttributes(new { @class = "checkbox-square", onchange = "onChangeExtraFee(" + index + "," + minimumItem + "," + typeId + "," + categoryTypeId + ");" }))
                                        @item.value.ExtraFeeName.ToTitleCase()
                                    </div>
                                    <div class="col-lg-3 col-md-6">
                                        (
                                        @item.value.Price @EnumHelper.GetEnumDescription(typeof(CurrencyType), item.value.CurrencyId.ToString())
                                        @if (item.value.TypeId == (int)ExtraFeeType.Multiple)
                                        {
                                            <span>/</span>
                                            @SiteResources.Item.ToLower()
                                        }
                                        )
                                        @if (item.value.TypeId == (int)ExtraFeeType.Multiple)
                                        {
                                            <input style="width: 50px;" asp-for="@Model.ExtraFees[index].Quantity" type="number" min="@minimumItem" />
                                            @SiteResources.Item.ToLower()
                                            <span asp-validation-for="@Model.ExtraFees[index].Quantity"></span>
                                        }
                                        else
                                        {
                                            <input hidden style="width: 50px;" asp-for="@Model.ExtraFees[index].Quantity" type="number" value="1" />
                                        }
                                    </div>
                                    <div class="paymentShowHide_@index col-lg-3 col-md-6">
                                        @(Html.Kendo().CheckBoxFor(m => Model.ExtraFees[index].Cash).Checked(Model.ExtraFees[index].Cash).HtmlAttributes(new { @class = "checkbox-square", onchange = "onChangeExtraFeePaymentMethods(" + index + ");" })) @SiteResources.Cash
                                        @(Html.Kendo().CheckBoxFor(m => Model.ExtraFees[index].Pos).Checked(Model.ExtraFees[index].Pos).HtmlAttributes(new { @class = "checkbox-square", onchange = "onChangeExtraFeePaymentMethods(" + index + ");" })) Pos
                                        @(Html.Kendo().CheckBoxFor(m => Model.ExtraFees[index].Online).Checked(Model.ExtraFees[index].Online).HtmlAttributes(new { @class = "checkbox-square", onchange = "onChangeExtraFeePaymentMethods(" + index + ");" })) Online
                                    </div>
                                </div>
                            }


                        }

                        <div class="separator separator-dashed my-5"></div>

                        <h3 class="font-size-lg text-dark font-weight-bold mb-6">@EnumHelper.GetEnumDescription(typeof(ExtraFeeCategoryType), 5.ToString()).ToTitleCase()</h3>
                        foreach (var item in Model.ExtraFees.Where(g => g.CategoryTypeId == 5).Select((value, groupIndex) => new { groupIndex, value }))
                        {
                            var index = Model.ExtraFees.IndexOf(item.value);
                            var typeId = item.value.TypeId;
                            var minimumItem = item.value.MinimumItem.HasValue ? item.value.MinimumItem.Value : 0;
                            var categoryTypeId = 5;

                            @Html.HiddenFor(m => Model.ExtraFees[index].ApplicationExtraFeeId)
                            @Html.HiddenFor(m => Model.ExtraFees[index].ExtraFeeId)
                            @Html.HiddenFor(m => Model.ExtraFees[index].TypeId)
                            @Html.HiddenFor(m => Model.ExtraFees[index].MinimumItem)
                            @Html.HiddenFor(m => Model.ExtraFees[index].CategoryTypeId)
                            @Html.HiddenFor(m => Model.ExtraFees[index].IsPreChecked)
                            @Html.HiddenFor(m => Model.ExtraFees[index].IsPreQuantity)
                            @Html.HiddenFor(m => Model.ExtraFees[index].ExtraFeeName)
                            @Html.HiddenFor(m => Model.ExtraFees[index].IsForRejectedApplications)
                            @Html.HiddenFor(m => Model.ExtraFees[index].RejectionFeePolicyType)


                            <div class="fee_@categoryTypeId-@index cargo_@index <EMAIL> form-group row"
                                 style="@(item.value.IsSubFee ? (Model.IsExistingApplication ? "" : "display:none;") : "")">
                                <div class="col-lg-3 col-md-6">
                                    @(Html.Kendo().CheckBoxFor(m => Model.ExtraFees[index].IsChecked)
                                    .Checked(item.value.IsChecked)
                                    .HtmlAttributes(new { @class = "checkbox-square", onchange = "onChangeExtraFee(" + index + "," + minimumItem + "," + typeId + "," + categoryTypeId + ");" }))
                                    @item.value.ExtraFeeName.ToTitleCase()
                                </div>
                                <div class="col-lg-3 col-md-6">
                                    (
                                    @item.value.Price @EnumHelper.GetEnumDescription(typeof(CurrencyType), item.value.CurrencyId.ToString())
                                    @if (item.value.TypeId == (int)ExtraFeeType.Multiple)
                                    {
                                        <span>/</span>
                                        @SiteResources.Item.ToLower()
                                    }
                                    )
                                    @if (item.value.TypeId == (int)ExtraFeeType.Multiple)
                                    {
                                        <input style="width: 50px;" asp-for="@Model.ExtraFees[index].Quantity" type="number" min="@minimumItem" />
                                        @SiteResources.Item.ToLower()
                                        <span asp-validation-for="@Model.ExtraFees[index].Quantity"></span>
                                    }
                                    else
                                    {
                                        <input hidden style="width: 50px;" asp-for="@Model.ExtraFees[index].Quantity" type="number" value="1" />
                                    }
                                </div>
                                <div class="paymentShowHide_@index col-lg-3 col-md-6">
                                    @(Html.Kendo().CheckBoxFor(m => Model.ExtraFees[index].Cash).Checked(Model.ExtraFees[index].Cash).HtmlAttributes(new { @class = "checkbox-square", onchange = "onChangeExtraFeePaymentMethods(" + index + ");" })) @SiteResources.Cash
                                    @(Html.Kendo().CheckBoxFor(m => Model.ExtraFees[index].Pos).Checked(Model.ExtraFees[index].Pos).HtmlAttributes(new { @class = "checkbox-square", onchange = "onChangeExtraFeePaymentMethods(" + index + ");" })) Pos
                                    @(Html.Kendo().CheckBoxFor(m => Model.ExtraFees[index].Online).Checked(Model.ExtraFees[index].Online).HtmlAttributes(new { @class = "checkbox-square", onchange = "onChangeExtraFeePaymentMethods(" + index + ");" })) Online
                                </div>
                            </div>
                        }
                        <div class="separator separator-dashed my-5"></div>

                        <h3 class="font-size-lg text-dark font-weight-bold mb-6">@EnumHelper.GetEnumDescription(typeof(ExtraFeeCategoryType), 2.ToString()).ToTitleCase()</h3>
                        foreach (var item in Model.ExtraFees.Where(g => g.CategoryTypeId == 2).Select((value, groupIndex) => new { groupIndex, value }))
                        {
                            var index = Model.ExtraFees.IndexOf(item.value);
                            var typeId = item.value.TypeId;
                            var minimumItem = item.value.MinimumItem.HasValue ? item.value.MinimumItem.Value : 0;
                            var categoryTypeId = 2;

                            @Html.HiddenFor(m => Model.ExtraFees[index].ApplicationExtraFeeId)
                            @Html.HiddenFor(m => Model.ExtraFees[index].ExtraFeeId)
                            @Html.HiddenFor(m => Model.ExtraFees[index].TypeId)
                            @Html.HiddenFor(m => Model.ExtraFees[index].MinimumItem)
                            @Html.HiddenFor(m => Model.ExtraFees[index].CategoryTypeId)
                            @Html.HiddenFor(m => Model.ExtraFees[index].IsPreChecked)
                            @Html.HiddenFor(m => Model.ExtraFees[index].IsPreQuantity)
                            @Html.HiddenFor(m => Model.ExtraFees[index].ExtraFeeName)
                            @Html.HiddenFor(m => Model.ExtraFees[index].IsForRejectedApplications)
                            @Html.HiddenFor(m => Model.ExtraFees[index].RejectionFeePolicyType)


                            <div class="fee_@categoryTypeId-@index form-group row">
                                <div class="col-lg-3 col-md-6">
                                    @(Html.Kendo().CheckBoxFor(m => Model.ExtraFees[index].IsChecked)
                                    .Checked(item.value.IsChecked)
                                    .HtmlAttributes(new { @class = "checkbox-square", onchange = "onChangeExtraFee(" + index + "," + minimumItem + "," + typeId + "," + categoryTypeId + ");" }))
                                    @item.value.ExtraFeeName.ToTitleCase()
                                </div>
                                <div class="col-lg-3 col-md-6">
                                    (
                                    @item.value.Price @EnumHelper.GetEnumDescription(typeof(CurrencyType), item.value.CurrencyId.ToString())
                                    @if (item.value.TypeId == (int)ExtraFeeType.Multiple)
                                    {
                                        <span>/</span>
                                        @SiteResources.Item.ToLower()
                                    }
                                    )
                                    @if (item.value.TypeId == (int)ExtraFeeType.Multiple)
                                    {
                                        <input style="width: 50px;" asp-for="@Model.ExtraFees[index].Quantity" type="number" min="@minimumItem" />
                                        @SiteResources.Item.ToLower()
                                        <span asp-validation-for="@Model.ExtraFees[index].Quantity"></span>
                                    }
                                    else
                                    {
                                        <input hidden style="width: 50px;" asp-for="@Model.ExtraFees[index].Quantity" type="number" value="1" />
                                    }
                                </div>
                                <div class="paymentShowHide_@index col-lg-3 col-md-6">
                                    @(Html.Kendo().CheckBoxFor(m => Model.ExtraFees[index].Cash).Checked(Model.ExtraFees[index].Cash).HtmlAttributes(new { @class = "checkbox-square", onchange = "onChangeExtraFeePaymentMethods(" + index + ");" })) @SiteResources.Cash
                                    @(Html.Kendo().CheckBoxFor(m => Model.ExtraFees[index].Pos).Checked(Model.ExtraFees[index].Pos).HtmlAttributes(new { @class = "checkbox-square", onchange = "onChangeExtraFeePaymentMethods(" + index + ");" })) Pos
                                    @(Html.Kendo().CheckBoxFor(m => Model.ExtraFees[index].Online).Checked(Model.ExtraFees[index].Online).HtmlAttributes(new { @class = "checkbox-square", onchange = "onChangeExtraFeePaymentMethods(" + index + ");" })) Online
                                </div>
                            </div>
                        }
                        <div class="separator separator-dashed my-5"></div>

                        <h3 class="font-size-lg text-dark font-weight-bold mb-6">@EnumHelper.GetEnumDescription(typeof(ExtraFeeCategoryType), 3.ToString()).ToTitleCase()</h3>
                        foreach (var item in Model.ExtraFees.Where(g => g.CategoryTypeId == 3).Select((value, groupIndex) => new { groupIndex, value }))
                        {
                            var index = Model.ExtraFees.IndexOf(item.value);
                            var typeId = item.value.TypeId;
                            var minimumItem = item.value.MinimumItem.HasValue ? item.value.MinimumItem.Value : 0;
                            var categoryTypeId = 3;

                            @Html.HiddenFor(m => Model.ExtraFees[index].ApplicationExtraFeeId)
                            @Html.HiddenFor(m => Model.ExtraFees[index].ExtraFeeId)
                            @Html.HiddenFor(m => Model.ExtraFees[index].TypeId)
                            @Html.HiddenFor(m => Model.ExtraFees[index].MinimumItem)
                            @Html.HiddenFor(m => Model.ExtraFees[index].CategoryTypeId)
                            @Html.HiddenFor(m => Model.ExtraFees[index].IsPreChecked)
                            @Html.HiddenFor(m => Model.ExtraFees[index].IsPreQuantity)
                            @Html.HiddenFor(m => Model.ExtraFees[index].ExtraFeeName)
                            @Html.HiddenFor(m => Model.ExtraFees[index].IsForRejectedApplications)
                            @Html.HiddenFor(m => Model.ExtraFees[index].RejectionFeePolicyType)


                            <div class="fee_@categoryTypeId-@index form-group row">
                                <div class="col-lg-3 col-md-6">
                                    @(Html.Kendo().CheckBoxFor(m => Model.ExtraFees[index].IsChecked)
                                    .Checked(item.value.IsChecked)
                                    .HtmlAttributes(new { @class = "checkbox-square", onchange = "onChangeExtraFee(" + index + "," + minimumItem + "," + typeId + "," + categoryTypeId + ");" }))
                                    @item.value.ExtraFeeName.ToTitleCase()
                                </div>
                                <div class="col-lg-3 col-md-6">
                                    (
                                    @item.value.Price @EnumHelper.GetEnumDescription(typeof(CurrencyType), item.value.CurrencyId.ToString())
                                    @if (item.value.TypeId == (int)ExtraFeeType.Multiple)
                                    {
                                        <span>/</span>
                                        @SiteResources.Item.ToLower()
                                    }
                                    )
                                    @if (item.value.TypeId == (int)ExtraFeeType.Multiple)
                                    {
                                        <input style="width: 50px;" asp-for="@Model.ExtraFees[index].Quantity" type="number" min="@minimumItem" />
                                        @SiteResources.Item.ToLower()
                                        <span asp-validation-for="@Model.ExtraFees[index].Quantity"></span>
                                    }
                                    else
                                    {
                                        <input hidden style="width: 50px;" asp-for="@Model.ExtraFees[index].Quantity" type="number" value="1" />
                                    }
                                </div>
                                <div class="paymentShowHide_@index col-lg-3 col-md-6">
                                    @(Html.Kendo().CheckBoxFor(m => Model.ExtraFees[index].Cash).Checked(Model.ExtraFees[index].Cash).HtmlAttributes(new { @class = "checkbox-square", onchange = "onChangeExtraFeePaymentMethods(" + index + ");" })) @SiteResources.Cash
                                    @(Html.Kendo().CheckBoxFor(m => Model.ExtraFees[index].Pos).Checked(Model.ExtraFees[index].Pos).HtmlAttributes(new { @class = "checkbox-square", onchange = "onChangeExtraFeePaymentMethods(" + index + ");" })) Pos
                                    @(Html.Kendo().CheckBoxFor(m => Model.ExtraFees[index].Online).Checked(Model.ExtraFees[index].Online).HtmlAttributes(new { @class = "checkbox-square", onchange = "onChangeExtraFeePaymentMethods(" + index + ");" })) Online
                                </div>
                            </div>
                        }
                        <div class="separator separator-dashed my-5"></div>

                        <h3 class="font-size-lg text-dark font-weight-bold mb-6">@EnumHelper.GetEnumDescription(typeof(ExtraFeeCategoryType), 7.ToString()).ToTitleCase()</h3>
                        foreach (var item in Model.ExtraFees.Where(g => g.CategoryTypeId == 7).Select((value, groupIndex) => new { groupIndex, value }))
                        {
                            var index = Model.ExtraFees.IndexOf(item.value);
                            var typeId = item.value.TypeId;
                            var minimumItem = item.value.MinimumItem ?? 0;
                            var categoryTypeId = 7;

                            @Html.HiddenFor(m => Model.ExtraFees[index].ApplicationExtraFeeId)
                            @Html.HiddenFor(m => Model.ExtraFees[index].ExtraFeeId)
                            @Html.HiddenFor(m => Model.ExtraFees[index].TypeId)
                            @Html.HiddenFor(m => Model.ExtraFees[index].MinimumItem)
                            @Html.HiddenFor(m => Model.ExtraFees[index].CategoryTypeId)
                            @Html.HiddenFor(m => Model.ExtraFees[index].IsPreChecked)
                            @Html.HiddenFor(m => Model.ExtraFees[index].IsPreQuantity)
                            @Html.HiddenFor(m => Model.ExtraFees[index].AgeRange)
                            @Html.HiddenFor(m => Model.ExtraFees[index].ExtraFeeName)
                            @Html.HiddenFor(m => Model.ExtraFees[index].IsForRejectedApplications)
                            @Html.HiddenFor(m => Model.ExtraFees[index].RejectionFeePolicyType)


                            <div class="fee_@categoryTypeId-@index yss_@index form-group row">
                                <div class="col-lg-3 col-md-6">
                                    @(Html.Kendo().CheckBoxFor(m => Model.ExtraFees[index].IsChecked)
                                    .Checked(Model.IsExistingApplication ? item.value.ApplicationExtraFeeId != null && item.value.IsChecked : item.value.IsChecked)
                                    .HtmlAttributes(new { @class = "checkbox-square", onchange = "onChangeExtraFee(" + index + "," + minimumItem + "," + typeId + "," + categoryTypeId + ");" }))
                                    @item.value.ExtraFeeName.ToTitleCase()
                                </div>
                                <div class="col-lg-3 col-md-6">
                                    (
                                    @item.value.Price @EnumHelper.GetEnumDescription(typeof(CurrencyType), item.value.CurrencyId.ToString())
                                    @if (item.value.TypeId == (int)ExtraFeeType.Multiple)
                                    {
                                        <span>/</span>
                                        @SiteResources.Item.ToLower()
                                    }
                                    )
                                    @if (item.value.TypeId == (int)ExtraFeeType.Multiple)
                                    {
                                        <input style="width: 50px;" asp-for="@Model.ExtraFees[index].Quantity" type="number" min="@minimumItem" />
                                        @SiteResources.Item.ToLower()
                                        <span asp-validation-for="@Model.ExtraFees[index].Quantity"></span>
                                    }
                                    else
                                    {
                                        <input hidden style="width: 50px;" asp-for="@Model.ExtraFees[index].Quantity" type="number" value="1" />
                                    }
                                </div>
                                <div class="paymentShowHide_@index col-lg-3 col-md-6">
                                    @(Html.Kendo().CheckBoxFor(m => Model.ExtraFees[index].Cash).Checked(Model.ExtraFees[index].Cash).HtmlAttributes(new { @class = "checkbox-square", onchange = "onChangeExtraFeePaymentMethods(" + index + ");" })) @SiteResources.Cash
                                    @(Html.Kendo().CheckBoxFor(m => Model.ExtraFees[index].Pos).Checked(Model.ExtraFees[index].Pos).HtmlAttributes(new { @class = "checkbox-square", onchange = "onChangeExtraFeePaymentMethods(" + index + ");" })) Pos
                                    @(Html.Kendo().CheckBoxFor(m => Model.ExtraFees[index].Online).Checked(Model.ExtraFees[index].Online).HtmlAttributes(new { @class = "checkbox-square", onchange = "onChangeExtraFeePaymentMethods(" + index + ");" })) Online
                                </div>
                            </div>
                        }
                        <div class="separator separator-dashed my-5"></div>

                        <h3 class="font-size-lg text-dark font-weight-bold mb-6">@EnumHelper.GetEnumDescription(typeof(ExtraFeeCategoryType), 8.ToString()).ToTitleCase()</h3>
                        foreach (var item in Model.ExtraFees.Where(g => g.CategoryTypeId == 8).Select((value, groupIndex) => new { groupIndex, value }))
                        {
                            var index = Model.ExtraFees.IndexOf(item.value);
                            var typeId = item.value.TypeId;
                            var minimumItem = item.value.MinimumItem ?? 0;
                            var categoryTypeId = 8;

                            @Html.HiddenFor(m => Model.ExtraFees[index].ApplicationExtraFeeId)
                            @Html.HiddenFor(m => Model.ExtraFees[index].ExtraFeeId)
                            @Html.HiddenFor(m => Model.ExtraFees[index].TypeId)
                            @Html.HiddenFor(m => Model.ExtraFees[index].MinimumItem)
                            @Html.HiddenFor(m => Model.ExtraFees[index].CategoryTypeId)
                            @Html.HiddenFor(m => Model.ExtraFees[index].IsPreChecked)
                            @Html.HiddenFor(m => Model.ExtraFees[index].IsPreQuantity)
                            @Html.HiddenFor(m => Model.ExtraFees[index].AgeRange)
                            @Html.HiddenFor(m => Model.ExtraFees[index].ExtraFeeName)
                            @Html.HiddenFor(m => Model.ExtraFees[index].IsForRejectedApplications)
                            @Html.HiddenFor(m => Model.ExtraFees[index].RejectionFeePolicyType)


                            <div class="fee_@categoryTypeId-@index ts_@index form-group row">
                                <div class="col-lg-3 col-md-6">
                                    @(Html.Kendo().CheckBoxFor(m => Model.ExtraFees[index].IsChecked)
                                    .Checked(Model.IsExistingApplication ? item.value.ApplicationExtraFeeId != null && item.value.IsChecked : item.value.IsChecked)
                                    .HtmlAttributes(new { @class = "checkbox-square", onchange = "onChangeExtraFee(" + index + "," + minimumItem + "," + typeId + "," + categoryTypeId + ");" }))
                                    @item.value.ExtraFeeName.ToTitleCase()
                                </div>
                                <div class="col-lg-3 col-md-6">
                                    (
                                    @item.value.Price @EnumHelper.GetEnumDescription(typeof(CurrencyType), item.value.CurrencyId.ToString())
                                    @if (item.value.TypeId == (int)ExtraFeeType.Multiple)
                                    {
                                        <span>/</span>
                                        @SiteResources.Item.ToLower()
                                    }
                                    )
                                    @if (item.value.TypeId == (int)ExtraFeeType.Multiple)
                                    {
                                        <input style="width: 50px;" asp-for="@Model.ExtraFees[index].Quantity" type="number" min="@minimumItem" />
                                        @SiteResources.Item.ToLower()
                                        <span asp-validation-for="@Model.ExtraFees[index].Quantity"></span>
                                    }
                                    else
                                    {
                                        <input hidden style="width: 50px;" asp-for="@Model.ExtraFees[index].Quantity" type="number" value="1" />
                                    }
                                </div>
                                <div class="paymentShowHide_@index col-lg-3 col-md-6">
                                    @(Html.Kendo().CheckBoxFor(m => Model.ExtraFees[index].Cash).Checked(Model.ExtraFees[index].Cash).HtmlAttributes(new { @class = "checkbox-square", onchange = "onChangeExtraFeePaymentMethods(" + index + ");" })) @SiteResources.Cash
                                    @(Html.Kendo().CheckBoxFor(m => Model.ExtraFees[index].Pos).Checked(Model.ExtraFees[index].Pos).HtmlAttributes(new { @class = "checkbox-square", onchange = "onChangeExtraFeePaymentMethods(" + index + ");" })) Pos
                                    @(Html.Kendo().CheckBoxFor(m => Model.ExtraFees[index].Online).Checked(Model.ExtraFees[index].Online).HtmlAttributes(new { @class = "checkbox-square", onchange = "onChangeExtraFeePaymentMethods(" + index + ");" })) Online
                                </div>
                            </div>
                        }
                        <div class="separator separator-dashed my-5"></div>

                        @if (Model.ExtraFees.Any(s => s.CategoryTypeId == (int)ExtraFeeCategoryType.RelatedInsurance))
                        {
                            <h3 class="font-size-lg text-dark font-weight-bold mb-6">@EnumHelper.GetEnumDescription(typeof(ExtraFeeCategoryType), 9.ToString()).ToTitleCase()</h3>

                            foreach (var item in Model.ExtraFees.Where(g => g.CategoryTypeId == 9).Select((value, groupIndex) => new { groupIndex, value }))
                            {
                                var index = Model.ExtraFees.IndexOf(item.value);
                                var typeId = item.value.TypeId;
                                var minimumItem = item.value.MinimumItem.HasValue ? item.value.MinimumItem.Value : 0;
                                var categoryTypeId = 9;

                                @Html.HiddenFor(m => Model.ExtraFees[index].ApplicationExtraFeeId)
                                @Html.HiddenFor(m => Model.ExtraFees[index].ExtraFeeId)
                                @Html.HiddenFor(m => Model.ExtraFees[index].TypeId)
                                @Html.HiddenFor(m => Model.ExtraFees[index].MinimumItem)
                                @Html.HiddenFor(m => Model.ExtraFees[index].CategoryTypeId)
                                @Html.HiddenFor(m => Model.ExtraFees[index].IsPreChecked)
                                @Html.HiddenFor(m => Model.ExtraFees[index].IsPreQuantity)
                                @Html.HiddenFor(m => Model.ExtraFees[index].ExtraFeeName)
                                @Html.HiddenFor(m => Model.ExtraFees[index].IsForRejectedApplications)
                                @Html.HiddenFor(m => Model.ExtraFees[index].RejectionFeePolicyType)


                                <div class="fee_@categoryTypeId-@index form-group row related-insurance" data-category="RelatedInsurance">
                                    <div class="col-lg-3 col-md-6">
                                        @(Html.Kendo().CheckBoxFor(m => Model.ExtraFees[index].IsChecked)
                                        .Checked(Model.IsExistingApplication == true ? (item.value.ApplicationExtraFeeId != null && item.value.IsChecked) : item.value.IsChecked)
                                        .HtmlAttributes(new { @class = "checkbox-square", onchange = "onChangeExtraFee(" + index + "," + minimumItem + "," + typeId + "," + categoryTypeId + ");" }))
                                        @item.value.ExtraFeeName.ToTitleCase()
                                    </div>
                                    <div class="col-lg-3 col-md-6">
                                        (
                                        @item.value.Price @EnumHelper.GetEnumDescription(typeof(CurrencyType), item.value.CurrencyId.ToString())
                                        @if (item.value.TypeId == (int)ExtraFeeType.Multiple)
                                        {
                                            <span>/</span>
                                            @SiteResources.Item.ToLower()
                                        }
                                        )
                                        @if (item.value.TypeId == (int)ExtraFeeType.Multiple)
                                        {
                                            <input style="width: 50px;" asp-for="@Model.ExtraFees[index].Quantity" type="number" min="@minimumItem" />
                                            @SiteResources.Item.ToLower()
                                            <span asp-validation-for="@Model.ExtraFees[index].Quantity"></span>
                                        }
                                        else
                                        {
                                            <input hidden style="width: 50px;" asp-for="@Model.ExtraFees[index].Quantity" type="number" value="1" />
                                        }
                                    </div>
                                    <div class="paymentShowHide_@index col-lg-3 col-md-6">
                                        @(Html.Kendo().CheckBoxFor(m => Model.ExtraFees[index].Cash).Checked(Model.ExtraFees[index].Cash).HtmlAttributes(new { @class = "checkbox-square", onchange = "onChangeExtraFeePaymentMethods(" + index + ");" })) @SiteResources.Cash
                                        @(Html.Kendo().CheckBoxFor(m => Model.ExtraFees[index].Pos).Checked(Model.ExtraFees[index].Pos).HtmlAttributes(new { @class = "checkbox-square", onchange = "onChangeExtraFeePaymentMethods(" + index + ");" })) Pos
                                        @(Html.Kendo().CheckBoxFor(m => Model.ExtraFees[index].Online).Checked(Model.ExtraFees[index].Online).HtmlAttributes(new { @class = "checkbox-square", onchange = "onChangeExtraFeePaymentMethods(" + index + ");" })) Online
                                    </div>
                                </div>
                            }
                            <div class="separator separator-dashed my-5"></div>
                        }
                    }
                }
            </div>
        </div>
    </div>
</div>

<script>
    $(function () {
        checkAdditionalServiceTypeExtraFee2();
        checkExtraFeesForNormalApplicant(-1);
        checkVehicleTypeTsExtraFee1();
        checkIraqSpecialSmsExtraFee1();
        checkRelatedInsuranceTypeExtraFee();
        checkMainFeesAndUpdateVisibility();
        checkOptinalSmsVip();
        checkIfBranchCountryShowsPayment();

        $('#ApplicationTogetherId').change(function () {
            checkRelatedInsuranceTypeExtraFee();
        });
    });
    var isExistingApplication = $("#IsExistingApplication").val();
    var typeIdMultiple = '@ExtraFeeType.Multiple.GetHashCode()';

    var extraFeeInputs = $("input[id*='ExtraFees_']");
    var extraFees = [];

    var mainFeeInputs = $("input[class*='main-fee_']");
    var mainFees = [];

    var onlyOneExtraFeeCategoryTypeIds = ['@ExtraFeeCategoryType.Insurance.GetHashCode()', '@ExtraFeeCategoryType.Application.GetHashCode()',
                                          '@ExtraFeeCategoryType.ServiceFee.GetHashCode()', '@ExtraFeeCategoryType.YSS.GetHashCode()',
                                          '@ExtraFeeCategoryType.TS.GetHashCode()', '@ExtraFeeCategoryType.Cargo.GetHashCode()',
                                          '@ExtraFeeCategoryType.RelatedInsurance.GetHashCode()'];

    $(extraFeeInputs).each(function (i) {
        if (extraFeeInputs[i].id.includes("IsChecked")) {
            extraFees.push(extraFeeInputs[i]);
        }
    });

    $(mainFeeInputs).each(function (i) {
        if (mainFeeInputs[i].id.includes("IsChecked")) {
            mainFees.push(mainFeeInputs[i]);
        }
    });

    $(extraFees).each(function (i) {
        var index = extraFees[i].id.split("ExtraFees_")[1].split("_")[0];
        var typeId = $("#ExtraFees_" + index + "__TypeId").val();
        var isChecked = $("#ExtraFees_" + index + "__IsChecked").is(":checked");
        var minimumItem = $("#ExtraFees_" + index + "__MinimumItem").val();
        var categoryTypeId = $("#ExtraFees_" + index + "__CategoryTypeId").val();
        var isAllowedOnlyRejectedApplications = $("#ExtraFees_" + index + "__IsForRejectedApplications").val();
        var rejectionFeePolicyType = $("#ExtraFees_" + index + "__RejectionFeePolicyType").val();

        console.log(isChecked);

        if (typeId == typeIdMultiple) {
            if (isExistingApplication === "false" || isExistingApplication === "False" || isExistingApplication === false) {
                resetQuantityMinimumValue(index, minimumItem);
            }
            else {
                if (!isChecked) {
                    resetQuantityMinimumValue(index, minimumItem);
                }
            }
        }

        if(isAllowedOnlyRejectedApplications === "true" || isAllowedOnlyRejectedApplications === "True" || isAllowedOnlyRejectedApplications === true)
        {
             if (isExistingApplication === "false" || isExistingApplication === "False" || isExistingApplication === false) {
                const className = `fee_${categoryTypeId}-${index}`;
                document.querySelectorAll(`.${className}`).forEach(element => {
                    hideRejectionFee(categoryTypeId,index,rejectionFeePolicyType);
                });
            }
            else {
                if (!isChecked) {
                const className = `fee_${categoryTypeId}-${index}`;
                document.querySelectorAll(`.${className}`).forEach(element => {
                    hideRejectionFee(categoryTypeId,index,rejectionFeePolicyType);
                });
                }
            }
        }

        if (!isChecked || $("#ShowPaymentMethods").val() == "False") {
            $(".paymentShowHide_" + index).hide();
        }
        else if (isChecked && $("#ShowPaymentMethods").val() == "True") {
            $(".paymentShowHide_" + index).show();
            onChangeExtraFeePaymentMethods(index);
        }
    });

    function hideRejectionFee(categoryTypeId,index,policyType)
    {
        const isChecked = $(`#ExtraFees_${index}__IsChecked`).is(":checked");
        const feeSelector = `.fee_${categoryTypeId}-${index}`
        const attributeName = 'data-fee';

        if (isChecked && (isExistingApplication === "true" || isExistingApplication === "True" || isExistingApplication === true)) {
            $(feeSelector).filter(`[${attributeName}="true"]`).each(function() {
                $(this).show().find('input[type="number"]').prop("disabled", false);
                $(this).removeAttr(attributeName);
            });

        } else {
             $(feeSelector).each(function() {
                $(this).attr(attributeName, JSON.stringify({
                    categoryTypeId: categoryTypeId,
                    index: index,
                    policyType :policyType,
                }));
            });

            $(feeSelector).hide().find('input[type="number"]').prop("disabled", true);
            $(feeSelector).hide().find('input[type="checkbox"]').prop("checked", false);
            $(feeSelector).find('div[class^="paymentShowHide_"]').hide();
            $(feeSelector).find('input[type="checkbox"]').prop("disabled", false);

        }
    }

    function onChangeExtraFee(index, minimumItem, typeId, categoryTypeId) {
        if (typeId == typeIdMultiple) {
            resetQuantityMinimumValue(index, minimumItem);
        }

        if (jQuery.inArray(categoryTypeId.toString(), onlyOneExtraFeeCategoryTypeIds) != -1) {
            checkOnlyOneExtraFeeCanBeSelected(index, categoryTypeId, minimumItem);
        }
        checkOnlyOneExtraFeePhotoCanBeSelected(index);
        checkOnlyOneExtraFeeESIMCanBeSelected(index);
        checkExtraFeesForNormalApplicant(index);

        if ($("#ShowPaymentMethods").val() == "True") {
            paymentShowHide(index);
        }
    }

    function resetQuantityMinimumValue(index, minimumItem) {
        var applicationType = $("#ApplicationTypeId").val(); // ApplicationTypeId=10 NonApplicationPhotograph
        var isPhotoBoothIntegrationActive = $("#IsPhotoBoothIntegrationActive").val();
        var extraFeeId = $("#ExtraFees_" + index + "__ExtraFeeId").val();
        var extraFeeName = $("#ExtraFees_" + index + "__ExtraFeeName").val();

        $("#ExtraFees_" + index + "__Quantity").prop("disabled", true);

        if ($("#ExtraFees_" + index + "__IsChecked").is(":checked")) {
            if ((isPhotoBoothIntegrationActive === "true" || isPhotoBoothIntegrationActive === "True" || isPhotoBoothIntegrationActive === true) && applicationType != 10 && (extraFeeName.includes("Fotoğraf") || (extraFeeName.includes("Photo") && !extraFeeName.includes("Photocopy")))) {
                $("#ExtraFees_" + index + "__Quantity").val(1);
                $("#ExtraFees_" + index + "__Quantity").prop("disabled", true);
            }
            else {
                $("#ExtraFees_" + index + "__Quantity").val(0);
                $("#ExtraFees_" + index + "__Quantity").prop("disabled", false);
            }
        }
        else {
            $("#ExtraFees_" + index + "__Quantity").val(minimumItem);
        }
    }

    function checkOnlyOneExtraFeeCanBeSelected(currentIndex, currentCategoryTypeId, minimumItem) {
        var totalSelectedExtraFeeCount = 0;

        $(extraFees).each(function (i) {
            var index = extraFees[i].id.split("ExtraFees_")[1].split("_")[0];
            var categoryTypeId = $("#ExtraFees_" + index + "__CategoryTypeId").val();
            var isChecked = $("#ExtraFees_" + index + "__IsChecked").is(":checked");

            if (categoryTypeId == currentCategoryTypeId && isChecked) {
                totalSelectedExtraFeeCount++;
            }
        });

        if (totalSelectedExtraFeeCount > 1) {
            bootbox.dialog({
                message: '<p style="color:#FFFFFF;"><b>' + jsResources.OnlyOneExtraFeeCanBeSelected + '</b></p>',
                size: 'extra-large',
                onEscape: true,
                backdrop: true
            });
            $('.modal-content').css("background-color", "#FFA800");

            $("#ExtraFees_" + currentIndex + "__IsChecked").prop('checked', false);

            var typeId = $("#ExtraFees_" + currentIndex + "__TypeId").val();

            if (typeId == typeIdMultiple) {
                $("#ExtraFees_" + currentIndex + "__Quantity").val(minimumItem);
                $("#ExtraFees_" + currentIndex + "__Quantity").prop("disabled", true);
            }
        }
    }
    function checkOnlyOneExtraFeePhotoCanBeSelected(currentIndex) {
        var totalSelectedExtraFeePhotoCount = 0;
        var applicationTypeId = $("#ApplicationTypeId").val();
        var isPhotoBoothIntegrationActive = $("#IsPhotoBoothIntegrationActive").val();

        if (applicationTypeId != @ApplicationType.NonApplicationPhotograph.ToInt() && (isPhotoBoothIntegrationActive === "true" || isPhotoBoothIntegrationActive === "True" || isPhotoBoothIntegrationActive === true)) {
            $(extraFees).each(function (i) {
                var index = extraFees[i].id.split("ExtraFees_")[1].split("_")[0];
                var isChecked = $("#ExtraFees_" + index + "__IsChecked").is(":checked");
                var extraFeeName = $("#ExtraFees_" + index + "__ExtraFeeName").val();

                if ((extraFeeName.includes("Fotoğraf") || (extraFeeName.includes("Photo") && !extraFeeName.includes("Photocopy"))) && isChecked) {
                    totalSelectedExtraFeePhotoCount++;
                }
            });

            if (totalSelectedExtraFeePhotoCount > 1) {
                bootbox.dialog({
                    message: '<p style="color:#FFFFFF;"><b>' + jsResources.OnlyOneExtraFeePhotoCanBeSelected + '</b></p>',
                    size: 'extra-large',
                    onEscape: true,
                    backdrop: true
                });
                $('.modal-content').css("background-color", "#FFA800");
                $("#ExtraFees_" + currentIndex + "__IsChecked").prop('checked', false);
            }
        }
    }

    function checkOnlyOneExtraFeeESIMCanBeSelected(currentIndex) {
        var totalSelectedExtraFeeESIMCount = 0;

        $(extraFees).each(function (i) {
            var index = extraFees[i].id.split("ExtraFees_")[1].split("_")[0];
            var isChecked = $("#ExtraFees_" + index + "__IsChecked").is(":checked");
            var extraFeeId = $("#ExtraFees_" + index + "__ExtraFeeId").val();
            if ((extraFeeId == 197 || extraFeeId == 198 || extraFeeId == 199) && isChecked) {
                totalSelectedExtraFeeESIMCount++;
            }
        });

        if (totalSelectedExtraFeeESIMCount > 1) {
            bootbox.dialog({
                message: '<p style="color:#FFFFFF;"><b>' + jsResources.OnlyOneExtraFeeESIMCanBeSelected + '</b></p>',
                size: 'extra-large',
                onEscape: true,
                backdrop: true
            });
            $('.modal-content').css("background-color", "#FFA800");
            $("#ExtraFees_" + currentIndex + "__IsChecked").prop('checked', false);
        }
    }

    function checkAdditionalServiceTypeExtraFee2() {
        var additionalServiceTypeId = $("#AdditionalServiceTypeId").val();
        var applicationTypeId = $("#ApplicationTypeId").val();
        var age = calculateAgeRange1();

        $(extraFees).each(function (i) {
            var index = extraFees[i].id.split("ExtraFees_")[1].split("_")[0];
            var categoryTypeId = $("#ExtraFees_" + index + "__CategoryTypeId").val();
            var ageRange = $("#ExtraFees_" + index + "__AgeRange").val();

            if (applicationTypeId == @ApplicationType.NonApplication.ToInt() && categoryTypeId != @ExtraFeeCategoryType.YSS.ToInt() && additionalServiceTypeId == @AdditionalServiceType.YSS.ToInt()) {
                $(extraFees[i]).prop('disabled', true);
                $(extraFees[i]).prop('checked', false);
            }
            if ((applicationTypeId == @ApplicationType.NonApplication.ToInt() && categoryTypeId == @ExtraFeeCategoryType.YSS.ToInt() && additionalServiceTypeId == @AdditionalServiceType.YSS.ToInt() && ageRange != age) ||
                (applicationTypeId != @ApplicationType.NonApplication.ToInt() && categoryTypeId == @ExtraFeeCategoryType.YSS.ToInt() && additionalServiceTypeId != @AdditionalServiceType.YSS.ToInt())) {
                var name = $(".yss_" + index);
                $(name).hide();
                $(extraFees[i]).prop('checked', false);
            }
            if (applicationTypeId == @ApplicationType.NonApplication.ToInt() && categoryTypeId == @ExtraFeeCategoryType.YSS.ToInt() && additionalServiceTypeId == @AdditionalServiceType.YSS.ToInt() && ageRange == age) {
                var name = $(".yss_" + index);
                $(name).show();
                $(extraFees[i]).prop('disabled', false);
            }
        });
    }

    function checkRelatedInsuranceTypeExtraFee() {
        var applicationTogether = $("#ApplicationTogetherId").val();

        if (applicationTogether == '@YesNoQuestion.No.ToInt()') {
            $(".related-insurance").hide();
            $("input[name*='IsChecked'][data-category='RelatedInsurance']").prop('checked', false);
        } else {
            $(".related-insurance").show();
            $("input[name*='IsChecked'][data-category='RelatedInsurance']").prop('disabled', false);
        }
    }


    function calculateAgeRange1() {
        var birthDate = kendo.parseDate($("#BirthDate").val(), jsResources.DatePickerFormatJs);
        var diff_ms = Date.now() - birthDate.getTime();
        var age_dt = new Date(diff_ms);
        var age = Math.abs(age_dt.getUTCFullYear() - 1970);

        if (age <= 15) {
            return 1;
        }
        else if (age >= 16 && age <= 25) {
            return 2;
        }
        else if (age >= 26 && age <= 35) {
            return 3;
        }
        else if (age >= 36 && age <= 45) {
            return 4;
        }
        else if (age >= 46 && age <= 50) {
            return 5;
        }
        else if (age >= 51 && age <= 55) {
            return 6;
        }
        else if (age >= 56 && age <= 60) {
            return 7;
        }
        else if (age >= 61 && age <= 65) {
            return 8;
        }
        else if (age >= 66 && age < 69) {
            return 9;
        }
        else {
            return 0;
        }
    }

    function checkVehicleTypeTsExtraFee1() {
        var additionalServiceTypeId = $("#AdditionalServiceTypeId").val();
        var applicationTypeId = $("#ApplicationTypeId").val();
        var vehicleTypeId = $("#VehicleTypeId").val();

        $(extraFees).each(function (i) {
            var index = extraFees[i].id.split("ExtraFees_")[1].split("_")[0];
            var categoryTypeId = $("#ExtraFees_" + index + "__CategoryTypeId").val();
            var extraFeeName = $("#ExtraFees_" + index + "__ExtraFeeName").val();


            if (applicationTypeId == @ApplicationType.NonApplication.ToInt() && categoryTypeId != @ExtraFeeCategoryType.TS.ToInt() && additionalServiceTypeId == @AdditionalServiceType.TS.ToInt()) {
                $(extraFees[i]).prop('disabled', true);
                $(extraFees[i]).prop('checked', false);
            }
            if ((applicationTypeId == @ApplicationType.NonApplication.ToInt() && categoryTypeId == @ExtraFeeCategoryType.TS.ToInt() && additionalServiceTypeId == @AdditionalServiceType.TS.ToInt() && !extraFeeName.includes("Hususi") && !extraFeeName.includes("Kamyonet")) ||
                (applicationTypeId != @ApplicationType.NonApplication.ToInt() && categoryTypeId == @ExtraFeeCategoryType.TS.ToInt() && additionalServiceTypeId != @AdditionalServiceType.TS.ToInt())) {
                var name = $(".ts_" + index);
                $(name).hide();
                $(extraFees[i]).prop('checked', false);
            }
            if (applicationTypeId == @ApplicationType.NonApplication.ToInt() && categoryTypeId == @ExtraFeeCategoryType.TS.ToInt() && additionalServiceTypeId == @AdditionalServiceType.TS.ToInt() && (vehicleTypeId == 1 ? extraFeeName.includes("Hususi") : extraFeeName.includes("Kamyonet"))) {
                var name = $(".ts_" + index);
                $(name).show();
                $(extraFees[i]).prop('disabled', false);
            }
        });

    }

    function checkIraqSpecialSmsExtraFee1() {

        var applicantTypeId = $("#ApplicantTypeId").val();
        var branchCountryId = $("#BranchCountryId").val();
        var familyIraqSmsSelected = $("#FamilyIraqSmsSelected").val();
        var familyIraqOpsiyonelSmsSelected = $("#FamilyIraqOpsiyonelSmsSelected").val();

        if (branchCountryId == 80) { //Iraq Branches
            $(extraFees).each(function (i) {
                var index = extraFees[i].id.split("ExtraFees_")[1].split("_")[0];
                var categoryTypeId = $("#ExtraFees_" + index + "__CategoryTypeId").val();
                var extraFeeName = $("#ExtraFees_" + index + "__ExtraFeeName").val();
                var applicationExtraFeeId = $("#ExtraFees_" + index + "__ApplicationExtraFeeId").val();
                var price = $("#ExtraFees_" + index + "__Price").val();

                if (categoryTypeId == @ExtraFeeCategoryType.Other.ToInt()) {

                    if (applicantTypeId == @ApplicantType.Family.ToInt() && (((isExistingApplication === "false" || isExistingApplication === "False" || isExistingApplication === false) && familyIraqSmsSelected == 1) || ((isExistingApplication === "true" || isExistingApplication === "True" || isExistingApplication === true) && familyIraqSmsSelected == 0)) && (extraFeeName == "SMS" || extraFeeName == "SMS VIP") && price == "0,0") {
                        var name = $(".other_" + index);
                        $(name).hide();
                        $(extraFees[i]).prop('checked', false);
                        $(".paymentShowHide_" + index).hide();
                    } else if (applicantTypeId == @ApplicantType.Family.ToInt() && (((isExistingApplication === "false" || isExistingApplication === "False" || isExistingApplication === false) && familyIraqOpsiyonelSmsSelected == 1) || ((isExistingApplication === "true" || isExistingApplication === "True" || isExistingApplication === true) && familyIraqOpsiyonelSmsSelected == 0)) && ((extraFeeName == "Opsiyonel SMS" || extraFeeName == "Optional SMS" && price == "3000") || (extraFeeName == "Opsiyonel SMS VIP" || extraFeeName == "Optional SMS VIP" && price == "0"))) {
                        var name = $(".other_" + index);
                        $(name).hide();
                        $(extraFees[i]).prop('checked', false);
                        $(".paymentShowHide_" + index).hide();
                    } else if (((isExistingApplication === "true" || isExistingApplication === "True" || isExistingApplication === true) && applicationExtraFeeId == "") && ((extraFeeName == "Opsiyonel SMS" || extraFeeName == "Optional SMS") && price == "3000")) {
                        var name = $(".other_" + index);
                        $(extraFees[i]).prop('checked', false);
                    }
                }
            });
        }
    }

    function paymentShowHide(index) {
        $(extraFees).each(function (i) {
            var isChecked = $("#ExtraFees_" + index + "__IsChecked").is(":checked");

            if (isChecked) {
                $(".paymentShowHide_" + index).show();
            } else {
                $(".paymentShowHide_" + index).hide();
                const paymentMethods = ["Cash", "Pos", "Online"];

                paymentMethods.forEach(method => {
                    const selector = `#ExtraFees_${index}__${method}`;
                    $(selector).prop('checked', false).prop('disabled', false);
                });
            }
        });
    }

    function onChangeExtraFeePaymentMethods(index) {
        if ($("#ExtraFees_" + index + "__Cash").is(":checked")) {
            $($("#ExtraFees_" + index + "__Pos")).prop('disabled', true);
            $($("#ExtraFees_" + index + "__Online")).prop('disabled', true);
        }
        else if ($("#ExtraFees_" + index + "__Pos").is(":checked")) {
            $($("#ExtraFees_" + index + "__Cash")).prop('disabled', true);
            $($("#ExtraFees_" + index + "__Online")).prop('disabled', true);
        }
        else if ($("#ExtraFees_" + index + "__Online").is(":checked")) {
            $($("#ExtraFees_" + index + "__Cash")).prop('disabled', true);
            $($("#ExtraFees_" + index + "__Pos")).prop('disabled', true);
        }
        else {
            $($("#ExtraFees_" + index + "__Cash")).prop('disabled', false);
            $($("#ExtraFees_" + index + "__Pos")).prop('disabled', false);
            $($("#ExtraFees_" + index + "__Online")).prop('disabled', false);
        }
    }

    function checkMainFeesAndUpdateVisibility() {

        $(mainFees).each(function () {
            var classList = $(this).attr('class').split(' ');
            var mainFeeClass = classList.find(cls => cls.startsWith('main-fee_'));
            var mainFeeId = mainFeeClass.split('_')[1];
            var index = $(this).attr('id').split("ExtraFees_")[1].split("_")[0];
            var extraFeeNameValue = $("#ExtraFees_" + index + "__ExtraFeeName").val();
            toggleSubFees(index, index, mainFeeId);
        });
    }

    function toggleSubFees(index, lastExtraFeeIndex, mainFeeId) {
        const isChecked = $(`#ExtraFees_${index}__IsChecked`).is(":checked");
        const subFeesSelector = `#sub-fees_${lastExtraFeeIndex}-${mainFeeId}`;
        const cargoSubFeeSelector = `.cargo-sub-fee_${mainFeeId}`;

        if (isChecked) {
            $(subFeesSelector).show();
            $(cargoSubFeeSelector).show().prop("disabled", false);
            if ($("#ShowPaymentMethods").val() == "True") {
                paymentShowHide(index);
            }
        } else {
            $(subFeesSelector).hide().find('input[type="number"]').prop("disabled", true);
            $(subFeesSelector).hide().find('input[type="checkbox"]').prop("checked", false);
            $(subFeesSelector).find('input[type="checkbox"]').prop("disabled", false);
            $(cargoSubFeeSelector).hide().prop("disabled", true).find('input[type="checkbox"]').prop("checked", false);
            $(cargoSubFeeSelector).find('input[type="number"]').prop("disabled", true);
            $(subFeesSelector).find('div[class^="paymentShowHide_"]').hide();
            $(cargoSubFeeSelector).find('div[class^="paymentShowHide_"]').hide();
            $(cargoSubFeeSelector).find('input[type="checkbox"]').prop("disabled", false);

            if ($("#ShowPaymentMethods").val() == "True") {
                paymentShowHide(index);
            }
        }
        checkExtraFeesForNormalApplicant(index);
        checkIraqSpecialSmsExtraFee1();
    }

    function checkOptinalSmsVip() {
        const encryptedTokenId = $("#EncryptedTokenId").val();

        const isApplicationInvalid = (isExistingApplication === "false" || isExistingApplication === "False" || isExistingApplication === false);
        const isTokenValid = (encryptedTokenId.length > 0 && encryptedTokenId != null && encryptedTokenId != undefined);

        let mainFeeId;

        if (isApplicationInvalid && isTokenValid) {
            let isVipChecked = false;

            // Check if VIP is checked
            $(extraFees).each(function (i) {
                const index = extraFees[i].id.split("ExtraFees_")[1].split("_")[0];
                const extraFeeName = $(`#ExtraFees_${index}__ExtraFeeName`).val();
                const isChecked = $(`#ExtraFees_${index}__IsChecked`).is(":checked");

                if (extraFeeName === "VIP") {
                    const mainFeeClass = $(`#ExtraFees_${index}__IsChecked`).attr('class');
                    const mainFeeIdMatch = mainFeeClass.match(/main-id_(\d+)/);
                    mainFeeId = mainFeeIdMatch ? mainFeeIdMatch[1] : null;
                    isVipChecked = isChecked;
                }
            });

            if (isVipChecked) {
                const cargoSubFeeSelector = `.cargo-sub-fee_${mainFeeId}`;
                $(cargoSubFeeSelector).show().prop("disabled", false);
            }
        }
    }

    function checkIfBranchCountryShowsPayment() {

        if ($("#ShowPaymentMethods").val() == "False") {

            $(extraFees).each(function (i) {
                const index = extraFees[i].id.split("ExtraFees_")[1].split("_")[0];
                $(".paymentShowHide_" + index).hide();
            });
        }
    }


</script>