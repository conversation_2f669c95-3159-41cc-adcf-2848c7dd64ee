﻿using Gateway.Notification.Application.Entities;
using Gateway.Notification.Application.Handlers.Dto;
using Gateway.Notification.Application.Repository;
using Gateway.Notification.Application.Sms;
using System;
using Gateway.Notification.Application.Factories;
using System.Threading.Tasks;
using Microsoft.Extensions.Logging;

namespace Gateway.Notification.Application.Handlers
{
    public class SmsCollectorHandler : ISmsCollectorHandler
    {
        private readonly ISmsService _smsService;
        private readonly ISmsQueueRepository _smsQueueRepository;
        private readonly ILogger<SmsCollectorHandler> _logger;


        #region ctor

        public SmsCollectorHandler(ISmsService smsService, ISmsQueueRepository smsQueueRepository, ILogger<SmsCollectorHandler> logger)
        {
            _smsService = smsService;
            _smsQueueRepository = smsQueueRepository;
            _logger = logger;
        }

        #endregion

        #region Public Methods

        public async Task CollectSms(SmsCollectorRequest request)
        {
            if (CheckAdditionalSmsRules(request))
            {
                try
                {
                    var smsQueue = new SmsQueue
                    {
                        Sender = request.SmsModel.Originator,
                        Receiver = request.SmsModel.Msisdn,
                        MsgTxt = request.SmsModel.Message,
                        SmsType = request.SmsModel.SmsType,
                        ExternalTransactionId = request.SmsModel.TransactionId,
                        ProviderId = request.SmsModel.ProviderId,
                        SendDate = request.SmsModel.StartDate,
                        RetryCount = 1,
                        IsDeliveryReport = false
                    };

                    smsQueue.Id = await _smsQueueRepository.SaveSmsQueue(smsQueue);

                    var sendSmsResponse = await _smsService.SendSms(request);

                    smsQueue.IsSendSuccessful = sendSmsResponse.IsSuccess;
                    smsQueue.ProviderTransactionId = sendSmsResponse.ProcessId;

                    if (sendSmsResponse.NumberOfParts != null)
                        smsQueue.NumberOfParts = (byte)sendSmsResponse.NumberOfParts;

                    if (!sendSmsResponse.IsSuccess)
                    {
                        smsQueue.FailedStatus = (byte)sendSmsResponse.Status;
                    }

                    _smsQueueRepository.UpdateSmsQueue(smsQueue);
                }
                catch (Exception ex)
                {
                    _logger.LogError(ex, $"CollectSms failed. {ex.Message} {ex.StackTrace}");

                    throw;
                }
            }
            else
            {
                _logger.LogWarning("CheckAdditionalSmsRules failed");
            }
        }

        private static bool CheckAdditionalSmsRules(SmsCollectorRequest request)
        {
            if (request.SmsModel.ProviderId == (int)SmsProviderType.Horisen) // horisen ise gönderilmesin şeklinde acil ister
                return false;
            
            else if (request.SmsModel.Msisdn.StartsWith("993") && request.SmsModel.SmsType is 3 or 4) // türkmenistan randevu sms i gitmesin acil ister
                return false;

            else if (request.SmsModel.Msisdn.StartsWith("971") && 
                (request.SmsModel.Message.Contains("185ENUAE") || request.SmsModel.Message.Contains("has been rejected"))) // uae başvuru ret onay kurgusu sms i kapatıldı acil ister
                return false;

            return true;
        }

        #endregion
    }
}


