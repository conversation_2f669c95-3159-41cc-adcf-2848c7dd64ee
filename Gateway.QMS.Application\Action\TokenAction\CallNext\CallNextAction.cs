﻿using Gateway.Core.Event;
using Gateway.Extensions;
using Gateway.QMS.Application.Action.Dto;
using Gateway.QMS.Application.Action.Event;
using Gateway.QMS.Application.Action.EventHandler;
using Gateway.QMS.Entity.Entities.Tokens;
using Gateway.QMS.Persistence;
using Gateway.QMS.Resources;
using Gateway.Validation;
using Microsoft.EntityFrameworkCore;
using Serilog;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;
using static Gateway.QMS.Application.Enums.Enums;

namespace Gateway.QMS.Application.Action.TokenAction.CallNext
{
    public class CallNextAction : BaseCallNextAction<ActionCallNextRequest>, IAction<ActionCallNextRequest, ActionCallNextResult>
    {
        private int _lineDepartmentId;
        private RedisLineActiveToken _activeToken;
        private List<ActionCallNextResult.Applicant> _applicants;
        private RedisUserToken _userToken;
        private List<int> _applicantIds;
        private Dictionary<Priority, int> _orderPriorityList = new Dictionary<Priority, int>()
        {
            { Priority.Normal, 1 }, 
            { Priority.Vip, 2 },
            { Priority.Priority, 3 },
            { Priority.Whitelist, 4 },
        };

        protected static readonly ILogger Logger = Log.ForContext<CallNextAction>();

        public CallNextAction(QMSDbContext dbContext, IValidationService validationService, IEventService eventService, IRedisRepository redisRepository) : base(dbContext, validationService, eventService, redisRepository) { }

        public async Task<ActionCallNextResult> DoAction(ActionCallNextRequest request)
        {
            await using var transaction = await DbContext.Database.BeginTransactionAsync();

            Logger.Information($"Request => BranchId:{request.Context.Identity.BranchId}, LineId:{request.LineId}, CounterId:{request.CounterId}, UserId:{request.Context.Identity.UserId}, TraceId:{request.Context.TraceId}, RequestId:{request.Context.RequestId}, Class:{GetType().Name}");

            try
            {
                ActionCallNextRequest = request;

                var validationResult = Validate(request);
                if (!validationResult.IsValid)
                    return new ActionCallNextResult
                    {
                        Status = CallNextStatus.InvalidInput,
                        Message = ServiceResources.INVALID_INPUT_ERROR,
                        ValidationMessages = validationResult.ErrorMessages
                    };

                await InitializeLineDepartment();

                var key = RedisKeysFactory.GetKey(RedisKeyType.CallNextLockKey,
                new RedisActionEvent
                {
                    BranchId = ActionCallNextRequest.BranchId,
                    LineId = ActionCallNextRequest.LineId,
                    LineDepartmentId = _lineDepartmentId,
                });

                await using var redLock = await RedisRepository.LockInstance(key);
                if (!redLock.IsAcquired)
                    return new ActionCallNextResult
                    {
                        Status = CallNextStatus.OngoingProcess,
                        Message = ServiceResources.OngoingProcess + " / " + ServiceResources.PleaseTryAgain
                    };

                await Initialize();

                var actionValidateResult = await ValidateAction();
                if (actionValidateResult.Status != CallNextStatus.Successful)
                    return new ActionCallNextResult
                    {
                        Status = actionValidateResult.Status,
                        Message = actionValidateResult.Message
                    };

                var tokenNumber = new StringBuilder();

                if (ActiveDepartment.IsSameProcessCounter)
                {
                    var applicationsResult = GetApplications();

                    if (applicationsResult.Status != CallNextStatus.Successful)
                    {
                        if (applicationsResult.Status != CallNextStatus.NotFound)
                            return new ActionCallNextResult
                            {
                                Status = applicationsResult.Status,
                                Message = applicationsResult.Message
                            };

                        var redisTransaction = RedisRepository.BeginTransaction();
                        await RedisRepository.UpdateActiveToken(redisTransaction, _activeToken, request.BranchId, request.LineId);
                        await RedisRepository.ExecuteTransactionAsync(redisTransaction);

                        Logger.Error($"APPOINTMENT_NOT_FOUND => BranchId:{ActionCallNextRequest.BranchId}, LineId:{ActionCallNextRequest.LineId}, UserId:{ActionCallNextRequest.Context.Identity.UserId}, LineDepartmentId:{_lineDepartmentId}, Token:{_activeToken?.Token}, TokenId:{_activeToken?.Id}, Counter:{CounterName}, Class:{GetType().Name}");

                        return new ActionCallNextResult
                        {
                            Status = applicationsResult.Status,
                            Message = applicationsResult.Message
                        };
                    }

                    await GetGeneratedTokens(_applicantIds);
                }

                else
                {
                    var applicationResult = GetApplication();
                    if (applicationResult.Status != CallNextStatus.Successful)
                    {
                        if (applicationResult.Status != CallNextStatus.NotFound)
                            return new ActionCallNextResult
                            {
                                Status = applicationResult.Status,
                                Message = applicationResult.Message
                            };

                        var redisTransaction = RedisRepository.BeginTransaction();
                        await RedisRepository.UpdateActiveToken(redisTransaction, _activeToken, request.BranchId, request.LineId);
                        await RedisRepository.ExecuteTransactionAsync(redisTransaction);

                        Logger.Error($"APPOINTMENT_NOT_FOUND => BranchId:{ActionCallNextRequest.BranchId}, LineId:{ActionCallNextRequest.LineId}, UserId:{ActionCallNextRequest.Context.Identity.UserId}, LineDepartmentId:{_lineDepartmentId}, Token:{_activeToken?.Token}, TokenId:{_activeToken?.Id}, Counter:{CounterName}, Class:{GetType().Name}");

                        return new ActionCallNextResult
                        {
                            Status = applicationResult.Status,
                            Message = applicationResult.Message
                        };
                    }

                    await GetGeneratedTokens(_applicantIds);
                    tokenNumber.Append(GeneratedTokens?.FirstOrDefault()?.Token);
                }

                CacheToken.ActiveDepartment = new RedisTokenActiveDepartment
                {
                    CounterId = request.CounterId,
                    LineDepartmentId = ActiveDepartment.LineDepartmentId,
                    UserId = request.UserId,
                    IsLineDepartmentCreateApplication = ActiveDepartment.IsLineDepartmentCreateApplication,
                    IsSameProcessCounter = ActiveDepartment.IsSameProcessCounter,
                    Interval = ActiveDepartment.Interval,
                    CalledAt = DateTime.UtcNow,
                    SearchByPassportNumber = ActiveDepartment.SearchByPassportNumber
                };

                var generatedTokenHistories = new List<GeneratedTokenHistory>();

                foreach (var applicant in _applicants)
                {
                    var generatedToken = GeneratedTokens?.Find(p => p.ApplicantId == applicant.Id);
                    if (generatedToken is null)
                    {
                        Logger.Information($"Request => applicant : {applicant.Id} BranchId:{request.Context.Identity.BranchId}, LineId:{request.LineId}, CounterId:{request.CounterId}, UserId:{request.Context.Identity.UserId}, TraceId:{request.Context.TraceId}, RequestId:{request.Context.RequestId}, Class:{GetType().Name} Applicant not found from generated token");

                        generatedToken = await CheckSelectedApplicantExist(applicant.Id);

                        if(generatedToken is null)
                            continue;
                    }

                    if (Department.TokenDepartments.FirstOrDefault()?.DepartmentId == ActiveDepartment.DepartmentId)
                    {
                        generatedToken.IsCalled = true;
                        DbContext.GeneratedToken.Update(generatedToken);
                    }

                    var generatedTokenHistory = new GeneratedTokenHistory
                    {
                        AppointmentId = Convert.ToInt32(CacheToken.AppointmentId),
                        ApplicantId = applicant.Id,
                        GeneratedTokenId = generatedToken.Id,
                        OwnerToken = generatedToken.OwnerToken,
                        Token = generatedToken.Token,
                        DepartmentId = ActiveDepartment.DepartmentId,
                        BranchId = request.BranchId,
                        LineId = request.LineId,
                        CounterId = request.CounterId,
                        Status = (byte)TokenStatus.InProgress,
                        AgentId = request.UserId,
                        CreatedBy = request.UserId,
                        CreatedAt = DateTime.UtcNow
                    };
                    generatedTokenHistories.Add(generatedTokenHistory);
                }

                await DbContext.GeneratedTokenHistory.AddRangeAsync(generatedTokenHistories);

                await DbContext.SaveChangesAsync();

                var hubConsumer = await EventService.RaiseWithResult<CallNextActionChangedEvent, HubConsumer>(new CallNextActionChangedEvent
                {
                    AppointmentId = Convert.ToInt32(CacheToken.AppointmentId),
                    Context = ActionCallNextRequest.Context,
                    BranchId = ActionCallNextRequest.BranchId,
                    UserId = ActionCallNextRequest.UserId,
                    ActiveTokenId = _activeToken.Id,
                    LineId = ActionCallNextRequest.LineId,
                    CounterId = ActionCallNextRequest.CounterId,
                    CounterName = CounterName,
                    LineDepartmentId = ActiveDepartment.LineDepartmentId,
                    Priority = _activeToken.Priority,
                    NumberOfApplicants = _applicants.Count,
                    ActiveToken = _activeToken,
                    CacheToken = CacheToken,
                    RedisTokenDepartment = Department,
 
                    QmsBranchApplicationCountryId = CacheToken.QmsBranchApplicationCountryId,

                    TokenNumber = ActiveDepartment.IsSameProcessCounter
                        ? CacheToken.OwnerToken
                        : tokenNumber.ToString(),
                    OwnerToken = CacheToken.OwnerToken,

                    IsSameProcessCounter = ActiveDepartment.IsSameProcessCounter,
                    Interval = ActiveDepartment.Interval.GetValueOrDefault(),
                    IsLineDepartmentCreateApplication = ActiveDepartment.IsLineDepartmentCreateApplication
                });

                await DefineConnectedLineOrdersAsync(hubConsumer);

                await transaction.CommitAsync();

                return new ActionCallNextResult
                {
                    Status = CallNextStatus.Successful,
                    Message = ServiceResources.RESOURCE_CREATED,
                    QmsBranchApplicationCountryId = CacheToken.QmsBranchApplicationCountryId,
                    ActiveTokenId = _activeToken.Id,
                    AppointmentId = Convert.ToInt32(CacheToken.AppointmentId),
                    Priority = _activeToken.Priority,
                    TokenNumber = ActiveDepartment.IsSameProcessCounter
                        ? CacheToken.OwnerToken
                        : tokenNumber.ToString(),
                    OwnerToken = CacheToken.OwnerToken,
                    IsLineDepartmentCreateApplication = ActiveDepartment.IsLineDepartmentCreateApplication,
                    IsSameProcessCounter = ActiveDepartment.IsSameProcessCounter,
                    SearchByPassportNumber = ActiveDepartment.SearchByPassportNumber,
                    Interval = ActiveDepartment.Interval,
                    Applicants = _applicants,
                    Notes = CacheToken.Notes,
                    LineDepartmentId = ActiveDepartment.LineDepartmentId,
                    HubConsumer = hubConsumer
                };
            }
            catch (Exception ex)
            {
                Logger.Error($"BranchId:{ActionCallNextRequest.BranchId}, LineId:{ActionCallNextRequest.LineId}, UserId:{ActionCallNextRequest.Context.Identity.UserId}, LineDepartmentId:{_lineDepartmentId}, Token:{_activeToken?.Token}, TokenId:{_activeToken?.Id}, Class:{GetType().Name}, Exception: {ex.Message}");

                await transaction.RollbackAsync();

                return new ActionCallNextResult
                {
                    Status = CallNextStatus.InternalServerError,
                    Message = ServiceResources.INTERNAL_SERVER_ERROR
                };
            }
        }     

        private async Task InitializeLineDepartment()
        {
            _lineDepartmentId = await DbContext.Counter.Where(p => p.Id == ActionCallNextRequest.CounterId)
                .Select(q => q.LineDepartmentId).FirstOrDefaultAsync();
        }

        private async Task Initialize()
        {
            _userToken = await RedisRepository.GetUserCache(ActionCallNextRequest.BranchId, ActionCallNextRequest.Context.Identity.UserId);          
            var activeTokens = await RedisRepository.GetRedisActiveTokens(ActionCallNextRequest.BranchId, ActionCallNextRequest.LineId);

            if (!ActionCallNextRequest.IsAuthoritativeForWhiteList)
                activeTokens = activeTokens?.Where(p => !p.Priority.Equals((byte)Priority.Whitelist)).ToList();

            _activeToken = activeTokens?.OrderByDescending(r => _orderPriorityList[(Priority)r.Priority])
                    .FirstOrDefault(p => p.IsCallable == true.ToByte() && p.LineDepartmentId == _lineDepartmentId);

            CacheToken = await RedisRepository.GetTokenCache(ActionCallNextRequest.BranchId, ActionCallNextRequest.LineId, _activeToken?.Token);

            Department = await RedisRepository.GetDepartments(ActionCallNextRequest.BranchId, ActionCallNextRequest.LineId, _activeToken?.Token, _activeToken?.Id ?? Guid.Empty);

            ActiveDepartment = Department?.TokenDepartments?.Find(p => p.IsActive);

            CounterName = await GetCounterName();
        }

        private async Task<ActionCallNextResult> ValidateAction()
        {
            if (_userToken?.State == (byte)TokenStatus.InProgress)
            {
                Logger.Warning($"User busy state. BranchId:{ActionCallNextRequest.BranchId}, LineId:{ActionCallNextRequest.LineId}, UserId:{ActionCallNextRequest.Context.Identity.UserId}, LineDepartmentId:{_lineDepartmentId}, Token:{_activeToken?.Token}, TokenId:{_activeToken?.Id}, Class:{GetType().Name}");

                await RedisRepository.DeleteUserCache(ActionCallNextRequest.BranchId, ActionCallNextRequest.Context.Identity.UserId);

                return new ActionCallNextResult
                {
                    Status = CallNextStatus.ResourceExists,
                    Message = ServiceResources.USER_STATE_REFRESH_MESSAGE
                };
            }

            if (_activeToken is null)
                return new ActionCallNextResult
                {
                    Status = CallNextStatus.NotFound,
                    Message = ServiceResources.APPOINTMENT_NOT_FOUND
                };

            if (CacheToken is null)
                throw new Exception("cached token not found");

            if (ActiveDepartment is null)
                throw new Exception("active department not found");

            return new ActionCallNextResult
            {
                Status = CallNextStatus.Successful,
                Message = ServiceResources.SUCCESS
            };
        }

        private ActionCallNextResult GetApplications()
        {
            _applicants = new List<ActionCallNextResult.Applicant>();

            var selectableApplicants = ActiveDepartment.Applicants
                    .Where(r => r.State == (byte)TokenStatus.Created)
                .Select(p => p.ApplicantId)
                .ToList();

            var applicants = CacheToken.Applicants
                    .Where(r => selectableApplicants.Contains(r.Id))
                .ToList();
            _applicantIds = applicants.Select(q => q.Id).ToList();

            foreach (var applicant in applicants)
            {
                _applicants.Add(new ActionCallNextResult.Applicant
                {
                    Id = applicant.Id,
                    GeneratedTokenId = applicant.GeneratedTokenId,
                    NameSurname = applicant.NameSurname,
                    PassportNumber = applicant.PassportNumber,
                    ApplicationTime = applicant.ApplicationTime,
                    IsIndividual = applicant.IsIndividual,
                    IsFamily = applicant.IsFamily,
                    IsGroup = applicant.IsGroup,
                    IsAddedToAnotherToken = applicant.IsAddedToAnotherToken,
                    IsApplicationCompleted = applicant.IsApplicationCompleted,
                    ApplicationCompletedCounterId = applicant.ApplicationCompletedCounterId,
                    ApplicationCompletedLineDepartmentId = applicant.ApplicationCompletedLineDepartmentId,
                    InsuranceTypeIds = applicant.InsuranceTypeIds,
                    IsBlackListApplicant = applicant.IsBlackListApplicant,
                    IsWhiteListApplicant = applicant.IsWhiteListApplicant,
                    HasNotCompletedReason = applicant.HasNotCompletedReason,
                    BlackListModel = applicant.BlackListModel != null ? new BlackListModel
                    {
                        BlackListNote = applicant.BlackListModel.BlackListNote,
                        BlackListNoteCreatedBy = applicant.BlackListModel.BlackListNoteCreatedBy,
                        BlackListNoteUpdatedBy = applicant.BlackListModel.BlackListNoteUpdatedBy
                    } : null,
                    WhiteListInformation = applicant.WhitelistModel != null ? new RedisWhiteListInformation
                    {
                        Id = applicant.WhitelistModel.Id,
                        BiometricData = applicant.WhitelistModel.BiometricData,
                        DocumentExemption = applicant.WhitelistModel.DocumentExemption,
                        MissionNotes = applicant.WhitelistModel.MissionNotes,
                        RelevantInstitutionPerson = applicant.WhitelistModel.RelevantInstitutionPerson
                    } : null,
                    NotCompletedReasons = applicant.NotCompletedReasons
                });
            }

            if (_applicants.Count == 0)
            {
                _activeToken.IsCallable = false.ToByte();

                return new ActionCallNextResult
                {
                    Status = CallNextStatus.NotFound,
                    Message = "Token:" + _activeToken?.Token + " " + ServiceResources.APPOINTMENT_NOT_FOUND
                };
            }

            _activeToken.IsCalled = true.ToByte();
            _activeToken.IsCallable = false.ToByte();
            var activeApplicants =
                ActiveDepartment.Applicants.Where(a => selectableApplicants.Contains(a.ApplicantId)).ToList();
            activeApplicants.ForEach(p => p.State = (int)TokenStatus.InProgress);

            return new ActionCallNextResult
            {
                Status = CallNextStatus.Successful,
                Message = ServiceResources.SUCCESS
            };
        }

        private ActionCallNextResult GetApplication()
        {
            var callableApplicant = ActiveDepartment.Applicants.Find(q => q.State == (byte)TokenStatus.Created);
            var applicant = CacheToken.Applicants?.Find(p => p.Id == callableApplicant?.ApplicantId);
            if (applicant is null)
            {
                _activeToken.IsCallable = false.ToByte();

                return new ActionCallNextResult
                {
                    Status = CallNextStatus.NotFound,
                    Message = "Token:" + _activeToken?.Token + " " + ServiceResources.APPOINTMENT_NOT_FOUND
                };
            }

            _applicantIds = new List<int> { callableApplicant!.ApplicantId };

            callableApplicant!.State = (int)TokenStatus.InProgress;
            _applicants = new List<ActionCallNextResult.Applicant>
            {
                new()
                {
                    Id = applicant.Id,
                    NameSurname = applicant.NameSurname,
                    PassportNumber = applicant.PassportNumber,
                    ApplicationTime = applicant.ApplicationTime,
                    IsIndividual = applicant.IsIndividual,
                    IsFamily = applicant.IsFamily,
                    IsGroup = applicant.IsGroup,
                    IsAddedToAnotherToken = applicant.IsAddedToAnotherToken,
                    ApplicationCompletedCounterId = applicant.ApplicationCompletedCounterId,
                    ApplicationCompletedLineDepartmentId = applicant.ApplicationCompletedLineDepartmentId,
                    IsBlackListApplicant = applicant.IsBlackListApplicant,
                    IsWhiteListApplicant = applicant.IsWhiteListApplicant,
                    HasNotCompletedReason = applicant.HasNotCompletedReason,
                    BlackListModel = applicant.BlackListModel != null ? new BlackListModel
                    {
                        BlackListNote = applicant.BlackListModel.BlackListNote,
                        BlackListNoteCreatedBy = applicant.BlackListModel.BlackListNoteCreatedBy,
                        BlackListNoteUpdatedBy = applicant.BlackListModel.BlackListNoteUpdatedBy
                    } : null,
                    WhiteListInformation = applicant.WhitelistModel != null ? new RedisWhiteListInformation
                    {
                        Id = applicant.WhitelistModel.Id,
                        BiometricData = applicant.WhitelistModel.BiometricData,
                        DocumentExemption = applicant.WhitelistModel.DocumentExemption,
                        MissionNotes = applicant.WhitelistModel.MissionNotes,
                        RelevantInstitutionPerson = applicant.WhitelistModel.RelevantInstitutionPerson
                    } : null,
                    NotCompletedReasons = applicant.NotCompletedReasons
                }
            };
            _activeToken.IsCalled = true.ToByte();

            var applicantCount = ActiveDepartment.Applicants.Count(r => r.State == (byte)TokenStatus.Created);

            if (applicantCount == 0)
                _activeToken.IsCallable = false.ToByte();

            return new ActionCallNextResult
            {
                Status = CallNextStatus.Successful,
                Message = ServiceResources.SUCCESS
            };
        }

        private async Task GetGeneratedTokens(List<int> applicantIds)
        {
            GeneratedTokens = await DbContext.GeneratedToken
                    .Where(p => p.LineId == ActionCallNextRequest.LineId && !p.IsCompleted && p.OwnerToken == _activeToken.Token &&
                        applicantIds.Contains(p.ApplicantId) && p.CreatedAt.Date == DateTime.UtcNow.Date)
                .ToListAsync();
        }

        private async Task<GeneratedToken> CheckSelectedApplicantExist(int applicantId)
        {
            return await DbContext.GeneratedToken
                .Where(p => p.LineId == ActionCallNextRequest.LineId && !p.IsCompleted && p.ApplicantId == applicantId && p.CreatedAt.Date == DateTime.UtcNow.Date).OrderByDescending(s => s.Id)
                .FirstOrDefaultAsync();
        }

        private async Task DefineConnectedLineOrdersAsync(HubConsumer consumer)
        {
			var lineDepartmentConnections = await DbContext.LineDepartmentConnection
				.Where(r => !r.IsDeleted && r.ConnectedLineDepartmentId == consumer.Screen.LineDepartmentId).OrderBy(o => o.ConnectionOrder)
				.ToListAsync();

			consumer.Screen.LineDepartmentConnectionModels = lineDepartmentConnections.Select(r =>
				new LineDepartmentConnectionModel
				{
					BaseLineDepartmentId = r.LineDepartmentId,
					ConnectedLineDepartmentId = r.ConnectedLineDepartmentId,
					ConnectedLineId = r.ConnectedLineId,
					MethodName = $"ConnectedLine-{r.ConnectionOrder}"
				}).ToList();

			if (consumer.NextDepartmentLineDepartmentId != 0)
			{
				var nextLineDepartmentConnections = await DbContext.LineDepartmentConnection
					.Where(r => !r.IsDeleted && r.ConnectedLineDepartmentId == consumer.NextDepartmentLineDepartmentId).OrderBy(o => o.ConnectionOrder)
					.ToListAsync();

				consumer.Screen.NextLineDepartmentConnectionModels = nextLineDepartmentConnections.Select(r =>
					new LineDepartmentConnectionModel
					{
						BaseLineDepartmentId = r.LineDepartmentId,
						ConnectedLineDepartmentId = r.ConnectedLineDepartmentId,
						ConnectedLineId = r.ConnectedLineId,
						MethodName = $"ConnectedLine-{r.ConnectionOrder}"
					}).ToList();
			}
		}
    }
}
