{
  "AppSettings": {
    "PortalGatewayApiUrl": "https://applb.gateway.com.tr", // API LB
    "PortalGatewayApiKey": "Gateway.ApiKey.2021",
    "AppointmentReturnUrl": "http://{Domain_URL}/User/Login",
    "GoogleCloudProjectId": "skilful-firefly-315409",
    "GoogleCloudCredentialsUrl": "Credentials/GoogleCloudCredentials.json",
    "Notification": true,
    "QmsChatHubAddress": "https://qmslbpub.gateway.com.tr/chathub",
    "PrintDataChatHubAddress": "https://printdatalbpub.gateway.com.tr/digitalsignaturehub",
    "IcrReturnUrl": "https://gatewayinternational.com.tr",
    "PortalGatewayUiUrl": "https://turkeyvisa.gateway.com.tr",
    "EnableQuarterCategoryStatsGraph": true,
    "EnableRejectedApplicationInsuranceCase": true,
    "MinioConfiguration": {
      "EndPoint": "visacdn.gateway.com.tr:443",
      "AccessKey": "eGBBZ2ZVzNWCFW5kvmKr",
      "SecretKey": "tnczwQAiKkNpEHIGwXwztZtFZCaT1i2JziBUZXY3",
      "BucketPrefix": ""
    },
    "QMS": {
      "BaseApiUrl": "https://qmslbpri.gateway.com.tr"
    },
    "Biometrics": {
      "BaseApiUrl": "http://***********:7897"
    },
    "Cargo": {
      "BaseApiUrl": "http://***********:6006"
    },
    "PrintData": {
      "BaseApiUrl": "https://printdatalbpri.gateway.com.tr"
    }
  },
  "elasticsearch": {
    "logindex": "slog-portal",
    "index": "slog-portal",
    "username": "elastic",
    "password": "htJsew49I8jPiL6How7U",
    "url": "https://visaelastic.gateway.com.tr:9200"
  },
  "Logging": {
    "LogLevel": {
      "Default": "Information",
      "Microsoft": "Warning",
      "Microsoft.Hosting.Lifetime": "Information"
    }
  },
  "CacheSettings": {
    "ServiceCallInterval": 1, // minute(s)
    "CacheExpiryTime": 1, // day(s)
    "CacheItems": [
      {
        "CacheKey": "CountryCache",
        "CacheRefreshInterval": 1 // minute(s)
      },
      {
        "CacheKey": "RoleActionCache",
        "CacheRefreshInterval": 1 // minute(s)
      },
      {
        "CacheKey": "BranchCache",
        "CacheRefreshInterval": 1 // minute(s)
      },
      {
        "CacheKey": "ApplicationStatusOrderCache",
        "CacheRefreshInterval": 1 // minute(s)
      },
      {
        "CacheKey": "ForeignCityCache",
        "CacheRefreshInterval": 1 // minute(s)
      },
      {
        "CacheKey": "BranchApplicationStatusCache",
        "CacheRefreshInterval": 1 // minute(s)
      },
      {
        "CacheKey": "ActiveCountriesCache",
        "CacheRefreshInterval": 720 // minute(s)
      },
      {
        "CacheKey": "DepartmentsCache",
        "CacheRefreshInterval": 240 // minute(s)
      },
      {
        "CacheKey": "VisaCategoryCache",
        "CacheRefreshInterval": 30 // minute(s)
      }
    ]
  },
  "Redis": {
    "Url": "visaredis.gateway.com.tr",
    "Port": "6379",
    "ConnectTimeout": 10000,
    "DefaultDatabase": 0
  },
  "RabbitMq": {
    "Host": "visarabbitmq.gateway.com.tr",
    "User": "visa_user",
    "Password": "5Ly42iy4ZHaAtz",
    "Port": 5672,
    "Exchange": "log.exchange",
    "LoggerQueue": "LoggerQueue",
    "EmailQueue": "EmailQueue",
    "SMSQueue": "SMSQueue"
  },
  "Session": {
    "Distributed": true,
    "IdleTimeout": 1,
    "Authentication": "ldap"
  }
}
