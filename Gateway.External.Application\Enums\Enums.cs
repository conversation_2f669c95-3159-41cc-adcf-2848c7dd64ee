﻿using Gateway.External.Application.Attributes;
using Gateway.External.Resources;
using System.ComponentModel;

namespace Gateway.External.Application.Enums
{
    public static class Enums
    {
        public enum VisaCategoryType
        {
            [LocalizedDescription(nameof(EnumResources.Unspecified))]
            Unspecified = 0,
            [LocalizedDescription(nameof(EnumResources.Touristic))]
            Touristic = 1,
            [LocalizedDescription(nameof(EnumResources.Health))]
            Health = 2,
            [LocalizedDescription(nameof(EnumResources.FamilyAndFriendVisit))]
            FamilyAndFriendVisit = 3,
            [LocalizedDescription(nameof(EnumResources.Business))]
            Business = 4,
            [LocalizedDescription(nameof(EnumResources.CulturalSportive))]
            CulturalSportive = 5,
            [LocalizedDescription(nameof(EnumResources.Student))]
            Student = 6,
            [LocalizedDescription(nameof(EnumResources.WorkPermit))]
            WorkPermit = 7,
            [LocalizedDescription(nameof(EnumResources.Transit))]
            Transit = 8,
            [LocalizedDescription(nameof(EnumResources.FamilyUnion))]
            FamilyUnion = 9,
            [LocalizedDescription(nameof(EnumResources.Driver))]
            Driver = 10,
            [LocalizedDescription(nameof(EnumResources.Consenting))]
            Consenting = 11,
            [LocalizedDescription(nameof(EnumResources.Servant))]
            Servant = 12,
            [LocalizedDescription(nameof(EnumResources.Montage))]
            Montage = 13,
            [LocalizedDescription(nameof(EnumResources.NonApplicationInsurance))]
            NonApplicationInsurance = 14,
            [LocalizedDescription(nameof(EnumResources.EntryBanned))]
            EntryBanned = 15,
            [LocalizedDescription(nameof(EnumResources.ApprovedWorkPermit))]
            ApprovedWorkPermit = 16,
            [LocalizedDescription(nameof(EnumResources.VisaTransfer))]
            VisaTransfer = 17,
            [LocalizedDescription(nameof(EnumResources.ConferenceFair))]
            ConferenceFair = 18,
            [LocalizedDescription(nameof(EnumResources.MinistryOfHealthApplication))]
            MinistryOfHealthApplication = 19,
            [LocalizedDescription(nameof(EnumResources.Companion))]
            Companion = 20,
            [LocalizedDescription(nameof(EnumResources.PatientCompanion))]
            PatientCompanion = 21,
            [LocalizedDescription(nameof(EnumResources.StudentCompanion))]
            StudentCompanion = 22,
            [LocalizedDescription(nameof(EnumResources.WorkPermitCompanion))]
            WorkPermitCompanion = 23
        }

        public enum DaysOfWeek
        {
            [LocalizedDescription(nameof(EnumResources.Sunday))]
            Sunday = 0,
            [LocalizedDescription(nameof(EnumResources.Monday))]
            Monday = 1,
            [LocalizedDescription(nameof(EnumResources.Tuesday))]
            Tuesday = 2,
            [LocalizedDescription(nameof(EnumResources.Wednesday))]
            Wednesday = 3,
            [LocalizedDescription(nameof(EnumResources.Thursday))]
            Thursday = 4,
            [LocalizedDescription(nameof(EnumResources.Friday))]
            Friday = 5,
            [LocalizedDescription(nameof(EnumResources.Saturday))]
            Saturday = 6,
        }

        public enum NotificationType
        {
            [Description("Status Notification")]
            StatusChanged,

            [Description("Appointment Confirmation ")]
            NewPreApplication = 3,

            [Description("Appointment Update Confirmation")]
            UpdatePreApplication = 4,

            [Description("Insurance Policy")]
            InsurancePolicy = 6,
        }

        public enum FileModuleType
        {
            AppointmentDemand = 1,
            ApplicationPnl = 2,
            Company = 3,
            CompanyUser = 4
        }

        public enum ApplicationType
        {
            [LocalizedDescription(nameof(EnumResources.ApplicationTypeNormal))]
            Normal = 1,
            [LocalizedDescription(nameof(EnumResources.ApplicationTypeFree))]
            Free = 2,
            [LocalizedDescription(nameof(EnumResources.ApplicationTypeNonApplicationInsurance))]
            NonApplicationInsurance = 3,
            [LocalizedDescription(nameof(EnumResources.ApplicationTypeNonApplicationPcr))]
            NonApplicationPcr = 4,
            [LocalizedDescription(nameof(EnumResources.ApplicationTypeTurquois))]
            Turquois = 5,
            [LocalizedDescription(nameof(EnumResources.ApplicationTypeTurquoisPremium))]
            TurquoisPremium = 6,
            [LocalizedDescription(nameof(EnumResources.ApplicationTypeTurquoisGratis))]
            TurquoisGratis = 7,
            [LocalizedDescription(nameof(EnumResources.ApplicationTypeNonApplicationPrintOut))]
            NonApplicationPrintOut = 8,
            [LocalizedDescription(nameof(EnumResources.ApplicationTypeNonApplicationPhotocopy))]
            NonApplicationPhotocopy = 9,
            [LocalizedDescription(nameof(EnumResources.ApplicationTypeNonApplicationPhotograph))]
            NonApplicationPhotograph = 10
        }

        public enum ChannelType
        {
            Portal = 1,
            B2B = 2,
            Mobile = 3,
            GwInternational = 4
        }

        public enum ApplicantType
        {
            [LocalizedDescription(nameof(EnumResources.ApplicantTypeIndividual))]
            Individual = 1,
            [LocalizedDescription(nameof(EnumResources.ApplicantTypeFamily))]
            Family = 2,
            [LocalizedDescription(nameof(EnumResources.ApplicantTypeGroup))]
            Group = 3,
            [LocalizedDescription(nameof(EnumResources.ApplicantTypeRepresentative))]
            Representative = 5
        }

        public enum VasType
        {
            [LocalizedDescription(nameof(EnumResources.NormalApplications))]
            NormalApplication = 0,
            [LocalizedDescription(nameof(EnumResources.PrimeTime))]
            PrimeTime = 1,
            [LocalizedDescription(nameof(EnumResources.VIP))]
            Vip = 2,
            [LocalizedDescription(nameof(EnumResources.Platinum))]
            Platinum = 3,
            [LocalizedDescription(nameof(EnumResources.MBS))]
            Mbs = 4,
            [LocalizedDescription(nameof(EnumResources.DocumentEditingService))]
            DocumentEditingService = 5,
            [LocalizedDescription(nameof(EnumResources.FlexibleAppointment))]
            FlexibleAppointment = 6
        }

        public enum SlotType
        {
            [LocalizedDescription(nameof(EnumResources.Individual))]
            Individual = 1,
            [LocalizedDescription(nameof(EnumResources.Agency))]
            Agency = 2,
            //[LocalizedDescription(nameof(EnumResources.VFS))]
            //Vfs = 3,
            [LocalizedDescription(nameof(EnumResources.CallCenter))]
            CallCenter = 4,
            [LocalizedDescription(nameof(EnumResources.B2B))]
            B2B = 5
        }

        public enum ApplicationStatus
        {
            [LocalizedDescription(nameof(EnumResources.ApplicationStatusActive))]
            Active = 1,
            [LocalizedDescription(nameof(EnumResources.ApplicationStatusCancelled))]
            Cancelled = 2,
            [LocalizedDescription(nameof(EnumResources.ApplicationStatusPartiallyRefunded))]
            PartiallyRefunded = 3
        }

        public enum Gender
        {
            [LocalizedDescription(nameof(EnumResources.Male))]
            Male = 1,
            [LocalizedDescription(nameof(EnumResources.Female))]
            Female = 2
        }
        public enum RelationShip
        {
            [LocalizedDescription(nameof(EnumResources.Wife))]
            Wife = 1,
            [LocalizedDescription(nameof(EnumResources.Child))]
            Child = 2,
            [LocalizedDescription(nameof(EnumResources.MainApplicant))]
            MainApplicant = 99
        }

        public enum ApplicationStatusType
        {
            ApplicationTaken = 1,
            DataDone = 2,
            SendToEmbassy = 3,
            ReceivedAtEmbassy = 4,
            SentToVACFromEmbassy = 13,
            RecievedAtVAC = 14,
            HandDeliveredToApplicant = 15,
            Rejection = 16,
            OutscanToCourier = 18,
            BiometricEnrollmentDone = 22,
            RejectionWithCountryEntryBanned = 24,
            OutscanPassportWithRejectionToCourier = 25,
            IstizanRejection = 26,
            ReceivedAtVisaCenter = 31,
            ReceivedAtVac = 32,
            UnrealDocument = 33,
            WaitingApproval = 34,
            FileWithdrewAccordingtoCustomerRequest = 35,
            IhbWaiting = 41,
            IhbUploaded = 42,
            ReceivedAtOC = 43,
            OutScanFromOCToVAC = 44,
            OutScanFromVACToOC = 45,
            OutScanFromEmbassyToOC = 46,
            OutScanFromOCToEmbassy = 47,
            CourierReturnedPassportToOffice = 52
        }

        public enum IncorrectApplicationStatusReason
        {
            [LocalizedDescription(nameof(EnumResources.WithdrawalByApplicant))]
            WithdrawalByApplicant = 1,
            [LocalizedDescription(nameof(EnumResources.ConsulateDecision))]
            ConsulateDecision = 2,
            [LocalizedDescription(nameof(EnumResources.ConsulateCanceledRefund))]
            ConsulateCanceledRefund = 3,
            [LocalizedDescription(nameof(EnumResources.OfficeWaitingForMissingDocuments))]
            OfficeWaitingForMissingDocuments = 4,
            [LocalizedDescription(nameof(EnumResources.Other))]
            Other = 5
        }

        public enum ApplicationStatusTrackingView
        {
            [LocalizedDescription(nameof(EnumResources.ApplicationTaken))]
            ApplicationTaken,
            [LocalizedDescription(nameof(EnumResources.ApplicationUnderEvaluation))]
            ApplicationUnderEvaluation,
            [LocalizedDescription(nameof(EnumResources.HandDeliveredToTheApplicant))]
            HandDeliveredToTheApplicant,
            [LocalizedDescription(nameof(EnumResources.DeliveredToCargo))]
            DeliveredToCargo,
            [LocalizedDescription(nameof(EnumResources.WorkPermitReferanceNumberSendSms))]
            WorkPermitReferanceNumberSendSms,
            [LocalizedDescription(nameof(EnumResources.WorkPermitReferanceNumberSendMail))]
            WorkPermitReferanceNumberSendMail,
            [LocalizedDescription(nameof(EnumResources.ReceivedAtVisaCenter))]
            ReceivedAtVisaCenter,
            [LocalizedDescription(nameof(EnumResources.FileWithdrewAccordingtoCustomerRequest))]
            FileWithdrewAccordingtoCustomerRequest,
            [LocalizedDescription(nameof(EnumResources.ReceivedAtMainVisaApplicationCenter))]
            ReceivedAtMainVisaApplicationCenter,
            [LocalizedDescription(nameof(EnumResources.SentFromMainVisaApplicationCenterToVisaApplicationCenter))]
            SentFromMainVisaApplicationCenterToVisaApplicationCenter,
            [LocalizedDescription(nameof(EnumResources.SentToMainVisaApplicationCenter))]
            SentToMainVisaApplicationCenter,
            [LocalizedDescription(nameof(EnumResources.SentFromEmbassyToMainVisaApplicationCenter))]
            SentFromEmbassyToMainVisaApplicationCenter,
            [LocalizedDescription(nameof(EnumResources.CourierReturnedPassportToOffice))]
            CourierReturnedPassportToOffice,
        }

        public enum BasicGuidelineType
        {
            ArabicFrenchEnglish = 1,
            KurdishArabic = 2,
            ArabicEnglish = 3,
            TurkmenRussian = 4
        }

        public enum Culture
        {
            [Description("tr-TR")]
            TR = 1,
            [Description("en-US")]
            EN = 2,
            [Description("ar-SA")]
            Ar = 3,
            [Description("tk-TKM")]
            TK = 4,
            [Description("ru-RU")]
            RU = 5,
            [Description("fr-FR")]
            FR = 6,
        }

        public enum Language
        {
            Turkish = 1,
            English = 2,
            Arabic = 3,
            Turkmen = 4,
            Russian = 5,
            French = 6
        }

        //B2B

        public enum B2BApprovalStatus
        {
            [LocalizedDescription(nameof(EnumResources.WaitingApproval))]
            WaitingApproval = 1,
            [LocalizedDescription(nameof(EnumResources.Revision))]
            Revision = 2,
            [LocalizedDescription(nameof(EnumResources.Rejected))]
            Rejected = 3,
            [LocalizedDescription(nameof(EnumResources.Approved))]
            Approved = 4,
            [LocalizedDescription(nameof(EnumResources.Expired))]
            Expired = 5,
            [LocalizedDescription(nameof(EnumResources.PaymentWaiting))]
            PaymentWaiting = 6
        }

        public enum B2BApprovalStatusWithPayment
        {
            [LocalizedDescription(nameof(EnumResources.WaitingApproval))]
            WaitingApproval = 1,
            [LocalizedDescription(nameof(EnumResources.PaymentCompleted))]
            PaymentCompleted = 2,
            [LocalizedDescription(nameof(EnumResources.Revision))]
            Revision = 3,
            [LocalizedDescription(nameof(EnumResources.Rejected))]
            Rejected = 4,
            [LocalizedDescription(nameof(EnumResources.Approved))]
            Approved = 5
        }

        public enum ApplicationActiveStatus
        {
            [LocalizedDescription(nameof(EnumResources.Active))]
            Active = 1,
            [LocalizedDescription(nameof(EnumResources.Passive))]
            Passive = 2
        }

        public enum PnlFileStatus
        {
            [LocalizedDescription(nameof(EnumResources.NotUploaded))]
            NotUploaded = 1,
            [LocalizedDescription(nameof(EnumResources.Uploaded))]
            Uploaded = 2,
        }

        public enum RedirectType
        {
            [LocalizedDescription(nameof(EnumResources.Appointment))]
            Appointment = 1,
            [LocalizedDescription(nameof(EnumResources.Slot))]
            Slot = 2,
            [LocalizedDescription(nameof(EnumResources.Application))]
            Application = 3,
            [LocalizedDescription(nameof(EnumResources.User))]
            User = 4,
        }

        public enum CompanyCompletionPropertyType
        {
            Company = 1,
            User = 2,
            CompanyFile = 3,
            UserFile = 4
        }

        public enum ReportType
        {
            CompanyUserReport = 1,
            SlotDemandReport = 2,
            AppointmentDemandReport = 3,
            ApplicationReport = 4,
            PnlReport = 5
        }

        public enum PaymentType
        {
            Slot = 1,
            Appointment = 2,
        }

        public enum ExtraFeeCategoryType
        {
            Other = 1,
            Insurance = 2,
            PCR = 3,
            Application = 4,
            Cargo = 5,
            ServiceFee = 6,
            YSS = 7,
            TS = 8,
            RelatedInsurance = 9
        }

        public enum InstitutionType
        {
            [LocalizedDescription(nameof(EnumResources.AllInstitutions))]
            [EnumMetadata("X")]
            AllInstitutions,

            [LocalizedDescription(nameof(EnumResources.Ambulance))]
            [EnumMetadata("ambulans")]
            Ambulance,

            [LocalizedDescription(nameof(EnumResources.DentalClinic))]
            [EnumMetadata("dis-klinigi")]
            DentalClinic,

            [LocalizedDescription(nameof(EnumResources.Pharmacy))]
            [EnumMetadata("eczane")]
            Pharmacy,

            [LocalizedDescription(nameof(EnumResources.HomeHealthCare))]
            [EnumMetadata("evde-bakim-merkezi")]
            HomeHealthCare,

            [LocalizedDescription(nameof(EnumResources.PhysicalTherapyCenter))]
            [EnumMetadata("fizik-tedavi-merkezi")]
            PhysicalTherapy,

            [LocalizedDescription(nameof(EnumResources.Hospital))]
            [EnumMetadata("hastane")]
            Hospital,

            [LocalizedDescription(nameof(EnumResources.Laboratory))]
            [EnumMetadata("laboratuvar")]
            Laboratory,

            [LocalizedDescription(nameof(EnumResources.MedicalMaterial))]
            [EnumMetadata("medikal-tibbi-malzeme")]
            MedicalMaterial,

            [LocalizedDescription(nameof(EnumResources.Surgery))]
            [EnumMetadata("muayenehane")]
            Surgery,

            [LocalizedDescription(nameof(EnumResources.Optical))]
            [EnumMetadata("optik")]
            Optical,

            [LocalizedDescription(nameof(EnumResources.Polyclinic))]
            [EnumMetadata("poliklinik-tip-merkezi")]
            Polyclinic,

            [LocalizedDescription(nameof(EnumResources.Radiology))]
            [EnumMetadata("radyoloji")]
            Radiology,
        }
    }
}