﻿//------------------------------------------------------------------------------
// <auto-generated>
//     This code was generated by a tool.
//     Runtime Version:4.0.30319.42000
//
//     Changes to this file may cause incorrect behavior and will be lost if
//     the code is regenerated.
// </auto-generated>
//------------------------------------------------------------------------------

namespace Gateway.QMS.Resources {
    using System;
    
    
    /// <summary>
    ///   A strongly-typed resource class, for looking up localized strings, etc.
    /// </summary>
    // This class was auto-generated by the StronglyTypedResourceBuilder
    // class via a tool like ResGen or Visual Studio.
    // To add or remove a member, edit your .ResX file then rerun ResGen
    // with the /str option, or rebuild your VS project.
    [global::System.CodeDom.Compiler.GeneratedCodeAttribute("System.Resources.Tools.StronglyTypedResourceBuilder", "17.0.0.0")]
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute()]
    [global::System.Runtime.CompilerServices.CompilerGeneratedAttribute()]
    public class ServiceResources {
        
        private static global::System.Resources.ResourceManager resourceMan;
        
        private static global::System.Globalization.CultureInfo resourceCulture;
        
        [global::System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1811:AvoidUncalledPrivateCode")]
        internal ServiceResources() {
        }
        
        /// <summary>
        ///   Returns the cached ResourceManager instance used by this class.
        /// </summary>
        [global::System.ComponentModel.EditorBrowsableAttribute(global::System.ComponentModel.EditorBrowsableState.Advanced)]
        public static global::System.Resources.ResourceManager ResourceManager {
            get {
                if (object.ReferenceEquals(resourceMan, null)) {
                    global::System.Resources.ResourceManager temp = new global::System.Resources.ResourceManager("Gateway.QMS.Resources.ServiceResources", typeof(ServiceResources).Assembly);
                    resourceMan = temp;
                }
                return resourceMan;
            }
        }
        
        /// <summary>
        ///   Overrides the current thread's CurrentUICulture property for all
        ///   resource lookups using this strongly typed resource class.
        /// </summary>
        [global::System.ComponentModel.EditorBrowsableAttribute(global::System.ComponentModel.EditorBrowsableState.Advanced)]
        public static global::System.Globalization.CultureInfo Culture {
            get {
                return resourceCulture;
            }
            set {
                resourceCulture = value;
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to It is not allowed to add an applicant with the same appointment number to the same appointment again..
        /// </summary>
        public static string ADDING_APPLICANT_WITH_SAME_APPOINTMENT_NUMBER_NOT_ALLOWED {
            get {
                return ResourceManager.GetString("ADDING_APPLICANT_WITH_SAME_APPOINTMENT_NUMBER_NOT_ALLOWED", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to All tickets used in the qms.
        /// </summary>
        public static string ALL_TICKETS_HAS_HISTORY {
            get {
                return ResourceManager.GetString("ALL_TICKETS_HAS_HISTORY", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Already defined before.
        /// </summary>
        public static string AlreadyDefinedBefore {
            get {
                return ResourceManager.GetString("AlreadyDefinedBefore", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Applicant with has ticket not found.
        /// </summary>
        public static string APPLICANT_HAS_TICKET_NOT_FOUND {
            get {
                return ResourceManager.GetString("APPLICANT_HAS_TICKET_NOT_FOUND", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Applicant in blacklist.
        /// </summary>
        public static string APPLICANT_IN_BLACKLIST {
            get {
                return ResourceManager.GetString("APPLICANT_IN_BLACKLIST", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Applicant in whitelist.
        /// </summary>
        public static string APPLICANT_IN_WHITELIST {
            get {
                return ResourceManager.GetString("APPLICANT_IN_WHITELIST", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Applicant not found.
        /// </summary>
        public static string APPLICANT_NOT_FOUND {
            get {
                return ResourceManager.GetString("APPLICANT_NOT_FOUND", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Applicant type.
        /// </summary>
        public static string ApplicantType {
            get {
                return ResourceManager.GetString("ApplicantType", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Application not found.
        /// </summary>
        public static string APPLICATION_NOT_FOUND {
            get {
                return ResourceManager.GetString("APPLICATION_NOT_FOUND", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Appointment not found.
        /// </summary>
        public static string APPOINTMENT_NOT_FOUND {
            get {
                return ResourceManager.GetString("APPOINTMENT_NOT_FOUND", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Appointment date.
        /// </summary>
        public static string AppointmentDate {
            get {
                return ResourceManager.GetString("AppointmentDate", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Appointment not for today.
        /// </summary>
        public static string AppointmentDateNotForToday {
            get {
                return ResourceManager.GetString("AppointmentDateNotForToday", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Available quota not found.
        /// </summary>
        public static string AVAILABLE_QUOTA_NOT_FOUND {
            get {
                return ResourceManager.GetString("AVAILABLE_QUOTA_NOT_FOUND", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Birth date.
        /// </summary>
        public static string BirthDate {
            get {
                return ResourceManager.GetString("BirthDate", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Branch application country not found.
        /// </summary>
        public static string BRANCH_APPLICATION_COUNTRY_NOT_FOUND {
            get {
                return ResourceManager.GetString("BRANCH_APPLICATION_COUNTRY_NOT_FOUND", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Branch Department not found.
        /// </summary>
        public static string BRANCH_DEPARTMENT_NOT_FOUND {
            get {
                return ResourceManager.GetString("BRANCH_DEPARTMENT_NOT_FOUND", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Branch not found.
        /// </summary>
        public static string BRANCH_NOT_FOUND {
            get {
                return ResourceManager.GetString("BRANCH_NOT_FOUND", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Branch translation not found.
        /// </summary>
        public static string BRANCH_TRANSLATION_NOT_FOUND {
            get {
                return ResourceManager.GetString("BRANCH_TRANSLATION_NOT_FOUND", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Branch name.
        /// </summary>
        public static string BranchName {
            get {
                return ResourceManager.GetString("BranchName", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Counter busy.
        /// </summary>
        public static string COUNTER_BUSY {
            get {
                return ResourceManager.GetString("COUNTER_BUSY", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Counter not found.
        /// </summary>
        public static string COUNTER_NOT_FOUND {
            get {
                return ResourceManager.GetString("COUNTER_NOT_FOUND", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Department not found.
        /// </summary>
        public static string DEPARTMENT_NOT_FOUND {
            get {
                return ResourceManager.GetString("DEPARTMENT_NOT_FOUND", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Appointment time is not come yet. The appointment date is:.
        /// </summary>
        public static string EarlyAppointmentProcess {
            get {
                return ResourceManager.GetString("EarlyAppointmentProcess", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Endwith cannot be smaller than startwith.
        /// </summary>
        public static string EndwithCannotBeSmallerThanStartwith {
            get {
                return ResourceManager.GetString("EndwithCannotBeSmallerThanStartwith", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to FAILED.
        /// </summary>
        public static string FAILED {
            get {
                return ResourceManager.GetString("FAILED", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to The family applicant counts can be more than 1 item.
        /// </summary>
        public static string FAMILY_APPLICANT_COUNT_ERROR {
            get {
                return ResourceManager.GetString("FAMILY_APPLICANT_COUNT_ERROR", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to First name.
        /// </summary>
        public static string FirstName {
            get {
                return ResourceManager.GetString("FirstName", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Gender.
        /// </summary>
        public static string Gender {
            get {
                return ResourceManager.GetString("Gender", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Generated token history not found.
        /// </summary>
        public static string GENERATED_TOKEN_HISTORY_NOT_FOUND {
            get {
                return ResourceManager.GetString("GENERATED_TOKEN_HISTORY_NOT_FOUND", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Generated token not found.
        /// </summary>
        public static string GENERATED_TOKEN_NOT_FOUND {
            get {
                return ResourceManager.GetString("GENERATED_TOKEN_NOT_FOUND", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Generated token notes not found.
        /// </summary>
        public static string GENERATED_TOKEN_NOTES_NOT_FOUND {
            get {
                return ResourceManager.GetString("GENERATED_TOKEN_NOTES_NOT_FOUND", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to The group applicant counts can be more than 1 item.
        /// </summary>
        public static string GROUP_APPLICANT_COUNT_ERROR {
            get {
                return ResourceManager.GetString("GROUP_APPLICANT_COUNT_ERROR", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to The individual applicant counts can not be more than 1 item.
        /// </summary>
        public static string INDIVIDUAL_APPLICANT_COUNT_ERROR {
            get {
                return ResourceManager.GetString("INDIVIDUAL_APPLICANT_COUNT_ERROR", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to .
        /// </summary>
        public static string INDIVIUAL_APPLICANT_COUNT_ERROR {
            get {
                return ResourceManager.GetString("INDIVIUAL_APPLICANT_COUNT_ERROR", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to INPUT_ERROR.
        /// </summary>
        public static string INPUT_ERROR {
            get {
                return ResourceManager.GetString("INPUT_ERROR", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to INTERNAL_SERVER_ERROR.
        /// </summary>
        public static string INTERNAL_SERVER_ERROR {
            get {
                return ResourceManager.GetString("INTERNAL_SERVER_ERROR", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Invalid.
        /// </summary>
        public static string Invalid {
            get {
                return ResourceManager.GetString("Invalid", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Invalid input error.
        /// </summary>
        public static string INVALID_INPUT_ERROR {
            get {
                return ResourceManager.GetString("INVALID_INPUT_ERROR", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Invalid request.
        /// </summary>
        public static string INVALID_REQUEST {
            get {
                return ResourceManager.GetString("INVALID_REQUEST", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Invalid applicant type.
        /// </summary>
        public static string InvalidApplicantType {
            get {
                return ResourceManager.GetString("InvalidApplicantType", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Invalid email format.
        /// </summary>
        public static string InvalidEmailFormat {
            get {
                return ResourceManager.GetString("InvalidEmailFormat", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Last name.
        /// </summary>
        public static string LastName {
            get {
                return ResourceManager.GetString("LastName", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Connection cant be more than 15.
        /// </summary>
        public static string LINE_DEPARTMENT_CONNECTION_LIMIT {
            get {
                return ResourceManager.GetString("LINE_DEPARTMENT_CONNECTION_LIMIT", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Line Department Not Found.
        /// </summary>
        public static string LINE_DEPARTMENT_NOT_FOUND {
            get {
                return ResourceManager.GetString("LINE_DEPARTMENT_NOT_FOUND", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Line not found.
        /// </summary>
        public static string LINE_NOT_FOUND {
            get {
                return ResourceManager.GetString("LINE_NOT_FOUND", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Nationality.
        /// </summary>
        public static string Nationality {
            get {
                return ResourceManager.GetString("Nationality", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to No property found to update.
        /// </summary>
        public static string NO_PROPERTY_FOUND_TO_UPDATE {
            get {
                return ResourceManager.GetString("NO_PROPERTY_FOUND_TO_UPDATE", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Ongoing Process.
        /// </summary>
        public static string OngoingProcess {
            get {
                return ResourceManager.GetString("OngoingProcess", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Order is already registered.
        /// </summary>
        public static string ORDER_ALREADY_REGISTERED {
            get {
                return ResourceManager.GetString("ORDER_ALREADY_REGISTERED", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Passport expiry date.
        /// </summary>
        public static string PassportExpiryDate {
            get {
                return ResourceManager.GetString("PassportExpiryDate", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Passport number.
        /// </summary>
        public static string PassportNumber {
            get {
                return ResourceManager.GetString("PassportNumber", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Please try again.
        /// </summary>
        public static string PleaseTryAgain {
            get {
                return ResourceManager.GetString("PleaseTryAgain", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Pre condition failed.
        /// </summary>
        public static string PRE_CONDITION_FAILED {
            get {
                return ResourceManager.GetString("PRE_CONDITION_FAILED", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Prime.
        /// </summary>
        public static string Prime {
            get {
                return ResourceManager.GetString("Prime", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to The property {0} can not be more than {1} characters.
        /// </summary>
        public static string PROPERTY_MAX_LENGTH_ERROR {
            get {
                return ResourceManager.GetString("PROPERTY_MAX_LENGTH_ERROR", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to {0} is required.
        /// </summary>
        public static string PROPERTY_REQUIRED {
            get {
                return ResourceManager.GetString("PROPERTY_REQUIRED", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Referance number.
        /// </summary>
        public static string ReferanceNumber {
            get {
                return ResourceManager.GetString("ReferanceNumber", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Resource is already registered.
        /// </summary>
        public static string RESOURCE_ALREADY_REGISTERED {
            get {
                return ResourceManager.GetString("RESOURCE_ALREADY_REGISTERED", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Resource created.
        /// </summary>
        public static string RESOURCE_CREATED {
            get {
                return ResourceManager.GetString("RESOURCE_CREATED", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Resource deleted.
        /// </summary>
        public static string RESOURCE_DELETED {
            get {
                return ResourceManager.GetString("RESOURCE_DELETED", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Resource found.
        /// </summary>
        public static string RESOURCE_FOUND {
            get {
                return ResourceManager.GetString("RESOURCE_FOUND", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Resource not found.
        /// </summary>
        public static string RESOURCE_NOT_FOUND {
            get {
                return ResourceManager.GetString("RESOURCE_NOT_FOUND", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Resource retrieved.
        /// </summary>
        public static string RESOURCE_RETRIEVED {
            get {
                return ResourceManager.GetString("RESOURCE_RETRIEVED", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Resource updated.
        /// </summary>
        public static string RESOURCE_UPDATED {
            get {
                return ResourceManager.GetString("RESOURCE_UPDATED", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Line same process information not found.
        /// </summary>
        public static string SAME_PROCESS_COUNTER_NOT_FOUND {
            get {
                return ResourceManager.GetString("SAME_PROCESS_COUNTER_NOT_FOUND", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Search operation not valid for this line department.
        /// </summary>
        public static string SearchOperationNotValidForThisLineDepartment {
            get {
                return ResourceManager.GetString("SearchOperationNotValidForThisLineDepartment", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Slot not found.
        /// </summary>
        public static string SLOT_NOT_FOUND {
            get {
                return ResourceManager.GetString("SLOT_NOT_FOUND", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to SUCCESS.
        /// </summary>
        public static string SUCCESS {
            get {
                return ResourceManager.GetString("SUCCESS", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to The passport has already been delivered to the applicant..
        /// </summary>
        public static string ThePassportHasNotYetBeenReceivedAtTheOffice {
            get {
                return ResourceManager.GetString("ThePassportHasNotYetBeenReceivedAtTheOffice", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to There is no person waiting in the selected department.
        /// </summary>
        public static string ThereIsNoPersonWaitingInTheSelectedDepartment {
            get {
                return ResourceManager.GetString("ThereIsNoPersonWaitingInTheSelectedDepartment", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to There is no person waiting on the selected line.
        /// </summary>
        public static string ThereIsNoPersonWaitingOnTheSelectedLine {
            get {
                return ResourceManager.GetString("ThereIsNoPersonWaitingOnTheSelectedLine", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Token already added to another tokens process.
        /// </summary>
        public static string TOKEN_ALREADY_ADDED_ON_ANOTHER_TOKEN {
            get {
                return ResourceManager.GetString("TOKEN_ALREADY_ADDED_ON_ANOTHER_TOKEN", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Token not found.
        /// </summary>
        public static string TOKEN_NOT_FOUND {
            get {
                return ResourceManager.GetString("TOKEN_NOT_FOUND", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to The ticket number has reached the upper limit.
        /// </summary>
        public static string TokenNumberHasReachedUpperLimit {
            get {
                return ResourceManager.GetString("TokenNumberHasReachedUpperLimit", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Token number process not found.
        /// </summary>
        public static string TokenNumberProcessNotFound {
            get {
                return ResourceManager.GetString("TokenNumberProcessNotFound", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to User busy.
        /// </summary>
        public static string USER_BUSY {
            get {
                return ResourceManager.GetString("USER_BUSY", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to User not found.
        /// </summary>
        public static string USER_NOT_FOUND {
            get {
                return ResourceManager.GetString("USER_NOT_FOUND", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Token already in progress from another user, please proceed with new token.
        /// </summary>
        public static string USER_STATE_REFRESH_MESSAGE {
            get {
                return ResourceManager.GetString("USER_STATE_REFRESH_MESSAGE", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Vip.
        /// </summary>
        public static string Vip {
            get {
                return ResourceManager.GetString("Vip", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Vip Appointment Tickets Can Only Be Get From The Vip Line.
        /// </summary>
        public static string VipAppointmentTicketsCanOnlyBeGetFromTheVipLine {
            get {
                return ResourceManager.GetString("VipAppointmentTicketsCanOnlyBeGetFromTheVipLine", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Application is located in a branch different from the relevant branch.
        /// </summary>
        public static string WRONG_BRANCH_SELECTION {
            get {
                return ResourceManager.GetString("WRONG_BRANCH_SELECTION", resourceCulture);
            }
        }
    }
}
