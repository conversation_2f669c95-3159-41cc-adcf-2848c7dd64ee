﻿using System;
using System.Collections.Generic;

namespace Portal.Gateway.Entity.Entities.Portal
{
    public class Application : BaseEntity
    {
        public Application()
        {
            ApplicationCancellations = new HashSet<ApplicationCancellation>();
            ApplicationConfirmationCodes = new HashSet<ApplicationConfirmationCode>();
            ApplicationEmailHistory = new HashSet<ApplicationEmailHistory>();
            ApplicationExtraFees = new HashSet<ApplicationExtraFee>();
            ApplicationFiles = new HashSet<ApplicationFile>();
            ApplicationHistories = new HashSet<ApplicationHistory>();
            ApplicationInsurance = new HashSet<ApplicationInsurance>();
            ApplicationNotes = new HashSet<ApplicationNote>();
            ApplicationReferenceNumbers = new HashSet<ApplicationReferenceNumber>();
            ApplicationSmsHistory = new HashSet<ApplicationSmsHistory>();
            ApplicationStatusHistories = new HashSet<ApplicationStatusHistory>();
            ApplicationSurveys = new HashSet<ApplicationSurvey>();
            ApplicationVisaDecisions = new HashSet<ApplicationVisaDecision>();
            ApplicationVisaHistories = new HashSet<ApplicationVisaHistory>();
            CargoTracks = new HashSet<CargoTrack>();
            ForeignHealthInsurances = new HashSet<ForeignHealthInsurance>();
            IncorrectApplicationStatusHistory = new HashSet<IncorrectApplicationStatusHistory>();
            SapApplicationOrder = new HashSet<SapApplicationOrder>();
            SendVisaRejectionSapLogs = new HashSet<SendVisaRejectionSapLog>();
            ApplicationSales = new HashSet<ApplicationSale>();
            ApplicationRejectionApprovalStatusHistories = new HashSet<ApplicationRejectionApprovalStatusHistory>();
            ClaimLossEntryLogs = new HashSet<ClaimLossEntryLog>();
            KsaClearTaxServiceLogs = new HashSet<KsaClearTaxServiceLog>();
            ApplicationOfficialNotes = new HashSet<ApplicationOfficialNote>();
			RelatedIndividualInsuranes = new HashSet<RelatedIndividualInsurane>();
            IhbOrders = new HashSet<IhbOrder>();
            CancelInsuranceOrders = new HashSet<CancelInsuranceOrder>();
			IsActive = true;
            IsDeleted = false;
        }
        public int Id { get; set; }
        public int BranchApplicationCountryId { get; set; }
        public int ApplicantTypeId { get; set; }
        public int ApplicationTypeId { get; set; }
        public string PassportNumber { get; set; }
        public DateTime? PassportExpireDate { get; set; }
        public int ApplicationPassportStatusId { get; set; }
        public int TitleId { get; set; }
        public string Name { get; set; }
        public string Surname { get; set; }
        public DateTime BirthDate { get; set; }
        public int GenderId { get; set; }
        public int? MaritalStatusId { get; set; }
        public int NationalityId { get; set; }
        public string MaidenName { get; set; }
        public string FatherName { get; set; }
        public string MotherName { get; set; }
        public string Email { get; set; }
        public string PhoneNumber1 { get; set; }
        public string PhoneNumber2 { get; set; }
        public string Address { get; set; }
        public int ApplicationStatusId { get; set; }
        public int? RelationalApplicationId { get; set; }
        public DateTimeOffset ApplicationTime { get; set; }
        public string Note { get; set; }
        public int CreatedBy { get; set; }
        public int StatusId { get; set; }
        public int? UpdatedBy { get; set; }
        public DateTimeOffset? UpdatedAt { get; set; }
        public int? PreApplicationApplicantId { get; set; }
        public int? AgencyId { get; set; }
        public int? CustomerId { get; set; }
        public bool? IsAllowDeniedPassport { get; set; }
        public string Reason { get; set; }
        public int? RelationShipId { get; set; }
        public int? LocalAuthorityStatusId { get; set; }
        public string VisaCategoryReferenceNumber { get; set; }
        public int ReferenceNumberSendCount { get; set; }
        public bool? IsInterviewRequired { get; set; }
        public bool? IsInterviewDone { get; set; }
        public int? GeneratedTokenId { get; set; }
        public int? ForeignCityId { get; set; }
        public int? PreviousCancelledApplicationInsuranceId { get; set; }
        public string PostalCode { get; set; }
        public int? ApplicantCount { get; set; }
        public string NameOfSecondContactPerson { get; set; }
        public string ResidenceNumber { get; set; }
        public int ActiveStatusId { get; set; }
        public bool? IsContactInformationVerified { get; set; }
        public int? WhiteListId { get; set; }
        public virtual Agency Agency { get; set; }
        public virtual ApplicationStatus ApplicationStatus { get; set; }
        public virtual BranchApplicationCountry BranchApplicationCountry { get; set; }
        public virtual Customer Customer { get; set; }
        public virtual Country Nationality { get; set; }
        public virtual PreApplicationApplicant PreApplicationApplicant { get; set; }
        public virtual ApplicationData ApplicationData { get; set; }
        public virtual ApplicationDocument ApplicationDocument { get; set; }
        public virtual User User { get; set; }
        public virtual PhotoBooth PhotoBooth { get; set; }
        public virtual ForeignCity City { get; set; }
        public virtual ApplicationLastMileData ApplicationLastMileData { get; set; }
        public virtual ICollection<ApplicationCancellation> ApplicationCancellations { get; set; }
        public virtual ICollection<ApplicationConfirmationCode> ApplicationConfirmationCodes { get; set; }
        public virtual ICollection<ApplicationEmailHistory> ApplicationEmailHistory { get; set; }
        public virtual ICollection<ApplicationExtraFee> ApplicationExtraFees { get; set; }
        public virtual ICollection<ApplicationFile> ApplicationFiles { get; set; }
        public virtual ICollection<ApplicationHistory> ApplicationHistories { get; set; }
        public virtual ICollection<ApplicationInsurance> ApplicationInsurance { get; set; }
        public virtual ICollection<ApplicationNote> ApplicationNotes { get; set; }
        public virtual ICollection<ApplicationReferenceNumber> ApplicationReferenceNumbers { get; set; }
        public virtual ICollection<ApplicationSmsHistory> ApplicationSmsHistory { get; set; }
        public virtual ICollection<ApplicationStatusHistory> ApplicationStatusHistories { get; set; }
        public virtual ICollection<ApplicationSurvey> ApplicationSurveys { get; set; }
        public virtual ICollection<ApplicationVisaDecision> ApplicationVisaDecisions { get; set; }
        public virtual ICollection<ApplicationVisaHistory> ApplicationVisaHistories { get; set; }
        public virtual ICollection<CargoTrack> CargoTracks { get; set; }
        public virtual ICollection<ForeignHealthInsurance> ForeignHealthInsurances { get; set; }
        public virtual ICollection<IncorrectApplicationStatusHistory> IncorrectApplicationStatusHistory { get; set; }
        public virtual ICollection<SapApplicationOrder> SapApplicationOrder { get; set; }
        public virtual ICollection<SendVisaRejectionSapLog> SendVisaRejectionSapLogs { get; set; }
        public virtual ICollection<ApplicationSale> ApplicationSales { get; set; }
        public virtual ICollection<ApplicationRejectionApprovalStatusHistory> ApplicationRejectionApprovalStatusHistories { get; set; }
        public virtual ICollection<ClaimLossEntryLog> ClaimLossEntryLogs { get; set; }
		public virtual ICollection<KsaClearTaxServiceLog> KsaClearTaxServiceLogs { get; set; }
        public virtual ICollection<ApplicationOfficialNote> ApplicationOfficialNotes { get; set; }
		public virtual ICollection<RelatedIndividualInsurane> RelatedIndividualInsuranes { get; set; }
		public virtual ICollection<IhbOrder> IhbOrders { get; set; }
		public virtual ICollection<CancelInsuranceOrder> CancelInsuranceOrders { get; set; }

	}
}
