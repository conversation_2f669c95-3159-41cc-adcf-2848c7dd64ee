﻿using System;
using System.Collections.Generic;
using System.ComponentModel.DataAnnotations;
using System.ComponentModel.DataAnnotations.Schema;
using Gateway.External.Entity.Entities.B2B.Application;
using Gateway.External.Entity.Entities.Branch;
using Gateway.External.Entity.Entities.PreApplications;

namespace Gateway.External.Entity.Entities.Application
{
    public class Application
    {
        public Application()
        {
            IsActive = true;
        }

        [Key]
        [DatabaseGenerated(DatabaseGeneratedOption.Identity)]
        public int Id { get; set; }

        [Required]
        public int BranchApplicationCountryId { get; set; }

        [Required]
        public int ApplicantTypeId { get; set; }

        [Required]
        public int ApplicationTypeId { get; set; }

        [Required]
        [Column(TypeName = "citext"), MaxLength(100)]
        public string PassportNumber { get; set; }

        public DateTime? PassportExpireDate { get; set; }

        [Required]
        public int ApplicationPassportStatusId { get; set; }

        public int? AgencyId { get; set; }

        public int? CustomerId { get; set; }

        [Required]
        public int TitleId { get; set; }

        [Required]
        [Column(TypeName = "citext"), MaxLength(100)]
        public string Name { get; set; }

        [Required]
        [Column(TypeName = "citext"), MaxLength(100)]
        public string Surname { get; set; }

        [Required]
        public DateTime BirthDate { get; set; }

        [Required]
        public int GenderId { get; set; }

        public int? MaritalStatusId { get; set; }

        [Required]
        public int NationalityId { get; set; }

        [Column(TypeName = "citext"), MaxLength(100)]
        public string MaidenName { get; set; }

        [Required]
        [Column(TypeName = "citext"), MaxLength(100)]
        public string FatherName { get; set; }

        [Required]
        [Column(TypeName = "citext"), MaxLength(100)]
        public string MotherName { get; set; }

        [Required]
        [Column(TypeName = "citext"), MaxLength(100)]
        public string Email { get; set; }

        [Required]
        public string PhoneNumber1 { get; set; }

        public string PhoneNumber2 { get; set; }

        public string Reason { get; set; }

        public bool? IsAllowDeniedPassport { get; set; }

        [Required]
        [Column(TypeName = "citext"), MaxLength(200)]
        public string Address { get; set; }

        [Required]
        public int ApplicationStatusId { get; set; }

        public int? RelationalApplicationId { get; set; }

        public int? RelationShipId { get; set; }

        public int? LocalAuthorityStatusId { get; set; }

        [Required]
        public DateTimeOffset ApplicationTime { get; set; }

        [Column(TypeName = "citext"), MaxLength(500)]
        public string Note { get; set; }

        [Required]
        public int CreatedBy { get; set; }

        [Required]
        public int StatusId { get; set; }

        public int? UpdatedBy { get; set; }

        public int? PreApplicationApplicantId { get; set; }

        public int? ActiveStatusId { get; set; }

        public DateTimeOffset? UpdatedAt { get; set; }

        public BranchApplicationCountry BranchApplicationCountry { get; set; }

        public Country.Country Nationality { get; set; }

        public ApplicationStatus ApplicationStatus { get; set; }

        public ApplicationDocument ApplicationDocument { get; set; }

        public ICollection<ApplicationStatusHistory> ApplicationStatusHistories { get; set; }

        public ICollection<ApplicationPnlFile> ApplicationPnlFiles { get; set; }
        public virtual ICollection<ApplicationExtraFee> ApplicationExtraFees { get; set; }
        public ICollection<ApplicationInsurance> ApplicationInsurances { get; set; }

        public ICollection<ApplicationCancellation> ApplicationCancellations { get; set; }
		public PreApplicationApplicant PreApplicationApplicant { get; set; }

        public  bool IsActive { get; set; }

        public  bool IsDeleted { get; set; }

    }
}