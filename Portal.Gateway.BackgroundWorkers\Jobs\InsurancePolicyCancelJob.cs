﻿using Portal.Gateway.Contracts.Entities.Enums;
using Portal.Gateway.Contracts.Services;
using Portal.Gateway.ExternalServices.Contracts;
using Quartz;

namespace Portal.Gateway.BackgroundWorkers.Jobs
{
    [DisallowConcurrentExecution]

    public class InsurancePolicyCancelJob:IJob
    {
        private readonly ILogger<InsurancePolicyCancelJob> _logger;
        private readonly IAppointmentService _appointmentService;
        private readonly IInsuranceService _insuranceService;

        public InsurancePolicyCancelJob(ILogger<InsurancePolicyCancelJob> logger, IAppointmentService appointmentService, IInsuranceService insuranceService)
        {
            _logger = logger;
            _appointmentService = appointmentService;
            _insuranceService = insuranceService;
        }

        public async Task Execute(IJobExecutionContext context)
        {
            _logger.LogInformation($"{nameof(InsurancePolicyCancelJob)} started at {DateTime.Now} successfully");

            var getInsuranceCancelOrderResponse = await _appointmentService.GetInsuranceCancelOrdersForJobAsync(15);

            if (getInsuranceCancelOrderResponse == null || !getInsuranceCancelOrderResponse.CancelOrders.Any())
            {
                _logger.LogWarning($"Insurance cancel order not found at {DateTime.Now}");
                return;
            }

            foreach (var insuranceCancelOrder in getInsuranceCancelOrderResponse.CancelOrders)
            {
                var finalStatus = (byte)InsuranceCancelOrderStatus.OperationCompleted;
                var insuranceCancelOrderId = 0;

                try
                {
                    insuranceCancelOrderId = insuranceCancelOrder.Id;

                    var result = await _insuranceService.CancelPolicy(
                        new ExternalServices.Models.Insurance.Requests.CancelPolicyServiceRequest()
                        {
                            PolicyNo = insuranceCancelOrder.PolicyNumber,
                            AppointmentId = insuranceCancelOrder.ApplicationId,
                            UserId = insuranceCancelOrder.CreatedBy,
                            ProviderId = insuranceCancelOrder.ProviderId
                        }).ConfigureAwait(false);

                    if (result.ServiceResultType == ServiceResultType.Error)
                    {
                        finalStatus = (byte)InsuranceCancelOrderStatus.FailOnCancelInsurance;

                        _logger.LogWarning($"Insurance cannot be cancelled for application: {insuranceCancelOrder.ApplicationId}, policyNumber : {insuranceCancelOrder.PolicyNumber}, provider : {insuranceCancelOrder.ProviderId} at {DateTime.Now}. error: {result.ErrorMessage}");

                        return;
                    }

                    _logger.LogInformation($"{nameof(InsurancePolicyCancelJob)} stopped at {DateTime.Now} successfully");

                }
                catch (Exception e)
                {
                    finalStatus = (byte)InsuranceCancelOrderStatus.Exception;

                    _logger.LogError($"{nameof(InsurancePolicyCancelJob)} returns exception at {DateTime.Now}, Exception : {e.Message}");
                }
                finally
                {
                    if (insuranceCancelOrderId != 0)
                    {
                        await _appointmentService.UpdateInsuranceCancelOrderStatusAsync(insuranceCancelOrderId, finalStatus, (byte)InsuranceCancellationReason.CancellationOfRejection);
                    }
                }
            }
        }
    }
}
