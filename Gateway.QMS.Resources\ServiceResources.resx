﻿<?xml version="1.0" encoding="utf-8"?>
<root>
  <!-- 
    Microsoft ResX Schema 
    
    Version 2.0
    
    The primary goals of this format is to allow a simple XML format 
    that is mostly human readable. The generation and parsing of the 
    various data types are done through the TypeConverter classes 
    associated with the data types.
    
    Example:
    
    ... ado.net/XML headers & schema ...
    <resheader name="resmimetype">text/microsoft-resx</resheader>
    <resheader name="version">2.0</resheader>
    <resheader name="reader">System.Resources.ResXResourceReader, System.Windows.Forms, ...</resheader>
    <resheader name="writer">System.Resources.ResXResourceWriter, System.Windows.Forms, ...</resheader>
    <data name="Name1"><value>this is my long string</value><comment>this is a comment</comment></data>
    <data name="Color1" type="System.Drawing.Color, System.Drawing">Blue</data>
    <data name="Bitmap1" mimetype="application/x-microsoft.net.object.binary.base64">
        <value>[base64 mime encoded serialized .NET Framework object]</value>
    </data>
    <data name="Icon1" type="System.Drawing.Icon, System.Drawing" mimetype="application/x-microsoft.net.object.bytearray.base64">
        <value>[base64 mime encoded string representing a byte array form of the .NET Framework object]</value>
        <comment>This is a comment</comment>
    </data>
                
    There are any number of "resheader" rows that contain simple 
    name/value pairs.
    
    Each data row contains a name, and value. The row also contains a 
    type or mimetype. Type corresponds to a .NET class that support 
    text/value conversion through the TypeConverter architecture. 
    Classes that don't support this are serialized and stored with the 
    mimetype set.
    
    The mimetype is used for serialized objects, and tells the 
    ResXResourceReader how to depersist the object. This is currently not 
    extensible. For a given mimetype the value must be set accordingly:
    
    Note - application/x-microsoft.net.object.binary.base64 is the format 
    that the ResXResourceWriter will generate, however the reader can 
    read any of the formats listed below.
    
    mimetype: application/x-microsoft.net.object.binary.base64
    value   : The object must be serialized with 
            : System.Runtime.Serialization.Formatters.Binary.BinaryFormatter
            : and then encoded with base64 encoding.
    
    mimetype: application/x-microsoft.net.object.soap.base64
    value   : The object must be serialized with 
            : System.Runtime.Serialization.Formatters.Soap.SoapFormatter
            : and then encoded with base64 encoding.

    mimetype: application/x-microsoft.net.object.bytearray.base64
    value   : The object must be serialized into a byte array 
            : using a System.ComponentModel.TypeConverter
            : and then encoded with base64 encoding.
    -->
  <xsd:schema id="root" xmlns="" xmlns:xsd="http://www.w3.org/2001/XMLSchema" xmlns:msdata="urn:schemas-microsoft-com:xml-msdata">
    <xsd:import namespace="http://www.w3.org/XML/1998/namespace" />
    <xsd:element name="root" msdata:IsDataSet="true">
      <xsd:complexType>
        <xsd:choice maxOccurs="unbounded">
          <xsd:element name="metadata">
            <xsd:complexType>
              <xsd:sequence>
                <xsd:element name="value" type="xsd:string" minOccurs="0" />
              </xsd:sequence>
              <xsd:attribute name="name" use="required" type="xsd:string" />
              <xsd:attribute name="type" type="xsd:string" />
              <xsd:attribute name="mimetype" type="xsd:string" />
              <xsd:attribute ref="xml:space" />
            </xsd:complexType>
          </xsd:element>
          <xsd:element name="assembly">
            <xsd:complexType>
              <xsd:attribute name="alias" type="xsd:string" />
              <xsd:attribute name="name" type="xsd:string" />
            </xsd:complexType>
          </xsd:element>
          <xsd:element name="data">
            <xsd:complexType>
              <xsd:sequence>
                <xsd:element name="value" type="xsd:string" minOccurs="0" msdata:Ordinal="1" />
                <xsd:element name="comment" type="xsd:string" minOccurs="0" msdata:Ordinal="2" />
              </xsd:sequence>
              <xsd:attribute name="name" type="xsd:string" use="required" msdata:Ordinal="1" />
              <xsd:attribute name="type" type="xsd:string" msdata:Ordinal="3" />
              <xsd:attribute name="mimetype" type="xsd:string" msdata:Ordinal="4" />
              <xsd:attribute ref="xml:space" />
            </xsd:complexType>
          </xsd:element>
          <xsd:element name="resheader">
            <xsd:complexType>
              <xsd:sequence>
                <xsd:element name="value" type="xsd:string" minOccurs="0" msdata:Ordinal="1" />
              </xsd:sequence>
              <xsd:attribute name="name" type="xsd:string" use="required" />
            </xsd:complexType>
          </xsd:element>
        </xsd:choice>
      </xsd:complexType>
    </xsd:element>
  </xsd:schema>
  <resheader name="resmimetype">
    <value>text/microsoft-resx</value>
  </resheader>
  <resheader name="version">
    <value>2.0</value>
  </resheader>
  <resheader name="reader">
    <value>System.Resources.ResXResourceReader, System.Windows.Forms, Version=4.0.0.0, Culture=neutral, PublicKeyToken=b77a5c561934e089</value>
  </resheader>
  <resheader name="writer">
    <value>System.Resources.ResXResourceWriter, System.Windows.Forms, Version=4.0.0.0, Culture=neutral, PublicKeyToken=b77a5c561934e089</value>
  </resheader>
  <data name="BRANCH_DEPARTMENT_NOT_FOUND" xml:space="preserve">
    <value>Branch Department not found</value>
  </data>
  <data name="APPLICANT_NOT_FOUND" xml:space="preserve">
    <value>Applicant not found</value>
  </data>
  <data name="APPOINTMENT_NOT_FOUND" xml:space="preserve">
    <value>Appointment not found</value>
  </data>
  <data name="BRANCH_NOT_FOUND" xml:space="preserve">
    <value>Branch not found</value>
  </data>
  <data name="FAILED" xml:space="preserve">
    <value>FAILED</value>
  </data>
  <data name="FAMILY_APPLICANT_COUNT_ERROR" xml:space="preserve">
    <value>The family applicant counts can be more than 1 item</value>
  </data>
  <data name="GROUP_APPLICANT_COUNT_ERROR" xml:space="preserve">
    <value>The group applicant counts can be more than 1 item</value>
  </data>
  <data name="INDIVIDUAL_APPLICANT_COUNT_ERROR" xml:space="preserve">
    <value>The individual applicant counts can not be more than 1 item</value>
  </data>
  <data name="INPUT_ERROR" xml:space="preserve">
    <value>INPUT_ERROR</value>
  </data>
  <data name="INTERNAL_SERVER_ERROR" xml:space="preserve">
    <value>INTERNAL_SERVER_ERROR</value>
  </data>
  <data name="INVALID_INPUT_ERROR" xml:space="preserve">
    <value>Invalid input error</value>
  </data>
  <data name="INVALID_REQUEST" xml:space="preserve">
    <value>Invalid request</value>
  </data>
  <data name="LINE_NOT_FOUND" xml:space="preserve">
    <value>Line not found</value>
  </data>
  <data name="PROPERTY_MAX_LENGTH_ERROR" xml:space="preserve">
    <value>The property {0} can not be more than {1} characters</value>
  </data>
  <data name="PROPERTY_REQUIRED" xml:space="preserve">
    <value>{0} is required</value>
  </data>
  <data name="RESOURCE_ALREADY_REGISTERED" xml:space="preserve">
    <value>Resource is already registered</value>
  </data>
  <data name="RESOURCE_CREATED" xml:space="preserve">
    <value>Resource created</value>
  </data>
  <data name="RESOURCE_DELETED" xml:space="preserve">
    <value>Resource deleted</value>
  </data>
  <data name="RESOURCE_FOUND" xml:space="preserve">
    <value>Resource found</value>
  </data>
  <data name="RESOURCE_NOT_FOUND" xml:space="preserve">
    <value>Resource not found</value>
  </data>
  <data name="RESOURCE_RETRIEVED" xml:space="preserve">
    <value>Resource retrieved</value>
  </data>
  <data name="RESOURCE_UPDATED" xml:space="preserve">
    <value>Resource updated</value>
  </data>
  <data name="SUCCESS" xml:space="preserve">
    <value>SUCCESS</value>
  </data>
  <data name="LINE_DEPARTMENT_NOT_FOUND" xml:space="preserve">
    <value>Line Department Not Found</value>
  </data>
  <data name="GENERATED_TOKEN_NOT_FOUND" xml:space="preserve">
    <value>Generated token not found</value>
  </data>
  <data name="TOKEN_NOT_FOUND" xml:space="preserve">
    <value>Token not found</value>
  </data>
  <data name="APPLICANT_IN_BLACKLIST" xml:space="preserve">
    <value>Applicant in blacklist</value>
  </data>
  <data name="APPLICANT_IN_WHITELIST" xml:space="preserve">
    <value>Applicant in whitelist</value>
  </data>
  <data name="BRANCH_APPLICATION_COUNTRY_NOT_FOUND" xml:space="preserve">
    <value>Branch application country not found</value>
  </data>
  <data name="BRANCH_TRANSLATION_NOT_FOUND" xml:space="preserve">
    <value>Branch translation not found</value>
  </data>
  <data name="DEPARTMENT_NOT_FOUND" xml:space="preserve">
    <value>Department not found</value>
  </data>
  <data name="ORDER_ALREADY_REGISTERED" xml:space="preserve">
    <value>Order is already registered</value>
  </data>
  <data name="GENERATED_TOKEN_NOTES_NOT_FOUND" xml:space="preserve">
    <value>Generated token notes not found</value>
  </data>
  <data name="USER_NOT_FOUND" xml:space="preserve">
    <value>User not found</value>
  </data>
  <data name="COUNTER_NOT_FOUND" xml:space="preserve">
    <value>Counter not found</value>
  </data>
  <data name="APPLICANT_HAS_TICKET_NOT_FOUND" xml:space="preserve">
    <value>Applicant with has ticket not found</value>
  </data>
  <data name="SLOT_NOT_FOUND" xml:space="preserve">
    <value>Slot not found</value>
  </data>
  <data name="AVAILABLE_QUOTA_NOT_FOUND" xml:space="preserve">
    <value>Available quota not found</value>
  </data>
  <data name="ThePassportHasNotYetBeenReceivedAtTheOffice" xml:space="preserve">
    <value>The passport has already been delivered to the applicant.</value>
  </data>
  <data name="COUNTER_BUSY" xml:space="preserve">
    <value>Counter busy</value>
  </data>
  <data name="ThereIsNoPersonWaitingInTheSelectedDepartment" xml:space="preserve">
    <value>There is no person waiting in the selected department</value>
  </data>
  <data name="VipAppointmentTicketsCanOnlyBeGetFromTheVipLine" xml:space="preserve">
    <value>Vip Appointment Tickets Can Only Be Get From The Vip Line</value>
  </data>
  <data name="GENERATED_TOKEN_HISTORY_NOT_FOUND" xml:space="preserve">
    <value>Generated token history not found</value>
  </data>
  <data name="ALL_TICKETS_HAS_HISTORY" xml:space="preserve">
    <value>All tickets used in the qms</value>
  </data>
  <data name="ThereIsNoPersonWaitingOnTheSelectedLine" xml:space="preserve">
    <value>There is no person waiting on the selected line</value>
  </data>
  <data name="SearchOperationNotValidForThisLineDepartment" xml:space="preserve">
    <value>Search operation not valid for this line department</value>
  </data>
  <data name="AlreadyDefinedBefore" xml:space="preserve">
    <value>Already defined before</value>
  </data>
  <data name="InvalidApplicantType" xml:space="preserve">
    <value>Invalid applicant type</value>
  </data>
  <data name="InvalidEmailFormat" xml:space="preserve">
    <value>Invalid email format</value>
  </data>
  <data name="Invalid" xml:space="preserve">
    <value>Invalid</value>
  </data>
  <data name="ApplicantType" xml:space="preserve">
    <value>Applicant type</value>
  </data>
  <data name="AppointmentDate" xml:space="preserve">
    <value>Appointment date</value>
  </data>
  <data name="BranchName" xml:space="preserve">
    <value>Branch name</value>
  </data>
  <data name="FirstName" xml:space="preserve">
    <value>First name</value>
  </data>
  <data name="Gender" xml:space="preserve">
    <value>Gender</value>
  </data>
  <data name="LastName" xml:space="preserve">
    <value>Last name</value>
  </data>
  <data name="Nationality" xml:space="preserve">
    <value>Nationality</value>
  </data>
  <data name="PassportNumber" xml:space="preserve">
    <value>Passport number</value>
  </data>
  <data name="AppointmentDateNotForToday" xml:space="preserve">
    <value>Appointment not for today</value>
  </data>
  <data name="ReferanceNumber" xml:space="preserve">
    <value>Referance number</value>
  </data>
  <data name="BirthDate" xml:space="preserve">
    <value>Birth date</value>
  </data>
  <data name="PassportExpiryDate" xml:space="preserve">
    <value>Passport expiry date</value>
  </data>
  <data name="OngoingProcess" xml:space="preserve">
    <value>Ongoing Process</value>
  </data>
  <data name="PleaseTryAgain" xml:space="preserve">
    <value>Please try again</value>
  </data>
  <data name="Prime" xml:space="preserve">
    <value>Prime</value>
  </data>
  <data name="Vip" xml:space="preserve">
    <value>Vip</value>
  </data>
  <data name="SAME_PROCESS_COUNTER_NOT_FOUND" xml:space="preserve">
    <value>Line same process information not found</value>
  </data>
  <data name="EarlyAppointmentProcess" xml:space="preserve">
    <value>Appointment time is not come yet. The appointment date is:</value>
  </data>
  <data name="LINE_DEPARTMENT_CONNECTION_LIMIT" xml:space="preserve">
    <value>Connection cant be more than 15</value>
  </data>
  <data name="USER_BUSY" xml:space="preserve">
    <value>User busy</value>
  </data>
  <data name="EndwithCannotBeSmallerThanStartwith" xml:space="preserve">
    <value>Endwith cannot be smaller than startwith</value>
  </data>
  <data name="TokenNumberProcessNotFound" xml:space="preserve">
    <value>Token number process not found</value>
  </data>
  <data name="TokenNumberHasReachedUpperLimit" xml:space="preserve">
    <value>The ticket number has reached the upper limit</value>
  </data>
  <data name="ADDING_APPLICANT_WITH_SAME_APPOINTMENT_NUMBER_NOT_ALLOWED" xml:space="preserve">
    <value>It is not allowed to add an applicant with the same appointment number to the same appointment again.</value>
  </data>
  <data name="NO_PROPERTY_FOUND_TO_UPDATE" xml:space="preserve">
    <value>No property found to update</value>
  </data>
  <data name="APPLICATION_NOT_FOUND" xml:space="preserve">
    <value>Application not found</value>
  </data>
  <data name="PRE_CONDITION_FAILED" xml:space="preserve">
    <value>Pre condition failed</value>
  </data>
  <data name="WRONG_BRANCH_SELECTION" xml:space="preserve">
    <value>Application is located in a branch different from the relevant branch</value>
  </data>
  <data name="INDIVIUAL_APPLICANT_COUNT_ERROR" xml:space="preserve">
    <value />
  </data>
  <data name="TOKEN_ALREADY_ADDED_ON_ANOTHER_TOKEN" xml:space="preserve">
    <value>Token already added to another tokens process</value>
  </data>
  <data name="USER_STATE_REFRESH_MESSAGE" xml:space="preserve">
    <value>Token already in progress from another user, please proceed with new token</value>
  </data>
</root>