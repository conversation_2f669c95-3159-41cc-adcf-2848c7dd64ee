﻿@{
    ViewData["Title"] = @SiteResources.Reporting.ToTitleCase();
}

@model FilterReportViewModel

<style>
    [aria-selected='true'] span{
        padding-right: 10px;
    }

    [aria-hidden='true'] span {
        top: -2px;
    }
</style>

<div class="card card-custom card-stretch">
    <div class="card-header">
        <div class="card-title">
            <h3 class="card-label">
                @SiteResources.Reporting.ToTitleCase()
            </h3>
        </div>
        <div class="card-toolbar">
            <div class="btn-group">
            </div>
        </div>
    </div>
    <div class="card-body">
        @if (ViewData.ModelState.Any(x => x.Value.Errors.Any()))
        {
            @foreach (var modelError in Html.ViewData.ModelState.SelectMany(keyValuePair => keyValuePair.Value.Errors))
            {
                <div class="alert alert-custom alert-light-danger fade show my-5" role="alert">
                    <div class="alert-icon"><i class="flaticon-danger"></i></div>
                    <div class="alert-text">
                        <span>@modelError.ErrorMessage</span>
                    </div>
                    <div class="alert-close">
                        <button type="button" class="close" data-dismiss="alert" aria-label="Close">
                            <span aria-hidden="true"><i class="ki ki-close"></i></span>
                        </button>
                    </div>
                </div>
            }
        }
        <form id="formReport" method="post" action="@Url.Action("CreateReport", "Report", new { Area = "Appointment" })">
            <div class="form-group row">
                <div class="col-lg-3 col-md-6">
                    <label class="font-weight-bold">@SiteResources.ReportType.ToTitleCase()</label>
                    @(Html.Kendo().DropDownListFor(m=> m.FilterReportTypeId)
                .HtmlAttributes(new { @class = "form-control" })
                .Filter(FilterType.Contains)
                .OptionLabel(SiteResources.Select)
                .DataTextField("Text")
                .DataValueField("Value")
                .DataSource(source => {
                    source.Read(read =>
                    {
                        read.Action("GetlimitedAuthorizedReportTypeSelectList", "Parameter", new { Area = "" });
                    });
                }))
                </div>
            </div>
            <div class="form-group row">
                <div id="divUser" class="col-lg-3 col-md-6">
                    <label class="font-weight-bold">@SiteResources.UserNameSurname.ToTitleCase()</label>
                    @(Html.Kendo().DropDownListFor(m=> m.FilterUserId)
                    .HtmlAttributes(new { @class = "form-control" })
                    .Filter(FilterType.Contains)
                    .OptionLabel(SiteResources.Select)
                    .DataTextField("Text")
                    .DataValueField("Value")
                    .DataSource(source => {
                        source.Read(read =>
                        {
                            read.Action("GetReportUserSelectList", "Parameter", new { Area = "" });
                        });
                    }))
                </div>
                <div id="divBranch" class="col-lg-3 col-md-6">
                    <label class="font-weight-bold">@SiteResources.BranchName.ToTitleCase()</label>
                    @(Html.Kendo().DropDownListFor(m=> m.FilterBranchId)
                    .HtmlAttributes(new { @class = "form-control" })
                    .Filter(FilterType.Contains)
                    .OptionLabel(SiteResources.Select)
                    .DataTextField("Text")
                    .DataValueField("Value")
                    .DataSource(source => {
                        source.Read(read =>
                        {
                            read.Action("GetReportBranchWithCountrySelectList", "Parameter", new { Area = "" });
                        });
                    }))
                </div>
                <div id="divBranches" class="col-lg-6 col-md-6">
                    <label class="font-weight-bold">@SiteResources.BranchName</label>
                    @(Html.Kendo().MultiSelectFor(m=> m.FilterBranchIds)
                        .HtmlAttributes(new { @class = "form-control" })
                        .Events(events => events.Change("MultiStatusSelectByBranches"))
                        .Filter(FilterType.Contains)
                        .Placeholder(SiteResources.SelectBranches)
                        .AutoClose(false)
                        .DataTextField("Text")
                        .DataValueField("Value")
                        .DataSource(source => {
                            source.Read(read =>
                            {
                                read.Action("GetReportBranchWithCountrySelectList", "Parameter", new { Area = "" }).Data("filterBranches");
                            });
                        }))
                </div>
                <div id="divApplicationStatus" class="col-lg-6 col-md-6">
                    <label class="font-weight-bold">@SiteResources.Status</label>
                    @(Html.Kendo().MultiSelectFor(m=> m.FilterApplicationStatusIds)
                        .HtmlAttributes(new { @class = "form-control" })
                        .Filter(FilterType.Contains)
                        .Placeholder(SiteResources.SelectStatuses)
                        .AutoClose(false)
                        .DataTextField("Text")
                        .DataValueField("Value")
                        .DataSource(source => {
                            source.Read(read =>
                            {
                                read.Action("GetCachedApplicationStatusList", "Parameter", new { Area = "", EncryptedBranchId = "", FilterBranchIds = Model?.FilterBranchIds });
                            });
                        }))
                </div>
                <div id="divBranchApplicationCountry" class="col-lg-3 col-md-6">
                    <label class="font-weight-bold">@SiteResources.BranchApplicationCountry.ToTitleCase()</label>
                    @(Html.Kendo().DropDownListFor(m=> m.FilterBranchApplicationCountryId)
                    .HtmlAttributes(new { @class = "form-control" })
                    .Filter(FilterType.Contains)
                    .OptionLabel(SiteResources.Select)
                    .DataTextField("Text")
                    .DataValueField("Value")
                    .DataSource(source => {
                        source.Read(read =>
                        {
                            read.Action("GetReportBranchApplicationCountrySelectList", "Parameter", new { Area = "" });
                        });
                    }))
                </div>
                <div id="divStartDate" class="col-lg-3 col-md-6">
                    <label class="font-weight-bold">@SiteResources.StartDate.ToTitleCase()</label>
                    @(Html.Kendo().DatePickerFor(m => m.FilterStartDate).Format(SiteResources.DatePickerFormatView).Value(DateTime.Today))
                </div>
                <div id="divEndDate" class="col-lg-3 col-md-6">
                    <label class="font-weight-bold">@SiteResources.EndDate.ToTitleCase()</label>
                    @(Html.Kendo().DatePickerFor(m => m.FilterEndDate).Format(SiteResources.DatePickerFormatView).Value(DateTime.Today))
                </div>
                <div id="divReportDate" class="col-lg-3 col-md-6">
                    <label class="font-weight-bold">@SiteResources.ReportDate.ToTitleCase()</label>
                    @(Html.Kendo().DatePickerFor(m => m.FilterReportDate).Format(SiteResources.DatePickerFormatView).Value(DateTime.Today))
                </div>
            </div>
            <div class="form-group row">
                <div class="col-lg-3 col-md-6">
                    <button type="submit" class="btn btn-primary font-weight-bold">@SiteResources.Create</button>
                </div>
            </div>
        </form>
    </div>
</div>

@section Scripts {
    <partial name="Scripts/_ValidationScripts" />

    <script src="~/js/site.js"></script>
    <script src="~/js/Appointment/Report/report.js"></script>
    <script src="~/js/Appointment/Report/MultiSelectreport.js"></script>

    <script>

        $(function () {
            checkReportFilters();

            $('#FilterReportTypeId').change(function () {
                checkReportFilters();
            });
        });

        function checkReportFilters() {

            $("#divUser").hide();
            $("#divBranch").hide();
            $("#divBranchApplicationCountry").hide();
            $("#divStartDate").hide();
            $("#divEndDate").hide();
            $("#divApplicationStatus").hide();
            $("#divBranches").hide();
            $("#divReportDate").hide();

            $("#FilterUserId").data("kendoDropDownList").select(null);
            $("#FilterBranchId").data("kendoDropDownList").select(null);
            $("#FilterBranchApplicationCountryId").data("kendoDropDownList").select(null);
            $("#FilterStartDate").val('');
            $("#FilterEndDate").val('');

            $('#FilterBranchIds').data('kendoMultiSelect').dataSource.read();
            $('#FilterBranchIds').data('kendoMultiSelect').refresh();

            // Report 1
            if ($("#FilterReportTypeId").val() === '@ReportType.AllStaffApplicationsByBranch.ToInt()') {
                $("#divBranch").show();
                $("#divStartDate").show();
                $("#divEndDate").show();
            }

            // Report 2
            if ($("#FilterReportTypeId").val() === '@ReportType.StaffExtraFeeSales.ToInt()') {
                $("#divUser").show();
                $("#divBranch").show();
                $("#divStartDate").show();
                $("#divEndDate").show();
            }

            // Report 3
            if ($("#FilterReportTypeId").val() === '@ReportType.ScanCycle.ToInt()') {
                $("#divStartDate").show();
                $("#divEndDate").show();
                $("#divApplicationStatus").show();
                $("#divBranches").show();
            }

            // Report 4
            if ($("#FilterReportTypeId").val() === '@ReportType.AllApplications.ToInt()') {
                $("#divBranches").show();
                $("#divStartDate").show();
                $("#divEndDate").show();
            }

            // Report 5
            if ($("#FilterReportTypeId").val() === '@ReportType.DailyBalance.ToInt()') {
                $("#divBranch").show();
                $("#divStartDate").show();
                $("#divEndDate").show();
            }

            // Report 6
            if ($("#FilterReportTypeId").val() === '@ReportType.DeletedApplications.ToInt()') {
                $("#divBranch").show();
                $("#divStartDate").show();
                $("#divEndDate").show();
            }

            // Report 7
            if ($("#FilterReportTypeId").val() === '@ReportType.FreeApplications.ToInt()') {
                $("#divBranch").show();
                $("#divStartDate").show();
                $("#divEndDate").show();
            }

            // Report 8
            if ($("#FilterReportTypeId").val() === '@ReportType.PartiallyRefundedApplications.ToInt()') {
                $("#divBranch").show();
                $("#divStartDate").show();
                $("#divEndDate").show();
            }

            // Report 9
            //if ($("#FilterReportTypeId").val() === 'ReportType.PcrStatus.ToInt()') {
            //    $("#divBranch").show();
            //    $("#divStartDate").show();
            //    $("#divEndDate").show();
            //}

            // Report 10
            //if ($("#FilterReportTypeId").val() === 'ReportType.PcrDaily.ToInt()') {
            //    $("#divBranch").show();
            //}

            // Report 11
            if ($("#FilterReportTypeId").val() === '@ReportType.Photobooth.ToInt()') {
                $("#divBranches").show();
                $("#divStartDate").show();
                $("#divEndDate").show();
            }

            // Report 12
            if ($("#FilterReportTypeId").val() === '@ReportType.InsuranceApplications.ToInt()') {
                $("#divBranch").show();
                $("#divStartDate").show();
                $("#divEndDate").show();
            }

            // Report 13
            if ($("#FilterReportTypeId").val() === '@ReportType.Insurance.ToInt()') {
                $("#divBranch").show();
                $("#divStartDate").show();
                $("#divEndDate").show();
            }

            // Report 14

            // Report 15
            if ($("#FilterReportTypeId").val() === '@ReportType.CashReport_1.ToInt()') {
                $("#divBranches").show();
                $("#divStartDate").show();
                $("#divEndDate").show();
            }

            // Report 16
            if ($("#FilterReportTypeId").val() === '@ReportType.CashReport_2.ToInt()') {
                $("#divBranches").show();
                $("#divStartDate").show();
                $("#divEndDate").show();
            }

            // Report 17
            if ($("#FilterReportTypeId").val() === '@ReportType.NonApplicationInsurance.ToInt()') {
                $("#divBranch").show();
                $("#divStartDate").show();
                $("#divEndDate").show();
            }

            // Report 18
            //if ($("#FilterReportTypeId").val() === 'ReportType.RejectionStatus.ToInt()') {
            //    $("#divBranch").show();
            //    $("#divStartDate").show();
            //    $("#divEndDate").show();
            //}

            // Report 19
            if ($("#FilterReportTypeId").val() === '@ReportType.Detail.ToInt()') {
                $("#divBranch").show();
                $("#divStartDate").show();
                $("#divEndDate").show();
            }

            // Report 20
            if ($("#FilterReportTypeId").val() === '@ReportType.DeliveredToCargo.ToInt()') {
                $("#divBranches").show();
                $("#divStartDate").show();
                $("#divEndDate").show();
            }

            // Report 21
            if ($("#FilterReportTypeId").val() === '@ReportType.Cargo.ToInt()') {
                $("#divBranches").show();
                $("#divStartDate").show();
                $("#divEndDate").show();
            }

            // Report 22
            if ($("#FilterReportTypeId").val() === '@ReportType.CancelCompletedApplications.ToInt()') {
                $("#divBranch").show();
                $("#divStartDate").show();
                $("#divEndDate").show();
            }

            // Report 23
            if ($("#FilterReportTypeId").val() === '@ReportType.ExtraFees.ToInt()') {
                $("#divBranch").show();
                $("#divStartDate").show();
                $("#divEndDate").show();
            }

            // Report 24
            //if ($("#FilterReportTypeId").val() === 'ReportType.PCRCancellation.ToInt()') {
            //    $("#divBranch").show();
            //    $("#divStartDate").show();
            //    $("#divEndDate").show();
            //}

            // Report 25

            // Report 26
            if ($("#FilterReportTypeId").val() === '@ReportType.InsuranceCancellation.ToInt()') {
                $("#divBranches").show();
                $("#divStartDate").show();
                $("#divEndDate").show();
            }

            // Report 27
            //if ($("#FilterReportTypeId").val() === 'ReportType.VisaRejection.ToInt()') {
            //    $("#divBranch").show();
            //    $("#divStartDate").show();
            //    $("#divEndDate").show();
            //}

            // Report 31
            //if ($("#FilterReportTypeId").val() === 'ReportType.PCRGeneral.ToInt()') {
            //    $("#divBranch").show();
            //    $("#divStartDate").show();
            //    $("#divEndDate").show();
            //}

            // Report 32
            //if ($("#FilterReportTypeId").val() === 'ReportType.InsuranceCancellationForRefund.ToInt()') {
            //    $("#divBranch").show();
            //    $("#divStartDate").show();
            //    $("#divEndDate").show();
            //}

            // Report 33
            if ($("#FilterReportTypeId").val() === '@ReportType.InsuranceDetail.ToInt()') {
                $("#divBranches").show();
                $("#divStartDate").show();
                $("#divEndDate").show();
            }

            // Report 34
            if ($("#FilterReportTypeId").val() === '@ReportType.UnInsuredApplications.ToInt()') {
                $("#divBranch").show();
                $("#divStartDate").show();
                $("#divEndDate").show();
            }

            // Report 35
            if ($("#FilterReportTypeId").val() === '@ReportType.Consular.ToInt()') {
                $("#divBranch").show();
                $("#divStartDate").show();
                $("#divEndDate").show();
            }

            // Report 36
            if ($("#FilterReportTypeId").val() === '@ReportType.ApplicationReportOfRejectedPassports.ToInt()') {
                $("#divBranches").show();
                $("#divStartDate").show();
                $("#divEndDate").show();
            }

            // Report 37
            if ($("#FilterReportTypeId").val() === '@ReportType.ApplicationVisaDecisionReport.ToInt()') {
                $("#divBranches").show();
                $("#divStartDate").show();
                $("#divEndDate").show();
            }

            // Report 43
            // if ($("#FilterReportTypeId").val() === '@ReportType.OldCashReport_1.ToInt()') {
            //     $("#divBranches").show();
            //     $("#divStartDate").show();
            //     $("#divEndDate").show();
            // }

            // Report 44
            // if ($("#FilterReportTypeId").val() === '@ReportType.OldCashReport_2.ToInt()') {
            //     $("#divBranches").show();
            //     $("#divStartDate").show();
            //     $("#divEndDate").show();
            // }
            
            // Report 40
            if ($("#FilterReportTypeId").val() === '@ReportType.PostApplicationCommunicationReport.ToInt()') {
                $("#divBranches").show();
                $("#divStartDate").show();
                $("#divEndDate").show();
            }
            // Report 45
            if ($("#FilterReportTypeId").val() === '@ReportType.IndiaCargo.ToInt()') {
                $("#divBranch").show();
                $("#divStartDate").show();
                $("#divEndDate").show();
            }
            // Report 46
            if ($("#FilterReportTypeId").val() === '@ReportType.UpdateApplication.ToInt()') {
                $("#divBranch").show();
                $("#divStartDate").show();
                $("#divEndDate").show();
            }
            // Report 49
            if ($("#FilterReportTypeId").val() === '@ReportType.TurkmenistanConsulateReport.ToInt()') {
                $("#divBranches").show();
                $("#divStartDate").show();
                $("#divEndDate").show();
            }
             // Report 50
            if ($("#FilterReportTypeId").val() === '@ReportType.FinalStatusReport.ToInt()') {
                $("#divStartDate").show();
                $("#divEndDate").show();
                $("#divApplicationStatus").show();
                $("#divBranches").show();
            }
             // Report 51
            if ($("#FilterReportTypeId").val() === '@ReportType.ApplicationDetailedVisaDecisionReport.ToInt()') {
                $("#divBranches").show();
                $("#divStartDate").show();
                $("#divEndDate").show();
            }
            // Report 57
            if ($("#FilterReportTypeId").val() === '@ReportType.IncorrectApplicationStatusReport.ToInt()') {
                $("#divBranch").show();
                $("#divStartDate").show();
                $("#divEndDate").show();
            }
            // QmsReport 1
            if ($("#FilterReportTypeId").val() === '@ExternalReportType.ReportQMSDepartment.ToInt()') {
                $("#divBranch").show();
                $("#divStartDate").show();
                $("#divEndDate").show();
            }
            // QmsReport 2
            if ($("#FilterReportTypeId").val() === '@ExternalReportType.ReportQMS.ToInt()') {
                $("#divBranches").show();
                $("#divStartDate").show();
                $("#divEndDate").show();
            }
            // QmsReport 3
            if ($("#FilterReportTypeId").val() === '@ExternalReportType.ReportQMSAction.ToInt()') {
                $("#divBranch").show();
                $("#divStartDate").show();
                $("#divEndDate").show();
            }
            // QmsReport 4
            if ($("#FilterReportTypeId").val() === '@ExternalReportType.ReportQMSPersonal.ToInt()') {
                $("#divBranch").show();
                $("#divStartDate").show();
                $("#divEndDate").show();
            }
            // Report 56
            if ($("#FilterReportTypeId").val() === '@ReportType.TurkmenistanApplicationStatisticsReport.ToInt()') {
                $("#divStartDate").show();
                $("#divEndDate").show();
            }
            // Report 58
            if ($("#FilterReportTypeId").val() === '@ReportType.ForeignHealthInsuranceReport.ToInt()') {
                $("#divBranches").show();
                $("#divStartDate").show();
                $("#divEndDate").show();
            }
            // Report 59
            // if ($("#FilterReportTypeId").val() === '@ReportType.PendingFileInOfficeReport.ToInt()') {
            //     $("#divBranches").show();
            //     $("#divStartDate").show();
            //     $("#divEndDate").show();
            // }
            // Report 60
            if ($("#FilterReportTypeId").val() === '@ReportType.UpdateInsuranceReport.ToInt()') {
                $("#divBranch").show();
                $("#divStartDate").show();
                $("#divEndDate").show();
            }

            // Report 61
            // if ($("#FilterReportTypeId").val() === '@ReportType.OutscanToEmbassy.ToInt()') {
            //     $("#divStartDate").show();
            //     $("#divEndDate").show();
            //     $("#divBranches").show();
            // }
            // Report 62
            if ($("#FilterReportTypeId").val() === '@ReportType.WorkPermitReferenceNumberReport.ToInt()') {
                $("#divBranches").show();
                $("#divStartDate").show();
                $("#divEndDate").show();
            }
            // Report 63
            if ($("#FilterReportTypeId").val() === '@ReportType.TrafficInsuranceReport.ToInt()') {
                $("#divBranch").show();
                $("#divStartDate").show();
                $("#divEndDate").show();
            }
            // Report 64
            if ($("#FilterReportTypeId").val() === '@ExternalReportType.ReportCargoDeliveredToCourier.ToInt()') {
                $("#divBranches").show();
                $("#divStartDate").show();
                $("#divEndDate").show();
            }

            // Report 65
            if ($("#FilterReportTypeId").val() === '@ReportType.InsuranceDetailSingleSheet.ToInt()') {
                $("#divBranches").show();
                $("#divStartDate").show();
                $("#divEndDate").show();
            }

            // Report 66
            if ($("#FilterReportTypeId").val() === '@ExternalReportType.CourierCheckReport.ToInt()') {
                $("#divBranches").show();
                $("#divStartDate").show();
                $("#divEndDate").show();
            }
            // Report 67
            if ($("#FilterReportTypeId").val() === '@ReportType.AllApplicationsSingleSheet.ToInt()') {
                $("#divBranches").show();
                $("#divStartDate").show();
                $("#divEndDate").show();
            }
            // Report 68
            if ($("#FilterReportTypeId").val() === '@ReportType.IndiaAllApplications.ToInt()') {
                $("#divBranches").show();
                $("#divStartDate").show();
                $("#divEndDate").show();
            }

            // Report 77
            if ($("#FilterReportTypeId").val() === '@ReportType.CashReport_3.ToInt()') {
                $("#divBranches").show();
                $("#divStartDate").show();
                $("#divEndDate").show();
            }

            // Report 78
            if ($("#FilterReportTypeId").val() === '@ReportType.RelatedInsurance.ToInt()') {
                $("#divBranches").show();
                $("#divStartDate").show();
                $("#divEndDate").show();
            }

            // Report 79
            if ($("#FilterReportTypeId").val() === '@ReportType.TurkmenistanApplicationTrackingReport.ToInt()') {
                $("#divStartDate").show();
                $("#divEndDate").show();
            }

            // Report 80
            if ($("#FilterReportTypeId").val() === '@ReportType.ReportFreeInsurance.ToInt()') {
                $("#divBranches").show();
                $("#divStartDate").show();
                $("#divEndDate").show();
            }
        }

        function filterBranches() {
            return {
                reportType: $("#FilterReportTypeId").val()
            };
        }

    </script>
}