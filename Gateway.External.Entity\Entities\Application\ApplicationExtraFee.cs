﻿using System;
using System.Collections.Generic;

namespace Gateway.External.Entity.Entities.Application
{
    public class ApplicationExtraFee
    {
        public ApplicationExtraFee()
        {
            IsActive = true;
            IsDeleted = false;
        }
        public int Id { get; set; }
        public int ApplicationId { get; set; }
        public int ExtraFeeId { get; set; }
        public int Quantity { get; set; }
        public decimal Price { get; set; }
        public decimal Tax { get; set; }
        public int CurrencyId { get; set; }
        public decimal TaxRatio { get; set; }
        public bool? IsRemoved { get; set; }
        public decimal BasePrice { get; set; }
        public decimal ServiceTax { get; set; }
        public bool? IsCancelled { get; set; }
        public int? CancelledBy { get; set; }
        public DateTime? CancelledAt { get; set; }
        public int? RemovedBy { get; set; }
        public DateTime? RemovedAt { get; set; }
        public bool IsCancellationStatus { get; set; }
        public bool Cash { get; set; }
        public bool Pos { get; set; }
        public bool Online { get; set; }
        public bool IsPaymentTypeUpdated { get; set; }
        public bool IsNewPaymentTypeFee { get; set; }
        public int? CreatedBy { get; set; }

        public DateTime? CreatedAt { get; set; }

        public int? UpdatedBy { get; set; }

        public DateTime? UpdatedAt { get; set; }
        public bool IsActive { get; set; }

        public bool IsDeleted { get; set; }

        public int? DeletedBy { get; set; }

        public DateTime? DeletedAt { get; set; }
        public virtual ExtraFee.ExtraFee ExtraFee { get; set; }
    }
}
