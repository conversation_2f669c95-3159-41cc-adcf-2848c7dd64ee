﻿using Microsoft.EntityFrameworkCore;
using Microsoft.EntityFrameworkCore.Metadata.Builders;
using Portal.Gateway.Entity.Entities.Portal;

namespace Portal.Gateway.Entity.EntityConfigurations.Portal
{
    public class CountryChecklistEntityConfiguration : IEntityTypeConfiguration<CountryChecklist>
    {
        public void Configure(EntityTypeBuilder<CountryChecklist> builder)
        {
            builder.ToTable("CountryChecklist");

            builder.HasIndex(e => e.CountryId, "IX_CountryChecklist_CountryId");

            #region StaticAuditableEntityConfig

            builder.HasKey(e => e.Id);
            builder.Property(e => e.Id).IsRequired().ValueGeneratedOnAdd();
            builder.Property(e => e.IsActive).IsRequired();
            builder.Property(e => e.IsDeleted).IsRequired();
            builder.Property(e => e.CreatedBy);
            builder.Property(e => e.CreatedAt).HasColumnType("timestamp without time zone");
            builder.Property(e => e.UpdatedBy);
            builder.Property(e => e.UpdatedAt).HasColumnType("timestamp without time zone");
            builder.Property(e => e.DeletedBy);
            builder.Property(e => e.DeletedAt).HasColumnType("timestamp without time zone");

            #endregion

            builder.Property(e => e.CountryId).IsRequired();
            builder.Property(e => e.MustCheck).IsRequired();


            builder.HasOne(d => d.Country)
                .WithMany(p => p.CountryChecklists)
                .HasForeignKey(d => d.CountryId)
                .OnDelete(DeleteBehavior.ClientSetNull);
        }
    }
}
