﻿using Gateway.Notification.Application.Consumers;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace Portal.Gateway.BackgroundWorkers.Workers
{
    internal class SmsSenderWorker : BackgroundService
    {
        private readonly ILogger<SmsSenderWorker> _logger;
        private readonly SmsSenderConsumer _smsSenderConsumer;
        private readonly IConfiguration _configuration;

        public SmsSenderWorker(ILogger<SmsSenderWorker> logger, SmsSenderConsumer smsSenderConsumer, IConfiguration configuration)
        {
            _logger = logger;
            _smsSenderConsumer = smsSenderConsumer;
            _configuration = configuration;
        }

        protected async override Task ExecuteAsync(CancellationToken stoppingToken)
        {
            if (!Convert.ToBoolean(_configuration["SmsProviders:IsEnabled"]))
            {
                _logger.LogWarning("SmsSender worker has been disabled in this environment.");

                return;
            }
            
            await _smsSenderConsumer.Start();
            _logger.LogInformation("SmsSender worker has been started.");
        }
    }
}
