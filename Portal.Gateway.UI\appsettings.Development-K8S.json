{
  "AppSettings": {
    "PortalGatewayApiUrl": "https://gateway-api-dev-k8s.gateway.com.tr",
    "PortalGatewayApiKey": "Gateway.ApiKey.2021",
    "AppointmentReturnUrl": "https://develop-turkeyvisa.gateway.com.tr/User/Login",
    "GoogleCloudProjectId": "skilful-firefly-315409",
    "GoogleCloudCredentialsUrl": "Credentials/GoogleCloudCredentials.json",
    "Notification": true,
    "QmsChatHubAddress": "https://qms-api-dev-k8s.gateway.com.tr/chathub",
    "PrintDataChatHubAddress": "https://printdata-api-dev-k8s.gateway.com.tr/digitalsignaturehub",
    "IcrReturnUrl": "https://gatewayinternational.com.tr",
    "PortalGatewayUiUrl": "https://develop-turkeyvisa.gateway.com.tr",
    "EnableQuarterCategoryStatsGraph": false,
    "EnableRejectedApplicationInsuranceCase": true,
    "MinioConfiguration": {
      "EndPoint": "visacdn.gateway.com.tr:443",
      "AccessKey": "eGBBZ2ZVzNWCFW5kvmKr",
      "SecretKey": "tnczwQAiKkNpEHIGwXwztZtFZCaT1i2JziBUZXY3",
      "BucketPrefix": "dev-"
    },
    "QMS": {
      "BaseApiUrl": "https://qms-api-dev-k8s.gateway.com.tr"
    },
    "Biometrics": {
      "BaseApiUrl": "https://biometrics-api-dev-k8s.gateway.com.tr"
    },
    "Cargo": {
      "BaseApiUrl": "https://cargo-api-dev-k8s.gateway.com.tr"
    },
    "PrintData": {
      "BaseApiUrl": "https://printdata-api-dev-k8s.gateway.com.tr"
    }
  },
  "elasticsearch": {
    "logindex": "slog-portal",
    "index": "slog-portal",
    "username": "elastic",
    "password": "htJsew49I8jPiL6How7U",
    "url": "https://visaelastic.gateway.com.tr:9200"
  },
  "Logging": {
    "LogLevel": {
      "Default": "Information",
      "Microsoft": "Warning",
      "Microsoft.Hosting.Lifetime": "Information"
    }
  },
  "CacheSettings": {
    "ServiceCallInterval": 1, // minute(s)
    "CacheExpiryTime": 1, // day(s)
    "CacheItems": [
      {
        "CacheKey": "CountryCache",
        "CacheRefreshInterval": 1 // minute(s)
      },
      {
        "CacheKey": "RoleActionCache",
        "CacheRefreshInterval": 1 // minute(s)
      },
      {
        "CacheKey": "BranchCache",
        "CacheRefreshInterval": 1 // minute(s)
      },
      {
        "CacheKey": "ApplicationStatusOrderCache",
        "CacheRefreshInterval": 1 // minute(s)
      },
      {
        "CacheKey": "ForeignCityCache",
        "CacheRefreshInterval": 1 // minute(s)
      },
      {
        "CacheKey": "BranchApplicationStatusCache",
        "CacheRefreshInterval": 1 // minute(s)
      },
      {
        "CacheKey": "ActiveCountriesCache",
        "CacheRefreshInterval": 720 // minute(s)
      },
      {
        "CacheKey": "DepartmentsCache",
        "CacheRefreshInterval": 240 // minute(s)
      },
      {
        "CacheKey": "VisaCategoryCache",
        "CacheRefreshInterval": 30 // minute(s)
      }

    ]
  },
  "Redis": {
    "Url": "***********",
    "Port": "6379",
    "ConnectTimeout": 10000,
    "DefaultDatabase": 7
  },
  "RabbitMq": {
    "Host": "visauatrabbitmq.gateway.com.tr",
    "User": "staging_test",
    "Password": "stagin_test*4mqp",
    "Port": 5672,
    "Exchange": "log.exchange",
    "LoggerQueue": "LoggerQueue",
    "EmailQueue": "EmailQueue",
    "SMSQueue": "SMSQueue"
  },
  "Session": {
    "Distributed": true,
    "IdleTimeout": 1,
    "Authentication": "ldap"
  }
}
