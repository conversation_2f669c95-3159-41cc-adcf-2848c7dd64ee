using AutoMapper;
using Gateway.QMS.Api.Models;
using Gateway.QMS.Api.Models.Dashboard;
using Gateway.QMS.Application.Dashboard;
using Gateway.QMS.Application.Dashboard.Dto;
using Gateway.QMS.Core.Context;
using Gateway.QMS.Resources;
using Microsoft.AspNetCore.Mvc;
using System.Threading.Tasks;

namespace Gateway.QMS.Api.Controllers
{
    [Route("api")]
    public class DashboardController : Controller
    {
        private readonly IContext _context;
        private readonly IDashboardService _dashboardService;
        private readonly IMapper _mapper;

        #region ctor

        public DashboardController(IContext context, IDashboardService dashboardService, IMapper mapper)
        {
            _context = context;
            _dashboardService = dashboardService;
            _mapper = mapper;
        }

        #endregion

        #region Public Methods

        /// <summary>
        /// Retrieves the paginated waiting summary report
        /// </summary>
        /// <param name="request"></param>  
        [HttpPost]
        [Route("dashboard/load-waiting")]
        public async Task<IActionResult> GetWaitingSummary([FromBody] DashboardRequestModel request)
        {
            if (request == null)
                return BadRequest(ApiResponseFactory.BuildBaseApiResponse(ServiceResources.INPUT_ERROR,
                    Resource.GetKey(ServiceResources.INVALID_REQUEST),
                    ServiceResources.INVALID_REQUEST));

            var serviceRequest = _mapper.Map<GetWaitingSummaryRequest>(request);
            serviceRequest.Context = _context;

            var result = await _dashboardService.GetWaitingSummary(serviceRequest);

            return DashboardResponseFactory.GetWaitingResponse(result);
        }

        /// <summary>
        /// Retrieves the paginated weekday summary report
        /// </summary>
        /// <param name="request"></param>  
        [HttpPost]
        [Route("dashboard/load-weekday")]
        public async Task<IActionResult> GetWeekdaySummary([FromBody] DashboardRequestModel request)
        {
            if (request == null)
                return BadRequest(ApiResponseFactory.BuildBaseApiResponse(ServiceResources.INPUT_ERROR,
                    Resource.GetKey(ServiceResources.INVALID_REQUEST),
                    ServiceResources.INVALID_REQUEST));

            var serviceRequest = _mapper.Map<GetWeekdaySummaryRequest>(request);
            serviceRequest.Context = _context;

            var result = await _dashboardService.GetWeekdaySummary(serviceRequest);

            return DashboardResponseFactory.GetWeekdayResponse(result);
        }
        
        /// <summary>
        /// Retrieves the paginated daily summary report
        /// </summary>
        /// <param name="request"></param> 
        [HttpPost]
        [Route("dashboard/load-daily")]
        public async Task<IActionResult> GetDailySummary([FromBody] DashboardRequestModel request)
        {
            if (request == null)
                return BadRequest(ApiResponseFactory.BuildBaseApiResponse(ServiceResources.INPUT_ERROR,
                    Resource.GetKey(ServiceResources.INVALID_REQUEST),
                    ServiceResources.INVALID_REQUEST));

            var serviceRequest = _mapper.Map<GetDailySummaryRequest>(request);
            serviceRequest.Context = _context;

            var result = await _dashboardService.GetDailySummary(serviceRequest);

            return DashboardResponseFactory.GetDailyResponse(result);
        }

        /// <summary>
        /// Retrieves the paginated weekday summary report
        /// </summary>
        /// <param name="request"></param> 
        [HttpPost]
        [Route("dashboard/load-hourly")]
        public async Task<IActionResult> GetHourlySummary([FromBody] DashboardRequestModel request)
        {
            if (request == null)
                return BadRequest(ApiResponseFactory.BuildBaseApiResponse(ServiceResources.INPUT_ERROR,
                    Resource.GetKey(ServiceResources.INVALID_REQUEST),
                    ServiceResources.INVALID_REQUEST));

            var serviceRequest = _mapper.Map<GetHourlySummaryRequest>(request);
            serviceRequest.Context = _context;

            var result = await _dashboardService.GetHourlySummary(serviceRequest);

            return DashboardResponseFactory.GetHourlyResponse(result);
        }

        /// <summary>
        /// Retrieves the Visitor Appointment by their application types
        /// </summary>
        /// <param name="request"></param> 
        [HttpPost]
        [Route("dashboard/load-applicationtype")]
        public async Task<IActionResult> GetAppointmentsSummaryByApplicantType([FromBody] DashboardRequestModel request)
        {
            if (request == null)
                return BadRequest(ApiResponseFactory.BuildBaseApiResponse(ServiceResources.INPUT_ERROR,
                    Resource.GetKey(ServiceResources.INVALID_REQUEST),
                    ServiceResources.INVALID_REQUEST));

            var serviceRequest = _mapper.Map<GetAppointmentsByApplicantTypeSummaryRequest>(request);
            serviceRequest.Context = _context;

            var result = await _dashboardService.GetAppointmentsByApplicantTypeSummary(serviceRequest);

            return DashboardResponseFactory.GetAppointmentsByApplicantTypeSummaryResponse(result);
        }

        /// <summary>
        /// Retrieves the Generated Token Status Summary by their application types
        /// </summary>
        /// <param name="request"></param> 
        [HttpPost]
        [Route("dashboard/load-tokenstatus")]
        public async Task<IActionResult> GetGeneratedTokenStatusSummary([FromBody] DashboardRequestModel request)
        {
            if (request == null)
                return BadRequest(ApiResponseFactory.BuildBaseApiResponse(ServiceResources.INPUT_ERROR,
                    Resource.GetKey(ServiceResources.INVALID_REQUEST),
                    ServiceResources.INVALID_REQUEST));

            var serviceRequest = _mapper.Map<GetGeneratedTokenStatusSummaryRequest>(request);
            serviceRequest.Context = _context;

            var result = await _dashboardService.GetGeneratedTokenStatusSummary(serviceRequest);

            return DashboardResponseFactory.GetGeneratedTokenStatusSummaryResponse(result);
        }

        /// <summary>
        /// Retrieves the Generated Token Status Summary by their application types
        /// </summary>
        /// <param name="request"></param> 
        [HttpPost]
        [Route("dashboard/load-totalapplicants")]
        public async Task<IActionResult> GetTotalApplicantsUsedQmsSummaryByBranches([FromBody] DashboardRequestModel request)
        {
            if (request == null)
                return BadRequest(ApiResponseFactory.BuildBaseApiResponse(ServiceResources.INPUT_ERROR,
                    Resource.GetKey(ServiceResources.INVALID_REQUEST),
                    ServiceResources.INVALID_REQUEST));

            var serviceRequest = _mapper.Map<GetTotalApplicantsUsedQmsByBranchesRequest>(request);
            serviceRequest.Context = _context;

            var result = await _dashboardService.GetTotalApplicantsUsedQmsByBranches(serviceRequest);

            return DashboardResponseFactory.GetTotalApplicantsUsedQmsByBranchesResponse(result);
        }

        /// <summary>
        /// Retrieves the Total applications taken Summary for selected user
        /// </summary>
        /// <param name="request"></param> 
        [HttpPost]
        [Route("dashboard/load-officer-total-application")]
        public async Task<IActionResult> GetTotalApplicationsTakenSummaryByUser([FromBody] DashboardRequestModel request)
        {
            if (request == null)
                return BadRequest(ApiResponseFactory.BuildBaseApiResponse(ServiceResources.INPUT_ERROR,
                    Resource.GetKey(ServiceResources.INVALID_REQUEST),
                    ServiceResources.INVALID_REQUEST));

            var serviceRequest = _mapper.Map<GetTotalApplicationsTakenSummaryByUserRequest>(request);
            serviceRequest.Context = _context;

            var result = await _dashboardService.GetTotalApplicationsTakenSummaryByUser(serviceRequest);

            return DashboardResponseFactory.GetTotalApplicationsTakenSummaryByUserResponse(result);
        }

        /// <summary>
        /// Retrieves the Total applications taken Summary for selected user
        /// </summary>
        /// <param name="request"></param> 
        [HttpPost]
        [Route("dashboard/load-officer-total-actions")]
        public async Task<IActionResult> GetTotalActionsAppliedSummaryByUser([FromBody] DashboardRequestModel request)
        {
            if (request == null)
                return BadRequest(ApiResponseFactory.BuildBaseApiResponse(ServiceResources.INPUT_ERROR,
                    Resource.GetKey(ServiceResources.INVALID_REQUEST),
                    ServiceResources.INVALID_REQUEST));

            var serviceRequest = _mapper.Map<GetTotalActionsAppliedSummaryByUserRequest>(request);
            serviceRequest.Context = _context;

            var result = await _dashboardService.GetTotalActionsAppliedSummaryByUser(serviceRequest);

            return DashboardResponseFactory.GetTotalActionsAppliedSummaryByUserResponse(result);
        }

        /// <summary>
        /// Retrieves the Total applications taken Summary for selected user
        /// </summary>
        /// <param name="request"></param> 
        [HttpPost]
        [Route("dashboard/load-officer-hourly-service-time")]
        public async Task<IActionResult> GetHourlyServiceTimeSummaryByUser([FromBody] DashboardRequestModel request)
        {
            if (request == null)
                return BadRequest(ApiResponseFactory.BuildBaseApiResponse(ServiceResources.INPUT_ERROR,
                    Resource.GetKey(ServiceResources.INVALID_REQUEST),
                    ServiceResources.INVALID_REQUEST));

            var serviceRequest = _mapper.Map<GetHourlyServiceTimeSummaryByUserRequest>(request);
            serviceRequest.Context = _context;

            var result = await _dashboardService.GetHourlyServiceTimeSummaryByUser(serviceRequest);

            return DashboardResponseFactory.GetHourlyServiceTimeSummaryByUserResponse(result);
        }

        #endregion      
    }
}
