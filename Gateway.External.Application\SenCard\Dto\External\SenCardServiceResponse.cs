﻿using System.Text.Json.Serialization;

namespace Gateway.External.Application.SenCard.Dto.External
{
    public class SenCardProvinceServiceResponse
    {
        [JsonPropertyName("ilKodu")]
        public string Code { get; set; }


        [JsonPropertyName("ilAdi")]

        public string Name { get; set; }
    }
    public class SenCardDistrictServiceResponse
    {
        [JsonPropertyName("ilceKodu")]
        public string Code { get; set; }


        [JsonPropertyName("ilceAdi")]

        public string Name { get; set; }
    }
    public class SenCardOrganizationServiceResponse
    {
        [JsonPropertyName("urunAdi")]
        public string ProductName { get; set; }

        [JsonPropertyName("kurumAdi")]
        public string OrganizationName { get; set; }

        [JsonPropertyName("aciklama")]
        public string Description { get; set; }

        [JsonPropertyName("adres")]
        public string Address { get; set; }

        [JsonPropertyName("networkCode")]
        public string NetworkCode { get; set; }

        [Json<PERSON>ropertyName("ilAdi")]
        public string ProvinceName { get; set; }

        [JsonPropertyName("ilceAdi")]
        public string DistrictName { get; set; }

        [JsonPropertyName("kurumTipi")]
        public string InstitutionType { get; set; }

        [JsonPropertyName("telNo")]
        public string PhoneNumber { get; set; }

        [JsonPropertyName("akademikUnvani")]
        public string AcademicTitle { get; set; }

        [JsonPropertyName("uzmanlik")]
        public string Expertise { get; set; }
    }
}
