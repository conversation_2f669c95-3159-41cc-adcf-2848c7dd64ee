﻿using Microsoft.Extensions.Logging;
using Portal.Gateway.Common.Utility.Extensions;
using Portal.Gateway.Contracts.Entities.Dto.Appointment.Application.Requests;
using Portal.Gateway.Contracts.Entities.Dto.DocumentManagement.Requests;
using Portal.Gateway.Contracts.Entities.Enums;
using Portal.Gateway.Contracts.Services;
using Quartz;

namespace Portal.Gateway.BackgroundWorkers.Jobs
{
    [DisallowConcurrentExecution]
    public class AutomaticIhbCreationFailResendJob : IJob
    {
        private readonly ILogger<AutomaticIhbCreationFailResendJob> _logger;
        private readonly IAppointmentService _appointmentService;
        private readonly IDocumentManagementService _documentManagementService;

        public AutomaticIhbCreationFailResendJob(ILogger<AutomaticIhbCreationFailResendJob> logger, IAppointmentService appointmentService, IDocumentManagementService documentManagementService)
        {
            _logger = logger;
            _appointmentService = appointmentService;
            _documentManagementService = documentManagementService;
        }

        public async Task Execute(IJobExecutionContext context)
        {
            _logger.LogInformation($"{nameof(AutomaticIhbCreationFailResendJob)} started at {DateTime.Now} successfully");

            var getAutomaticIhbListResponse = await _appointmentService.GetIhbOrdersFailResendForJobAsync(10);

            if (getAutomaticIhbListResponse == null || !getAutomaticIhbListResponse.IhbOrders.Any())
            {
                _logger.LogWarning($"IHB order not found at {DateTime.Now}");
                return;
            }

            foreach (var ihbOrderData in getAutomaticIhbListResponse.IhbOrders)
            {
                var finalStatus = (byte)AutomaticIhbOrderStatus.OperationCompleted;
                var ihbOrderId = 0;

                try
                {
                    var ihbOrder = ihbOrderData;
                    ihbOrderId = ihbOrder.Id;

                var ihbFile = await _documentManagementService.CreateIhbFileAsync(
                    Path.Combine(AppDomain.CurrentDomain.BaseDirectory, "Assets"), new CreateIhbFileRequestDto()
                    {
                        MainApplicationId = ihbOrder.ApplicationId,
                        IhbNo = ihbOrder.IhbNumber,
                        IsSuitable = ihbOrder.IsSuitable,
                        UserAuditId = ihbOrder.CreatedBy
                    });

                    if (ihbFile == null)
                    {
                        finalStatus = (byte)AutomaticIhbOrderStatus.FailOnCreatingIhb;

                        _logger.LogWarning(
                            $"IHB file cannot created for application {ihbOrder.ApplicationId} at {DateTime.Now}");
                        return;
                    }

                    var addDocument = await _documentManagementService.AddDocumentsAsync(new AddDocumentsRequestDto()
                    {
                        UserAuditId = ihbOrder.CreatedBy,
                        Documents = new List<AddDocumentRequestDto>()
                    {
                        new()
                        {
                            CreatedBy = ihbOrder.CreatedBy,
                            UserAuditId = ihbOrder.CreatedBy,
                            DocumentTypeId = (int)DocumentType.Application,
                            FileContent = ihbFile.FileBytes,
                            FileExtension = ".pdf",
                            FileName = Guid.NewGuid().ToString(),
                            UploadPath = $"{DocumentType.Application}/{ihbOrder.ApplicationId.ToEncrypt()}",
                            ReferenceId = ihbOrder.ApplicationId
                        }
                    }
                    });

                    if (addDocument == null)
                    {
                        finalStatus = (byte)AutomaticIhbOrderStatus.FailOnAddingDocument;

                        _logger.LogWarning(
                            $"IHB file cannot added to minio for {ihbOrder.ApplicationId} at {DateTime.Now}");
                        return;
                    }

                    var addApplicationFile = await
                        _appointmentService.AddApplicationFileAsync(new AddApplicationFileRequestDto()
                        {
                            ApplicationId = ihbOrder.ApplicationId,
                            UserAuditId = ihbOrder.CreatedBy,
                            IsSuitable = ihbOrder.IsSuitable,
                            IhbNo = ihbOrder.IhbNumber,
                            IhbDocumentNumber = ihbOrder.IhbDocumentNumber,
                            DocumentId = addDocument.Ids.First(),
                            FileTypeId = (int)ApplicationFileType.IHB,
                            Note = string.Empty,
                        });

                    if (addApplicationFile == null)
                    {
                        finalStatus = (byte)AutomaticIhbOrderStatus.FailOnAddingFile;

                        _logger.LogWarning(
                            $"file cannot added to ApplicationFile for {ihbOrder.ApplicationId} at {DateTime.Now}");
                        return;
                    }

                    var scanSycleUpdate = await
                        _appointmentService.UpdateSelectedApplicationStatusForApplicationFile(
                            new UpdateSelectedApplicationStatusForApplicationFileRequestDto()
                            {
                                ApplicationId = ihbOrder.ApplicationId,
                                ApplicationStatusId = (int)ApplicationStatusType.IHBUploaded,
                                UserId = ihbOrder.CreatedBy
                            });

                    if (scanSycleUpdate == null)
                    {
                        finalStatus = (byte)AutomaticIhbOrderStatus.FailOnScanSycle;

                        _logger.LogWarning(
                            $"Scan Sycle Status cannot updated for {ihbOrder.ApplicationId} at {DateTime.Now}");
                        return;
                    }

                    _logger.LogInformation($"{nameof(AutomaticIhbCreationFailResendJob)} stopped at {DateTime.Now} successfully");

                }
                catch (Exception e)
                {
                    finalStatus = (byte)AutomaticIhbOrderStatus.Exception;

                    _logger.LogError(
                        $"{nameof(AutomaticIhbCreationFailResendJob)} returns exception at {DateTime.Now}, Exception : {e.Message}");
                }
                finally
                {
                    if (ihbOrderId != 0)
                    {
                        await _appointmentService.UpdateIhbOrderStatusAsync(ihbOrderId, finalStatus);
                    }
                }
            }
        }
    }
}
