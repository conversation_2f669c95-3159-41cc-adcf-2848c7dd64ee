﻿using AutoMapper;
using Gateway.External.Application.Extensions;
using Gateway.External.Application.SenCard.Dto;
using Gateway.External.Application.SenCard.Dto.External;
using Gateway.External.Application.SenCard.Dto.Request;
using Gateway.External.Application.SenCard.Dto.Response;
using Gateway.External.Application.SenCard.Validator;
using Gateway.External.Resources;
using Gateway.Http;
using Gateway.Validation;
using Microsoft.Extensions.Configuration;
using System.Collections.Generic;
using System.Linq;
using System.Net;
using System.Threading.Tasks;
using static Gateway.External.Application.Enums.Enums;

namespace Gateway.External.Application.SenCard
{
    public class SenCardService(IConfiguration configuration, IValidationService validationService, IMapper mapper)
        : ISenCardService
    {
        public WebHeaderCollection Header = new();
        public SenCardEndpointRouting Routing = new();

        public async Task<GetSenCardProvinceResult> GetSenCardProvince(SenCardProvinceRequest request)
        {
            var baseAddress = configuration.GetValue<string>("SenCard:Url") + Routing.GetProvinces;

            var response = await RestHttpClient.Create().Get<List<SenCardProvinceServiceResponse>>(baseAddress, Header).ConfigureAwait(false);

            if (response == null || !response.Any())
            {
                return new GetSenCardProvinceResult()
                {
                    Message = ServiceResources.RESOURCE_NOT_FOUND,
                    Status = SenCardResultStatus.NotFound
                };
            }

            return new GetSenCardProvinceResult()
            {
                Message = ServiceResources.RESOURCE_RETRIEVED,
                Status = SenCardResultStatus.Successful,
                Provinces = response.Select(s => new SenCardProvinceDto()
                {
                    Code = s.Code,
                    Name = s.Name.Trim()
                }).ToList()
            };
        }

        public async Task<GetSenCardInstitutionResult> GetSenCardInstitution(SenCardProvinceRequest request)
        {
            var allInstitutions = EnumExtensions.GetAll<InstitutionType>().ToList();

            if (!allInstitutions.Any())
            {
                return new GetSenCardInstitutionResult()
                {
                    Message = ServiceResources.RESOURCE_NOT_FOUND,
                    Status = SenCardResultStatus.NotFound
                };
            }

            return new GetSenCardInstitutionResult
            {
                Message = ServiceResources.RESOURCE_RETRIEVED,
                Status = SenCardResultStatus.Successful,
                Institutions = allInstitutions.Select(s => new SenCardInstitutionDto()
                {
                    Code = s.Code,
                    Name = s.Name.Trim()
                }).ToList()
            };
        }

        public async Task<GetSenCardDistrictResult> GetSenCardDistrict(SenCardDistrictRequest request)
        {
            var validationResult = validationService.Validate(typeof(GetSenCardDistrictValidator), request);

            if (!validationResult.IsValid)
                return new GetSenCardDistrictResult
                {
                    Status = SenCardResultStatus.InvalidInput,
                    Message = ServiceResources.INVALID_INPUT_ERROR,
                    ValidationMessages = validationResult.ErrorMessages
                };

            var baseAddress = configuration.GetValue<string>("SenCard:Url") + Routing.GetDistricts
                .Replace("{ilId}", request.ProvinceId);

            var response = await RestHttpClient.Create().Get<List<SenCardDistrictServiceResponse>>(baseAddress, Header).ConfigureAwait(false);

            if (response == null || !response.Any())
            {
                return new GetSenCardDistrictResult()
                {
                    Message = ServiceResources.RESOURCE_NOT_FOUND,
                    Status = SenCardResultStatus.NotFound
                };
            }

            return new GetSenCardDistrictResult()
            {
                Message = ServiceResources.RESOURCE_RETRIEVED,
                Status = SenCardResultStatus.Successful,
                Districts = response.Select(s => new SenCardDistrictDto()
                {
                    Code = s.Code,
                    Name = s.Name.Trim()
                }).ToList()
            };
        }

        public async Task<GetSenCardOrganizationResult> GetSenCardOrganization(SenCardOrganizationRequest request)
        {
            var validationResult = validationService.Validate(typeof(GetSenCardOrganizationValidator), request);

            if (!validationResult.IsValid)
                return new GetSenCardOrganizationResult
                {
                    Status = SenCardResultStatus.InvalidInput,
                    Message = ServiceResources.INVALID_INPUT_ERROR,
                    ValidationMessages = validationResult.ErrorMessages
                };

            var baseAddress = configuration.GetValue<string>("SenCard:Url") + Routing.GetOrganizations
                .Replace("{ilId}", request.ProvinceId)
                .Replace("{ilceId}", request.DistrictId)
                .Replace("{kurumTipi}", request.InstitutionId);

            var response = await RestHttpClient.Create().Get<List<SenCardOrganizationServiceResponse>>(baseAddress, Header).ConfigureAwait(false);

            if (response == null || !response.Any())
            {
                return new GetSenCardOrganizationResult()
                {
                    Message = ServiceResources.RESOURCE_NOT_FOUND,
                    Status = SenCardResultStatus.NotFound
                };
            }

            return new GetSenCardOrganizationResult()
            {
                Message = ServiceResources.RESOURCE_RETRIEVED,
                Status = SenCardResultStatus.Successful,
                Organizations = mapper.Map<List<SenCardOrganizationDto>>(response)
            };
        }
    }
}
