﻿using System.Collections.Generic;
using System.ComponentModel.DataAnnotations.Schema;

namespace Portal.Gateway.Entity.Entities.Portal
{
    public class Country
    {
        public Country()
        {
            Agencies = new HashSet<Agency>();
            AgencyTypeFiles = new HashSet<AgencyTypeFile>();
            ApplicationDatas = new HashSet<ApplicationData>();
            ApplicationDataContacts = new HashSet<ApplicationDataContact>();
            ApplicationDataDemographicsCountry = new HashSet<ApplicationDataDemographic>();
            ApplicationDataDemographicsNationality = new HashSet<ApplicationDataDemographic>();
            ApplicationVisaHistories = new HashSet<ApplicationVisaHistory>();
            Applications = new HashSet<Application>();
            BlackLists = new HashSet<BlackList>();
            BranchApplicationCountries = new HashSet<BranchApplicationCountry>();
            Branches = new HashSet<Branch>();
            Cities = new HashSet<City>();
            Companies = new HashSet<Company>();
            Customers = new HashSet<Customer>();
            ForeignCities = new HashSet<ForeignCity>();
            LookupRules = new HashSet<LookupRule>();
            VasTypeCosts = new HashSet<VasTypeCost>();
            PreApplicationApplicants = new HashSet<PreApplicationApplicant>();
            CountryChecklists = new HashSet<CountryChecklist>();
        }
        public int Id { get; set; }
        public string Name { get; set; }
        public string ISO2 { get; set; }
        public string ISO3 { get; set; }
        public string CallingCode { get; set; }
        public string NameTr { get; set; }
        public string NameAr { get; set; }
        public string NameTm { get; set; }
        public string NameRu { get; set; }
        public string NameFr { get; set; }

        public virtual UnicoBranchCountry UnicoBranchCountry { get; set; }
        public virtual ICollection<Agency> Agencies { get; set; }
        public virtual ICollection<AgencyTypeFile> AgencyTypeFiles { get; set; }
        public virtual ICollection<ApplicationData> ApplicationDatas { get; set; }
        public virtual ICollection<ApplicationDataContact> ApplicationDataContacts { get; set; }
        public virtual ICollection<ApplicationDataDemographic> ApplicationDataDemographicsCountry { get; set; }
        public virtual ICollection<ApplicationDataDemographic> ApplicationDataDemographicsNationality { get; set; }
        public virtual ICollection<ApplicationVisaHistory> ApplicationVisaHistories { get; set; }
        public virtual ICollection<Application> Applications { get; set; }
        public virtual ICollection<BlackList> BlackLists { get; set; }
        public virtual ICollection<BranchApplicationCountry> BranchApplicationCountries { get; set; }
        public virtual ICollection<Branch> Branches { get; set; }
        public virtual ICollection<City> Cities { get; set; }
        public virtual ICollection<Company> Companies { get; set; }
        public virtual ICollection<Customer> Customers { get; set; }
        public virtual ICollection<ForeignCity> ForeignCities { get; set; }
        public virtual ICollection<LookupRule> LookupRules { get; set; }
        public virtual ICollection<VasTypeCost> VasTypeCosts { get; set; }
        public virtual ICollection<PreApplicationApplicant> PreApplicationApplicants { get; set; }
        public virtual ICollection<CountryChecklist> CountryChecklists { get; set; }

    }
}
