﻿using BarcodeStandard;
using SkiaSharp;

namespace Gateway.Barcode
{
    public class BarcodeClient
    {
        public static int DefaultWidth => 2400;
        public static int DefaultHeight => 800;
        public static int PhotoboothHeight => 480;
        public static int ICRHeight => 750;

        private static SKFont GetStandardFont(int width)
        {
            var fontSize = width / 32f;

            return new SKFont
            {
                Typeface = SKTypeface.FromFamilyName("Microsoft Sans Serif", SKFontStyle.Bold),
                Size = fontSize * (96f / 72),
            };
        }

        public static byte[] CreateBarcodeByteArray(string value, SKColor foreColor, int width, int height)
        {
            var barcode = new BarcodeStandard.Barcode
            {
                IncludeLabel = true,
                Alignment = AlignmentPositions.Center,
                Width = width,
                Height = height,
                // RotateFlipType = RotateFlipType.RotateNoneFlipNone,
                BackColor = SKColors.White,
                ForeColor = foreColor,
                LabelFont = BarcodeClient.GetStandardFont(width),
            };
            var barcodeImage = barcode.Encode(BarcodeStandard.Type.Code128B, value);

            return barcodeImage.Encode().ToArray();
        }

        public static byte[] CreateBarcodeByteArray(string value)
        {
            return CreateBarcodeByteArray(value, SKColors.Black, DefaultWidth, DefaultHeight);
        }
    }
}
