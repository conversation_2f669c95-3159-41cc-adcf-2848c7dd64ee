﻿using Microsoft.EntityFrameworkCore.Migrations;

#nullable disable

namespace Portal.Gateway.Entity.EFMigrationsHistory.PortalDb
{
    public partial class AddColumnCancellnsuranceOrder : Migration
    {
        protected override void Up(MigrationBuilder migrationBuilder)
        {
            migrationBuilder.AddColumn<int>(
                name: "RejectedPolicyType",
                table: "CancelInsuranceOrder",
                type: "integer",
                nullable: false,
                defaultValue: 0);
        }

        protected override void Down(MigrationBuilder migrationBuilder)
        {
            migrationBuilder.DropColumn(
                name: "RejectedPolicyType",
                table: "CancelInsuranceOrder");
        }
    }
}
