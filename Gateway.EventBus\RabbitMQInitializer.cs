﻿using Gateway.Extensions;
using Microsoft.Extensions.Configuration;
using RabbitMQ.Client;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace Gateway.EventBus
{
    public class RabbitMQInitializer
    {
        private readonly IConfiguration _configuration;

        public RabbitMQInitializer(IConfiguration configuration)
        {
            _configuration = configuration;
        }

        public async Task Initialize()
        {
            var factory = new ConnectionFactory()
            {
                HostName = _configuration["RabbitMq:Host"],
                UserName = _configuration["RabbitMq:User"],
                Password = _configuration["RabbitMq:Password"]
            };

            var loggerQueueName = _configuration["RabbitMq:LoggerQueue"].AddEnvironmentSuffix();
            var loggerExchangeName = _configuration["RabbitMq:Exchange"].AddEnvironmentSuffix();
            var emailQueueName = _configuration["RabbitMq:EmailQueue"].AddEnvironmentSuffix();
            var smsQueueName = _configuration["RabbitMq:SMSQueue"].AddEnvironmentSuffix();

            using var connection = await factory.CreateConnectionAsync();
            using var channel = await connection.CreateChannelAsync();

            await CreateQueueAndExchangeAsync(channel, loggerQueueName, loggerExchangeName);

            await channel.QueueDeclareAsync(queue: emailQueueName, durable: true, exclusive: false, autoDelete: false, arguments: null);

            await channel.QueueDeclareAsync(queue: smsQueueName, durable: true, exclusive: false, autoDelete: false,
                arguments: new Dictionary<string, object>
                {
                    { "x-max-priority", 10 }
                });
        }

        private async Task CreateQueueAndExchangeAsync(IChannel channel, string queueName, string exchangeName)
        {
            await channel.ExchangeDeclareAsync(exchange: exchangeName, type: ExchangeType.Direct, durable: true);

            await channel.QueueDeclareAsync(queue: queueName,
                                 durable: true,
                                 exclusive: false,
                                 autoDelete: false,
                                 arguments: null);

            await channel.QueueBindAsync(queue: queueName,
                              exchange: exchangeName,
                              routingKey: null);
        }
    }
}
