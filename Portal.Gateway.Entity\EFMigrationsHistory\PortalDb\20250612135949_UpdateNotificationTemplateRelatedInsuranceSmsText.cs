﻿using Microsoft.EntityFrameworkCore.Migrations;

#nullable disable

namespace Portal.Gateway.Entity.EFMigrationsHistory.PortalDb
{
    /// <inheritdoc />
    public partial class UpdateNotificationTemplateRelatedInsuranceSmsText : Migration
    {
        /// <inheritdoc />
        protected override void Up(MigrationBuilder migrationBuilder)
        {
            var seedName = "20250612165934_Up";
            migrationBuilder.Sql(Portal.Gateway.Entity.Context.PortalDbContext.SeedCustomDbScripts(seedName));
        }

        /// <inheritdoc />
        protected override void Down(MigrationBuilder migrationBuilder)
        {
            var seedName = "20250612165934_Down";
            migrationBuilder.Sql(Portal.Gateway.Entity.Context.PortalDbContext.SeedCustomDbScripts(seedName));
        }
    }
}
