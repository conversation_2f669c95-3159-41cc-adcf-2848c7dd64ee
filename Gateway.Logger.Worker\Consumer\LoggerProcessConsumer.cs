﻿using Gateway.EventBus.Subscribers;
using Gateway.Logger.Worker.Handler;
using System.Threading.Tasks;
using Gateway.Logger.Worker.Handler.Dto;
using Microsoft.Extensions.Configuration;
using Gateway.Extensions;

namespace Gateway.Logger.Worker.Consumer
{
    public class LoggerProcessConsumer : BaseConsumer
    {
        private readonly IMessageSubscriber _messageSubscriber;
        private readonly ILoggerCollectorHandler _loggerCollectorHandler;
        private readonly IConfiguration _configuration;

        public LoggerProcessConsumer(IMessageSubscriber messageSubscriber,
            ILoggerCollectorHandler loggerCollectorHandler,
            IConfiguration configuration)
        {
            _messageSubscriber = messageSubscriber;
            _loggerCollectorHandler = loggerCollectorHandler;
            _configuration = configuration;
        }

        public override void Start()
        {
            var queueName = _configuration["RabbitMq:LoggerQueue"].AddEnvironmentSuffix();

            _messageSubscriber.SubscribeMessage<LoggerCollectorRequest>(queueName,false,(msg, _) =>
            {
                _loggerCollectorHandler.CollectLogger(msg);
                return Task.CompletedTask;
            });
        }
    }
}