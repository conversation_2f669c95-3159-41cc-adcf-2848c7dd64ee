﻿using EmaaLossClaimService;
using EmaaService;
using Gateway.Extensions;
using Microsoft.Extensions.Logging;
using Microsoft.Extensions.Options;
using Nest;
using Portal.Gateway.Common.LoggerMiddleware.Extensions;
using Portal.Gateway.Common.Utility;
using System.Xml.Linq;
using System.Linq;
using Portal.Gateway.Contracts.Services;
using Portal.Gateway.Contracts.Entities.Dto.Appointment.Insurance;
using Portal.Gateway.Contracts.Settings.AppSettings;
using Serilog;
using System;
using System.IO;
using System.Net.Http;
using System.Text;
using System.Threading.Tasks;
using System.Xml;
using System.Xml.Serialization;
using ILogger = Serilog.ILogger;

namespace Portal.Gateway.ExternalServices.Integrations.Emaa
{
    public abstract class BaseService<TService>
    {
        private readonly ILogger<TService> _logger;
        private readonly IElasticClient _elasticClient;
        private readonly IAppointmentService _appointmentService;
        private static readonly ILogger Logger = Log.ForContext<BaseService<TService>>();

        public BaseService(
            IOptions<IntegrationSettings> integrationSettings, ILogger<TService> logger, IElasticClient elasticClient, IAppointmentService appointmentService)
        {
            _integrationSettings = integrationSettings.Value;
            _logger = logger;
            _elasticClient = elasticClient;
            _client = new SFSPolicyIntegrationServiceSoapClient(SFSPolicyIntegrationServiceSoapClient.EndpointConfiguration.SFSPolicyIntegrationServiceSoap12, _integrationSettings.Emaa.ServiceUrl);
            _claimLossClient = new FileOperationsClient(FileOperationsClient.EndpointConfiguration.BasicHttpBinding_FileOperations, _integrationSettings.Emaa.LossClaimServiceUrl);
            _client.Endpoint.AddPortalEndpointBehavior(logger, elasticClient);
            _appointmentService = appointmentService;
        }

        private readonly IntegrationSettings _integrationSettings;

        protected SFSPolicyIntegrationServiceSoapClient _client;
        protected FileOperationsClient _claimLossClient;

        public virtual async Task CloseAsync() { }

        public virtual void Abort() { }

        public async Task<TResult> SendAsync<TRequest, TResult>(TRequest request, int applicationId, int userId, string requestTag, string responseTag)
        {
            var content = Envelope(request, requestTag);
            String sendServiceUrl = _integrationSettings.Emaa.ServiceUrl;
            var message = LogExtensions.LogExternalRequest(_elasticClient, new Uri(sendServiceUrl), content, requestTag);

            var RequestProccess = "Create & Certificate Service Request";
            Logger.Information($"CancelInsuranceServiceLogRequest => ApplicationId:{applicationId.ToString()}, Process:{RequestProccess}, ServiceLog:{message.Message.ToString()}, CreatedBy:{userId}");

            var response = await SendInsuranceRequest(content, applicationId);

            LogExtensions.LogExternalResponse(_elasticClient, response, message, requestTag);

            var ResponseProccess = "Create & Certificate Service Response";
            Logger.Information($"CancelInsuranceServiceLogResponse => ApplicationId:{applicationId.ToString()}, Process:{ResponseProccess}, ServiceLog:{response.ToString()}, CreatedBy:{userId}");

            var resultXDoc = XDocument.Parse(response);

            XNamespace ns = "http://tempuri.org/";

            var unwrappedResponse = resultXDoc.Descendants(ns + responseTag).First();

            var oXmlSerializer = new XmlSerializer(typeof(TResult));

            var result = (TResult)oXmlSerializer.Deserialize(unwrappedResponse.CreateReader());

            return result;
        }
        public async Task<TResult> SendCancelAsync<TRequest, TResult>(TRequest request, int applicationId, int userId, string requestTag, string responseTag)
        {
            var content = Envelope(request, requestTag);
            String sendServiceUrl = _integrationSettings.Emaa.ServiceUrl;
            var message = LogExtensions.LogExternalRequest(_elasticClient, new Uri(sendServiceUrl), content, requestTag);

            var RequestProccess = "Cancel Service Request";
            Logger.Information($"CancelInsuranceServiceLogRequest => ApplicationId:{applicationId.ToString()}, Process:{RequestProccess}, ServiceLog:{message.Message.ToString()}, CreatedBy:{userId}");

            var response = await SendInsuranceRequest(content, applicationId);

            LogExtensions.LogExternalResponse(_elasticClient, response, message, requestTag);

            var ResponseProccess = "Cancel Service Response";
            Logger.Information($"CancelInsuranceServiceLogResponse => ApplicationId:{applicationId.ToString()}, Process:{ResponseProccess}, ServiceLog:{response.ToString()}, CreatedBy:{userId}");

            var resultXDoc = XDocument.Parse(response);

            XNamespace ns = "http://tempuri.org/";

            var unwrappedResponse = resultXDoc.Descendants(ns + responseTag).First();

            var oXmlSerializer = new XmlSerializer(typeof(TResult));

            var result = (TResult)oXmlSerializer.Deserialize(unwrappedResponse.CreateReader());

            return result;
        }

        public async Task<TResult> SendClaimLossAsync<TRequest, TResult>(TRequest request, string requestTag, string responseTag)
        {
            var content = ClaimEnvelope(request, requestTag);
            String sendLossClaimServiceUrl = _integrationSettings.Emaa.LossClaimServiceUrl;

            var message = LogExtensions.LogExternalRequest(_elasticClient, new Uri(sendLossClaimServiceUrl), content, requestTag);

            Logger.Information($"content: {content.ToJson()}");

            var response = await SendLossClaimRequest(content);

            LogExtensions.LogExternalResponse(_elasticClient, response, message, requestTag);

            var resultXDoc = XDocument.Parse(response);

            XNamespace ns = "http://schemas.xmlsoap.org/soap/envelope/";

            var unwrappedResponse = resultXDoc.Descendants(ns + responseTag).First();

            var oXmlSerializer = new XmlSerializer(typeof(TResult));

            var result = (TResult)oXmlSerializer.Deserialize(unwrappedResponse.CreateReader());

            return result;
        }
        public async Task<TResult> SendClaimLossExcelAsync<TRequest, TResult>(TRequest request, string requestTag, string responseTag)
        {
            var content = ClaimEnvelope(request, requestTag);

            var message = LogExtensions.LogExternalRequest(_elasticClient, new Uri(_integrationSettings.Emaa.ServiceUrl), content, requestTag);

            var response = await SendLossClaimRequest(content);

            LogExtensions.LogExternalResponse(_elasticClient, response, message, requestTag);

            var resultXDoc = XDocument.Parse(response);

            XNamespace ns = "http://tempuri.org/";

            var unwrappedResponse = resultXDoc.Descendants(ns + responseTag).First();

            var oXmlSerializer = new XmlSerializer(typeof(TResult));

            var result = (TResult)oXmlSerializer.Deserialize(unwrappedResponse.CreateReader());

            return result;
        }
        private static string Envelope(object request, string tag)
        {
            var xmlDoc = new XmlDocument();

            var xmlSerializer = new XmlSerializer(request.GetType());

            var requestEnvelope = "<soapenv:Envelope xmlns:soapenv=\"http://schemas.xmlsoap.org/soap/envelope/\" xmlns:tem=\"http://tempuri.org/\"> " +
                                  "<soapenv:Header/>" +
                                  "<soapenv:Body>" +
                                  "<tem:" + tag + ">" +
                                  "<tem:sRequestXml>";

            using (var xmlStream = new MemoryStream())
            {
                xmlSerializer.Serialize(xmlStream, request);

                xmlStream.Position = 0;

                xmlDoc.Load(xmlStream);

                requestEnvelope += "<![CDATA[" + xmlDoc.ChildNodes[1].InnerXml + "]]>";
            }

            requestEnvelope += "</tem:sRequestXml>" +
                               "</tem:" + tag + ">" +
                               "</soapenv:Body>" +
                               "</soapenv:Envelope>";

            return requestEnvelope;
        }
        private static string ClaimEnvelope(object request, string tag)
        {
            var xmlDoc = new XmlDocument();

            var xmlSerializer = new XmlSerializer(request.GetType());
            var nameSpace = new XmlSerializerNamespaces();
            nameSpace.Add("sfs", "http://schemas.datacontract.org/2004/07/SFS.Services.Claim.Library");
            var requestEnvelope = "<soapenv:Envelope xmlns:soapenv=\"http://schemas.xmlsoap.org/soap/envelope/\" xmlns:tem=\"http://tempuri.org/\" xmlns:sfs=\"http://schemas.datacontract.org/2004/07/SFS.Services.Claim.Library\"> " +
            "<soapenv:Header/>" +
                                  "<soapenv:Body>" +
                                  "<tem:" + tag + ">" + "<tem:value>";

            using (var xmlStream = new MemoryStream())
            {
                xmlSerializer.Serialize(xmlStream, request, nameSpace);

                xmlStream.Position = 0;

                xmlDoc.Load(xmlStream);

                requestEnvelope += xmlDoc.ChildNodes[1].InnerXml;

            }

            requestEnvelope += "</tem:value>" + "</tem:" + tag + ">" +
                               "</soapenv:Body>" +
                               "</soapenv:Envelope>";

            return requestEnvelope;
        }

        private async Task<string?> SendInsuranceRequest(string content, int applicationId)
        {
            string? response;
            var requestMessage = new HttpRequestMessage
            {
                Method = HttpMethod.Post,
                RequestUri = new Uri(_integrationSettings.Emaa.ServiceUrl),
                Content = new StringContent(
                    content,
                    Encoding.UTF8,
                    "text/xml")
            };

            requestMessage.Headers.Add("X-Request-Id", applicationId.ToString() + "-" + DateTime.Now.ToString("dd/MM/yyyy HH:mm"));

            try
            {
                response = await PortalHttpClientHelper.SendAsync(requestMessage);
            }
            catch (HttpRequestException ex)
            {
                _logger.LogError(ex, $"Send request to {_integrationSettings.Emaa.ServiceUrl} failed.");

                requestMessage.RequestUri = new Uri(_integrationSettings.Emaa.ServiceDisasteryUrl);
                response = await PortalHttpClientHelper.SendAsync(requestMessage);
            }

            return response;
        }

        private async Task<string?> SendLossClaimRequest(string content)
        {
            string? response;
            var requestMessage = new HttpRequestMessage
            {
                Method = HttpMethod.Post,
                RequestUri = new Uri(_integrationSettings.Emaa.LossClaimServiceUrl),
                Content = new StringContent(
                    content,
                    Encoding.UTF8,
                    "text/xml")
            };

            requestMessage.Headers.Add("SOAPAction", "http://tempuri.org/FileOperations/claimEntry");

            try
            {
                response = await PortalHttpClientHelper.SendAsync(requestMessage);
            }
            catch (HttpRequestException ex)
            {
                _logger.LogError(ex, $"Send request to {_integrationSettings.Emaa.LossClaimServiceUrl} failed.");

                if (_integrationSettings.Emaa.LossClaimServiceDisasteryUrl == _integrationSettings.Emaa.LossClaimServiceUrl)
                {
                    throw;
                }

                requestMessage.RequestUri = new Uri(_integrationSettings.Emaa.LossClaimServiceDisasteryUrl);
                response = await PortalHttpClientHelper.SendAsync(requestMessage);
            }

            return response;
        }
    }
}
