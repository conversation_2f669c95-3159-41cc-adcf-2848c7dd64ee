﻿using System;
using System.Collections.Generic;
using System.Text;
using System.Threading.Tasks;
using Microsoft.Extensions.Primitives;
using Portal.Gateway.ApiModel.Requests.Insurance;
using Portal.Gateway.ApiModel.Requests.Insurance.EmaaHasarServisExcel;
using Portal.Gateway.ApiModel.Responses.Insurance;
using Portal.Gateway.Contracts.Entities.Enums;
using Portal.Gateway.Contracts.Services;
using Portal.Gateway.ExternalServices.Helpers;
using Portal.Gateway.ExternalServices.Models.Insurance.Requests;
using Portal.Gateway.ExternalServices.Models.Insurance.Response;

namespace Portal.Gateway.ExternalServices
{
    public partial class InsuranceService
    {
        private async Task<ServiceResult<CreatePolicyServiceResponse>> Emaa_CreatePolicy(CreatePolicyServiceRequest request)
        {
            try
            {
                var result = await Process<PolCreatePolicyServiceResponse>(
                                        () => _polService.CreatePolicy(EmaaHelper.GetCreatePolicyRequest(request, _integrationSettings.Emaa.UserName, _integrationSettings.Emaa.Password),request.AppointmentId,request.UserId)
                                    ).ConfigureAwait(false);

                if (result.ServiceResultType == ServiceResultType.Error)
                    return this.GetServiceResult<CreatePolicyServiceResponse>(result.ErrorMessage);

                if (result.Result.CreatePolicyResult.Response.Result == 0)
                    return this.GetServiceResult<CreatePolicyServiceResponse>($"{result.Result.CreatePolicyResult.Response.Error}-{result.Result.CreatePolicyResult.Response.ErrorDetail}");

                // Return
                return new ServiceResult<CreatePolicyServiceResponse>()
                {
                    ServiceResultType = ServiceResultType.Success,
                    Result = new CreatePolicyServiceResponse()
                    {
                        ProviderId = _integrationSettings.Emaa.Id,
                        CustomerNo = string.Empty,
                        PolicyNumber = result.Result.CreatePolicyResult.Response.CreatePolicyData.SfsProductionSchema.Master.PolicyNumber
                    }
                };
            }

            finally
            {

            }
        }

        private async Task<ServiceResult<CancelPolicyServiceResponse>> Emaa_CancelPolicy(CancelPolicyServiceRequest request)
        {
            try
            {
                var result = await Process<PolCancelPolicyServiceResponse>(
                                        () => _polService.CancelPolicy(EmaaHelper.GetCancelPolicyRequest(request, _integrationSettings.Emaa.UserName, _integrationSettings.Emaa.Password),request.AppointmentId,request.UserId)
                                    ).ConfigureAwait(false);

                if (result.ServiceResultType == ServiceResultType.Error)
                    return this.GetServiceResult<CancelPolicyServiceResponse>(result.ErrorMessage);

                if (result.Result.CancellationFromInceptionResult.Response.Result == 0)
                    return this.GetServiceResult<CancelPolicyServiceResponse>($"{result.Result.CancellationFromInceptionResult.Response.Error}-{result.Result.CancellationFromInceptionResult.Response.ErrorDetail}");

                // Return
                return new ServiceResult<CancelPolicyServiceResponse>()
                {
                    ServiceResultType = ServiceResultType.Success
                };
            }

            finally
            {

            }
        }

        private async Task<ServiceResult<CertificatePolicyServiceResponse>> Emaa_CerficatePolicy(CertificatePolicyServiceRequest request)
        {
            try
            {
                var result = await Process<PolPrintDocumentServiceResponse>(
                                        () => _polService.PrintDocument(EmaaHelper.GetPolPrintDocumentServiceRequest(request, _integrationSettings.Emaa.UserName, _integrationSettings.Emaa.Password),request.AppointmentId,request.UserId)
                                    ).ConfigureAwait(false);

                if (result.ServiceResultType == ServiceResultType.Error)
                    return this.GetServiceResult<CertificatePolicyServiceResponse>(result.ErrorMessage);

                if (result.Result.PrintDocumentResult.Response.Result == 0)
                    return this.GetServiceResult<CertificatePolicyServiceResponse>($"{result.Result.PrintDocumentResult.Response.Error}-{result.Result.PrintDocumentResult.Response.ErrorDetail}");

                return new ServiceResult<CertificatePolicyServiceResponse>()
                {
                    ServiceResultType = ServiceResultType.Success,
                    Result = new CertificatePolicyServiceResponse()
                    {
                        Certificate = Encoding.ASCII.GetBytes(result.Result.PrintDocumentResult.Response.PrintDocumentData.PolicyPrintOut.File)
                    }
                };
            }

            finally
            {

            }
        }

        private async Task<ServiceResult<ClaimEntryPolicyApiResponse>> Emaa_ClaimLossEntry(ClaimEntryServiceRequest request)
        {
            var result = await Process<PolClaimEntryServiceResponse>(
                         () => _polService.ClaimLossEntry(EmaaHelper.GetClaimEntryRequest(request, _integrationSettings.Emaa.UserName, _integrationSettings.Emaa.Password))
                     ).ConfigureAwait(false);

            if (result.ServiceResultType == ServiceResultType.Error)
                return this.GetServiceResult<ClaimEntryPolicyApiResponse>(result.ErrorMessage);


            if (result.Result.Fault != null)
            {
                StringBuilder errorMessageBld = new StringBuilder();

                for (int i = 0; i < result.Result.Fault.Detail.ServiceException.FailureList.Failure.Count; i++)
                {
                    errorMessageBld.Append(result.Result.Fault.Detail.ServiceException.FailureList.Failure[i].Message.ToString());
                    errorMessageBld.Append(' ');
                }
                errorMessageBld.Append(result.Result.Fault.Faultstring?.Text);

                return new ServiceResult<ClaimEntryPolicyApiResponse>()
                {
                    ServiceResultType = ServiceResultType.Success,
                    Result = new ClaimEntryPolicyApiResponse()
                    {
                        ClaimNo = "",
                        Message = errorMessageBld.ToString()

                    },
                    ErrorMessage = errorMessageBld.ToString()
                };
            }
            else
            {
                return new ServiceResult<ClaimEntryPolicyApiResponse>()
                {
                    ServiceResultType = ServiceResultType.Success,
                    Result = new ClaimEntryPolicyApiResponse()
                    {
                        ClaimNo = result.Result.ClaimEntryResponse.ClaimEntryResult.ClaimNo,
                        CustomerRepresentative = result.Result.ClaimEntryResponse.ClaimEntryResult.CustomerRepresentative,
                        ExpertNo = result.Result.ClaimEntryResponse.ClaimEntryResult.ExpertNo,
                        IsOperationCompletedSuccessfully = result.Result.ClaimEntryResponse.ClaimEntryResult.IsOperationCompletedSuccessfully,
                        Message = result.Result.ClaimEntryResponse.ClaimEntryResult.Message,
                        ProcessRespUserName = result.Result.ClaimEntryResponse.ClaimEntryResult.ProcessRespUserName,
                        SuffererNo = result.Result.ClaimEntryResponse.ClaimEntryResult.SuffererNo,
                        TransactionNo = result.Result.ClaimEntryResponse.ClaimEntryResult.TransactionNo
                    }
                };
            }
        }

		private async Task<ServiceResult<CreatePolicyServiceResponse>> Emaa_TsCreatePolicy(CreatePolicyServiceRequest request)
		{
			try
			{
				var result = await Process<PolCreatePolicyServiceResponse>(
										() => _polService.TsCreatePolicy(EmaaHelper.GetTsCreatePolicyRequest(request, _integrationSettings.Emaa.UserName, _integrationSettings.Emaa.Password),request.AppointmentId,request.UserId)
									).ConfigureAwait(false);

				if (result.ServiceResultType == ServiceResultType.Error)
					return this.GetServiceResult<CreatePolicyServiceResponse>(result.ErrorMessage);

				if (result.Result.CreatePolicyResult.Response.Result == 0)
					return this.GetServiceResult<CreatePolicyServiceResponse>($"{result.Result.CreatePolicyResult.Response.Error}-{result.Result.CreatePolicyResult.Response.ErrorDetail}");

				// Return
				return new ServiceResult<CreatePolicyServiceResponse>()
				{
					ServiceResultType = ServiceResultType.Success,
					Result = new CreatePolicyServiceResponse()
					{
						ProviderId = request.ProviderId,
						CustomerNo = string.Empty,
						PolicyNumber = result.Result.CreatePolicyResult.Response.CreatePolicyData.SfsProductionSchema.Master.PolicyNumber
					}
				};
			}

			finally
			{

			}
		}

		private async Task<ServiceResult<CertificatePolicyServiceResponse>> Emaa_TsCerficatePolicy(CertificatePolicyServiceRequest request)
		{
			try
			{
				var result = await Process<PolPrintDocumentServiceResponse>(
										() => _polService.PrintDocument(EmaaHelper.GetTsPolPrintDocumentServiceRequest(request, _integrationSettings.Emaa.UserName, _integrationSettings.Emaa.Password),request.AppointmentId,request.UserId)
									).ConfigureAwait(false);

				if (result.ServiceResultType == ServiceResultType.Error)
					return this.GetServiceResult<CertificatePolicyServiceResponse>(result.ErrorMessage);

				if (result.Result.PrintDocumentResult.Response.Result == 0)
					return this.GetServiceResult<CertificatePolicyServiceResponse>($"{result.Result.PrintDocumentResult.Response.Error}-{result.Result.PrintDocumentResult.Response.ErrorDetail}");

				return new ServiceResult<CertificatePolicyServiceResponse>()
				{
					ServiceResultType = ServiceResultType.Success,
					Result = new CertificatePolicyServiceResponse()
					{
						Certificate = Encoding.ASCII.GetBytes(result.Result.PrintDocumentResult.Response.PrintDocumentData.PolicyPrintOut.File)
					}
				};
			}

			finally
			{

			}
		}
        
        private async Task<ServiceResult<CreatePolicyServiceResponse>> Emaa_RiCreatePolicy(CreatePolicyServiceRequest request)
        {
            try
            {
                var result = await Process<PolCreatePolicyServiceResponse>(
                    () => _polService.CreatePolicy(EmaaHelper.GetRiCreatePolicyRequest(request, _integrationSettings.Emaa.UserName, _integrationSettings.Emaa.Password),request.AppointmentId,request.UserId)
                ).ConfigureAwait(false);

                if (result.ServiceResultType == ServiceResultType.Error)
                    return this.GetServiceResult<CreatePolicyServiceResponse>(result.ErrorMessage);

                if (result.Result.CreatePolicyResult.Response.Result == 0)
                    return this.GetServiceResult<CreatePolicyServiceResponse>($"{result.Result.CreatePolicyResult.Response.Error}-{result.Result.CreatePolicyResult.Response.ErrorDetail}");

                // Return
                return new ServiceResult<CreatePolicyServiceResponse>()
                {
                    ServiceResultType = ServiceResultType.Success,
                    Result = new CreatePolicyServiceResponse()
                    {
                        ProviderId = _integrationSettings.Emaa.Id,
                        CustomerNo = string.Empty,
                        PolicyNumber = result.Result.CreatePolicyResult.Response.CreatePolicyData.SfsProductionSchema.Master.PolicyNumber,
                        DocumentId = result.Result.CreatePolicyResult.Response.CreatePolicyData.SfsProductionSchema.Master.DocumentId
                    }
                };
            }

            finally
            {

            }
        }

	}

}

