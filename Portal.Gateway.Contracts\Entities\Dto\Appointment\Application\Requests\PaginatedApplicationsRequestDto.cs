﻿using System;
using System.Collections.Generic;

namespace Portal.Gateway.Contracts.Entities.Dto.Appointment.Application.Requests
{
    public class PaginatedApplicationsRequestDto : BaseRequestDto
    {
        public PaginatedApplicationsRequestDto()
        {
            Pagination = new PaginationRequestDto();
        }

        public string ApplicationNumber { get; set; }

        public int? ApplicationId { get; set; }

        public IList<int> BranchIds { get; set; }

        public IList<int> CountryIds { get; set; }

        public int? CountryId { get; set; }

		public int? FilterResidingCountryId { get; set; }

		public int? FilterVerificationTypeId { get; set; }

		public int? ApplicantTypeId { get; set; }

        public int? LocalAuthorityStatusId { get; set; }

        public int? AgencyId { get; set; }

        public int? CustomerId { get; set; }

        public IList<int> ApplicationTypeIds { get; set; }

        public string Name { get; set; }

        public string Surname { get; set; }

        public DateTime? BirthDate { get; set; }

        public string MaidenName { get; set; }

        public string FatherName { get; set; }

        public string MotherName { get; set; }

        public string PassportNumber { get; set; }

        public string NationalityName { get; set; }

        public int? NationalityId { get; set; }

        public string Email { get; set; }

        public string PhoneNumber { get; set; }

        public string PhoneNumber1 { get; set; }

        public int? VisaCategoryId { get; set; }

        public int? ApplicationStatusId { get; set; }
        public IList<int?> ApplicationStatusIds { get; set; }

        public IList<int> ApplicationStatusHistoryStatusIds { get; set; }

        public DateTimeOffset? StartDate { get; set; }

        public DateTimeOffset? EndDate { get; set; }

        public bool IncludeApplicationStatusHistories { get; set; }

        public bool IngonreCancelledApplications { get; set; }

        public bool AllowPassiveDeletedApplications { get; set; }
        public bool FilterIsTurkmenistanPage { get; set; }

        public DateTime? FilterStartDate { get; set; }

        public DateTime? FilterEndDate { get; set; }

        public DateTime? ApplicationDate { get; set; }

        public DateTime? RejectionStartDate { get; set; }

        public DateTime? RejectionEndDate { get; set; }
        public int? FilterResidenceApplication { get; set; }

        public PaginationRequestDto Pagination { get; set; }

        public bool isRefundable { get; set; }

        public int? DocumentWaitingTimeId { get; set; }
        public DateTime? EvaluationDate { get; set; }
        public bool TurkmenPageAllList { get; set; }
        public bool FilterSuitable { get; set; }
        public bool FilterNotSuitable { get; set; }
        public bool FilterWaiting { get; set; }
        public bool IgnoreEarlyApplications { get; set; }
        public DateTime? FilterBirthDate { get; set; }
    }
}
