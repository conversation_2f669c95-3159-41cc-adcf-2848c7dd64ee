﻿using System;

namespace Gateway.Logger.Worker.Provider.Dto
{
    public class LogRequest
    {
        public DateTime Timestamp { get; set; }
        public string Level { get; set; }
        public string RenderedMessage { get; set; }
        public string Exception { get; set; }
        public Properties Properties { get; set; }

        public LogRequest()
        {
            Properties = new Properties();
        }
    }
    
    public class Properties
    {
        public string SourceContext { get; set; }
        public string ActionId { get; set; }
        public string ActionName { get; set; }
        public string RequestId { get; set; }
        public string SpanId { get; set; }
        public string TraceId { get; set; }
        public string ParentId { get; set; }
        public string ConnectionId { get; set; }
        public string Instance { get; set; }
        public DateTime DateTime { get; set; }
        public string ApplicationName { get; set; }
        public string EnvironmentName { get; set; }
        public string ClientIp { get; set; }
        public string UserAgent { get; set; }
    }
}