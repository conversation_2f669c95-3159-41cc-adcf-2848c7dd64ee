﻿using System;
using System.Collections.Generic;
using Portal.Gateway.Contracts.Entities.Enums;

namespace Portal.Gateway.Contracts.Entities.Dto.Appointment.Application
{
    public class ApplicationDto
    {
        public int Id { get; set; }

        public int? PreApplicationApplicantId { get; set; }

        public bool IsVip { get; set; }

        public List<string> RelationalApplications { get; set; }

		public bool IsMainApplicant { get; set; }

		public bool IsApplicationUpdateAllowed { get; set; }

        public string? ExtraVisaFeeName { get; set; }

        public string? VisaFeeName { get; set; }

        public string? ExtraGatewayServiceFeeName { get; set; }

        public string? GatewayServiceFeeName { get; set; }
		public int? GatewayServiceFeeCurrencyId { get; set; }

		public string? ExtraVisaPrice { get; set; }

        public decimal? VisaPrice { get; set; }

		public string? ExtraGatewayServicePrice { get; set; }

        public decimal? GatewayServicePrice { get; set; }

		public bool IsCargoIntegrationExists { get; set; }

		public decimal TimeZoneOffset { get; set; }

        public bool HasApplicationNotes { get; set; }
        public string? PolicyName { get; set; }
        public DateTimeOffset? PolicyEndDate { get; set; }
        public int? PolicyPeriod { get; set; }
        public bool IsCancelledInsurance { get; set; }
        public int? VisaCategoryId { get; set; }

        public DateTimeOffset? ArrivalDate { get; set; }

        public bool IsSapApplicationOrderExists { get; set; }

        public bool HasApplicationFile { get; set; }

        public bool HasApplicationApprovalExist { get; set; }

        public IEnumerable<ApplicationRejectionApprovalHistory> RejectionApprovalHistories { get; set; }

        public class ApplicationRejectionApprovalHistory
        {
            public string Code { get; set; }
            public int CreatedBy { get; set; }
            public DateTime CreatedAt { get; set; }
            public bool IsApprovalCompleted { get; set; }
            public int NotificationTypeId { get; set; }
        }

        #region Application

        public int BranchApplicationCountryId { get; set; }

        public int BranchApplicationCountry { get; set; }

        public int BranchId { get; set; }

        public string BranchName { get; set; }

        public int BranchSmsProviderId { get; set; }
        public int BranchEmailProviderId { get; set; }
        public string BranchTelephone { get; set; }
        public string BranchAddress { get; set; }
        public string BranchCountryNameTr { get; set; }
        public string BranchCountryCode { get; set; }
        public string BranchCountryIso3 { get; set; }
		public byte? CargoProviderId { get; set; }

		public string BranchCityName { get; set; }

        public int CountryId { get; set; }

        public string CountryName { get; set; }

		public string ResidingCountryName { get; set; }

        public bool ShowPaymentMethods { get; set; }

        public int ApplicantTypeId { get; set; }

        public int? ApplicantCount { get; set; }

        public int ApplicationTypeId { get; set; }

        public string PassportNumber { get; set; }

        public DateTime? PassportExpireDate { get; set; }

        public int ApplicationPassportStatusId { get; set; }

        public int? CustomerId { get; set; }

        public int? AgencyId { get; set; }

        public string AgencyName { get; set; }

        public bool? IsAllowDeniedPassport { get; set; }

        public string? Reason { get; set; }

        public int TitleId { get; set; }

        public string Name { get; set; }

        public string Surname { get; set; }

        public DateTime BirthDate { get; set; }
        public string EmailAdress { get; set; }
        public string GsmPhone { get; set; }
        public int GenderId { get; set; }

        public int? MaritalStatusId { get; set; }

        public int NationalityId { get; set; }

        public string Nationality { get; set; }

        public string NationalityIso2 { get; set; }

        public string ResidenceNumber { get; set; }

        public string MaidenName { get; set; }

        public string FatherName { get; set; }

        public string MotherName { get; set; }

        public string Email { get; set; }

        public string PhoneNumber1 { get; set; }

        public string PhoneNumber2 { get; set; }

        public string Address { get; set; }

        public string City { get; set; }
        public int? ForeignCityId { get; set; }
        public string PostalCode { get; set; }
        public string NameOfSecondContactPerson { get; set; }

        public string AreaId { get; set; }
        public string AreaName { get; set; }
        public string GovernorateId { get; set; }
        public string GovernorateName { get; set; }
        public bool? IsCargoIntegrationSelected { get; set; }
        public bool IsCargoIntegrationActive { get; set; }
        public bool ShowCityDropdown { get; set; }

        public int ApplicationStatusId { get; set; }

        public string ApplicationStatus { get; set; }

        public DateTimeOffset ApplicationTime { get; set; }

        public string Note { get; set; }
        public string ApplicationNote { get; set; }

        public IList<string> ApplicationNotes { get; set; }
        public IList<string> ApplicationNoteTimes { get; set; }

        public int? RelationalApplicationId { get; set; }

        public int? RelationShipId { get; set; }

        public int? LocalAuthorityStatusId { get; set; }

        public int CreatedBy { get; set; }

        public string CreatedByNameSurname { get; set; }

        public int StatusId { get; set; }

        public bool IsActive { get; set; }

        public bool IsDeleted { get; set; }

        public bool RejectionReturnStatementFileUpload { get; set; }
        public bool RejectionDataPageFileUpload { get; set; }
        public bool RejectionPassportFileUpload { get; set; }
        public bool IsInquiryActive { get; set; }
        public string IhbDocumentNumber { get; set; }
        public string IhbNumber { get; set; }
        public DateTime? EvaluationTime { get; set; }
        public int? IHBStatusId { get; set; }
        public string EncryptedIhbDocumentId { get; set; }
        public string EncryptedIhbOrderId { get; set; }

        public bool? IsInterviewRequired { get; set; }

        public bool? IsInterviewDone { get; set; }
        public bool? PrintAll { get; set; } = false;
        public bool? HasPolicyRefundBlock { get; set; }
        public DateTime? PolicyRefundBlockDate { get; set; }
        public int? PolicyRefundBlockType { get; set; }

        public bool? IsContactInformationVerified { get; set; }
        #endregion

        #region ApplicationDocument

        public ApplicationDocument Document { get; set; }

        public class ApplicationDocument
        {
            public int? TotalYearInCountry { get; set; }

            public int? ReimbursementTypeId { get; set; }

            public string ReimbursementSponsorDetail { get; set; }

            public string Job { get; set; }
           
            public int? OccupationId { get; set; }
            public string CompanyName { get; set; }

            public int? TotalYearInCompany { get; set; }

            public int? MonthlySalary { get; set; }

            public int? MonthlySalaryCurrencyId { get; set; }

            public bool HasBankAccount { get; set; }

            public int? BankBalance { get; set; }

            public int? BankBalanceCurrencyId { get; set; }

            public bool HasDeed { get; set; }

            public int VisaCategoryId { get; set; }

            public int? AdditionalServiceTypeId { get; set; }

            public int? NumberOfEntryId { get; set; }

            public bool HasEntryBan { get; set; }

            public DateTime EntryDate { get; set; }

            public DateTime ExitDate { get; set; }

            public string CityName { get; set; }
            public string AccomodationDetail { get; set; }

            public bool HasRelativeAbroad { get; set; }

            public string RelativeLocation { get; set; }

            public string PersonTravelWith { get; set; }

            public bool PersonTravelWithHasVisa { get; set; }
			public bool ProvidedWithHasRelatedInsurance { get; set; }

			public bool ApplicationTogether { get; set; }
			public int? ApplicationTogetherFiftyYearCount { get; set; }
			public int? ApplicationTogetherFifteenYearCount { get; set; }
			public bool? HasPersonVisitedTurkeyBefore { get; set; }
            public int? VehicleTypeId { get; set; }
            public int? BrandModelId { get; set; }
            public string BrandCode { get; set; }

            public string PlateNo { get; set; }

            public int? ModelYear { get; set; }
            public string ChassisNumber { get; set; }
			public string BrandText { get; set; }
			public string ModelText { get; set; }
			public int DocumentId { get; set; }
            public int ResidenceApplication { get; set; }

            public bool? ResidenceApplicationToBeMade
            {
                get { return ResidenceApplication switch { (int)YesNoQuestion.Yes => true, (int)YesNoQuestion.No => false, (int)YesNoQuestion.Unspecified => null, _ => null }; }
            }
        }

        #endregion

        #region ApplicationInsurance

        public ApplicationInsurance? Insurance { get; set; }
        public ApplicationInsurance? AllInsurance { get; set; }

        public class ApplicationInsurance
        {
            public List<string> Number { get; set; }
            public DateTime? StartDate { get; set; }
			public DateTime? EndDate { get; set; }
			public int ProviderId { get; set; }
			public List<int> RelatedIndividualInsuraneId { get; set; }
		}

		#endregion

		#region ApplicationVisaHistory

		public IList<ApplicationVisaHistory> VisaHistories { get; set; }

        public class ApplicationVisaHistory
        {
            public int ApplicationVisaHistoryId { get; set; }

            public int CountryId { get; set; }

            public string CountryName { get; set; }

            public DateTime? FromDate { get; set; }

            public DateTime? UntilDate { get; set; }

            public bool? IsUsed { get; set; }
            public int? NumberOfEntryId { get; set; }

            public int? VisaIsUsedYear { get; set; }

            public int? OldVisaDecisionId { get; set; }
        }

        #endregion

        #region ApplicationExtraFee

        public IList<ApplicationExtraFee> ExtraFees { get; set; }

        public class ApplicationExtraFee
        {
            public int ApplicationExtraFeeId { get; set; }

            public int ExtraFeeId { get; set; }

            public int TypeId { get; set; }

            public string ExtraFeeName { get; set; }
            
            public string ExtraFeeNameFr { get; set; }

            public decimal Price { get; set; }

            public decimal Tax { get; set; }

            public decimal TaxRatio { get; set; }

            public int CurrencyId { get; set; }

            public int Quantity { get; set; }
            public int? PolicyPeriod { get; set; }

            public int Category { get; set; }

            public bool ShowInSummary { get; set; }

            public bool IsCancellationStatus { get; set; }

            public bool Cash { get; set; }

            public bool Pos { get; set; }

            public bool Online { get; set; }

			public string PaymentMethod { get; set; } = string.Empty;
		}

		#endregion

		#region ApplicationStatusHistory

		public IEnumerable<ApplicationStatusHistory> StatusHistories { get; set; }

        public class ApplicationStatusHistory
        {
            public int Order { get; set; }

            public int ApplicationStatusId { get; set; }

            public string Status { get; set; }

            public string NameSurname { get; set; }

            public DateTimeOffset StatusDate { get; set; }

            public bool IsApplicationStatusHidden { get; set; }
        }

        #endregion

        #region ApplicationHistory

        public IList<ApplicationHistory> ApplicationHistories { get; set; }

        public class ApplicationHistory
        {
            public int ApplicationHistoryId { get; set; }

            public string PropertyName { get; set; }

            public string PreviousValue { get; set; }

            public string CurrentValue { get; set; }

            public int CreatedBy { get; set; }

            public string CreatedByNameSurname { get; set; }

            public DateTimeOffset CreatedAt { get; set; }
        }

        #endregion

        #region EmaaLossClaim

        public string ClaimNo { get; set; }

		#endregion

		#region SAP
		public bool SendVisaRejection { get; set; }

        #endregion

        #region Inquiry
        public IList<Inquiry> Inquiries { get; set; }

        public class Inquiry
        {
            public int Id { get; set; }
            public int BranchId { get; set; }
            public string Name { get; set; }
            public string Observation { get; set; }
            public List<InquiryQuestion> InquiryQuestions { get; set; }
        }

        public class InquiryQuestion
        {
            public int Id { get; set; }
            public string Name { get; set; }
            public int QuestionTypeId { get; set; }
            public bool IsRequired { get; set; }
            public bool IsMultiSelectable { get; set; }
            public int ColSpan { get; set; }
            public int DescriptionColSpan { get; set; }
            public bool IsDescriptionIncluded { get; set; }
            public InquiryElectiveAnswerDto ElectiveAnswer { get; set; }
            public List<InquiryExplanationAnswerDto> ExplanationAnswers { get; set; }
            public List<InquiryQuestionChoice> Choices { get; set; }
        }

        public class InquiryElectiveAnswerDto
        {
            public List<int>? Ids { get; set; }
            public string Description { get; set; }
        }

        public class InquiryExplanationAnswerDto
        {
            public int Id { get; set; }
            public string Explanation { get; set; }
        }

        public class InquiryQuestionChoice
        {
            public int Id { get; set; }
            public int ChoiceTypeId { get; set; }
            public int ColSpan { get; set; }
            public bool IsChecked { get; set; }
            public string Name { get; set; }
        }
        #endregion

    }
}