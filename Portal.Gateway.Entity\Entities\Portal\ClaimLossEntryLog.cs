﻿using System;

namespace Portal.Gateway.Entity.Entities.Portal
{
    public class ClaimLossEntryLog : BaseEntity
    {
        public ClaimLossEntryLog()
        {
            IsActive = true;
            IsDeleted = false;
        }
        public int Id { get; set; }
        public int ApplicationId { get; set; }
        public string ServiceResponse { get; set; }
        public string ClaimNo { get; set; }
        public int CreatedBy { get; set; }
        public DateTimeOffset CreatedAt { get; set; }
        public int? UpdatedBy { get; set; }
        public DateTime? UpdatedAt { get; set; }
        public string ClaimAmount { get; set; }
        public virtual Application Application { get; set; }
        public virtual User User { get; set; }
        public int? Status { get; set; }
        public DateTime? NextAttemptDateTime { get; set; }
        public int? RetryCount { get; set; }
    }
}
