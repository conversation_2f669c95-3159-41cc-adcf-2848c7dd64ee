﻿using Gateway.Cargo.Application.Shipment.Validator;
using Gateway.Cargo.Dto.Request;
using Gateway.Cargo.Dto.Response;
using Gateway.Cargo.Persistence;
using Gateway.Cargo.Resources;
using Gateway.Validation;
using Microsoft.EntityFrameworkCore;
using Serilog;
using System;
using System.Linq;
using System.Threading.Tasks;
using Microsoft.Extensions.Configuration;

namespace Gateway.Cargo.Application.Shipment
{
    public class ShipmentService : IShipmentService
    {
        private readonly IValidationService _validationService;
        private readonly CargoDbContext _dbContext;
        private static readonly ILogger Logger = Log.ForContext<ShipmentService>();
        private IConfiguration Configuration { get; }

        public ShipmentService(IConfiguration configuration, IValidationService validationService, CargoDbContext dbContext)
        {
            _validationService = validationService;
            _dbContext = dbContext;
            Configuration = configuration;
        }

        public async Task<ShipmentResultDto> Shipment(ShipmentRequest request, int applicationId)
        {
            var validationResult = _validationService.Validate(typeof(ShipmentRequestValidator), request);
            if (!validationResult.IsValid)
                return new ShipmentResultDto
                {
                    Status = GetShipmentStatus.InvalidInput,
                    Message = ServiceResources.INVALID_INPUT_ERROR,
                    ValidationMessages = validationResult.ErrorMessages
                };

            try
            {
                var cargoBranch = await _dbContext.CargoBranch.FirstOrDefaultAsync(c => c.BranchId == request.Context.Identity.BranchId && c.IsActive && !c.IsDeleted);
                if (cargoBranch == null)
                    return new ShipmentResultDto
                    {
                        Status = GetShipmentStatus.InternalServerError,
                        Message = ServiceResources.BRANCH_NOT_FOUND
                    };

                var cargoService = CargoServiceProviderFactory.GetInstance(cargoBranch, Configuration);

                if (cargoService.GetType().Name == "LastMileServiceProvider")
                {
                    var cargoBranchOffice = await _dbContext.CargoBranchOffice.Where(r =>
                        r.IsActive && !r.IsDeleted && r.BranchId == cargoBranch.BranchId).FirstOrDefaultAsync();

                    if (cargoBranchOffice == null)
                        return new ShipmentResultDto
                        {
                            Status = GetShipmentStatus.NotFound,
                            Message = ServiceResources.OFFICE_INFORMATION_NOT_FOUND
                        };

                    request.OfficeId = cargoBranchOffice.OfficeId;
                }

                var result = await cargoService.Shipment(request);

                if (result is not { Status: GetShipmentStatus.Successful })
                    return new ShipmentResultDto
                    {
                        Status = result.Status,
                        Message = result.Message,
                        ShipmentResult = new ShipmentResult { ApplicationId = request.ApplicationId }
                    };

                var existingCargo = await _dbContext.CargoTrack.FirstOrDefaultAsync(w => w.ApplicationId == applicationId && w.IsActive && !w.IsDeleted);
                if (existingCargo != null)
                {
                    existingCargo.UpdatedBy = request.Context.Identity.UserId;
                    existingCargo.UpdatedAt = DateTime.Now;
                    existingCargo.IsActive = false;

                    _dbContext.CargoTrack.Update(existingCargo);

                    var newCargoTrack = new Entity.Entities.Cargo.CargoTrack
                    {
                        ApplicationId = applicationId,
                        BranchId = cargoBranch.BranchId,
                        CargoProviderId = request.CargoProviderId,
                        Description = result.ShipmentResult.Description,
                        ShipmentId = result.ShipmentResult.ShipmentId,
                        PackageId = result.ShipmentResult.PackageId,
						PackageTrackingNumber = result.ShipmentResult.PackageTrackingNumber,
						CreatedBy = request.Context.Identity.UserId,
                        CreatedDate = DateTime.Now,
                        CargoTransactionId = result.ShipmentResult.TrackingNumber,
                        GraphicImage = request.CargoProviderId == (byte)CourierType.UPS && (request.EnvironmentType == "Production" || request.EnvironmentType == "Production-K8S")
                            ? null : result.ShipmentResult.GraphicImage,
                        IsActive = true,
                        IsDeleted = false
                    };

                    await _dbContext.CargoTrack.AddAsync(newCargoTrack);
                }

                else
                {
                    var newCargoTrack = new Entity.Entities.Cargo.CargoTrack
                    {
                        ApplicationId = applicationId,
                        BranchId = cargoBranch.BranchId,
                        CargoProviderId = request.CargoProviderId,
                        Description = result.ShipmentResult.Description,
                        ShipmentId= result.ShipmentResult.ShipmentId,
						PackageId = result.ShipmentResult.PackageId,
						PackageTrackingNumber = result.ShipmentResult.PackageTrackingNumber,
						CreatedBy = request.Context.Identity.UserId,
                        CreatedDate = DateTime.Now,
                        CargoTransactionId = result.ShipmentResult.TrackingNumber,
                        GraphicImage = request.CargoProviderId == (byte)CourierType.UPS && (request.EnvironmentType == "Production" || request.EnvironmentType == "Production-K8S")
                            ? null : result.ShipmentResult.GraphicImage,
                        IsActive = true,
                        IsDeleted = false
                    };

                    await _dbContext.CargoTrack.AddAsync(newCargoTrack);
                }

                await _dbContext.SaveChangesAsync();

                return new ShipmentResultDto
                {
                    Status = GetShipmentStatus.Successful,
                    ShipmentResult = new ShipmentResult
                    {
                        ApplicationId = applicationId,
                        GraphicImage = result.ShipmentResult.GraphicImage,
                        TrackingNumber = result.ShipmentResult.TrackingNumber
                    }
                };
            }
            catch (Exception ex)
            {
                Logger.Error(ex.Message);

                return new ShipmentResultDto
                {
                    Status = GetShipmentStatus.InternalServerError,
                    Message = ServiceResources.INTERNAL_SERVER_ERROR
                };
            }
        }

        public async Task<ShipmentResultDto> LabelRecovery(LabelRecoveryServiceRequest request)
        {
            try
            {
                var cargoBranch = await _dbContext.CargoBranch.FirstOrDefaultAsync(c => c.BranchId == request.Context.Identity.BranchId && c.IsActive && !c.IsDeleted);
                if (cargoBranch == null)
                    return new ShipmentResultDto
                    {
                        Status = GetShipmentStatus.InternalServerError,
                        Message = ServiceResources.BRANCH_NOT_FOUND
                    };

                var cargoService = CargoServiceProviderFactory.GetInstance(cargoBranch, Configuration);

                var cargoTrack = await _dbContext.CargoTrack.FirstOrDefaultAsync(p => p.IsActive && !p.IsDeleted && p.ApplicationId == request.ApplicationId);
                if (cargoTrack == null)
                    return new ShipmentResultDto
                    {
                        Status = GetShipmentStatus.NotFound,
                        Message = ServiceResources.RESOURCE_NOT_FOUND
                    };

                var result = await cargoService.LabelRecovery(new LabelRecoveryServiceRequest { TrackingNumber = cargoTrack.CargoTransactionId });

                if (result is not { Status: GetShipmentStatus.Successful })
                    return new ShipmentResultDto
                    {
                        Status = result.Status,
                        Message = result.Message,
                        ShipmentResult = new ShipmentResult { ApplicationId = request.ApplicationId }
                    };

                return new ShipmentResultDto
                {
                    Status = GetShipmentStatus.Successful,
                    ShipmentResult = new ShipmentResult
                    {
                        GraphicImage = result.ShipmentResult.GraphicImage,
                        TrackingNumber = result.ShipmentResult.TrackingNumber
                    }
                };
            }
            catch (Exception ex)
            {
                Logger.Error(ex.Message);

                return new ShipmentResultDto
                {
                    Status = GetShipmentStatus.InternalServerError,
                    Message = ServiceResources.INTERNAL_SERVER_ERROR
                };
            }
        }
    }
}
