﻿using Dapper;
using Gateway.Notification.Application.Email;
using Gateway.Notification.Application.Entities;
using Gateway.Notification.Application.Handlers.Dto;
using Gateway.Notification.Application.Repository;
using Microsoft.Extensions.Logging;
using System;
using System.Data;
using System.Threading.Tasks;

namespace Gateway.Notification.Application.Handlers
{
    public class EmailCollectorHandler : IEmailCollectorHandler
    {
        private readonly IEmailService _emailService;
        private readonly IMailQueueRepository _mailQueueRepository;
        private readonly ILogger<EmailCollectorHandler> _logger;

        #region ctor

        public EmailCollectorHandler(IEmailService emailService, IMailQueueRepository mailQueueRepository, ILogger<EmailCollectorHandler> logger)
        {
            _emailService = emailService;
            _mailQueueRepository = mailQueueRepository;
            _logger = logger;
        }

        #endregion

        #region Public Methods

        public async Task CollectEmails(EmailCollectorRequest request)
        {
            try
            {
                var mailQueue = new MailQueue
                {
                    FromMailAddress = request.EmailModel.From,
                    DisplayName = request.EmailModel.DisplayName,
                    ToMailAddress = request.EmailModel.To != null ? string.Join(";", request.EmailModel.To) : string.Empty,
                    CcMailAddress = request.EmailModel.Cc != null ? string.Join(";", request.EmailModel.Cc) : string.Empty,
                    BccMailAddress = request.EmailModel.Bcc != null ? string.Join(";", request.EmailModel.Bcc) : string.Empty,
                    MailContent = request.EmailModel.Content,
                    Subject = request.EmailModel.Subject,
                    MailType = request.EmailModel.MailType,
                    SendDate = request.EmailModel.SendDate,
                    ExternalTransactionId = request.EmailModel.TransactionId,
                    ProviderId = request.EmailModel.ProviderId,
                    RetryCount = 1
                };

                mailQueue.Id = await _mailQueueRepository.SaveMailQueue(mailQueue);

                var sendEmailResponse = await _emailService.SendEmail(request);

                mailQueue.IsSendSuccessful = sendEmailResponse.IsSuccess;
                mailQueue.ProviderTransactionId = sendEmailResponse.ProcessId;

                if (!sendEmailResponse.IsSuccess)
                {
                    mailQueue.FailedStatus = (byte)sendEmailResponse.Status;
                }

                _mailQueueRepository.UpdateMailQueue(mailQueue);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, $"CollectEmails failed. {ex.Message} {ex.StackTrace}");
                throw;
            }
        }

        #endregion
    }
}