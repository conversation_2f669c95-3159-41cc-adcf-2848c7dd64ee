﻿using Gateway.External.Application.Country.Dto;
using Gateway.External.Application.Lookup.Dto;
using System.Threading.Tasks;

namespace Gateway.External.Application.Country
{
    public interface ICountryService
    {
        Task<CountriesResult> GetCountries(GetCountriesRequest request);
        Task<CountryResult> GetCountry(GetCountryRequest request);
        Task<BranchesByCountryResult> GetBranchesByCountry(BranchesByCountryRequest request);
		Task<GetLookupResult> GetLookupValueByCountry(GetLookupRequest request);
		Task<GetCountryVisaInformationResult> GetCountryVisaInformation(GetCountryVisaInformationRequest request);
        Task<GetCityByCountryResult> GetCityByCountry(GetCityByCountryRequest request);
        Task<GetVasTypeMessageByCountryResult> GetVasTypeMessageByCountry(GetVasTypeMessageByCountryRequest request);
        Task<GetChecklistByCountryResult> GetChecklistByCountry(GetChecklistByCountryRequest request);
    }
}
