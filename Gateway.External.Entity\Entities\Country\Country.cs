﻿using System.Collections.Generic;
using System.ComponentModel.DataAnnotations;
using System.ComponentModel.DataAnnotations.Schema;
using Gateway.External.Entity.Entities.Agency;
using Gateway.External.Entity.Entities.B2B.AppointmentDemand;
using Gateway.External.Entity.Entities.Branch;

namespace Gateway.External.Entity.Entities.Country
{
    public class Country
    {
        [Key]
        [DatabaseGenerated(DatabaseGeneratedOption.Identity)]
        public int Id { get; set; }

        [Required]
        [Column(TypeName = "citext"), MaxLength(100)]
        public string Name { get; set; }

        [Required]
        [Column(TypeName = "citext"), MaxLength(5)]
        public string ISO2 { get; set; }

        [Required]
        [Column(TypeName = "citext"), MaxLength(5)]
        public string ISO3 { get; set; }

        [Required]
        [Column(TypeName = "citext"), MaxLength(5)]
        public string CallingCode { get; set; }

        [Required]
        [Column(TypeName = "citext"), MaxLength(100)]
        public string NameTr { get; set; }

        [Required]
        [Column(TypeName = "citext"), MaxLength(100)]
        public string NameAr { get; set; }

        [Required]
        [Column(TypeName = "citext"), MaxLength(100)]
        public string NameTm { get; set; }

        [Required]
        [Column(TypeName = "citext"), MaxLength(100)]
        public string NameRu { get; set; }

        public ICollection<Branch.Branch> Branches { get; set; }

        public ICollection<BranchApplicationCountry> BranchApplicationCountries { get; set; }

        public ICollection<Application.Application> Applications { get; set; }

        public ICollection<Agency.Agency> Agencies { get; set; }

        public ICollection<B2B.Company.Company> Companies { get; set; }

        public ICollection<AgencyTypeFile> AgencyTypeFiles { get; set; }

		public ICollection<AppointmentApplicantDemand> AppointmentApplicantDemands { get; set; }

		public ICollection<CountryChecklist> CountryChecklists { get; set; }
	}
}
