﻿-- Gateway portal veri tabanında çalıştırmak istediğiniz sql sorgularını lütfen sırasıyla aşağıya ekleyiniz. 
-- Sorgular yukarıdan aşağıya doğru çalışacağından ekleme yaptığınız sıralama çok önemlidir.
-- Düzenlemeleriniz bittikten sonra bu dosyayı check-in yapacağınız dosya kümesine eklediğinizden mutlaka emin olunuz aksi halde değişiklikler geçersiz olacaktır.
-- Otomatik oluşturulan bu dosya ismini Migration-add işleminden sonra \EFMigrationsHistory\PortalDb altında oluşan migration classına seed-readme.md de anlatıldığı gibi eklemeyi unutmayınız!

UPDATE public."NotificationTemplate"
SET  "SmsText" = 'You can click link on below:'
WHERE "NotificationTypeId" = 20 AND "LanguageId" = 2;