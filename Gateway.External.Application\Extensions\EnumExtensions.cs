﻿using Gateway.External.Application.Attributes;
using System;
using System.Collections.Generic;
using System.Linq;

namespace Gateway.External.Application.Extensions
{
    public static class EnumExtensions
    {
        public static string GetCode<TEnum>(this TEnum enumValue) where TEnum : struct, Enum
        {
            var attribute = GetMetadataAttribute(enumValue);
            return attribute?.Code ?? enumValue.ToString();
        }

        public static string GetLocalizedDescription<TEnum>(this TEnum enumValue) where TEnum : struct, Enum
        {
            var fieldInfo = enumValue.GetType().GetField(enumValue.ToString());
            var descriptionAttribute = fieldInfo?
                .GetCustomAttributes(typeof(LocalizedDescriptionAttribute), false)
                .FirstOrDefault() as LocalizedDescriptionAttribute;

            return descriptionAttribute?.Description ?? enumValue.ToString();
        }

        private static EnumMetadataAttribute? GetMetadataAttribute<TEnum>(TEnum enumValue) where TEnum : struct, Enum
        {
            var fieldInfo = enumValue.GetType().GetField(enumValue.ToString());
            return fieldInfo?.GetCustomAttributes(typeof(EnumMetadataAttribute), false)
                .FirstOrDefault() as EnumMetadataAttribute;
        }

        public static TEnum? FromCode<TEnum>(string code) where TEnum : struct, Enum
        {
            if (string.IsNullOrEmpty(code))
                return null;

            foreach (TEnum enumValue in Enum.GetValues(typeof(TEnum)))
            {
                if (GetCode(enumValue).Equals(code, StringComparison.OrdinalIgnoreCase))
                    return enumValue;
            }
            return null;
        }

        public static IEnumerable<EnumInfo<TEnum>> GetAll<TEnum>() where TEnum : struct, Enum
        {
            return Enum.GetValues(typeof(TEnum))
                .Cast<TEnum>()
                .Select(t => new EnumInfo<TEnum>(
                    Convert.ToInt32(t),
                    t.GetCode(),
                    t.GetLocalizedDescription()
                ));
        }
    }

    public record EnumInfo<TEnum>(int Id, string Code, string Name) where TEnum : struct, Enum;
}
