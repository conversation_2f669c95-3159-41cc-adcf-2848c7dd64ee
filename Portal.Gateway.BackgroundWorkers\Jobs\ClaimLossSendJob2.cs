﻿using System.Globalization;
using Gateway.Extensions;
using Portal.Gateway.ApiModel.Responses.Insurance;
using Portal.Gateway.Contracts.Entities.Dto.Appointment.Application.Requests;
using Portal.Gateway.Contracts.Entities.Dto.Appointment.Insurance;
using Portal.Gateway.Contracts.Entities.Dto.DocumentManagement.Requests;
using Portal.Gateway.Contracts.Entities.Dto.Insurance;
using Portal.Gateway.Contracts.Entities.Enums;
using Portal.Gateway.Contracts.Services;
using Portal.Gateway.ExternalServices.Contracts;
using Quartz;

namespace Portal.Gateway.BackgroundWorkers.Jobs
{
    [DisallowConcurrentExecution]
    internal class ClaimLossSendJob2 : IJob
    {
        private readonly IInsuranceService _insuranceService;
        private readonly IAppointmentService _appointmentService;
        private readonly IDocumentManagementService _documentManagementService;
        private readonly ILogger<ClaimLossSendJob2> _logger;

        public ClaimLossSendJob2(
            IInsuranceService insuranceService,
            IAppointmentService appointmentService,
            IDocumentManagementService documentManagementService,
            ILogger<ClaimLossSendJob2> logger)
        {
            _appointmentService = appointmentService;
            _insuranceService = insuranceService;
            _documentManagementService = documentManagementService;
            _logger = logger;
        }

        public async Task Execute(IJobExecutionContext context)
        {
            var claimFound = true;

            while (claimFound)
            {
                var getClaimLossIdAndClaimAmountListResponse = await _appointmentService.GetClaimLossIdAndClaimAmountForJobAsync2();
                claimFound = getClaimLossIdAndClaimAmountListResponse.ClaimLossIdAndClaimAmount.FirstOrDefault() != null;

                if (claimFound)
                {
                    await CreateClaimLoss(getClaimLossIdAndClaimAmountListResponse.ClaimLossIdAndClaimAmount.First());
                }
            }
        }

        private async Task CreateClaimLoss(ClaimLossIdAndClaimAmountDto claimLoss)
        {
            int id = claimLoss.ApplicationId;
            String claimAmount = claimLoss.ClaimAmount;
            DateTimeOffset lossDate = claimLoss.LossDate;
            var request = new ApplicationRequestDto { Id = id };

            var apiResponse = await _appointmentService.GetApplicationInsuranceInformationAsync(request);

            var insuranceDocumentsApiRequest = new InsuranceDocumentsRequestDto
            {
                ReferenceIds = new List<int> { apiResponse.Id },
            };

            var getInsuranceDocumentsApiResponse = await _documentManagementService.GetInsuranceDocumentsAsync(insuranceDocumentsApiRequest);
            if (getInsuranceDocumentsApiResponse.InsuranceDocuments == null)
            {
                await _appointmentService.UpdateClaimLossStatusAsync2(new AddClaimLossEntryLogDto()
                {
                    Id = claimLoss.Id,
                    Status = (int)ClaimLossEntryLogStatusEnum.DocumentNotFound,
                }).ConfigureAwait(false);

                return;
            }

            var claimLossRequest = new ExternalServices.Models.Insurance.Requests.ClaimEntryServiceRequest()
            {
                PolicyNo = long.Parse(apiResponse.Insurance.Number[0]),
                LossDate = lossDate.ToString("dd/MM/yyyy", CultureInfo.InvariantCulture),
                LossTime = lossDate.ToString("HH:mm"),
                Sufferers = new ExternalServices.Models.Insurance.Requests.Sufferers
                {
                    DataContractSufferer = new ExternalServices.Models.Insurance.Requests.DataContractSufferer
                    {
                        ClaimDate = DateTime.Today.ToString("dd/MM/yyyy", CultureInfo.InvariantCulture),
                        ClaimTime = DateTime.Now.ToString("HH:mm"),
                        SuffererName = apiResponse.Name,
                        SuffererSurname = apiResponse.Surname,
                        SuffererFirmName = apiResponse.Name + " " + apiResponse.Surname,
                        ClaimAmount = claimAmount,
                        ClaimerName = apiResponse.Name,
                        ClaimerSurname = apiResponse.Surname,
                        ClaimerFirmName = apiResponse.Name + " " + apiResponse.Surname,
                        FirstClaimAmount = claimAmount,
                        Outstandings = new ExternalServices.Models.Insurance.Requests.Outstandings
                        {
                            DataContractOutstanding = new ExternalServices.Models.Insurance.Requests.DataContractOutstanding
                            {
                                OutsDetail = new ExternalServices.Models.Insurance.Requests.OutsDetail
                                {
                                    DataContractOutsDetail = new ExternalServices.Models.Insurance.Requests.DataContractOutsDetail
                                    {
                                        OutstandingClaimAmount = claimAmount,
                                        OutstandingClaimDate = DateTime.Today.ToString("dd/MM/yyyy", CultureInfo.InvariantCulture)
                                    }
                                },
                                OutstandingClaimAmount = claimAmount,
                                OutstandingClaimDate = DateTime.Today.ToString("dd/MM/yyyy", CultureInfo.InvariantCulture)
                            }

                        },
                        Documents = new ExternalServices.Models.Insurance.Requests.Documents
                        {
                            DataContractDocument = new List<ExternalServices.Models.Insurance.Requests.DataContractDocument> {
                                     new ExternalServices.Models.Insurance.Requests.DataContractDocument
                                    {
                                        DocumentRequestDate = DateTime.Today.ToString("dd/MM/yyyy",CultureInfo.InvariantCulture),
                                        DocumentRequestTime = DateTime.Now.ToString("HH:mm"),
                                        DocumentCompletionDate = apiResponse.Document.EntryDate.ToString("dd/MM/yyyy",CultureInfo.InvariantCulture),
                                        DocumentCompletionTime = DateTime.Now.ToString("HH:mm"),
                                        DocumentData = getInsuranceDocumentsApiResponse.InsuranceDocuments.Count(q => q.FileTypeId==(int)ApplicationFileType.RejectionReturnStatement) > 0 ? Convert.ToBase64String(getInsuranceDocumentsApiResponse.InsuranceDocuments.Where(q => q.FileTypeId==(int)ApplicationFileType.RejectionReturnStatement).Select(q=>q.FileContent).FirstOrDefault()) : "",
                                        DocumentExtension =getInsuranceDocumentsApiResponse.InsuranceDocuments.Count(q => q.FileTypeId==(int)ApplicationFileType.RejectionReturnStatement) > 0 ? getInsuranceDocumentsApiResponse.InsuranceDocuments.Where(q => q.FileTypeId == (int)ApplicationFileType.RejectionReturnStatement).Select(q => q.FileExtension).FirstOrDefault() : ""
                                    },
                                    new ExternalServices.Models.Insurance.Requests.DataContractDocument
                                    {
                                        DocumentRequestDate = DateTime.Today.ToString("dd/MM/yyyy",CultureInfo.InvariantCulture),
                                        DocumentRequestTime = DateTime.Now.ToString("HH:mm"),
                                        DocumentCompletionDate = apiResponse.Document.EntryDate.ToString("dd/MM/yyyy",CultureInfo.InvariantCulture),
                                        DocumentCompletionTime = DateTime.Now.ToString("HH:mm"),
                                        DocumentData = getInsuranceDocumentsApiResponse.InsuranceDocuments.Count(q => q.FileTypeId==(int)ApplicationFileType.RejectionPassport) > 0 ? Convert.ToBase64String(getInsuranceDocumentsApiResponse.InsuranceDocuments.Where(q => q.FileTypeId==(int)ApplicationFileType.RejectionPassport).Select(q=>q.FileContent).FirstOrDefault()) : "",
                                        DocumentExtension = getInsuranceDocumentsApiResponse.InsuranceDocuments.Count(q => q.FileTypeId==(int)ApplicationFileType.RejectionPassport) > 0 ? getInsuranceDocumentsApiResponse.InsuranceDocuments.Where(q => q.FileTypeId == (int)ApplicationFileType.RejectionPassport).Select(q => q.FileExtension).FirstOrDefault() : ""
                                    },
                                    new ExternalServices.Models.Insurance.Requests.DataContractDocument
                                    {
                                        DocumentRequestDate = DateTime.Today.ToString("dd/MM/yyyy",CultureInfo.InvariantCulture),
                                        DocumentRequestTime = DateTime.Now.ToString("HH:mm"),
                                        DocumentCompletionDate = apiResponse.Document.EntryDate.ToString("dd/MM/yyyy",CultureInfo.InvariantCulture),
                                        DocumentCompletionTime = DateTime.Now.ToString("HH:mm"),
                                        DocumentData = getInsuranceDocumentsApiResponse.InsuranceDocuments.Count(q => q.FileTypeId==(int)ApplicationFileType.RejectionDataPage) > 0 ? Convert.ToBase64String(getInsuranceDocumentsApiResponse.InsuranceDocuments.Where(q => q.FileTypeId==(int)ApplicationFileType.RejectionDataPage).Select(q=>q.FileContent).FirstOrDefault()) : "",
                                        DocumentExtension = getInsuranceDocumentsApiResponse.InsuranceDocuments.Count(q => q.FileTypeId==(int)ApplicationFileType.RejectionDataPage) > 0 ? getInsuranceDocumentsApiResponse.InsuranceDocuments.Where(q => q.FileTypeId == (int)ApplicationFileType.RejectionDataPage).Select(q => q.FileExtension).FirstOrDefault() : ""
                                    },
                                }
                        },
                        PaymentDesicions = new ExternalServices.Models.Insurance.Requests.PaymentDesicions
                        {
                            PaymentDecisions = new ExternalServices.Models.Insurance.Requests.PaymentDecisions
                            {
                                DataContractPaymentDecision = new ExternalServices.Models.Insurance.Requests.DataContractPaymentDecision
                                {
                                    InvoiceDate = DateTime.Today.ToString("dd/MM/yyyy", CultureInfo.InvariantCulture),
                                    PaymentDecisionAmount = claimAmount

                                }
                            },
                            PaymentDetails = new ExternalServices.Models.Insurance.Requests.PaymentDetails
                            {
                                DataContractPaymentDetails = new ExternalServices.Models.Insurance.Requests.DataContractPaymentDetails
                                {
                                    EntryDate = DateTime.Today.ToString("dd/MM/yyyy", CultureInfo.InvariantCulture),
                                    NextPaymentDate = DateTime.Today.ToString("dd/MM/yyyy", CultureInfo.InvariantCulture),
                                    PaymentDate = DateTime.Today.ToString("dd/MM/yyyy", CultureInfo.InvariantCulture),
                                    TotalPaymentAmount = claimAmount,
                                    PaidPerson = apiResponse.Name + " " + apiResponse.Surname
                                }
                            }
                        },
                        ExLaws = new ExternalServices.Models.Insurance.Requests.ExLaws
                        {
                            DataContractExLaw = new ExternalServices.Models.Insurance.Requests.DataContractExLaw
                            {
                                AppointmentDate = apiResponse.ApplicationTime.DateTime.ToString("dd/MM/yyyy", CultureInfo.InvariantCulture),
                                ReportAmount = claimAmount
                            }
                        }
                    }
                }
            };

            ServiceResult<ClaimEntryPolicyApiResponse>? result = null;

            try
            {
                result = await _insuranceService.ClaimLossEntry(claimLossRequest).ConfigureAwait(false);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "ClaimLossEntry service failed.");

                await _appointmentService.UpdateClaimLossStatusAsync2(new AddClaimLossEntryLogDto()
                {
                    Id = claimLoss.Id,
                    ServiceResponse = ex.Message,
                    ClaimNo = "",
                    Status = (int)ClaimLossEntryLogStatusEnum.Failed,
                    RetryCount = claimLoss.RetryCount.GetValueOrDefault(0) + 1,
                    NextAttemptDateTime = FindNextAttemptDateTime(claimLoss),
                }).ConfigureAwait(false);

                return;
            }

            if (result.ErrorMessage.IsNullOrWhitespace() && result.Result != null && !result.Result.ClaimNo.IsNullOrWhitespace())
            {
                await _appointmentService.UpdateClaimLossStatusAsync2(new AddClaimLossEntryLogDto()
                {
                    Id = claimLoss.Id,
                    ServiceResponse = result.Result.Message,
                    ClaimNo = result.Result.ClaimNo,
                    Status = (int)ClaimLossEntryLogStatusEnum.Completed,
                    NextAttemptDateTime = null,
                }).ConfigureAwait(false);
            }
            else
            {
                await _appointmentService.UpdateClaimLossStatusAsync2(new AddClaimLossEntryLogDto()
                {
                    Id = claimLoss.Id,
                    ServiceResponse = result.Result?.Message ?? result.ErrorMessage,
                    ClaimNo = "",
                    Status = (int)ClaimLossEntryLogStatusEnum.Failed,
                    RetryCount = claimLoss.RetryCount.GetValueOrDefault(0) + 1,
                    NextAttemptDateTime = FindNextAttemptDateTime(claimLoss),
                }).ConfigureAwait(false);
            }
        }

        private DateTime? FindNextAttemptDateTime(ClaimLossIdAndClaimAmountDto claimLoss)
        {
            var retryCount = claimLoss.RetryCount.GetValueOrDefault(0);

            if (retryCount == 0)
            {
                return DateTime.UtcNow.AddMinutes(1);
            }
            else if (retryCount == 1)
            {
                return DateTime.UtcNow.AddMinutes(5);
            }
            else if (retryCount == 2)
            {
                return DateTime.UtcNow.AddMinutes(10);
            }
            else if (retryCount == 3)
            {
                return DateTime.UtcNow.AddMinutes(15);
            }
            else if (retryCount == 4)
            {
                return DateTime.UtcNow.AddMinutes(30);
            }
            else
            {
                return null; // No more retries
            }
        }
    }
}
