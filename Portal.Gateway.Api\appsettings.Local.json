{
  "AppSettings": {
    "ApiKey": "Gateway.ApiKey.2021",
    "ActiveDirectoryUrl": "gateway.com.tr",
    "EsimBucket": "esim",
    "EnableRejectedApplicationInsuranceCase": true,
    "DigitalInsuranceUrl": "http://localhost:5269",
    "EmaaInsuranceConfirmationUrl": "http://gwdev.gateway.com.tr:1334",
    "TinyUrl": {
      "TokenKey": "seW0fPqTDZ9ccrfileclUdnXbFok8T1kjRlQ7Vosj7RM11A049n1yqHdrgfU",
      "ApiUrl": "https://api.tinyurl.com/create",
      "Enabled": true,
      "Domain": "gateway.tr"
    },
    "B2B": {
      "BaseUrl": "http://localhost:6001",
      "ForgetPasswordUrl": "forget-password"
    },
    "Ldap": {
      "PrimaryServer": {
        "HostName": "GWDC01.gateway.com.tr",
        "Port": 389
      },
      "SecondaryServer": {
        "HostName": "GWDC02.gateway.com.tr",
        "Port": 389
      },
      "SearchBase": "DC=gateway,DC=com,DC=tr",
      "UserIdAttributeName": "sAMAccountName",
      "ServiceUserDN": "CN=Gateway Portal Service,OU=SERVICE_USERS,OU=USERS-GLOBAL,DC=gateway,DC=com,DC=tr",
      "ServiceUserPassword": "2024@Pass",
      "AllowedUnits": [
        "OU=USERS-GLOBAL,DC=gateway,DC=com,DC=tr"
      ],
      "NotAllowedUnits": [
        "OU=MYNEST,OU=USERS-GLOBAL,DC=gateway,DC=com,DC=tr",
        "OU=SERVICE_USERS,OU=USERS-GLOBAL,DC=gateway,DC=com,DC=tr",
        "OU=EXTERNALS,OU=USERS-GLOBAL,DC=gateway,DC=com,DC=tr"
      ],
      "Notification": {
        "Subject": "Kullanıcı İşlemleri Hakkında Bilgilendirme",
        "Contact": "<EMAIL>"
      }
    },
    "PublicKey": [
      {
        "Id": 1,
        "Token": "E9A48DD1-F72D-4C49-BB0D-CE61A2B35F5E",
        "Username": "Biometrics",
        "Urls": [
          "/api/Public/GetApplication",
          "/api/Public/AddBiometricsFingerPrintDocument"
        ]
      },
      {
        "Id": 2,
        "Token": "D9D35D60-DCE3-4651-86EB-E071D674BC6D",
        "Username": "GatewayGlobe",
        "Urls": [
          "/api/Public/GetApplicationStatus"
        ]
      },
      {
        "Id": 3,
        "Token": "EC604752-7E56-4077-B40D-6CDA6C1BBCF9",
        "Username": "Photobooth",
        "Urls": [
          "/api/Public/ValidatePhotoBooth",
          "/api/Public/UpdatePhotoBooth"
        ]
      },
      {
        "Id": 4,
        "Token": "B3115BC3-2B31-4F34-965F-FD63A2AF2128",
        "Username": "B2B",
        "Urls": [
          "/api/Public/GetApplication",
          "/api/Public/GetApplicationStatus",
          "/api/Public/ValidatePhotoBooth",
          "/api/Public/UpdatePhotoBooth",
          "/api/Public/AddPreApplication",
          "/api/Public/GetPaginatedPreApplications",
          "/api/Public/GetPreApplication",
          "/api/Public/DeletePreApplication",
          "/api/Public/UpdatePreApplication",
          "/api/Public/NotifyForgetPassword",
          "/api/Public/ValidateForgetPasswordToken",
          "/api/Public/SetPassword",
          "/api/Public/AgencyUserLogin",
          "/api/Public/ChangePassword",
          "/api/Public/GetBranchApplicationCountry",
          "/api/Public/GetSlots",
          "/api/Public/GetCustomerSlots",
          "/api/Public/AddAgency",
          "/api/Public/UpdateAgency",
          "/api/Public/GetAgency",
          "/api/Public/GetPaginatedAgencyFiles",
          "/api/Public/AddAgencyUser",
          "/api/Public/DeleteAgencyUser",
          "/api/Public/UpdateAgencyUser",
          "/api/Public/GetAgencyUser",
          "/api/Public/GetAllAgencyUsers",
          "/api/Public/GetCountries",
          "/api/Public/GetAgencyTypes",
          "/api/Public/GetBranchApplicationCountriesByCountryId",
          "/api/Public/GetEnumSelectList",
          "/api/Public/GetAllBranchApplicationCountryFiles",
          "/api/Public/GetAgencyApplicationStats",
          "/api/Public/GetActiveCountries",
          "/api/Public/GetPeriodicAppointmentStats",
          "/api/Public/GetAgencyInsuranceStats",
          "/api/Public/AddDocuments",
          "/api/Public/GetDocuments",
          "/api/Public/GetApplicationStatuses",
          "/api/Public/AddCustomer",
          "/api/Public/UpdateCustomer",
          "/api/Public/GetCustomer",
          "/api/Public/ChangeCustomerPassword",
          "/api/Public/CustomerLogin",
          "/api/Public/GetVisaCategoryFiles",
          "/api/Public/AddApplication",
          "/api/Public/GetPaginatedInsuranceApplications",
          "/api/Public/GetPreApplicationForm",
          "/api/Public/CreatePolicy",
          "/api/Public/CertificatePolicy",
          "/api/Public/UpdateSignStatus"
        ]
      },
      {
        "Id": 5,
        "Token": "D2797DAB-FEE2-4D62-94CF-3D6CC77DF672",
        "Username": "Avaya",
        "Urls": [
          "/api/Public/GetApplication",
          "/api/Public/GetApplicationStatus",
          "/api/Public/GetApplicationStatusNew"
        ]
      },
      {
        "Id": 6,
        "Token": "X2797DAB-FEE2-4D62-94CF-3D6CC77DF672",
        "Username": "Test",
        "Urls": [
          "/api/Public/GetApplication",
          "/api/Public/GetApplicationStatus",
          "/api/Ministry/UploadPackage",
          "/api/Ministry/CreatePackage"
        ]
      }
    ],
    "SftpConfiguration": {
      "Host": "************",
      "Port": 22,
      "Username": "sftptestuser",

      "Password": "4*Yhq!5Gw37@",

      "Fingerprint": "ssh-ed25519 255 Jb2iqhCnQ1Z2miSlbxTGOjxRLZT5Z63rmdQ2kgoYC2s=",
      "RootFolder": "./Uploads"
    },
    "MinioConfiguration": {
      "EndPoint": "visacdn.gateway.com.tr:443",
      "AccessKey": "eGBBZ2ZVzNWCFW5kvmKr",
      "SecretKey": "tnczwQAiKkNpEHIGwXwztZtFZCaT1i2JziBUZXY3",
      "BucketPrefix": "test-"
    },
    "FileEncryptDecrypt": {
      "CipherKey": "DrdI28+0ag282rrqHsmxEwxK4K/FAKo8Pl03rVWC9zw=",
      "CipherIV": "aa0OzGWMLJGfpxCrz/h5PQ=="
    }
  },
  "IntegrationSettings": {
    "UnicoInsurance": {
      "Id": 1, // DB Provider Id
      "SecurityServiceEndpointUrl": "https://previva.unicosigorta.com.tr/Public/SecurityService.svc",
      "IntegrationServiceEndpointUrl": "https://previva.unicosigorta.com.tr/Public/Integration017.svc",
      "PolicyServiceEndpointUrl": "https://previva.unicosigorta.com.tr/Public/PolicyService.svc",
      "CertificateServiceEndpointUrl": "http://PolicyCertificateWS.unicosigorta.com.tr/Certificate.asmx",
      "Environment": "PreProd",
      "AppSecurityKey": "Gateway#",
      "UserName": "GRAVIS_WEB3",
      "Password": "EVVANAL1",
      "BranchCode": "13",
      "SourceCode": "3",
      "AgentNo": "0516052"
    },
    "Sap": {
      "Id": 2, // DB Provider Id
      "CreateOrderEndpointUrl": "https://sapqa.gateway.com.tr/sap/bc/srt/rfc/sap/zgwsd_fg004_001/400/zgwsd_fg004_001/zgwsd_fg004_001",
      "QueryInvoiceEndpointUrl": "https://sapqa.gateway.com.tr/sap/bc/srt/rfc/sap/zgwsd_fg004_003/400/zgwsd_fg004_003/zgwsd_fg004_003",
      "RefundInvoiceEndpointUrl": "https://sapqa.gateway.com.tr/sap/bc/srt/rfc/sap/zgwsd_fg004_004/400/zgwsd_fg004_004/zgwsd_fg004_004",
      "CancelInvoiceEndpointUrl": "https://sapqa.gateway.com.tr/sap/bc/srt/rfc/sap/zgwsd_fg004_005/400/zgwsd_fg004_005/zgwsd_fg004_005",
      "ExchangeInvoiceEndpointUrl": "https://sapqa.gateway.com.tr/sap/bc/srt/rfc/sap/zgwsd_fg004_009/400/zgwsd_fg004_009/zgwsd_fg004_009",
      "GetSapRateInformationUrl": "http://GWS4QA.gateway.com.tr:8000/sap/bc/srt/rfc/sap/zgwfi_ws_fg007/400/zgwfi_ws_fg007/zgwfi_ws_fg007_lp",
      "SendVisaRejectionSapUrl": "http://GWS4QA.gateway.com.tr:8000/sap/bc/srt/rfc/sap/zgwfi_ws_fg008/400/ws_fg008/ws_fg008",
      "Binding": "SapWebServiceBinding",
      "UserName": "WEBSERVICE",
      "Password": "WEBSERVICE2020"
    },
    "Emaa": {
      "Id": 3, // DB Provider Id
      "ServiceUrl": "http://10.180.90.50/POLSVC/SFSPOLSVCWS.asmx",
      "ServiceDisasteryUrl": "http://10.180.90.50/POLSVC/SFSPOLSVCWS.asmx",
      "ServerUrl": "10.180.90.50",
      "ServerDisasteryUrl": "10.180.90.50",
      "LossClaimServiceUrl": "http://10.31.55.14/SFS.Services.Claim/FileOperations.svc",
      "LossClaimServiceDisasteryUrl": "http://10.31.55.14/SFS.Services.Claim/FileOperations.svc",
      "LossClaimServerUrl": "10.31.55.14",
      "LossClaimServerDisasteryUrl": "10.31.55.14",
      "UserName": "TpiIFQDekYEKwg6zpZbPJA==",
      "Password": "m4YKwOovSAQ="
    },
    "Ministry": {
      "Token": "3e23e8160039594a33894f6564e1b1348bbd7a0088d42c4acb73eeaed59c009e",
      "VisaEndpointBaseUrl": "http://test.konsolosluk.gov.tr/api/v1/visapack/"
    },
    "Biometrics": {
      "BiometricsEndpointUrl": "http://10.31.42.70:3434",
      "BiometricsEndpointUrl-legacy": "http://visa.gateway.com.tr/WebServices/Biometry.asmx?op=BasvuruBilgileri",
      "Token": "604B120D-5BF3-4B3A-913A-753786214D49",
      "UserName": "BioUser",
      "Password": "432BY?"
    },
    "Sms": {
      "Clickatell": {
        "ClickatellServiceEndpointUrl": "https://platform.clickatell.com/v1/",
        "ClickatellApiMethod": "message/",
        "ApiKey": "xXLICydCQDKxB348M-_Y5w==",
        "Channel": "sms"
      },
      "TurkTelekom": {
        "TurkTelekomServiceEndpointUrl": "https://ws.ttmesaj.com/Service1.asmx",
        "Username": "gateway",
        "Password": "G4A9T1M6",
        "Origin": "Gateway"
      },
      "Turkcell": {
        "TurkcellServiceEndpointUrl": "https://api.dataport.com.tr/restapi/",
        "RegisterApiMethod": "Register/",
        "SendSmsApiMethod": "api/Messages/SendSMS/",
        "AccountNumber": "???",
        "UserName": "???",
        "Password": "???",
        "GrantType": "???"
      }
    },
    "Email": {
      "SmtpOffice365": {
        "UserName": "<EMAIL>",
        "Password": "eqYcSU5!",
        "Smtp": "smtp.office365.com",
        "Port": "587"
      },
      "SendGrid": {
        "Sender": "<EMAIL>",
        "Credential": "*********************************************************************"
      }
    },
    "ClearTaxKsaIcr": {
      "GenerateEInvoiceServiceUrl": "https://api-sandbox.cleartax.com/middle-east/ksa/einvoicing/v2/einvoices/generate"
    }
  },
  "elasticsearch": {
    "logindex": "slog-portal",
    "index": "slog-portal",
    "username": "elastic",
    "password": "htJsew49I8jPiL6How7U",
    "url": "http://***********:9200"
  },
  "ConnectionStrings": {
    "GatewayPortalDbConnection": "Server=**********;Username=gw_portal_stg_app_usr;Password=*****************;Database=MigrationTestPortal;"
  },
  "Logging": {
    "LogLevel": {
      "Default": "Information",
      "Microsoft": "Warning",
      "Microsoft.Hosting.Lifetime": "Information"
    }
  },
  "RabbitMq": {
    "Host": "GWMQ01.gateway.com.tr",
    "User": "test_user",
    "Password": "okmenn",
    "Port": 5672,
    "Exchange": "log.exchange"
  },
  "Redis": {
    "Url": "***********",
    "Port": "6379",
    "ConnectTimeout": 10000,
    "ConnectRetry": 3,
    "DefaultDatabase": 8,
    "PrinterServiceDefaultDatabase": 2
  },
  "ContactInformationVerificationSecret": "OvwtalPB3YjeeLReriRWjJyzt3phRCax"
}
