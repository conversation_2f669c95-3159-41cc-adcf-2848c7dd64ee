﻿using Gateway.Biometrics.Entity.Entities.Appeal;
using Gateway.Biometrics.Entity.Entities.Cabin;
using Gateway.Biometrics.Entity.Entities.Category;
using Gateway.Biometrics.Entity.Entities.ClientConfiguration;
using Gateway.Biometrics.Entity.Entities.Inventory;
using Gateway.Biometrics.Entity.Entities.NeuroTec;
using Gateway.Biometrics.Entity.Entities.Office;
using Gateway.Biometrics.Entity.Entities.Plugin;
using Microsoft.EntityFrameworkCore;
using System;
using System.Threading;
using System.Threading.Tasks;

namespace Gateway.Biometrics.Persistence
{
    public class BiometricsDbContext : DbContext
    {
        public BiometricsDbContext(DbContextOptions<BiometricsDbContext> options) : base(options)
        {
            AppContext.SetSwitch("Npgsql.EnableLegacyTimestampBehavior", true);
        }
        public DbSet<GenericAttribute> GenericAttribute { get; set; }
        public DbSet<ClientConfiguration> ClientConfiguration { get; set; }
        public DbSet<ClientConfigurationInventory> ClientConfigurationInventory { get; set; }
        public DbSet<Inventory> Inventory { get; set; }
        public DbSet<InventoryType> InventoryType { get; set; }
        public DbSet<InventoryDefinition> InventoryDefinition { get; set; }
        public DbSet<InventoryAttribute> InventoryAttribute { get; set; }
        public DbSet<InventoryValueSet> InventoryValueSet { get; set; }
        public DbSet<InventoryIpCamera> InventoryIpCamera { get; set; }
        public DbSet<Plugin> Plugin { get; set; }
        public DbSet<Cabin> Cabin { get; set; }
        public DbSet<Office> Office { get; set; }
        public DbSet<Appeal> Appeal { get; set; }
        public DbSet<AppealDetail> AppealDetail { get; set; }
        public DbSet<AppealMetaData> AppealMetaData { get; set; }
        public DbSet<InventoryStatusLog> InventoryStatusLog { get; set; }
        public DbSet<NeurotecLicense> NeurotecLicense { get; set; }
        public DbSet<NeurotecLicenseUsageLog> NeurotecLicenseUsageLog { get; set; }



        public override Task<int> SaveChangesAsync(bool acceptAllChangesOnSuccess,
          CancellationToken cancellationToken = new CancellationToken())
        {
            OnBeforeSaving();
            return base.SaveChangesAsync(acceptAllChangesOnSuccess, cancellationToken);
        }

        protected override void OnConfiguring(DbContextOptionsBuilder optionsBuilder)
        {
            optionsBuilder.UseLazyLoadingProxies();
        }

        protected override void OnModelCreating(ModelBuilder modelBuilder)
        {
            base.OnModelCreating(modelBuilder);

            modelBuilder.Entity<Inventory>()
                .HasQueryFilter(p => !p.IsDeleted);

            modelBuilder.Entity<InventoryAttribute>()
                .HasQueryFilter(p => !p.IsDeleted);

            modelBuilder.Entity<InventoryDefinition>()
                .HasQueryFilter(p => !p.IsDeleted);

            modelBuilder.Entity<InventoryValueSet>()
                .HasQueryFilter(p => !p.IsDeleted);

            modelBuilder.Entity<ClientConfigurationInventory>()
                .HasQueryFilter(p => !p.IsDeleted);

            modelBuilder.Entity<InventoryStatusLog>()
                .HasQueryFilter(p => !p.IsDeleted);

            modelBuilder.Entity<NeurotecLicense>()
                .HasQueryFilter(p => !p.IsDeleted);

            modelBuilder.Entity<InventoryIpCamera>()
                .HasQueryFilter(p => !p.IsDeleted);
        }

        protected virtual void OnBeforeSaving()
        {
            foreach (var entry in ChangeTracker.Entries())
                switch (entry.State)
                {
                    case EntityState.Added:
                        if (entry?.GetType().GetProperty("IsDeleted") != null)
                            entry.Entity.GetType().GetProperty("IsDeleted")?.SetValue(entry.Entity, false);
                        if (entry?.Entity.GetType().GetProperty("CreatedAt") != null)
                            entry.Entity.GetType().GetProperty("CreatedAt")?.SetValue(entry.Entity, DateTime.UtcNow);
                        break;
                    case EntityState.Deleted:
                        if (entry.Entity.GetType().GetProperty("IsDeleted") != null)
                            entry.Entity.GetType().GetProperty("IsDeleted")?.SetValue(entry.Entity, true);
                        if (entry.Entity.GetType().GetProperty("DeletedAt") != null)
                            entry.Entity.GetType().GetProperty("DeletedAt")?.SetValue(entry.Entity, DateTime.UtcNow);
                        break;
                    case EntityState.Modified:
                        if (entry.Entity.GetType().GetProperty("UpdatedAt") != null)
                            entry.Entity.GetType().GetProperty("UpdatedAt")?.SetValue(entry.Entity, DateTime.UtcNow);
                        break;
                }
        }
    }
}
