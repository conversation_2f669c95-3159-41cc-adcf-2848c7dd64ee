﻿using Portal.Gateway.ApiModel.Requests.Appointment.ApplicationStatus;
using Portal.Gateway.ApiModel.Requests.Insurance;
using Portal.Gateway.Contracts.Entities;
using Portal.Gateway.Contracts.Entities.Dto;
using Portal.Gateway.Contracts.Entities.Dto.Appointment.Application;
using Portal.Gateway.Contracts.Entities.Dto.Appointment.Application.Requests;
using Portal.Gateway.Contracts.Entities.Dto.Appointment.Application.Responses;
using Portal.Gateway.Contracts.Entities.Dto.Appointment.ApplicationStatus.Requests;
using Portal.Gateway.Contracts.Entities.Dto.Appointment.ApplicationStatus.Responses;
using Portal.Gateway.Contracts.Entities.Dto.Appointment.BranchStatusEmail.Requests;
using Portal.Gateway.Contracts.Entities.Dto.Appointment.BranchStatusEmail.Responses;
using Portal.Gateway.Contracts.Entities.Dto.Appointment.BranchStatusSms.Requests;
using Portal.Gateway.Contracts.Entities.Dto.Appointment.BranchStatusSms.Responses;
using Portal.Gateway.Contracts.Entities.Dto.Appointment.ExtraFee.Requests;
using Portal.Gateway.Contracts.Entities.Dto.Appointment.ExtraFee.Responses;
using Portal.Gateway.Contracts.Entities.Dto.Appointment.Insurance;
using Portal.Gateway.Contracts.Entities.Dto.Appointment.Insurance.Responses;
using Portal.Gateway.Contracts.Entities.Dto.Appointment.PhotoBooth.Requests;
using Portal.Gateway.Contracts.Entities.Dto.Appointment.PhotoBooth.Responses;
using Portal.Gateway.Contracts.Entities.Dto.Appointment.PreApplication.Requests;
using Portal.Gateway.Contracts.Entities.Dto.Appointment.PreApplication.Responses;
using Portal.Gateway.Contracts.Entities.Dto.Appointment.Sap;
using Portal.Gateway.Contracts.Entities.Dto.Management.ApplicationFormElement.Responses;
using System;
using System.Collections.Generic;
using System.Threading.Tasks;
using Portal.Gateway.Contracts.Entities.Dto.Insurance;
using Portal.Gateway.Contracts.Entities.Dto.Insurance.EmaaHasarServisExcel.Request;
using Portal.Gateway.ApiModel.Requests.Appointment.Application;
using Portal.Gateway.ApiModel.Responses.Public.Application;
using Portal.Gateway.ApiModel.Responses.General;
using Portal.Gateway.Contracts.Entities.Dto.General.Requests;
using Portal.Gateway.Contracts.Entities.Dto.General;
using Portal.Gateway.ApiModel.Responses.Accounting;
using Portal.Gateway.Contracts.Entities.Dto.Appointment.Cargo;
using Portal.Gateway.ApiModel.Requests.Management.User;
using Portal.Gateway.Contracts.Entities.Dto.Appointment.KsaIcr;
using Portal.Gateway.Contracts.Entities.Dto.Appointment.VisaType.Requests;
using Portal.Gateway.Contracts.Entities.Dto.Appointment.VisaType.Responses;
using Portal.Gateway.Contracts.Entities.Dto.Appointment.MainExtraFeeCategory.Requests;
using Portal.Gateway.Contracts.Entities.Dto.Appointment.MainExtraFeeCategory.Responses;

namespace Portal.Gateway.Contracts.Services
{
    public interface IAppointmentService : IBaseService
    {
        #region Insurance

        Task<AddResponseDto> AddInsuranceAsync(AddInsuranceDto request);

        Task<bool> CheckApplicationInsuranceIsExist(int applicationId);

        Task<string> GetUnicoCountryId(string countryIso2);

        Task<InsuranceBranchCountryDto> GetInsuranceBranchCountryId(int branchId);

        Task<AddResponseDto> GetNllChechByPassportNumberAndNationalty(GetNllCheckByPassportNumberNationaltyApiRequest request);
        Task<ApplicationChechRefundDoneResponseDto> GetRejectionRefundDoneChechByPassportNumberAndNationalty(GetNllCheckByPassportNumberNationaltyApiRequest request);

        Task<ApplicationInsuranceResponseDto> GetInsuranceByPolicyNumberAsync(string policyNumber);
        Task<AddResponseDto> AddClaimLossEntryLogAsync(AddClaimLossEntryLogDto request);
        Task<UpdateResponseDto> UpdateTempHasarServisVerileriAsync(AddClaimLossEntryLogDto request);
		Task<UpdateResponseDto> UpdateOldInsurancePassiveAsync(int applicationId,string policyNumber);

		Task<Pagination<EmaaHasarServisExcelListDto>> GetSanitizedPaginatedEmaaHasarServisExcelAsync(PaginatedEmaaHasarServisApiRequestDto request);
		Task<UpdateResponseDto> UpdateClaimLossStatusAsync(AddClaimLossEntryLogDto request);
        Task<UpdateResponseDto> UpdateClaimLossStatusAsync2(AddClaimLossEntryLogDto request);
		Task<UpdateResponseDto> UpdateFailReSendClaimLossStatusAsync(AddClaimLossEntryLogDto request);


		#endregion

		#region ExtraFees

		Task<AddResponseDto> AddExtraFeeAsync(AddExtraFeeRequestDto request);

        Task<UpdateResponseDto> UpdateExtraFeeAsync(UpdateExtraFeeRequestDto request);

        Task<DeleteResponseDto> DeleteExtraFeeAsync(int id);

        Task<ExtraFeeResponseDto> GetExtraFeeAsync(int id);

        Task<Pagination<ExtraFeesResponseDto>> GetPaginatedExtraFeesAsync(PaginatedExtraFeesRequestDto request);

        #endregion

        #region ApplicationStatus

        Task<AddResponseDto> AddApplicationStatusAsync(AddApplicationStatusRequestDto request);

        Task<UpdateResponseDto> UpdateApplicationStatusAsync(UpdateApplicationStatusRequestDto request);
        Task<DeleteResponseDto> DeleteApplicationStatusAsync(DeleteRequestDto request);

        Task<ApplicationStatusResponseDto> GetApplicationStatusAsync(int id);

        Task<Pagination<PaginatedApplicationStatusResponseDto>> GetPaginatedApplicationStatusAsync(PaginatedApplicationStatusRequestDto request);
     
        #endregion

        #region ApplicationStatusOrder

        Task<UpdateResponseDto> UpdateApplicationStatusOrderAsync(UpdateApplicationStatusOrderRequestDto request);
        Task<UpdateResponseDto> UpdateIncorrectApplicationStatusAsync(UpdateIncorrectApplicationStatusRequestDto request);

        #endregion

        #region BranchStatusSms

        Task<UpdateResponseDto> UpdateBranchStatusSmsAsync(UpdateBranchStatusSmsRequestDto request);

        Task<BranchStatusSmsResponseDto> GetBranchStatusSmsAsync(BranchStatusSmsRequestDto request);

        #endregion

        #region BranchStatusEmail

        Task<UpdateResponseDto> UpdateBranchStatusEmailAsync(UpdateBranchStatusEmailRequestDto request);
        Task<BranchStatusEmailResponseDto> GetBranchStatusEmailAsync(BranchStatusEmailRequestDto request);

        #endregion

        #region Application

        Task<UpdateResponseDto> UpdateSelectedApplicationStatusForApplicationFile(UpdateSelectedApplicationStatusForApplicationFileRequestDto request);
        Task<ForeignHealthInsuranceCheckDto> ForeignHealthInsuranceCheckAsync(ApplicationSummaryRequestDto request);
        Task<AddResponseDto> AddNonApplicationYssAsync(AddNonApplicationYssRequestDto request);
        Task<UpdateResponseDto> UpdateApplicationCurrentStatusAsync(UpdateApplicationsCurrentStatusRequestDto request);
        Task<UpdateResponseDto> UpdateAllApplicationCurrentStatusAsync(UpdateAllApplicationsCurrentStatusRequestDto request);
        Task<Tuple<UpdateResponseDto, IEnumerable<ApplicationDto>>> UpdateSelectedApplicationsCurrentStatusAsync(UpdateSelectedApplicationsCurrentStatusRequestDto request);
        Task<BranchApplicationCountriesResponseDto> GetBranchApplicationCountriesAsync(BranchApplicationCountriesRequestDto request);
        Task<BranchApplicationCountryVisaCategoriesResponseDto> GetBranchApplicationCountryVisaCategoriesAsync(BranchApplicationCountryVisaCategoriesRequestDto request);
        Task<PreApplicationFormResponseDto> GetPreApplicationFormAsync(PreApplicationFormRequestDto request);
        Task<AddResponseDto> AddApplicationAsync(AddUpdateApplicationRequestDto request);
        Task<UpdateApplicationResponseDto> UpdateApplicationAsync(AddUpdateApplicationRequestDto request);
        Task<UpdateResponseDto> UpdateApplicationEasyUseAsync(UpdateApplicationEasyUseRequestDto request);
        Task<Pagination<ApplicationsDto>> GetPaginatedApplicationsAsync(PaginatedApplicationsRequestDto request);
        Task<ApplicationDto> GetApplicationAsync(ApplicationRequestDto request);
        Task<ApplicationDto> GetApplicationInsuranceInformationAsync(ApplicationRequestDto request);
        Task<ApplicationExtraFeeDto> GetSanitizedApplicationExtraFeesAsync(ApplicationExtraFeeRequestDto request);
		Task<ApplicationVisaRejectionDto> GetSendVisaRejectionApplicationAsync(ApplicationVisaRejectionRequestDto request);
		Task<ApplicationSummaryDto> GetSanitizedApplicationSummaryAsync(ApplicationSummaryRequestDto request);
        Task<ApplicationDocumentDto> GetSanitizedApplicationDocumentAsync(ApplicationDocumentRequestDto request);
        Task<ApplicationStatusHistoryDto> GetSanitizedApplicationStatusHistoryAsync(ApplicationStatusHistoryRequestDto request);
        Task<ApplicationHistoryDto> GetSanitizedApplicationHistoryAsync(ApplicationHistoryRequestDto request);
        Task<ApplicationEntryFormDto> GetSanitizedApplicationEntryFormAsync(ApplicationEntryFormRequestDto request);
        Task<List<string>> GetApplicationStatusHistoryAsync(ApplicationRequestDto request);
        Task<List<TrackingView>> GetApplicationStatusHistoryNewAsync(ApplicationRequestDto request);
        Task<Pagination<ApplicationsDto>> GetPaginatedApplicationsForRejectionControlAsync(PaginatedApplicationsRequestDto request);
        Task<ApplicationEasyUseDto> GetApplicationEasyUseAsync(ApplicationEasyUseRequestDto request);
        Task<RelationalApplicationsDto> GetRelationalApplicationAsync(RelationalApplicationsRequestDto request);
        Task<ApplicationNotificationResponseDto> GetStatusChangeNotificationList(GetStatusChangeNotificationRequestDto request);
        Task<ApplicationNotificationResponseDto> GetApplicationEsimNotifications(GetStatusChangeNotificationRequestDto request);
        Task<List<byte[]>> GetEsimContents(int applicationId);
        Task<ApplicationNotificationResponseDto> GetAllStatusChangeNotificationList(GetAllStatusChangeNotificationRequestDto request);
        Task<ApplicationNotificationResponseDto> GetSelectedStatusChangeNotificationList(GetSelectedStatusChangeNotificationRequestDto request);
        Task<ApplicationNotificationResponseDto> GetApprovedStatusNotificationContentAsync(GetApprovedStatusNotificationContentRequestDto request);
        Task<System.Collections.Generic.IEnumerable<int>> GetApplicationIdsByApplicationStatusAsync(int statusId);
        Task<Pagination<InsuranceApplicationsResponseDto>> GetPaginatedInsuranceApplicationsAsync(PaginatedInsuranceApplicationsRequestDto request);
        Task<UpdateResponseDto> GetPreApplicationNotExistPassportNumberAsync(string passportNumber);
        Task<Pagination<ApplicationsDto>> AddUpdateApplicationsControlForJobAsync(PaginatedApplicationsRequestDto request);
        Task<MissionRejectionResponseDto> ApplicationControlForMissionRejection(MissionRejectionRequestDto request);  
        Task<Pagination<ApplicationsDto>> AddUpdateApplicationsControlForPassportNumberAsync(PaginatedApplicationsRequestDto request);
        Task<Pagination<ApplicationsDto>> GetSanitizedPaginatedApplicationsAsync(PaginatedApplicationsRequestDto request);
        Task<Pagination<ApplicationDetailExemptPersonsDto>> GetDetailedInformationAboutExemptPersonsAsync(PaginatedApplicationDetailExemptPersonsRequestDto request);
        Task<Pagination<ApplicationsDto>> GetSanitizedPaginatedApplicationsForScanSycleAsync(PaginatedApplicationsRequestDto request);
        Task<RelationShipCheckApiResponseDto> RelationShipCheckAsync(RelationShipCheckApiRequestDto request);
        Task<UpdateResponseDto> UpdateApplicationLocalAuthorityStatusAsync(UpdateApplicationLocalAuthorityStatusRequestDto request);
        Task<AddResponseDto> GetApplicationVasTypeAsync(string passportNumber, int branchApplicationCountryId);

        Task<UpdateResponseDto> UpdateInterviewInformationAsync(ApplicationUpdateInterviewInformationRequestDto request);
        Task<CountriesDto> GetCountriesAccordingToBranchesAsync(GetBranchesRequestDto request);
        Task<ApplicationIsInsuranceAllowsForMoreThanOneYearDto> IsInsuranceAllowsForMoreThanOneYearAsync(string extraFee);
        Task<GetSapRateInformationResponseDto> GetSapRateInformationFromInsertAsync(string fcurr);
        Task<LastApplicationDto> IsLastApplicationAsync(int applicationId);
        Task<CheckContactInformationUpdateAllowedDto> CheckContactInformationUpdateAllowedAsync(int applicationId);
        Task<ValidateResponseDto> IsMainApplicationAsync(int relationalApplicationId, int id);
        Task<ValidateResponseDto> IsNotCreatedInsuranceExistAsync(int applicationId);
		Task<ClaimLossIdAndClaimAmountListDto> GetClaimLossIdAndClaimAmountForJobAsync(int count = 15);
        Task<ClaimLossIdAndClaimAmountListDto> GetClaimLossIdAndClaimAmountForJobAsync2();
		Task<ClaimLossIdAndClaimAmountListDto> GetFailReSendClaimLossIdAndClaimAmountForJobAsync(int count = 15);

		#region ApplicationCancellation

		Task<AddResponseDto> AddApplicationCancellationAsync(AddApplicationCancellationRequestDto request);
        Task<Pagination<ApplicationCancellationsDto>> GetPaginatedApplicationCancellationsAsync(PaginatedApplicationCancellationsRequestDto request);
        Task<ApplicationCancellationDto> GetApplicationCancellationAsync(ApplicationCancellationRequestDto request);
        Task<ValidateResponseDto> GetExistingPhotoboothApplicationUsedCheckAsync(int applicationId);
        Task<UpdateResponseDto> UpdateApplicationCancellationStatusAsync(UpdateApplicationCancellationStatusRequestDto request);
        Task<UpdateResponseDto> UpdatePhotoBoothApplicationDeleteAsync(int applicationId,bool usedCheck);
        Task<UpdateResponseDto> UpdatePhotoBoothApplicationStatusAsync(int photoBoothId,int statusId);

		#endregion
		Task<GetApplicationsVisaRejectionReportResponseDto> GetApplicationsVisaRejectionReportAsync(GetApplicationsVisaRejectionReportRequestDto request);
        Task<UpdateResponseDto> SendKsaIcrGenerateEInvoiceAsync(SendKsaIcrGenerateEInvoiceDto request);
        Task<UpdateResponseDto> SendKsaIcrPartialOrCancelEInvoiceAsync(SendKsaIcrGenerateEInvoiceDto request);
        Task<GetApplicationsWithExemptInsuranceRelationResponseDto> GetApplicationsWithExemptInsuranceRelationAsync(ApplicationRequestDto request);

        #endregion


        #region ContactInformationVerify
        public Task<VeriyfContactInformationCreateCodeResponseDto> ContactInformationGenerateVerificationCode(ContactInformationGenerateVerificationCodeDto request);

        public  Task<VeriyfContactInformationVerifyCodeResponseDto> ContactInformationVerifyVerificationCode(ContactInformationVerifyVerificationCodeDto request); 
        public  Task<VeriyfContactInformationVerifyCodeResponseDto> ContactInformationVerifyUser(ContactInformationVerifyUserDto request);
        Task<List<ContactInformationVerificationHistoryDto>> GetContactInformationVerificationHistory(string passportNumber, string encryptedId, int relationalApplicationId, int applicantTypeId);
        #endregion

        #region PhotoBooth

        Task<AddResponseDto> AddPhotoBoothAsync(AddPhotoBoothRequestDto request);
        Task<DeleteResponseDto> DeletePhotoBoothAsync(DeleteRequestDto request);
        Task<PhotoBoothValidationResponseDto> CheckPhotoBoothValidationAsync(long photoBoothId);
        Task<UpdatePhotoBoothResponseDto> UpdatePhotoBoothStatusAsync(long photoBoothId,int status);
        Task<Pagination<PaginatedPhotoBoothsResponseDto>> GetPaginatedNonReferencedPhotoBoothAsync(PaginatedPhotoBoothRequestDto request);
        Task<Pagination<PaginatedPhotoBoothsResponseDto>> GetPaginatedReferencedPhotoBoothAsync(PaginatedPhotoBoothRequestDto request);
        Task<Pagination<PaginatedPhotoBoothsResponseDto>> GetPaginatedPhotoBoothAsync(PaginatedPhotoBoothRequestDto request);
        Task<PhotoBoothResponseDto> GetPhotoBoothAsync(int id);


		#endregion

		#region SAP
		Boolean GetInvoceCheckFamilyAndGroupApplicationSap(string sapGroupId);
        Task<SapApplicationOrderDto> InsertOrGetSapGroupId(System.Collections.Generic.List<int> appointmentIds);
        Task<UpdateResponseDto> DeleteOrGetSapGroupId(System.Collections.Generic.List<int> appointmentIds);

        Task<UpdateResponseDto> UpdateSapOrderStatus(System.Collections.Generic.List<System.Tuple<int, System.Collections.Generic.List<int>>> appointmentExtraFeesWaittingForSap,string sapGroupId);

        Task<UpdateResponseDto> DeleteSapOrderStatus(System.Collections.Generic.List<System.Tuple<int, System.Collections.Generic.List<int>>> appointmentExtraFeesWaittingForSap);

        Task<UpdateResponseDto> CancelSapOrderExtraFee(string sapGroupId, System.Collections.Generic.List<int> extraFeeIds, System.Collections.Generic.List<int> applicationIds,bool exchangeSapCheck);
        Task<UpdateResponseDto> PartialRefundSapOrderExtraFee(string sapGroupId, System.Collections.Generic.List<int> extraFeeIds, System.Collections.Generic.List<int> applicationIds);
        Task<UpdateResponseDto> CancelFamilyAndGroupSapOrderExtraFee(string sapGroupId, System.Collections.Generic.List<int> extraFeeIds, System.Collections.Generic.List<int> applicationIds);
        Task<UpdateResponseDto> UpdateRefundRollbackExtraFee(int appointmentId, System.Collections.Generic.IList<int> extraFeeIds);
		Task<UpdateResponseDto> InsertSapRateInformation(ApiModel.Responses.Accounting.GetSapRateInformationResponse.GetSapRateServiceResponse response);
		Task<AddResponseDto> AddSendVisaRejectionSapLogAsync(AddSendVisaRejectionSapLogDto request);
		#endregion

		#region PreApplication

		Task<AddResponseDto> AddPreApplicationAsync(AddUpdatePreApplicationRequestDto request);

        Task<Pagination<PaginatedPreApplicationResponseDto>> GetPaginatedPreApplicationsAsync(PaginatedPreApplicationRequestDto request);

        Task<PreApplicationResponseDto> GetPreApplicationAsync(GetPreApplicationRequestDto request);

        Task<DeleteResponseDto> DeletePreApplicationAsync(DeletePreApplicationRequestDto request);

        Task<DeleteResponseDto> DeletePreApplicationApplicantAsync(DeletePreApplicationApplicantRequestDto request);

        Task<UpdatePreApplicationResponseDto> UpdatePreApplicationAsync(AddUpdatePreApplicationRequestDto request);

        Task<UpdateResponseDto> PostponePreApplicationAsync(PostponePreApplicationRequestDto request);

        Task<PreApplicationReportResponseDto> GetPreApplicationReportAsync(GetPreApplicationReportRequestDto request);
        Task<ProcedureControlReportResponseDto> GetProcedureControlReportAsync(GetProcedureControlReportRequestDto request);

        Task<UpdateResponseDto> ActivatePreApplicationAsync(int preApplicationId, int userId);

        Task<AddResponseDto> AddUpdateApplicantFileAsync(AddUpdatePreApplicationApplicantFileRequestDto request);

        Task<CheckCallCenterErrorOrFutureAppointmentResponseDto> CallCenterError(string passportNumber);
        Task<CheckCallCenterErrorOrFutureAppointmentResponseDto> CheckFutureAppointment(string passportNumber, int branchId);

        Task<GetPreApplicationApplicantHistoryByPreApplicationResponseDto>
            GetPreApplicationApplicantHistoryByPreApplicationAsync(int preApplicationId);

        #endregion

        #region ApplicationFile

        Task<AddResponseDto> AddApplicationFileAsync(AddApplicationFileRequestDto request);
        Task<Pagination<ApplicationFilesDto>> GetPaginatedApplicationFilesAsync(PaginatedApplicationFilesRequestDto request);
        Task<ApplicationFilesDto> GetApplicationFilesAsync(PaginatedApplicationFilesRequestDto request);
        Task<DeleteResponseDto> DeleteApplicationFileAsync(DeleteRequestDto request);

        #endregion

        #region ApplicationNote

        Task<AddResponseDto> AddApplicationNoteAsync(AddApplicationNoteRequestDto request);
        Task<Pagination<ApplicationNotesDto>> GetPaginatedApplicationNotesAsync(PaginatedApplicationNotesRequestDto request);
        Task<DeleteResponseDto> DeleteApplicationNoteAsync(int id);
        Task<AddResponseDto> AddApplicationOfficialNoteAsync(AddApplicationOfficialNoteRequestDto request);

        Task<Pagination<ApplicationOfficialNotesDto>> GetPaginatedApplicationOfficialNotesAsync(PaginatedApplicationOfficialNotesRequestDto request);
        Task<DeleteResponseDto> DeleteApplicationOfficialNoteAsync(int id);

        Task<ApplicationReportResponseDto> GetApplicationReportAsync(GetApplicationReportRequestDto request);
        Task<GetSendToSapOrderDto> GetSendToSapOrder(int appointmentId);
        
        #endregion

        #region ApplicationVisaDecision

        Task<AddResponseDto> AddApplicationVisaDecisionAsync(AddApplicationVisaDecisionRequestDto request);
        Task<Pagination<ApplicationVisaDecisionsDto>> GetPaginatedApplicationVisaDecisionsAsync(PaginatedApplicationVisaDecisionsRequestDto request);
        Task<DeleteResponseDto> DeleteApplicationVisaDecisionAsync(int id);
        Task<GetApplicationVisaDecisionInformationResponseDto> GetApplicationVisaDecisionInformation(int id, int languageId);
        Task<PreApplicationConfirmationPdfMailResponseDto> AddUpdatePreApplicationConfirmationPdfAsync(int slotId, int preApplicationId, int languageId);
        Task<ApplicationFormElementsResponseDto> IsRequiredAsync(int branchApplicationCountryId);

        #endregion

        #region ApplicationRelationalServices

        Task<ApplicationRelatedServicesDto> GetRelatedServicesApplicationsAsync(int id, int languageId);
        Task<DeleteResponseDto> DeleteRelatedInsuranceSmsHistory(int id, int userId);
        #endregion

        #region ApplicationSurvey

        Task<ApplicationSurveyDubaiDto> GetSanitizedApplicationDubaiSurveyAsync(ApplicationSurveyDubaiRequestDto request);
        Task<AddResponseDto> SaveApplicationSurveyDubaiAsync(SaveApplicationSurveyDubaiRequestDto request);
        Task<ApplicationUpdateSurveyDubaiDto> UpdateApplicationSurveyDubaiAsync(int id, int languageId);
        Task<Pagination<ApplicationSurveyDto>> GetPaginatedApplicationSurveyAsync(PaginatedApplicationSurveyRequestDto request);
        Task<DeleteResponseDto> DeleteApplicationSurveyDubaiAsync(int id, int userId);
        Task<ApplicationIsExistSurveyDto> IsExistSurveyAsync(int id);

        #endregion

        #region ApplicationNotifications

        Task<ApplicationNotificationResponseDto> GetResendNotificationListAsync(GetResendNotificationListRequestDto request);
        Task<ApplicationSmsHistoryDto> GetApplicationSmsHistoryAsync(ApplicationSmsHistoryRequestDto request);
        Task<AddResponseDto> AddApplicationSmsHistoryAsync(List<AddApplicationSmsHistoryRequestDto> request);
        Task<ApplicationEmailHistoryDto> GetApplicationEmailHistoryAsync(ApplicationEmailHistoryRequestDto request);
        Task<AddResponseDto> AddApplicationEmailHistoryAsync(List<AddApplicationEmailHistoryRequestDto> request);
        Task<ApplicationVisaCategoryReferenceNumberResponseDto> GetApplicationVisaCategoryReferenceNumberAsync(ApplicationVisaCategoryReferenceNumberRequestDto request);
        Task<UpdateApplicationReferenceNumberResponseDto> UpdateApplicationReferenceNumberAsync(UpdateApplicationReferenceNumberRequestDto request);
        Task<SendInvidualInsuranceSmsForApplicationResponseDto> SendInvidualInsuranceSmsForApplicationAsync(SendInvidualInsuranceSmsForApplicationRequestDto request);
        Task<ApplicationConfirmationCodeResponseDto> GetApplicationConfirmationCodeAsync(ApplicationConfirmationCodeRequestDto request);
        Task<UpdateApplicationConfirmationCodeResponseDto> UpdateApplicationConfirmationCodeAsync(UpdateApplicationConfirmationCodeRequestDto request);
        Task<VisaRejectionRefundForNewApplicationDto> VisaRejectionRefundForNewApplicationAsync(VisaRejectionRefundForNewApplicationRequestDto request);
        Task<GetApplicationRejectionApprovalStatusResponseDto> GetApplicationRejectionApprovalStatusAsync(int applicationId);
        Task<UpdateResponseDto> CompleteRejectionApprovalHistoryAsync(CompleteRejectionApprovalHistoryRequestDto request);
        Task<VerifyRejectionApprovalCodeResponseDto> VerifyRejectionApprovalCodeAsync(VerifyRejectionApprovalCodeRequestDto request);
        Task<AddRejectionApprovalHistoryResponseDto> AddRejectionApprovalHistoryAsync(AddRejectionApprovalHistoryRequestDto request);
        Task<CheckSupervisorInformationResponseDto> CheckSupervisorInformationAsync(CheckSupervisorInformationRequestDto request);
        Task<CheckSupervisorInformationResponseDto> CheckSupervisorInformationWithLdapAsync(CheckSupervisorInformationRequestDto request);

        #endregion

        #region Inquiry

        Task<GetApplicationInquiryResponseDto> GetApplicationInquiry(int id, int languageId);
        Task<GetInquiryInformationResponseDto> GetInquiryInformation(int id, int languageId);
        Task<DeleteResponseDto> DeleteApplicationInquiry(int id, int inquiryId);
        Task<AddResponseDto> SaveApplicationInquiry(SaveApplicationInquiryRequestDto request);

        #endregion

        #region VisaTypes

        Task<AddResponseDto> AddVisaTypeAsync(AddVisaTypeRequestDto request);

        Task<UpdateResponseDto> UpdateVisaTypeAsync(UpdateVisaTypeRequestDto request);

        Task<DeleteResponseDto> DeleteVisaTypeAsync(int id);

        Task<VisaTypeResponseDto> GetVisaTypeAsync(int id);

        Task<Pagination<VisaTypesResponseDto>> GetPaginatedVisaTypesAsync(PaginatedVisaTypesRequestDto request);

        #endregion

        #region MainExtraFeeCategory
        Task<AddResponseDto> AddMainExtraFeeCategoryAsync(AddMainExtraFeeCategoryRequestDto request);
        Task<UpdateResponseDto> UpdateMainExtraFeeCategoryAsync(UpdateMainExtraFeeCategoryRequestDto request);
        Task<DeleteResponseDto> DeleteMainExtraFeeCategoryAsync(int id);
        Task<MainExtraFeeCategoryResponseDto> GetMainExtraFeeCategoryAsync(int id);
        Task<MainExtraFeeCategoriesResponseDto> GetMainExtraFeeCategoriesAsync(GetMainExtraFeeCategoriesRequestDto request);
        #endregion

        #region AutomaticIhbCreation

        Task<IhbOrderListDto> GetIhbOrdersForJobAsync(int count = 5);
        Task<IhbOrderListDto> GetIhbOrdersFailResendForJobAsync(int count = 5);
        Task UpdateIhbOrderStatusAsync(int id,byte status);
        Task<AddResponseDto> AddIhbOrderAsync(AddIhbOrderRequestDto request);


        #endregion

        #region CancelInsuranceJob

        Task<CancelInsuranceOrderListDto> GetInsuranceCancelOrdersForJobAsync(int count = 5);
        Task<CancelInsuranceOrderListDto> GetInsuranceCancelOrdersFailResendForJobAsync(int count = 5);
        Task UpdateInsuranceCancelOrderStatusAsync(int id, byte status, byte cancellationReasonId);

        #endregion

    }
}
