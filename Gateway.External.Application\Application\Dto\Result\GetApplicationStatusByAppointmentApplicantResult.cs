﻿using System;
using System.Collections.Generic;

namespace Gateway.External.Application.Application.Dto.Result
{
    public class GetApplicationStatusByAppointmentResult : BaseServiceResult<GetApplicationStatus>
    {
        public List<TrackingViews> TrackingViewsList { get; set; }

    }

    public class TrackingViews
    {
        public string NameSurname { get; set; }
        public DateTimeOffset ApplicationTime { get; set; }
        public string ApplicationTimeText { get; set; }
        public IList<TrackingView> TrackingView { get; set; }
    }
}
