﻿@model AddBranchApplicationCountryExtraFeeViewModel
@{
    var currentCulture = System.Threading.Thread.CurrentThread.CurrentCulture;
}

<form id="formAddBranchApplicationCountryExtraFee" class="card card-custom card-stretch form">
    @Html.HiddenFor(m => m.BranchApplicationCountryId)
    <input type="hidden" id="languageId" value="@currentCulture" />
    <div class="card-body">
        <div class="row">
            <div class="col-xl-12">
                <div class="form-group row mb-10">
                    <div class="col-lg-12">
                        <label class="font-weight-bolder">@SiteResources.BranchName.ToTitleCase(): <span class="font-italic font-weight-light">@Html.DisplayFor(m => m.BranchName)</span></label>
                    </div>
                    <div class="col-lg-12">
                        <label class="font-weight-bolder">@SiteResources.CountryName.ToTitleCase(): <span class="font-italic font-weight-light">@Html.DisplayFor(m => m.CountryName)</span></label>
                    </div>
                </div>
                <div class="form-group row">
                    <div class="col-lg-3">
                        <label class="font-weight-bold">@SiteResources.ExtraFeeName.ToTitleCase()</label>
                        @(Html.Kendo().DropDownListFor(m=> m.ExtraFeeId)
                            .HtmlAttributes(new { @class = "form-control" })
                            .OptionLabel(SiteResources.Select)
                            .DataTextField("Text")
                            .DataValueField("Value")
                            .DataSource(source => {
                                source.Read(read =>
                                {
                                    read.Action("GetExtraFeeSelectList", "Parameter", new { Area = "" });
                                });
                            })
                            )
                        <span asp-validation-for="ExtraFeeId"></span>
                    </div>
                    <div class="col-lg-3" style="padding-top: 25px;">
                        @(Html.Kendo().CheckBoxFor(m => m.IsAutoChecked).HtmlAttributes(new { @class = "checkbox-square" }).Label(SiteResources.IsAutoChecked))
                    </div>
                    <div class="col-lg-3" style="padding-top: 25px;">
                        @(Html.Kendo().CheckBoxFor(m => m.ShowInICR).HtmlAttributes(new { @class = "checkbox-square" }).Label(SiteResources.ShowInICR))
                    </div>
                    <div class="col-lg-3" style="padding-top: 25px;">
                        @(Html.Kendo().CheckBoxFor(m => m.ShowInSummary).HtmlAttributes(new { @class = "checkbox-square" }).Label(SiteResources.ShowInSummary))
                    </div>
                    <div class="col-lg-3" style="padding-top: 25px;">
                        @(Html.Kendo().CheckBoxFor(m => m.IsShowInReport).HtmlAttributes(new { @class = "checkbox-square" }).Label(SiteResources.IsShowInReport))
                    </div>
                    <div class="col-lg-3" style="padding-top: 25px;">
                        @(Html.Kendo().CheckBoxFor(m => m.IsGroupInIcr).HtmlAttributes(new { @class = "checkbox-square" }).Label(SiteResources.IsGroupInIcr))
                    </div>
                    <div class="col-lg-3" style="padding-top: 25px;">
                        @(Html.Kendo().CheckBoxFor(m => m.IsShowInRejectionList).HtmlAttributes(new { @class = "checkbox-square" }).Label(SiteResources.IsShowInRejectionList))
                    </div>
                    <div class="col-lg-3" style="padding-top: 25px;">
                        @(Html.Kendo().CheckBoxFor(m => m.IsShowInAllApplicationsReport).HtmlAttributes(new { @class = "checkbox-square" }).Label(SiteResources.ShowInAllApplicationsReport))
                    </div>
                </div>
                <div class="form-group row">
                    <div class="col-lg-3">
                        <label class="font-weight-bold">@SiteResources.SalesFee.ToTitleCase()</label>
                        @(Html.Kendo().NumericTextBoxFor(m => m.BasePrice).HtmlAttributes(new { @class = "checkbox-square" }))
                    </div>
                    <div class="col-lg-3">
                        <label class="font-weight-bold">@SiteResources.TaxRate.ToTitleCase() (%)</label>
                        @(Html.Kendo().NumericTextBoxFor(m => m.TaxRatio).HtmlAttributes(new { @class = "checkbox-square" }).Min(0))
                    </div>
                    <div class="col-lg-3">
                        <label class="font-weight-bold">@SiteResources.Tax.ToTitleCase()</label><br />
                        @(Html.Kendo().TextBoxFor(m => m.Tax).HtmlAttributes(new { @class = "checkbox-square", @readonly="true" }))
                    </div>
                    <div class="col-lg-3">
                        <label class="font-weight-bold">@SiteResources.ServiceTax.ToTitleCase()</label><br />
                        @(Html.Kendo().NumericTextBoxFor(m => m.ServiceTax).HtmlAttributes(new { @class = "checkbox-square" }).Min(0))
                    </div>
                </div>
                <div class="form-group row">
                    <div class="col-lg-2">
                        <div class="form-group">
                            <label asp-for="CurrencyId">@SiteResources.Currency.ToTitleCase()</label>
                            <select asp-for="CurrencyId" asp-items="Model.CurrencyList" class="form-control"> </select>
                        </div>
                    </div>
                    <div class="col-lg-3" style="padding-top: 25px;">
                        @(Html.Kendo().CheckBoxFor(m => m.IsShowInApplicationAfterRejection).HtmlAttributes(new { @class = "checkbox-square" }).Label(SiteResources.ShowExtraFeeOnNewApplicationByRejectionStatus.ToTitleCase()))
                    </div>
                </div>
                <div class="form-group row">
                    <div class="col-lg-3">
                        <label class="font-weight-bold">@SiteResources.ExtraFeeSapId.ToTitleCase()</label>
                        <input type="text" asp-for="SapExtraFeeId" class="form-control" />
                    </div>
                </div>
            </div>
        </div>
    </div>
    <div class="modal-footer">
        <button type="button" class="btn btn-light-primary font-weight-bold" data-dismiss="modal">@SiteResources.Close</button>
        <button type="submit" class="btn btn-primary font-weight-bold">@SiteResources.Save</button>
    </div>
</form>

<partial name="Scripts/_ValidationScripts" />

<script src="~/js/site.js"></script>
<script src="~/js/Management/BranchApplicationCountryExtraFee/branchApplicationCountryExtraFee.js"></script>