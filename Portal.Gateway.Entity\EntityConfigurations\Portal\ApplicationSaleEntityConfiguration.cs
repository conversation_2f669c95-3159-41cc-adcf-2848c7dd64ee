﻿using Microsoft.EntityFrameworkCore.Metadata.Builders;
using Microsoft.EntityFrameworkCore;
using Portal.Gateway.Entity.Entities.Portal;

namespace Portal.Gateway.Entity.EntityConfigurations.Portal
{
    public class ApplicationSaleEntityConfiguration : IEntityTypeConfiguration<ApplicationSale>
    {
        public void Configure(EntityTypeBuilder<ApplicationSale> builder)
        {
            builder.ToTable("application_sale");

            builder.HasIndex(e => e.ApplicationId, "IX_ApplicationSale_ApplicationId");

            builder.HasIndex(e => e.Status, "IX_ApplicationSale_Status");

            #region StaticAuditableEntityConfig

            builder.HasKey(e => e.Id);
            builder.Property(e => e.Id).IsRequired().ValueGeneratedOnAdd().HasColumnName("id");
            builder.Property(e => e.IsActive).IsRequired().HasColumnName("is_active");
            builder.Property(e => e.IsDeleted).IsRequired().HasColumnName("is_deleted");
            builder.Property(e => e.CreatedBy).HasColumnName("created_by");
            builder.Property(e => e.CreatedAt).HasColumnType("timestamp with time zone").HasColumnName("created_at");
            builder.Property(e => e.UpdatedBy).HasColumnName("updated_by");
            builder.Property(e => e.UpdatedAt).HasColumnType("timestamp without time zone").HasColumnName("updated_at");
            builder.Property(e => e.DeletedBy).HasColumnName("deleted_by");
            builder.Property(e => e.DeletedAt).HasColumnType("timestamp without time zone").HasColumnName("deleted_at");

            #endregion

            builder.Property(e => e.ApplicationExtraFeeId).HasColumnName("application_extra_fee_id");
            builder.Property(e => e.ApplicationId).HasColumnName("application_id");

            builder.Property(e => e.ExtraFeeId)
                .HasColumnName("extra_fee_id")
                .HasDefaultValueSql("200");

            builder.Property(e => e.SaleId).HasColumnName("sale_id");
            builder.Property(e => e.Status).HasColumnName("status");
            builder.Property(e => e.Type).HasColumnName("type");

            builder.Property(e => e.Xmin).HasColumnType("xid").ValueGeneratedOnAddOrUpdate().IsConcurrencyToken();
        }
    }
}
