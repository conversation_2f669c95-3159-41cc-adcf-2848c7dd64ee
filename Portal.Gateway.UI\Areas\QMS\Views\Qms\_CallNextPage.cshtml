﻿@model GetCallNextViewModel
@using Microsoft.AspNetCore.Http
@using Microsoft.AspNetCore.Mvc.TagHelpers
@using Microsoft.Extensions.Caching.Memory
@using Portal.Gateway.ApiModel.Responses.Management.RoleAction
@using SessionExtensions = Portal.Gateway.UI.Extensions.SessionExtensions
@using Kendo.Mvc.TagHelpers
@using Portal.Gateway.UI.Constants
@using Portal.Gateway.UI.Models
@using Gateway.Extensions
@inject IHttpContextAccessor HttpContextAccessor
@inject IMemoryCache MemoryCache
@{
    var currentUser = SessionExtensions.Get<UserModel>(HttpContextAccessor.HttpContext?.Session, SessionKeys.UserSession);
    var isAuthorizedForPostponeButton = false;
    if (MemoryCache.TryGetValue(CacheKeys.RoleActionCache, out var actions))
    {
        var roleActions = (RoleActionApiResponse)actions;
        isAuthorizedForPostponeButton = roleActions.RoleActionSites.Where(r => currentUser.RoleIds.Contains(r.Role.Id))
            .Any(p => p.RoleActions.Any(q => q.Action.ActionTranslations.Any(r => r.Name == SiteResources.IsAuthorizedForPostponeButton) && q.Action.IsActive));
    }
    var vfsTypes = new List<int> { (int)QmsCompanyType.Vfs, (int)QmsCompanyType.GatewayManagementVfs, (int)QmsCompanyType.GatewayInternationalVfs };
}

<style>
    .container {
        background-color: #fff4de;
        color: black;
        padding: 20px;
        text-align: center;
        font-weight: bold;
        font-size: 20px;
        letter-spacing: 3px;
        box-shadow: rgb(*********** / 20%) 0px 4px 24px;
    }

        .container.tab-top:before {
            content: " ";
            position: absolute;
            width: 60%;
            height: 7px;
            left: 50%;
            transform: translateX(-50%);
            background: #663259;
            border-radius: 20px 20px 0 0;
            top: -7px;
        }

        .container.tab-bottom:after {
            content: " ";
            position: absolute;
            width: 60%;
            height: 7px;
            left: 50%;
            transform: translateX(-50%);
            background: #663259;
            border-radius: 0 0 20px 20px;
            bottom: -7px;
        }

    .containerMessage {
        width: 350px;
        height: 230px;
        background-color: white;
        border: 5px solid #663259;
        color: #fff;
        padding: 20px;
        position: relative;
        margin: 20px;
        top: -20px;
        float: right;
    }

        .containerMessage.tab-top:before {
            content: " ";
            position: absolute;
            width: 60%;
            height: 7px;
            left: 50%;
            transform: translateX(-50%);
            background: #663259;
            border-radius: 20px 20px 0 0;
            top: -7px;
        }

        .containerMessage.tab-bottom:after {
            content: " ";
            position: absolute;
            width: 60%;
            height: 7px;
            left: 50%;
            transform: translateX(-50%);
            background: #663259;
            border-radius: 0 0 20px 20px;
            bottom: -7px;
        }

    #QmsCardBody {
        padding: 20px;
    }

    .searchIcon {
        position: absolute;
        left: 2px;
        margin-left: 5px;
    }

    .QmsApplicationsTable td {
    }

    .actionButtons {
        width: 140px;
        margin-right: 15px;
        height: 40px;
        margin-bottom: 10px;
        vertical-align: middle;
        font-size: 14px;
        border-radius: 15px;
    }

    .actionRecallButton {
        width: 140px;
        margin-right: 15px;
        height: 40px;
        margin-bottom: 10px;
        vertical-align: middle;
        font-size: 14px;
        border-radius: 15px;
        border-color: transparent;
        background-color: #059f05;
        color: #fff;
    }

        .actionRecallButton:hover {
            color: #fff;
            border-color: transparent;
            background-color: #06c506;
        }

    .open ul.dropdown-menu {
        display: block;
    }

    .close {
        font-size: 25px;
        font-weight: 300;
    }

    .dropdown a {
        cursor: pointer;
    }

    .blacklist-icon{
        width: 25px;
        height: 25px;
        background-image: url('/assets/svg/bootstrap.svg');
        background-size: contain;
        background-repeat: no-repeat;
        display: inline-block;
    }

    .blacklist-icon-link {
        text-decoration: none; 
        display: flex;
        align-items: center;
    }

    #qmsMainTable td {
        border: none !important;
    }

</style>

@Html.HiddenFor(m => m.LineId, new { @id = "paramLineId" })
@Html.HiddenFor(m => m.CounterId, new { @id = "paramCounterId" })
@Html.HiddenFor(m => m.TotalSeconds, new { @id = "paramTotalSeconds" })
@Html.HiddenFor(m => m.Interval, new { @id = "interval" })
@Html.HiddenFor(m => m.IsLineDepartmentCreateApplication, new { @id = "isLineDepartmentCreateApplicationForTimer" })
@Html.HiddenFor(m => m.OwnerToken)
@Html.HiddenFor(m => m.LineId)
@Html.HiddenFor(m => m.IsSameProcessCounter)
@Html.HiddenFor(m => m.ActiveTokenId)
@Html.HiddenFor(m => m.LineDepartmentId)

<div class="form">
    <div id="QmsCardBody">
        <div class="row">
            <div class="col-xl-12">
                <div class="form-group row mb-5">
                    <div class="col-lg-4">
                        @{
                            if (!Model.SearchByPassportNumber)
                            {
                                <form id="report-form">
                                    <label class="font-weight-bold" style="font-size: 1.1rem">@string.Concat(SiteResources.AppointmentNumber.ToTitleCase(), "")</label>
                                    <div class="input-group input-group-solid">

                                        <input name="filterAppointmentNumber" type="text" asp-for="QmsFilterAppointmentNumber" class="form-control" oninput="$(this).val($(this).val().replace(/[^0-9]/g, '')); $(this).val($(this).val().replace(/^0+/, ''));" />
                                        <div class="input-group-append">
                                            <a class="btn btn-outline-success" style="font-size: 12px; padding-left: 8px" @*data-toggle='modal' data-target='#searchPreApplicationApplicantByAppointmentNumber'*@ href='javascript:void(0);' onclick="partialSearchPreApplicationApplicantByAppointmentNumber();">@SiteResources.Search</a>
                                        </div>
                                    </div>
                                </form>
                            }
                        }
                    </div>
                    <div class="col-lg-3 container">
                        <span id="MainTokenNumber" style="text-align: center">@Model.Screen</span>
                    </div>
                    <div class="float-right">
                        <button type="button" class="btn btn-lg btn-primary" style="font-size: 14px; font-weight: bold; background-color: #31d970; width: 150px; border-radius: 65px; border: 0;" id="timer">
                            <span class="timerContent">
                                <span id="hours">00</span>:
                                <span id="minutes">00</span>:
                                <span id="seconds">00</span>
                            </span>
                        </button>
                    </div>
                </div>
                <ul class="nav nav-tabs col-lg-10" role="tablist">
                    <li class="nav-item">
                        <a class="nav-link active" data-toggle="tab" href="#ApplicantTable">
                            <span class="nav-icon">
                                <i class="la la-file"></i>
                            </span>
                            <span class="nav-text">@SiteResources.ApplicantList.ToTitleCase()</span>
                        </a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link" data-toggle="tab" href="#GeneratedTokenNote">
                            <span class="nav-icon">
                                <i class="la la-file"></i>
                            </span>
                            <span class="nav-text">@SiteResources.Notes.ToTitleCase()</span>
                        </a>
                    </li>
                </ul>
                <div class="form-group row mb-5">
                    <div class="tab-content col-lg-10" style="padding-top: 20px">
                        <div style="height: 100%;" class="tab-pane fade active show" id="ApplicantTable" role="tabpanel" aria-labelledby="ApplicantTable-tab">
                            <div style="height: 75%; overflow-y: auto; " class="soft-scroll">
                                <table id="qmsMainTable" class="table">
                                    <thead>
                                        <tr>
                                            <th scope="col">@SiteResources.Applicant</th>
                                            <th scope="col">@SiteResources.PassportNumber</th>
                                            @if (Model.SearchByPassportNumber)
                                            {
                                                <th scope="col">@SiteResources.ApplicationDate</th>
                                            }
                                            @if (Model.IsLineDepartmentCreateApplication)
                                            {
                                                <th scope="col">@SiteResources.Individual.ToTitleCase()</th>
                                                <th scope="col">@SiteResources.Family.ToTitleCase()</th>
                                                <th scope="col">@SiteResources.Group.ToTitleCase()</th>
                                            }
                                        </tr>
                                    </thead>

                                    @Html.HiddenFor(m => Model.AppointmentId)

                                    @foreach (var item in Model.Applicants.Where(r => r.ApplicantState == (byte)TokenState.InProgress))
                                    {
                                        @Html.HiddenFor(model => item.Id, new { @class = "ParamApplicantIds" })
                                    }

                                    <tbody id="body-callnext">
                                        @foreach (var item in Model.Applicants.Select((value, index) => new { index, value }))
                                        {
                                            <tr class="tbl-row" row-index="@Html.Raw(item.index)" show-create-application-controls="@Html.Raw(item.value.ShowCreateApplicationControls)" show-application-completed-counter="@Html.Raw(item.value.ApplicationCompletedCounterId)" show-application-completed-line-department="@Html.Raw(item.value.ApplicationCompletedLineDepartmentId)"
                                                show-individual-action-controls="@Html.Raw(item.value.ApplicantState)" applicant-Id="@Html.Raw(item.value.Id)" is-blacklist-applicant="@Html.Raw(item.value.IsBlackListApplicant)" is-whitelist-applicant="@Html.Raw(item.value.IsWhiteListApplicant)">
                                                <td>
                                                    <span>@item.value.NameSurname.ToTitleCase() <br /></span>
                                                    @if (item.value.IsWhiteListApplicant && item.value.WhiteListInformationViewModel != null)
                                                    {
                                                        <span><button style="font-size: 11px;text-align: left;" onclick="ShowWhiteListInformation('@item.index', '@item.value.WhiteListInformationViewModel.ToJson().Replace("'", "\\'")')">@SiteResources.ViewWhiteListNotes.ToTitleCase()</button></span>
                                                    }
                                                </td>
                                                <td>
                                                    <span>@item.value.PassportNumber.ToTitleCase()<br /></span>
                                                    @if (item.value.HasNotCompletedReason && item.value.NotCompletedReasonViewModel != null)
                                                    {
                                                        <span><button style="font-size: 11px;text-align: left;" onclick="ShowNotCompletedReason('@item.index', '@item.value.NotCompletedReasonViewModel.ToJson()');">@SiteResources.ViewNotCompletedReasons.ToTitleCase()</button></span>
                                                    }
                                                </td>
                                                @if (Model.SearchByPassportNumber)
                                                {
                                                    <td>@item.value.ApplicationTime</td>
                                                }
                                                <td class="canEmpty">
                                                    @if (item.value.ShowCreateApplicationControls)
                                                    {
                                                        @(Html.Kendo().CheckBoxFor(m => item.value.IsIndividual)
                                                            .HtmlAttributes(new { @class = "checkbox-square", @onchange = "cbChanged(this)" }))
                                                    }
                                                </td>
                                                <td class="canEmpty">
                                                    @if (item.value.ShowCreateApplicationControls)
                                                    {
                                                        @(Html.Kendo().CheckBoxFor(m => item.value.IsFamily)
                                                            .HtmlAttributes(new { @class = "checkbox-square", @onchange = "cbChanged(this)" }))
                                                    }
                                                </td>
                                                <td class="canEmpty">
                                                    @if (item.value.ShowCreateApplicationControls)
                                                    {
                                                        @(Html.Kendo().CheckBoxFor(m => item.value.IsGroup)
                                                            .HtmlAttributes(new { @class = "checkbox-square", @onchange = "cbChanged(this)" }))
                                                    }
                                                </td>
                                                <td class="canEmpty" style="font-weight:bold; color:red; padding-left:0.25rem; padding-right:0.25rem;">
                                                    @if (item.value.HealthInsurance)
                                                    {
                                                        <span scope="col">@SiteResources.FHI.ToTitleCase()</span>
                                                    }
                                                </td>
                                                <td class="canEmpty" style="font-weight:bold; color:red; padding-left:0.25rem; padding-right:0.25rem;">
                                                    @if (item.value.TrafficInsurance)
                                                    {
                                                        <span scope="col">@SiteResources.TI.ToTitleCase()</span>
                                                    }
                                                </td>
                                                <td class="canEmpty">
                                                    @if (item.value.ShowCreateApplicationControls)
                                                    {
                                                        <button type="button" class="btn btn-light mr-5 app-btn" style="width: 145px;" disabled onclick="CreateApplication('@item.value.Id.ToEncrypt()', '@item.value.GeneratedTokenId.ToEncrypt()', '@item.value.WhiteListInformationViewModel?.Id.ToEncrypt()', this);">@SiteResources.CreateApplication</button>
                                                    }
                                                </td>

                                                @if (Model.Applicants.Length > 1)
                                                {
                                                    @if (item.value.IsApplicationCompleted && item.value.ApplicationCompletedLineDepartmentId == Model.LineDepartmentId)
                                                    {
                                                        <td class="dropdown" style="width: 145px">
                                                            <button class="btn btn-primary  dropdown-toggle actions actionButtons_" id="individualAction" data-toggle="dropdown" aria-haspopup="true" aria-expanded="false">
                                                                @SiteResources.IndividualAction
                                                            </button>
                                                            <ul class="dropdown-menu" aria-labelledby="individualAction">
                                                                <li> <a style="font-size: 15px" class="dropdown-item" onclick="partialAssign('@true', '@item.value.Id');" data-toggle='modal' data-target='#Assign'>@SiteResources.Assign.ToTitleCase()</a></li>
                                                                @*<li> <a style="font-size: 15px" class="dropdown-item" onclick="IndividualAction('@TokenState.HoldOn.ToInt()', '@item.value.Id');">@SiteResources.HoldOn.ToTitleCase()</a></li>*@
                                                            </ul>
                                                        </td>
                                                    }
                                                    else
                                                    {
                                                        <td class="dropdown" style="width: 145px">
                                                            <button class="btn btn-primary  dropdown-toggle actions actionButtons_" id="individualAction" data-toggle="dropdown" aria-haspopup="true" aria-expanded="false">
                                                                @SiteResources.IndividualAction
                                                            </button>
                                                            <ul class="dropdown-menu" aria-labelledby="individualAction">
                                                                <li> <a style="font-size: 15px" class="dropdown-item" onclick="partialAssign('@true', '@item.value.Id');" data-toggle='modal' data-target='#Assign'>@SiteResources.Assign.ToTitleCase()</a></li>

                                                                @if (isAuthorizedForPostponeButton && !vfsTypes.Contains(currentUser.QmsCompanyId))
                                                                {
                                                                    <li> <a style="font-size: 15px" class="dropdown-item" onclick="partialPostpone('@(Model.AppointmentId)', '@(Model.QmsBranchApplicationCountryId)', '1', '@true', '@item.value.Id', '@null')" data-toggle='modal' data-target='#Postpone'>@SiteResources.Postpone.ToTitleCase()</a></li>
                                                                }

                                                                @*  <li> <a style="font-size: 15px" class="dropdown-item" onclick="IndividualAction('@TokenState.HoldOn.ToInt()', '@item.value.Id');">@SiteResources.HoldOn.ToTitleCase()</a></li>*@

                                                                @*@if (!isNotAuthorizedCancelActionRoles)
                                                    {
                                                    <li> <a style="font-size: 15px" class="dropdown-item" onclick="IndividualAction('@TokenState.CancelledByApplicant.ToInt()', '@item.value.Id');">@SiteResources.Cancelled.ToTitleCase()</a></li>
                                                    }*@

                                                                <li> <a style="font-size: 15px" class="dropdown-item" onclick="partialNotFound('@true', '@item.value.Id');" data-toggle='modal' data-target='#NotFound'>@SiteResources.NotCompleted.ToTitleCase()</a></li>
                                                            </ul>
                                                        </td>
                                                    }
                                                }
                                                else
                                                {
                                                    @if (item.value.IsApplicationCompleted && item.value.ApplicationCompletedLineDepartmentId == Model.LineDepartmentId)
                                                    {
                                                        <td class="dropdown" style="width: 145px; display: none">
                                                            <button class="btn btn-primary  dropdown-toggle actions actionButtons_" id="individualAction" data-toggle="dropdown" aria-haspopup="true" aria-expanded="false">
                                                                @SiteResources.IndividualAction
                                                            </button>
                                                            <ul class="dropdown-menu" aria-labelledby="individualAction">
                                                                <li> <a style="font-size: 15px" class="dropdown-item" onclick="partialAssign('@true', '@item.value.Id');" data-toggle='modal' data-target='#Assign'>@SiteResources.Assign.ToTitleCase()</a></li>
                                                                @* <li> <a style="font-size: 15px" class="dropdown-item" onclick="IndividualAction('@TokenState.HoldOn.ToInt()', '@item.value.Id');">@SiteResources.HoldOn.ToTitleCase()</a></li>*@
                                                            </ul>
                                                        </td>
                                                    }
                                                    else
                                                    {
                                                        <td class="dropdown" style="width: 145px; display: none">
                                                            <button class="btn btn-primary  dropdown-toggle actions actionButtons_" id="individualAction" data-toggle="dropdown" aria-haspopup="true" aria-expanded="false">
                                                                @SiteResources.IndividualAction
                                                            </button>
                                                            <ul class="dropdown-menu" aria-labelledby="individualAction">
                                                                <li> <a style="font-size: 15px" class="dropdown-item" onclick="partialAssign('@true', '@item.value.Id');" data-toggle='modal' data-target='#Assign'>@SiteResources.Assign.ToTitleCase()</a></li>

                                                                @if (isAuthorizedForPostponeButton && !vfsTypes.Contains(currentUser.QmsCompanyId))
                                                                {
                                                                    <li> <a style="font-size: 15px" class="dropdown-item" onclick="partialPostpone('@(Model.AppointmentId)', '@(Model.QmsBranchApplicationCountryId)', '1', '@true', '@item.value.Id', '@null')" data-toggle='modal' data-target='#Postpone'>@SiteResources.Postpone.ToTitleCase()</a></li>
                                                                }

                                                                @* <li> <a style="font-size: 15px" class="dropdown-item" onclick="IndividualAction('@TokenState.HoldOn.ToInt()', '@item.value.Id');">@SiteResources.HoldOn.ToTitleCase()</a></li>*@

                                                                @*@if (!isNotAuthorizedCancelActionRoles)
                                                    {
                                                    <li> <a style="font-size: 15px" class="dropdown-item" onclick="IndividualAction('@TokenState.CancelledByApplicant.ToInt()', '@item.value.Id');">@SiteResources.Cancelled.ToTitleCase()</a></li>
                                                    }*@

                                                                <li> <a style="font-size: 15px" class="dropdown-item" onclick="partialNotFound('@true', '@item.value.Id');" data-toggle='modal' data-target='#NotFound'>@SiteResources.NotCompleted.ToTitleCase()</a></li>
                                                            </ul>
                                                        </td>
                                                    }
                                                }
                                                @{
                                                    if (@item.value.IsBlackListApplicant && @item.value.BlackListInformationViewModel != null)
                                                    {
                                                        if (item.value.BlackListInformationViewModel.BlackListNote != null)
                                                        {
                                                            <td style=" vertical-align:middle;">
                                                                <a id="blacklist_button" class="blacklist-icon-link" style="position: relative; cursor: pointer; justify-content:center;" onclick="ShowBlackListInformation('@item.index');"> <div class="blacklist-icon"></div></a>
                                                            </td>

                                                            var partialContent = await Html.PartialAsync("_BlackListNotesMainPage", item.value.BlackListInformationViewModel);

                                                            @(Html.Kendo().Window()
                                                            .Name($"BlackListWindow_{item.index}")
                                                            .Content(      
                                                            @<text>
                                                                @partialContent
                                                            </text>)
                                                            .Title(SiteResources.BlackListNotes.ToTitleCase())
                                                            .Draggable()
                                                            .Resizable()
                                                            .Width(600)
                                                            .Scrollable(false)
                                                            .Visible(false))
                                                        }
                                                    }
                                                }

                                            </tr>

                                            @if (item.value.IsWhiteListApplicant && item.value.WhiteListInformationViewModel != null)
                                            {
                                                @(Html.Kendo().Window()
                                                    .Name($"WhiteListWindow_{item.index}")
                                                    .Content("")
                                                    .Title(SiteResources.WhiteListNotes.ToTitleCase())
                                                    .Draggable()
                                                    .Resizable()
                                                    .Width(600)
                                                    .Scrollable(false)
                                                    .Visible(false)
                                                    )
                                            }

                                            @if (item.value.HasNotCompletedReason && item.value.NotCompletedReasonViewModel != null)
                                            {
                                                Html.Kendo().Window()
                                                .Name($"NotCompletedReasonWindow_{item.index}")
                                                .Title(SiteResources.NotCompletedReason.ToTitleCase())
                                                .Draggable()
                                                .Resizable()
                                                .Width(600)
                                                .Scrollable(false)
                                                .Visible(false)
                                                .Content(
                                                Html.Kendo().Grid(item.value.NotCompletedReasonViewModel)
                                                .Name($"NotCompletedReasonGrid_{item.index}")
                                                .Columns(columns =>
                                                {
                                                    columns.Bound(o => o.ActionBy).Title(SiteResources.ProcessedBy.ToTitleCase());
                                                    columns.Bound(o => o.Reason).Title(SiteResources.Reason.ToTitleCase());
                                                    columns.Bound(o => o.ActionAt).Title(SiteResources.ProcessAt.ToTitleCase()).Media("lg");
                                                })
                                                .DataSource(dataSource => dataSource
                                                .Ajax()
                                                .ServerOperation(false))
                                                .ToHtmlString()
                                                )
                                                .Render();
                                            }
                                        }
                                    </tbody>
                                </table>
                            </div>
                        </div>
                        <div class="tab-pane fade" id="GeneratedTokenNote" role="tabpanel" aria-labelledby="GeneratedTokenNote-tab">
                            @await Html.PartialAsync("_QmsMainMessage", Model)
                        </div>
                    </div>
                    <div class="col-lg-2">
                        <div style="margin-left: 10px;">
                            <div>
                                <button type="button" class="btn font-weight-bold actionRecallButton" onclick="ActionRecall(@Html.Raw(Json.Serialize(Model.Applicants.Where(r => r.ApplicantState == (byte)TokenState.InProgress).Select(r => r.Id))));">
                                    @SiteResources.Recall
                                </button>
                            </div>
                            <div>
                                <button type="button" class="btn btn-primary font-weight-bold actions actionButtons actionButtons_" id="assign" onclick="partialAssign('@false', '@null', @Html.Raw(Json.Serialize(Model.Applicants.Where(r => r.ApplicantState == (byte)TokenState.InProgress).Select(r => r.Id))), @Model.IsSameProcessCounter.ToString().ToLower());" data-toggle='modal' data-target='#Assign' href='javascript:void(0);'>
                                    @SiteResources.Assign
                                </button>
                            </div>
                            <div>
                                @if (isAuthorizedForPostponeButton && !vfsTypes.Contains(currentUser.QmsCompanyId))
                                {
                                    @if (Model.Screen != null && Model.Screen.Contains("-"))
                                    {
                                        <button type="button" class="btn btn-primary font-weight-bold actionButtons" disabled>
                                            @SiteResources.Postpone
                                        </button>
                                    }
                                    else
                                    {
                                        <button type="button" class="btn btn-primary font-weight-bold actions actionButtons actionButtons_" onclick="partialPostpone('@(Model.AppointmentId)', '@(Model.QmsBranchApplicationCountryId)', '@Model.Applicants.Count(r => r.ApplicantState == (byte)TokenState.InProgress)', '@false', '@null', @Html.Raw(Json.Serialize(Model.Applicants.Where(r => r.ApplicantState == (byte)TokenState.InProgress).Select(r => r.Id))))" data-toggle='modal' data-target='#Postpone' href='javascript:void(0);'>
                                            @SiteResources.Postpone
                                        </button>
                                    }
                                }
                            </div>
                            @*                            <div>
                                <button type="button" class="btn btn-primary font-weight-bold actions actionButtons actionButtons_" id="holdOn" onclick="ActionHoldOn(@Html.Raw(Json.Serialize(Model.Applicants.Where(r => r.ApplicantState == (byte)TokenState.InProgress).Select(r => r.Id))), @Model.IsSameProcessCounter.ToString().ToLower());">
                                    @SiteResources.HoldOn
                                </button>
                            </div>*@
                            @*<div>
                            @if (!isNotAuthorizedCancelActionRoles)
                            {
                            <button type="button" class="btn btn-primary font-weight-bold actions actionButtons actionButtons_" onclick="ActionCancelledByApplicant(@Html.Raw(Json.Serialize(Model.Applicants.Where(r => r.ApplicantState == (byte)TokenState.InProgress).Select(r => r.Id))), @Model.IsSameProcessCounter.ToString().ToLower());">
                            @SiteResources.Cancelled
                            </button>
                            }
                            </div>*@
                            <div>
                                <button type="button" class="btn btn-primary font-weight-bold actions actionButtons actionButtons_" id="notFound" onclick="partialNotFound('@false', '@null', @Html.Raw(Json.Serialize(Model.Applicants.Where(r => r.ApplicantState == (byte)TokenState.InProgress).Select(r => r.Id))), @Model.IsSameProcessCounter.ToString().ToLower());" data-toggle='modal' data-target='#NotFound' href='javascript:void(0);'>
                                    @SiteResources.NotCompleted
                                </button>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

@(Html.Kendo().Tooltip()
        .For("#blacklist_button")
        .Position(TooltipPosition.Top)
        .Width(120)
        .Content(SiteResources.ClickToSeeNote) // Tooltip content
    )
