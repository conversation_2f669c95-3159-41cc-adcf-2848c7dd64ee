﻿using Gateway.External.Application.Application.Dto.Result;
using Gateway.External.Resources;
using Microsoft.AspNetCore.Mvc;

namespace Gateway.External.Api.Models.Application
{
    public static class ApplicationResponseFactory
    {
        public static ObjectResult GetApplicationStatusResponse(GetApplicationStatusResult result)
        {
            switch (result.Status)
            {
                case GetApplicationStatus.Successful:
                    return new ObjectResult(new BaseApiResponse
                    {
                        Status = ServiceResources.SUCCESS,
                        Code = Resource.GetKey(ServiceResources.RESOURCE_RETRIEVED),
                        Message = result.Message,
                        Data = result.TrackingView
                    })
                    { StatusCode = HttpStatusCodes.Ok };
                case GetApplicationStatus.InvalidInput:
                    return new ObjectResult(new BaseApiResponse
                    {
                        Status = ServiceResources.INPUT_ERROR,
                        Code = Resource.GetKey(ServiceResources.INPUT_ERROR),
                        Message = result.Message,
                        ValidationMessages = result.ValidationMessages
                    })
                    { StatusCode = HttpStatusCodes.InvalidInput };
                case GetApplicationStatus.ApplicationNotFound:
                    return new ObjectResult(new BaseApiResponse
                    {
                        Status = ServiceResources.FAILED,
                        Code = Resource.GetKey(ServiceResources.RESOURCE_NOT_FOUND),
                        Message = result.Message,
                    })
                    { StatusCode = HttpStatusCodes.ResourceNotFound };
                case GetApplicationStatus.ApplicationStatusNotFound:
                    return new ObjectResult(new BaseApiResponse
                    {
                        Status = ServiceResources.FAILED,
                        Code = Resource.GetKey(ServiceResources.RESOURCE_NOT_FOUND),
                        Message = result.Message,
                    })
                    { StatusCode = HttpStatusCodes.ResourceNotFound };
                default:
                    return new ObjectResult(new BaseApiResponse
                    {
                        Status = ServiceResources.FAILED,
                        Code = Resource.GetKey(ServiceResources.FAILED),
                        Message = ServiceResources.FAILED
                    })
                    { StatusCode = HttpStatusCodes.InternalError };
            }
        }

        public static ObjectResult GetApplicationStatusByAppointmentResponse(GetApplicationStatusByAppointmentResult result)
        {
            switch (result.Status)
            {
                case GetApplicationStatus.Successful:
                    return new ObjectResult(new BaseApiResponse
                    {
                        Status = ServiceResources.SUCCESS,
                        Code = Resource.GetKey(ServiceResources.RESOURCE_RETRIEVED),
                        Message = result.Message,
                        Data = result.TrackingViewsList
                    })
                    { StatusCode = HttpStatusCodes.Ok };
                case GetApplicationStatus.InvalidInput:
                    return new ObjectResult(new BaseApiResponse
                    {
                        Status = ServiceResources.INPUT_ERROR,
                        Code = Resource.GetKey(ServiceResources.INPUT_ERROR),
                        Message = result.Message,
                        ValidationMessages = result.ValidationMessages
                    })
                    { StatusCode = HttpStatusCodes.InvalidInput };
                case GetApplicationStatus.ApplicationNotFound:
                    return new ObjectResult(new BaseApiResponse
                    {
                        Status = ServiceResources.FAILED,
                        Code = Resource.GetKey(ServiceResources.APPLICANT_NOT_FOUND),
                        Message = result.Message,
                    })
                    { StatusCode = HttpStatusCodes.ResourceNotFound };
                case GetApplicationStatus.AppointmentNotFound:
                    return new ObjectResult(new BaseApiResponse
                        {
                            Status = ServiceResources.FAILED,
                            Code = Resource.GetKey(ServiceResources.APPOINTMENT_NOT_FOUND),
                            Message = result.Message,
                        })
                        { StatusCode = HttpStatusCodes.ResourceNotFound };
                case GetApplicationStatus.ApplicationStatusNotFound:
                    return new ObjectResult(new BaseApiResponse
                    {
                        Status = ServiceResources.FAILED,
                        Code = Resource.GetKey(ServiceResources.APPLICATION_STATUS_NOT_FOUND),
                        Message = result.Message,
                    })
                    { StatusCode = HttpStatusCodes.ResourceNotFound };
                case GetApplicationStatus.AppointmentNotConvertedToApplication:
                    return new ObjectResult(new BaseApiResponse
                        {
                            Status = ServiceResources.FAILED,
                            Code = Resource.GetKey(ServiceResources.APPOINTMENT_NOT_CONVERTED_APPLICATION),
                            Message = result.Message,
                        })
                        { StatusCode = HttpStatusCodes.ResourceNotFound };
                default:
                    return new ObjectResult(new BaseApiResponse
                    {
                        Status = ServiceResources.FAILED,
                        Code = Resource.GetKey(ServiceResources.FAILED),
                        Message = ServiceResources.FAILED
                    })
                    { StatusCode = HttpStatusCodes.InternalError };
            }
        }
    }
}