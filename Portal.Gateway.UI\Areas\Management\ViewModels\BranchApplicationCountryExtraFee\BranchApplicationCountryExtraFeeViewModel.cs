﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Threading.Tasks;

namespace Portal.Gateway.UI.Areas.Management.ViewModels.BranchApplicationCountryExtraFee
{

    public class BranchApplicationCountryExtraFeeViewModel
    {
        public string EncryptedId { get; set; }

        public string ExtraFeeName { get; set; }

        public bool IsAutoChecked { get; set; }

        public decimal Price { get; set; }

        public decimal Tax { get; set; }

        public decimal TaxRatio { get; set; }

        public decimal ServiceTax { get; set; }

        public decimal BasePrice { get; set; }

        public string Currency { get; set; }

        public bool IsActive { get; set; }

        public bool ShowInICR { get; set; }

        public bool ShowInSummary { get; set; }

		public bool IsShowInAllApplicationsReport { get; set; }
        public bool IsShowInApplicationAfterRejection { get; set; }

    }
}
