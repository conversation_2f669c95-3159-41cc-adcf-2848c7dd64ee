﻿using System.Collections.Generic;

namespace Gateway.External.Application.SenCard.Dto.Response
{
    public class GetSenCardProvinceResult:BaseServiceResult<SenCardResultStatus>
    {
        public List<SenCardProvinceDto> Provinces { get; set; }
    }

    public class GetSenCardInstitutionResult : BaseServiceResult<SenCardResultStatus>
    {
        public List<SenCardInstitutionDto> Institutions { get; set; }
    }
    public class GetSenCardDistrictResult : BaseServiceResult<SenCardResultStatus>
    {
        public List<SenCardDistrictDto> Districts { get; set; }
    }
    public class GetSenCardOrganizationResult : BaseServiceResult<SenCardResultStatus>
    {
        public List<SenCardOrganizationDto> Organizations { get; set; }
    }
}
