﻿using System;
using System.Collections.Generic;

namespace Portal.Gateway.Entity.Entities.Portal
{
    public class ApplicationInsurance : AuditableEntity
    {
        public ApplicationInsurance()
        {
            IsActive = true;
            IsDeleted = false;
        }
        public int Id { get; set; }
        public int ApplicationId { get; set; }
        public int ProviderId { get; set; }
        public string Number { get; set; }
        public DateTime StartDate { get; set; }
        public DateTime EndDate { get; set; }
        public int ApplicationExtraFeeId { get; set; }
        public int RelatedIndividualInsuraneId { get; set; }
        public int? OldRelatedIndividualInsuraneId { get; set; }
        public string DocumentId { get; set; }
        public string Price2 { get; set; }
        public bool IsCancelled { get; set; }
        public DateTime? CancelledDate { get; set; }
        public int? CancelledBy { get; set; }
        public int? CancellationReasonId { get; set; }
        public virtual Application Application { get; set; }
        public virtual Provider Provider { get; set; }
		public virtual ApplicationExtraFee ApplicationExtraFee { get; set; }
        public virtual ICollection<CancelInsuranceOrder> CancelInsuranceOrders { get; set; }
    }
}
