﻿using System;
using System.Collections.Generic;

namespace Portal.Gateway.Contracts.Entities.Dto.Report.Common.Report_60
{
	public class Report_UpdateInsuranceResponse
	{
		public IEnumerable<Application> Applications { get; set; }

		public class Application
		{
			public string ApplicationId { get; set; }
			public DateTime ApplicationDate { get; set; }

			public string PassportNumber { get; set; }

			public string ApplicantName { get; set; }
			public string FirstInsurancePolicyDuration { get; set; }
			public string FirstInsurancePolicyNumber { get; set; }
			public decimal FirstInsurancePolicyPrice { get; set; }
			public string UpdateInsurancePolicyDuration { get; set; }
			public string UpdateInsurancePolicyNumber { get; set; }
			public decimal UpdateInsurancePolicyPrice { get; set; }
			public decimal RefundAmount { get; set; }
			public string Nationality { get; set; }
			public string VisaDecision { get; set; }

			public DateTime BirthDate { get; set; }

			public DateTime? ProcessedDate { get; set; }

			public string ProcessedBy { get; set; }
			public decimal? CompanyPrice { get; set; }
            public string? CompanyPriceCurrency { get; set; }
            public decimal? Price2 { get; set; }
            public string? Price2Currency { get; set; }
            public int? ApplicationInsuranceCategory { get; set; }
        }

	}
}
