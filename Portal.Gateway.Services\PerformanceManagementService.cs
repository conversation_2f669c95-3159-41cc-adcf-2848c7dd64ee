﻿using Gateway.Extensions;
using Microsoft.EntityFrameworkCore;
using Microsoft.Extensions.Options;
using Portal.Gateway.ApiModel;
using Portal.Gateway.Common.Utility.Helpers;
using Portal.Gateway.Contracts.Entities;
using Portal.Gateway.Contracts.Entities.Dto;
using Portal.Gateway.Contracts.Entities.Dto.PerformanceManagement.PMS.Requests;
using Portal.Gateway.Contracts.Entities.Dto.PerformanceManagement.PMS.Responses;
using Portal.Gateway.Contracts.Entities.Enums;
using Portal.Gateway.Contracts.Extensions;
using Portal.Gateway.Contracts.Services;
using Portal.Gateway.Contracts.Settings.AppSettings;
using Portal.Gateway.Contracts.UnitOfWork;
using Portal.Gateway.Entity.Context;
using Portal.Gateway.Entity.Entities.Portal;
using Portal.Gateway.Resources;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Threading.Tasks;
using Portal.Gateway.Common.Utility.Extensions;
using Nest;
using static Portal.Gateway.Contracts.Entities.Constants.ProjectConst;

namespace Portal.Gateway.Services
{
    public class PerformanceManagementService : BaseService, IPerformanceManagementService
    {
        private readonly AppSettings _appSettings;
        private readonly IUnitOfWork<PortalDbContext> _unitOfWorkPortalDb;

        public PerformanceManagementService(
           IOptions<AppSettings> appSettings,
           IUnitOfWork<PortalDbContext> unitOfWorkPortalDb) : base(appSettings)
        {
            _appSettings = appSettings.Value;
            _unitOfWorkPortalDb = unitOfWorkPortalDb;
        }

        #region Get
        public async Task<PMSResponseDto> GetPMSAsync(int id)
        {
            var existingPMS = await _unitOfWorkPortalDb.GetRepository<PerformanceManagement>().Entities
                                                .Where(p => p.IsActive && !p.IsDeleted && p.Id == id)
                                                .FirstOrDefaultAsync();

            if (existingPMS == null)
                throw new NoFoundDataPortalException(nameof(SiteResources.Exception_NoRecordsFound).ToSiteResourcesValue());

            var result = new PMSResponseDto
            {
                CountryId = existingPMS.CountryId,
                BranchId = existingPMS.BranchId,
                UserId = existingPMS.UserId,
                DataGroupId = existingPMS.DataGroupId,
                DataTypeId = existingPMS.DataTypeId,
                Date = existingPMS.Date,
                Explanation = existingPMS.Explanation,
                Value = existingPMS.Value,
            };

            return result;
        }
        #endregion

        #region Post
        public async Task<Pagination<PaginatedPMSResponseDto>> PaginatedPMSAsync(PaginatedPMSRequestDto request)
        {
            var paginationResult = new Pagination<PaginatedPMSResponseDto> { Page = request.Pagination.Page, PageSize = request.Pagination.PageSize };

            var appPMS = new PaginatedPMSResponseDto() { PerformanceManagemet = new List<PMSResponseDto>() };

            var performanceManagement = await _unitOfWorkPortalDb.GetRepository<PerformanceManagement>().Entities.Where(w => w.IsActive && !w.IsDeleted && w.UserId == request.UserId && request.BranchId == w.BranchId && w.Date >= request.Date && w.Date < request.Date.AddDays(1)).ToListAsync();
            if (performanceManagement.Any())
            {
                var users = await _unitOfWorkPortalDb.GetRepository<User>().Entities.Where(w => performanceManagement.Select(s => s.CreatedBy).Contains(w.Id)).ToListAsync();

                var dataType = "";
                foreach (var performance in performanceManagement)
                {
                    if (performance.DataGroupId == (int)PMSDataGroup.Operation)
                        dataType = EnumHelper.GetEnumAsDictionary(typeof(PMSOperationDataType)).GetOrDefault(performance.DataTypeId);
                    else if (performance.DataGroupId == (int)PMSDataGroup.Vas)
                        dataType = EnumHelper.GetEnumAsDictionary(typeof(PMSVASDataType)).GetOrDefault(performance.DataTypeId);
                    else if (performance.DataGroupId == (int)PMSDataGroup.Mistakes)
                        dataType = EnumHelper.GetEnumAsDictionary(typeof(PMSMistakesDataType)).GetOrDefault(performance.DataTypeId);
                    else if (performance.DataGroupId == (int)PMSDataGroup.Warnings)
                        dataType = EnumHelper.GetEnumAsDictionary(typeof(PMSWarningsDataType)).GetOrDefault(performance.DataTypeId);
                    else if (performance.DataGroupId == (int)PMSDataGroup.ThanksComplaints)
                        dataType = EnumHelper.GetEnumAsDictionary(typeof(PMSThanksComplaintsDataType)).GetOrDefault(performance.DataTypeId);

                    var pM = new PMSResponseDto()
                    {
                        Id = performance.Id,
                        Date = performance.Date,
                        Country = request.Country,
                        Branch = request.Branch,
                        User = request.User,
                        DataGroup = EnumHelper.GetEnumAsDictionary(typeof(PMSDataGroup)).GetOrDefault(performance.DataGroupId),
                        DataType = dataType,
                        Explanation = performance.Explanation,
                        Value = performance.Value,
                        DataDate = performance.CreatedAt ?? DateTime.Now,
                        CreatedBy = users.FirstOrDefault(f => f.Id == performance.CreatedBy).Name + " " + users.FirstOrDefault(f => f.Id == performance.CreatedBy).Surname,
                    };
                    appPMS.PerformanceManagemet.Add(pM);
                }
            }

            var feeIds = new List<string> { };
            var extraFeeIds = new List<string> { ExtraFeeFlagIds.Photocopy, ExtraFeeFlagIds.PrintOut, ExtraFeeFlagIds.FormFilling, ExtraFeeFlagIds.DigitalSimCard8GB, ExtraFeeFlagIds.DigitalSimCard20GB, ExtraFeeFlagIds.DigitalSimCard50GB }; 
            var photographExtraFeeIds = new List<string> { ExtraFeeFlagIds.Photo, ExtraFeeFlagIds.PhotoIQD, ExtraFeeFlagIds.PhotoUSD, ExtraFeeFlagIds.PhotoDZD, ExtraFeeFlagIds.PhotoTMT, ExtraFeeFlagIds.PhotoKWD };
            var courierExtraFeeIds = new List<string> { ExtraFeeFlagIds.CourierOutOfCity, ExtraFeeFlagIds.CourierInCity, ExtraFeeFlagIds.CourierInCity3, ExtraFeeFlagIds.OutOfCity3Courier };
            var mobileBiometricsExtraFeeIds = new List<string> { ExtraFeeFlagIds.MobileBiometrics1To4ApplicantsInsideCity, ExtraFeeFlagIds.MobileBiometrics4ApplicantsAndMoreInsideCity, ExtraFeeFlagIds.MobileBiometricsOutsideCity }; 
            feeIds.AddRange(extraFeeIds);
            feeIds.AddRange(photographExtraFeeIds);
            feeIds.AddRange(courierExtraFeeIds);
            feeIds.AddRange(mobileBiometricsExtraFeeIds);

            var applicationExtraFeePMS = await _unitOfWorkPortalDb.GetRepository<ApplicationExtraFee>().Entities
                                        .AsSplitQuery()
                                        .AsNoTracking()
                                        .Include(q => q.ExtraFee)
                                        .Include(q => q.Application)
                                            .ThenInclude(q => q.BranchApplicationCountry)
                                        .Where(p => p.IsActive && !p.IsDeleted 
                                                    && feeIds.Contains(p.ExtraFee.FlagId.ToString()) && p.ExtraFee.IsActive && !p.ExtraFee.IsDeleted
                                                    && p.Application.BranchApplicationCountry.BranchId == request.BranchId && p.Application.IsActive && !p.Application.IsDeleted && p.Application.StatusId != (int)Contracts.Entities.Enums.ApplicationStatus.Cancelled 
                                                    && p.CreatedAt >= request.Date && p.CreatedAt < request.Date.AddDays(1)).ToListAsync();

            var isManager = await _unitOfWorkPortalDb.GetRepository<UserRole>().Entities.Where(w => w.UserId == request.UserId && w.IsActive).AnyAsync(a => a.RoleId == 1 || a.RoleId == 2 || a.RoleId == 5 || a.RoleId == 8); // SysAdmin, Admin, Operation Manager, Country Manager
            if (isManager)
                applicationExtraFeePMS = applicationExtraFeePMS.Where(w => w.CreatedBy == request.UserId && w.CreatedBy == w.Application.CreatedBy).ToList();
            else
            {
                var applicationIds = await _unitOfWorkPortalDb.GetRepository<ApplicationExtraFee>().Entities
                    .AsSplitQuery()
                    .AsNoTracking()
                    .Include(q => q.ExtraFee)
                    .Include(q => q.Application)
                        .ThenInclude(q => q.BranchApplicationCountry)
                    .Where(p => p.IsActive && !p.IsDeleted && p.CreatedBy == request.UserId
                                && p.Application.BranchApplicationCountry.BranchId == request.BranchId && p.Application.IsActive && !p.Application.IsDeleted && p.Application.StatusId != (int)Contracts.Entities.Enums.ApplicationStatus.Cancelled
                                && p.CreatedAt >= request.Date && p.CreatedAt < request.Date.AddDays(1)).Select(s => s.ApplicationId).Distinct().ToListAsync();

                applicationExtraFeePMS = applicationExtraFeePMS.Where(w => applicationIds.Contains(w.ApplicationId)).ToList();
            }

            var appStatusIds = new List<int> { 1, 2, 4, 22 }; // Application Taken, Data Done, Received At Embassy, Biometric Enrollment Done
            var appPassportDeliveryStatusIds = new List<int> { 15, 18 }; // Hand Delivered To The Applicant, Outscan to courrier

            var applicationStatusPMS = await _unitOfWorkPortalDb.GetRepository<ApplicationStatusHistory>().Entities
                                       .Include(q => q.Application)
                                           .ThenInclude(q => q.BranchApplicationCountry)
                                       .Where(p => p.IsActive && !p.IsDeleted && p.CreatedAt >= request.Date && p.CreatedAt < request.Date.AddDays(1) && p.UserId == request.UserId && (appStatusIds.Contains(p.ApplicationStatusId) || appPassportDeliveryStatusIds.Contains(p.ApplicationStatusId)) && p.Application.BranchApplicationCountry.BranchId == request.BranchId && p.Application.IsActive && !p.Application.IsDeleted && p.Application.StatusId != (int)Contracts.Entities.Enums.ApplicationStatus.Cancelled
                                                    && p.Application.ApplicationTypeId != (int)ApplicationType.NonApplicationInsurance && p.Application.ApplicationTypeId != (int)ApplicationType.NonApplicationPcr && p.Application.ApplicationTypeId != (int)ApplicationType.NonApplicationPrintOut && p.Application.ApplicationTypeId != (int)ApplicationType.NonApplicationPhotocopy && p.Application.ApplicationTypeId != (int)ApplicationType.NonApplicationPhotograph && p.Application.ApplicationTypeId != (int)ApplicationType.NonApplication && p.Application.ApplicationTypeId != (int)ApplicationType.NonApplicationRelatedInsurance).ToListAsync();

            if (applicationExtraFeePMS.Any() || applicationStatusPMS.Any())
            {                   
                var dataTypeId = 0;

                foreach (var flag in extraFeeIds)
                {
                    if (flag == ExtraFeeFlagIds.Photocopy)
                        dataTypeId = (int)PMSVASDataType.Photocopy;
                    else if (flag == ExtraFeeFlagIds.PrintOut)
                        dataTypeId = (int)PMSVASDataType.PrintOut;
                    else if (flag == ExtraFeeFlagIds.FormFilling)
                        dataTypeId = (int)PMSVASDataType.FormFilling;
                    else if (flag == ExtraFeeFlagIds.DigitalSimCard8GB)
                        dataTypeId = (int)PMSVASDataType.eSim8;
                    else if (flag == ExtraFeeFlagIds.DigitalSimCard20GB)
                        dataTypeId = (int)PMSVASDataType.eSim20;
                    else if (flag == ExtraFeeFlagIds.DigitalSimCard50GB)
                        dataTypeId = (int)PMSVASDataType.eSim50;

                    if (applicationExtraFeePMS.Any() && applicationExtraFeePMS.Exists(e => e.ExtraFee.FlagId.ToString() == flag))
                    {
                        var pMS = applicationExtraFeePMS.Where(w => w.ExtraFee.FlagId.ToString() == flag).Select(s => new PMSResponseDto()
                        {
                            Date = request.Date,
                            Country = request.Country,
                            Branch = request.Branch,
                            User = request.User,
                            DataGroup = EnumHelper.GetEnumAsDictionary(typeof(PMSDataGroup)).GetOrDefault((int)PMSDataGroup.Vas),
                            DataType = EnumHelper.GetEnumAsDictionary(typeof(PMSVASDataType)).GetOrDefault(dataTypeId),
                            Value = applicationExtraFeePMS.Where(w => w.ExtraFee.FlagId.ToString() == flag).Sum(u => u.Quantity == 0 ? 1 : u.Quantity),
                            CreatedBy = "System",
                            DataDate = request.Date
                        }).FirstOrDefault();

                        appPMS.PerformanceManagemet.Add(pMS);
                    }
                }

                if (applicationExtraFeePMS.Any() && applicationExtraFeePMS.Exists(e => photographExtraFeeIds.Contains(e.ExtraFee.FlagId.ToString())))
                {
                    var pms = applicationExtraFeePMS.Where(w => photographExtraFeeIds.Contains(w.ExtraFee.FlagId.ToString())).Select(s => new PMSResponseDto()
                    {
                        Date = request.Date,
                        Country = request.Country,
                        Branch = request.Branch,
                        User = request.User,
                        DataGroup = EnumHelper.GetEnumAsDictionary(typeof(PMSDataGroup)).GetOrDefault((int)PMSDataGroup.Vas),
                        DataType = EnumHelper.GetEnumAsDictionary(typeof(PMSVASDataType)).GetOrDefault((int)PMSVASDataType.Photo),
                        Value = applicationExtraFeePMS.Where(w => photographExtraFeeIds.Contains(w.ExtraFee.FlagId.ToString())).Sum(u => u.Quantity == 0 ? 1 : u.Quantity),
                        CreatedBy = "System",
                        DataDate = request.Date
                    }).FirstOrDefault();
                    appPMS.PerformanceManagemet.Add(pms);
                }

                if (applicationExtraFeePMS.Any() && applicationExtraFeePMS.Exists(e => courierExtraFeeIds.Contains(e.ExtraFee.FlagId.ToString())))
                {
                    var pms = applicationExtraFeePMS.Where(w => courierExtraFeeIds.Contains(w.ExtraFee.FlagId.ToString())).Select(s => new PMSResponseDto()
                    {
                        Date = request.Date,
                        Country = request.Country,
                        Branch = request.Branch,
                        User = request.User,
                        DataGroup = EnumHelper.GetEnumAsDictionary(typeof(PMSDataGroup)).GetOrDefault((int)PMSDataGroup.Vas),
                        DataType = EnumHelper.GetEnumAsDictionary(typeof(PMSVASDataType)).GetOrDefault((int)PMSVASDataType.Courier),
                        Value = applicationExtraFeePMS.Where(w => courierExtraFeeIds.Contains(w.ExtraFee.FlagId.ToString())).Sum(u => u.Quantity == 0 ? 1 : u.Quantity),
                        CreatedBy = "System",
                        DataDate = request.Date
                    }).FirstOrDefault();
                    appPMS.PerformanceManagemet.Add(pms);
                }

                if (applicationExtraFeePMS.Any() && applicationExtraFeePMS.Exists(e => mobileBiometricsExtraFeeIds.Contains(e.ExtraFee.FlagId.ToString())))
                {
                    var pms = applicationExtraFeePMS.Where(w => mobileBiometricsExtraFeeIds.Contains(w.ExtraFee.FlagId.ToString())).Select(s => new PMSResponseDto()
                    {
                        Date = request.Date,
                        Country = request.Country,
                        Branch = request.Branch,
                        User = request.User,
                        DataGroup = EnumHelper.GetEnumAsDictionary(typeof(PMSDataGroup)).GetOrDefault((int)PMSDataGroup.Vas),
                        DataType = EnumHelper.GetEnumAsDictionary(typeof(PMSVASDataType)).GetOrDefault((int)PMSVASDataType.MobilBiometrics),
                        Value = applicationExtraFeePMS.Where(w => mobileBiometricsExtraFeeIds.Contains(w.ExtraFee.FlagId.ToString())).Sum(u => u.Quantity == 0 ? 1 : u.Quantity),
                        CreatedBy = "System",
                        DataDate = request.Date
                    }).FirstOrDefault();
                    appPMS.PerformanceManagemet.Add(pms);
                }

                if (applicationStatusPMS.Any() && applicationStatusPMS.Exists(e => appStatusIds.Contains(e.ApplicationStatusId)))
                {
                    foreach (var statusId in appStatusIds)
                    {
                        if (statusId == 1)
                            dataTypeId = (int)PMSOperationDataType.ApplicationTaken;
                        else if (statusId == 2)
                            dataTypeId = (int)PMSOperationDataType.DataDone;
                        else if (statusId == 4)
                            dataTypeId = (int)PMSOperationDataType.ReceivedAtEmbassy;
                        else if (statusId == 22)
                            dataTypeId = (int)PMSOperationDataType.Biometri;

                        if (applicationStatusPMS.Exists(e => e.ApplicationStatusId == statusId))
                        {
                            var pms = applicationStatusPMS.Where(w => w.ApplicationStatusId == statusId).Select(s => new PMSResponseDto()
                            {
                                Date = request.Date,
                                Country = request.Country,
                                Branch = request.Branch,
                                User = request.User,
                                DataGroup = EnumHelper.GetEnumAsDictionary(typeof(PMSDataGroup)).GetOrDefault((int)PMSDataGroup.Operation),
                                DataType = EnumHelper.GetEnumAsDictionary(typeof(PMSOperationDataType)).GetOrDefault(dataTypeId),
                                Value = applicationStatusPMS.Count(c => c.ApplicationStatusId == statusId),
                                CreatedBy = "System",
                                DataDate = request.Date
                            }).FirstOrDefault();

                            appPMS.PerformanceManagemet.Add(pms);
                        }
                    }
                }

                if (applicationStatusPMS.Any() && applicationStatusPMS.Exists(e => e.ApplicationStatusId == 15 || e.ApplicationStatusId == 18)) // Hand Delivered To The Applicant, Outscan to courrier
                {
                    var pms = applicationStatusPMS.Where(w => w.ApplicationStatusId == 15 || w.ApplicationStatusId == 18).Select(s => new PMSResponseDto()
                    {
                        Date = request.Date,
                        Country = request.Country,
                        Branch = request.Branch,
                        User = request.User,
                        DataGroup = EnumHelper.GetEnumAsDictionary(typeof(PMSDataGroup)).GetOrDefault((int)PMSDataGroup.Operation),
                        DataType = EnumHelper.GetEnumAsDictionary(typeof(PMSOperationDataType)).GetOrDefault((int)PMSOperationDataType.PassportDelivery),
                        Value = applicationStatusPMS.Count(c => c.ApplicationStatusId == 15 || c.ApplicationStatusId == 18),
                        CreatedBy = "System",
                        DataDate = request.Date
                    }).FirstOrDefault();

                    appPMS.PerformanceManagemet.Add(pms);
                }
            }

            var qms = await _unitOfWorkPortalDb.GetRepository<GeneratedToken>().Entities.Where(w => !w.IsDeleted && w.CreatedBy == request.UserId && w.BranchId == request.BranchId && w.CreatedAt >= request.Date && w.CreatedAt < request.Date.AddDays(1)).ToListAsync();
            if (qms.Any())
            {
                var QMS = new PMSResponseDto()
                {
                    Date = request.Date,
                    Country = request.Country,
                    Branch = request.Branch,
                    User = request.User,
                    DataGroup = EnumHelper.GetEnumAsDictionary(typeof(PMSDataGroup)).GetOrDefault((int)PMSDataGroup.Operation),
                    DataType = EnumHelper.GetEnumAsDictionary(typeof(PMSOperationDataType)).GetOrDefault((int)PMSOperationDataType.Info),
                    Value = qms.Count,
                    CreatedBy = "System",
                    DataDate = request.Date
                };
                appPMS.PerformanceManagemet.Add(QMS);
            }

            paginationResult.TotalItemCount = appPMS.PerformanceManagemet.Count();
            if (appPMS.PerformanceManagemet.Count != 0)
                appPMS.PerformanceManagemet = appPMS.PerformanceManagemet.OrderByDescending(o => o.Date).Skip((request.Pagination.Page - 1) * request.Pagination.PageSize).Take(request.Pagination.PageSize).ToList();
            paginationResult.Items.Add(appPMS);
            return paginationResult;
        }

        public async Task<Pagination<PaginatedPMSResponseDto>> PaginatedPMSScoreCardAsync(PaginatedPMSRequestDto request)
        {
            var paginationResult = new Pagination<PaginatedPMSResponseDto> { Page = request.Pagination.Page, PageSize = request.Pagination.PageSize };

            var appPMS = new PaginatedPMSResponseDto() { PerformanceManagemet = new List<PMSResponseDto>() };

            var performanceManagement = await _unitOfWorkPortalDb.GetRepository<PerformanceManagement>().Entities
                .Include(i => i.User)
                .Where(w => w.IsActive
                            && !w.IsDeleted && w.UserId == request.UserId && w.BranchId == request.BranchId
                            && w.Date >= request.StartDate.Date && w.Date < request.EndDate.AddDays(1).Date)
                .ToListAsync();

            if (performanceManagement.Any())
            {
                var dataType = "";
                foreach (var performance in performanceManagement)
                {
                    dataType = performance.DataGroupId switch
                    {
                        (int)PMSDataGroup.Operation => EnumHelper.GetEnumDescription(typeof(PMSOperationDataType), performance.DataTypeId.ToString()),
                        (int)PMSDataGroup.Vas => EnumHelper.GetEnumDescription(typeof(PMSVASDataType), performance.DataTypeId.ToString()),
                        (int)PMSDataGroup.Mistakes => EnumHelper.GetEnumDescription(typeof(PMSMistakesDataType), performance.DataTypeId.ToString()),
                        (int)PMSDataGroup.Warnings => EnumHelper.GetEnumDescription(typeof(PMSWarningsDataType), performance.DataTypeId.ToString()),
                        (int)PMSDataGroup.ThanksComplaints => EnumHelper.GetEnumDescription(typeof(PMSThanksComplaintsDataType), performance.DataTypeId.ToString()),
                        _ => dataType
                    };

                    var createdBy = performance.CreatedBy.HasValue ? await GetCreatedUserInformation(performance.CreatedBy.Value) : string.Empty;

                    var pM = new PMSResponseDto
                    {
                        Id = performance.Id,
                        Date = performance.Date,
                        Country = request.Country,
                        CountryId = performance.CountryId,
                        Branch = request.Branch,
                        BranchId = performance.BranchId,
                        User = request.User,
                        UserId = performance.UserId,
                        DataGroupId = performance.DataGroupId,
                        DataGroup = EnumHelper.GetEnumDescription(typeof(PMSDataGroup), performance.DataGroupId.ToString()),
                        DataTypeId = performance.DataTypeId,
                        DataType = dataType,
                        Explanation = performance.Explanation,
                        Value = performance.Value,
                        DataDate = performance.CreatedAt ?? DateTime.Now,
                        CreatedBy = createdBy
                    };

                    appPMS.PerformanceManagemet.Add(pM);
                }
            }

            var feeIds = new List<string> { };
            var extraFeeIds = new List<string> { ExtraFeeFlagIds.Photocopy, ExtraFeeFlagIds.PrintOut, ExtraFeeFlagIds.FormFilling, ExtraFeeFlagIds.DigitalSimCard8GB, ExtraFeeFlagIds.DigitalSimCard20GB, ExtraFeeFlagIds.DigitalSimCard50GB };
            var photographExtraFeeIds = new List<string> { ExtraFeeFlagIds.Photo, ExtraFeeFlagIds.PhotoIQD, ExtraFeeFlagIds.PhotoUSD, ExtraFeeFlagIds.PhotoDZD, ExtraFeeFlagIds.PhotoTMT, ExtraFeeFlagIds.PhotoKWD };
            var courierExtraFeeIds = new List<string> { ExtraFeeFlagIds.CourierOutOfCity, ExtraFeeFlagIds.CourierInCity, ExtraFeeFlagIds.CourierInCity3, ExtraFeeFlagIds.OutOfCity3Courier };
            var mobileBiometricsExtraFeeIds = new List<string> { ExtraFeeFlagIds.MobileBiometrics1To4ApplicantsInsideCity, ExtraFeeFlagIds.MobileBiometrics4ApplicantsAndMoreInsideCity, ExtraFeeFlagIds.MobileBiometricsOutsideCity };
            feeIds.AddRange(extraFeeIds);
            feeIds.AddRange(photographExtraFeeIds);
            feeIds.AddRange(courierExtraFeeIds);
            feeIds.AddRange(mobileBiometricsExtraFeeIds);

            var appStatusIds = new List<int> { 1, 2, 4, 22 }; // Application Taken, Data Done, Received At Embassy, Biometric Enrollment Done
            var appPassportDeliveryStatusIds = new List<int> { 15, 18 }; // Hand Delivered To The Applicant, Outscan to courrier
               
            var applicationExtraFeePMS = await _unitOfWorkPortalDb.GetRepository<ApplicationExtraFee>().Entities
                                            .AsSplitQuery()
                                            .AsNoTracking()
                                            .Include(q => q.ExtraFee)
                                            .Include(q => q.Application)
                                                .ThenInclude(q => q.BranchApplicationCountry)
                                            .Where(p => p.IsActive && !p.IsDeleted
                                                        && feeIds.Contains(p.ExtraFee.FlagId.ToString()) && p.ExtraFee.IsActive && !p.ExtraFee.IsDeleted
                                                        && p.Application.BranchApplicationCountry.BranchId == request.BranchId
                                                        && p.Application.IsActive && !p.Application.IsDeleted
                                                        && p.Application.StatusId != (int)Contracts.Entities.Enums.ApplicationStatus.Cancelled
                                                        && p.CreatedAt >= request.StartDate.Date && p.CreatedAt < request.EndDate.AddDays(1).Date)
                                            .ToListAsync();

            var isManager = await _unitOfWorkPortalDb.GetRepository<UserRole>().Entities.Where(w => w.UserId == request.UserId && w.IsActive).AnyAsync(a => a.RoleId == 1 || a.RoleId == 2 || a.RoleId == 5 || a.RoleId == 8); // SysAdmin, Admin, Operation Manager, Country Manager
            if (isManager)
                applicationExtraFeePMS = applicationExtraFeePMS.Where(w => w.CreatedBy == request.UserId && w.CreatedBy == w.Application.CreatedBy).ToList();
            else
            {
                var applicationIds = await _unitOfWorkPortalDb.GetRepository<ApplicationExtraFee>().Entities
                                    .AsSplitQuery()
                                    .AsNoTracking()
                                    .Include(q => q.ExtraFee)
                                    .Include(q => q.Application)
                                        .ThenInclude(q => q.BranchApplicationCountry)
                                    .Where(p => p.IsActive && !p.IsDeleted && p.CreatedBy == request.UserId
                                                && p.Application.BranchApplicationCountry.BranchId == request.BranchId
                                                && p.Application.IsActive && !p.Application.IsDeleted
                                                && p.Application.StatusId != (int)Contracts.Entities.Enums.ApplicationStatus.Cancelled
                                                && p.CreatedAt >= request.StartDate.Date && p.CreatedAt < request.EndDate.AddDays(1).Date)
                                    .Select(s => s.ApplicationId).Distinct().ToListAsync();

                applicationExtraFeePMS = applicationExtraFeePMS.Where(w => applicationIds.Contains(w.ApplicationId)).ToList();
            }

            var applicationStatusPMS = await _unitOfWorkPortalDb.GetRepository<ApplicationStatusHistory>().Entities
                .AsSplitQuery()
                .AsNoTracking()
                .Include(q => q.Application)
                    .ThenInclude(q => q.BranchApplicationCountry)
                .Where(p => p.IsActive && !p.IsDeleted
                                       && p.CreatedAt >= request.StartDate.Date && p.CreatedAt < request.EndDate.AddDays(1).Date
                                       && p.UserId == request.UserId && (appStatusIds.Contains(p.ApplicationStatusId) || appPassportDeliveryStatusIds.Contains(p.ApplicationStatusId))
                                       && p.Application.BranchApplicationCountry.BranchId == request.BranchId
                                       && p.Application.IsActive && !p.Application.IsDeleted && p.Application.StatusId != (int)Contracts.Entities.Enums.ApplicationStatus.Cancelled
                                       && p.Application.ApplicationTypeId != (int)ApplicationType.NonApplicationInsurance 
                                       && p.Application.ApplicationTypeId != (int)ApplicationType.NonApplicationPcr 
                                       && p.Application.ApplicationTypeId != (int)ApplicationType.NonApplicationPrintOut 
                                       && p.Application.ApplicationTypeId != (int)ApplicationType.NonApplicationPhotocopy 
                                       && p.Application.ApplicationTypeId != (int)ApplicationType.NonApplicationPhotograph 
                                       && p.Application.ApplicationTypeId != (int)ApplicationType.NonApplication 
                                       && p.Application.ApplicationTypeId != (int)ApplicationType.NonApplicationRelatedInsurance)
                .ToListAsync();

            var qms = await _unitOfWorkPortalDb.GetRepository<GeneratedToken>().
                Entities.Where(w => !w.IsDeleted && w.CreatedBy == request.UserId && w.BranchId == request.BranchId
                                                 && w.CreatedAt >= request.StartDate.Date && w.CreatedAt < request.EndDate.AddDays(1).Date).ToListAsync();

            for (int k = 0; k <= (request.EndDate.Date - request.StartDate.Date).Days; k++)
            {
                var currentDate = request.StartDate.AddDays(k).Date;

                var currentapplicationExtraFeePMS = applicationExtraFeePMS
                    .Where(p => p.CreatedAt.Value.Date == currentDate).ToList();

                var currentapplicationStatusPMS = applicationStatusPMS
                    .Where(p => p.CreatedAt.Date == currentDate).ToList();

                if (currentapplicationExtraFeePMS.Any())
                {
                    var dataTypeId = 0;

                    foreach (var id in extraFeeIds)
                    {
                        dataTypeId = id switch
                        {
                            ExtraFeeFlagIds.Photocopy => (int)PMSVASDataType.Photocopy,
                            ExtraFeeFlagIds.PrintOut => (int)PMSVASDataType.PrintOut,
                            ExtraFeeFlagIds.FormFilling => (int)PMSVASDataType.FormFilling,
                            ExtraFeeFlagIds.DigitalSimCard8GB => (int)PMSVASDataType.eSim8,
                            ExtraFeeFlagIds.DigitalSimCard20GB => (int)PMSVASDataType.eSim20,
                            ExtraFeeFlagIds.DigitalSimCard50GB => (int)PMSVASDataType.eSim50,
                            _ => dataTypeId
                        };

                        if (currentapplicationExtraFeePMS.Any(a => a.ExtraFee.FlagId.ToString() == id))
                        {
                            var pMS = currentapplicationExtraFeePMS.Where(w => w.ExtraFee.FlagId.ToString() == id).Select(s => new PMSResponseDto()
                            {
                                Date = currentDate,
                                Country = request.Country,
                                Branch = request.Branch,
                                User = request.User,
                                DataGroup = EnumHelper.GetEnumDescription(typeof(PMSDataGroup), PMSDataGroup.Vas.ToInt().ToString()),
                                DataType = EnumHelper.GetEnumDescription(typeof(PMSVASDataType), dataTypeId.ToString()),
                                Value = currentapplicationExtraFeePMS.Sum(u => u.ExtraFee.FlagId.ToString() == id ? (u.Quantity == 0 ? 1 : u.Quantity) : 0),
                                CreatedBy = "System",
                                DataDate = s.CreatedAt ?? currentDate
                            }).FirstOrDefault();

                            appPMS.PerformanceManagemet.Add(pMS);
                        }
                    }

                    if (currentapplicationExtraFeePMS.Any(a => photographExtraFeeIds.Contains(a.ExtraFee.FlagId.ToString())))
                    {
                        var pms = currentapplicationExtraFeePMS.Where(a => photographExtraFeeIds.Contains(a.ExtraFee.FlagId.ToString())).Select(s => new PMSResponseDto()
                        {
                            Date = currentDate,
                            Country = request.Country,
                            Branch = request.Branch,
                            User = request.User,
                            DataGroup = EnumHelper.GetEnumDescription(typeof(PMSDataGroup), PMSDataGroup.Vas.ToInt().ToString()),
                            DataType = EnumHelper.GetEnumDescription(typeof(PMSVASDataType), PMSVASDataType.Photo.ToInt().ToString()),
                            Value = currentapplicationExtraFeePMS.Where(w => photographExtraFeeIds.Contains(w.ExtraFee.FlagId.ToString())).Sum(u => u.Quantity == 0 ? 1 : u.Quantity),
                            CreatedBy = "System",
                            DataDate = s.CreatedAt ?? currentDate
                        }).FirstOrDefault();
                        appPMS.PerformanceManagemet.Add(pms);
                    }

                    if (currentapplicationExtraFeePMS.Any(a => courierExtraFeeIds.Contains(a.ExtraFee.FlagId.ToString())))
                    {
                        var pms = currentapplicationExtraFeePMS.Where(a => courierExtraFeeIds.Contains(a.ExtraFee.FlagId.ToString())).Select(s => new PMSResponseDto()
                        {
                            Date = currentDate,
                            Country = request.Country,
                            Branch = request.Branch,
                            User = request.User,
                            DataGroup = EnumHelper.GetEnumDescription(typeof(PMSDataGroup), PMSDataGroup.Vas.ToInt().ToString()),
                            DataType = EnumHelper.GetEnumDescription(typeof(PMSVASDataType), PMSVASDataType.Courier.ToInt().ToString()),
                            Value = currentapplicationExtraFeePMS.Where(w => courierExtraFeeIds.Contains(w.ExtraFee.FlagId.ToString())).Sum(u => u.Quantity == 0 ? 1 : u.Quantity),
                            CreatedBy = "System",
                            DataDate = s.CreatedAt ?? currentDate
                        }).FirstOrDefault();
                        appPMS.PerformanceManagemet.Add(pms);
                    }

                    if (currentapplicationExtraFeePMS.Any(a => mobileBiometricsExtraFeeIds.Contains(a.ExtraFee.FlagId.ToString())))
                    {
                        var pms = currentapplicationExtraFeePMS.Where(w => mobileBiometricsExtraFeeIds.Contains(w.ExtraFee.FlagId.ToString())).Select(s => new PMSResponseDto()
                        {
                            Date = currentDate,
                            Country = request.Country,
                            Branch = request.Branch,
                            User = request.User,
                            DataGroup = EnumHelper.GetEnumDescription(typeof(PMSDataGroup), PMSDataGroup.Vas.ToInt().ToString()),
                            DataType = EnumHelper.GetEnumDescription(typeof(PMSVASDataType), PMSVASDataType.MobilBiometrics.ToInt().ToString()),
                            Value = currentapplicationExtraFeePMS.Where(w => mobileBiometricsExtraFeeIds.Contains(w.ExtraFee.FlagId.ToString())).Sum(u => u.Quantity == 0 ? 1 : u.Quantity),
                            CreatedBy = "System",
                            DataDate = s.CreatedAt ?? currentDate
                        }).FirstOrDefault();
                        appPMS.PerformanceManagemet.Add(pms);
                    }
                }

                if (currentapplicationStatusPMS.Any())
                {
                    var dataTypeId = 0;

                    foreach (var statusId in appStatusIds)
                    {
                        dataTypeId = statusId switch
                        {
                            1 => (int)PMSOperationDataType.ApplicationTaken,
                            2 => (int)PMSOperationDataType.DataDone,
                            4 => (int)PMSOperationDataType.ReceivedAtEmbassy,
                            22 => (int)PMSOperationDataType.Biometri,
                            _ => dataTypeId
                        };

                        if (currentapplicationStatusPMS.Any(e => e.ApplicationStatusId == statusId))
                        {
                            var pms = currentapplicationStatusPMS.Where(w => w.ApplicationStatusId == statusId).Select(s => new PMSResponseDto()
                            {
                                Date = currentDate,
                                Country = request.Country,
                                Branch = request.Branch,
                                User = request.User,
                                DataGroup = EnumHelper.GetEnumDescription(typeof(PMSDataGroup), PMSDataGroup.Operation.ToInt().ToString()),
                                DataType = EnumHelper.GetEnumDescription(typeof(PMSOperationDataType), dataTypeId.ToString()),
                                Value = currentapplicationStatusPMS.Count(c => c.ApplicationStatusId == statusId),
                                CreatedBy = "System",
                                DataDate = s.CreatedAt.Date
                            }).FirstOrDefault();

                            appPMS.PerformanceManagemet.Add(pms);
                        }
                    }

                    if (currentapplicationStatusPMS.Any(e => e.ApplicationStatusId == 15 || e.ApplicationStatusId == 18))
                    {
                        var pms = currentapplicationStatusPMS.Where(w => w.ApplicationStatusId == 15 || w.ApplicationStatusId == 18).Select(s => new PMSResponseDto()
                        {
                            Date = currentDate,
                            Country = request.Country,
                            Branch = request.Branch,
                            User = request.User,
                            DataGroup = EnumHelper.GetEnumDescription(typeof(PMSDataGroup), PMSDataGroup.Operation.ToInt().ToString()),
                            DataType = EnumHelper.GetEnumDescription(typeof(PMSOperationDataType), PMSOperationDataType.PassportDelivery.ToInt().ToString()),
                            Value = currentapplicationStatusPMS.Count(c => c.ApplicationStatusId == 15 || c.ApplicationStatusId == 18),
                            CreatedBy = "System",
                            DataDate = s.CreatedAt.Date
                        }).FirstOrDefault();

                        appPMS.PerformanceManagemet.Add(pms);
                    }
                }

                var currentQmsGeneratedTokens = qms.Where(w => w.CreatedAt.Date == currentDate).ToList();

                if (currentQmsGeneratedTokens.Any())
                {
                    var QMS = new PMSResponseDto()
                    {
                        Date = currentDate,
                        Country = request.Country,
                        Branch = request.Branch,
                        User = request.User,
                        DataGroup = EnumHelper.GetEnumDescription(typeof(PMSDataGroup), PMSDataGroup.Operation.ToInt().ToString()),
                        DataType = EnumHelper.GetEnumDescription(typeof(PMSOperationDataType), PMSOperationDataType.Info.ToInt().ToString()),
                        Value = currentQmsGeneratedTokens.Count,
                        CreatedBy = "System",
                        DataDate = currentDate
                    };
                    appPMS.PerformanceManagemet.Add(QMS);
                }

            }

            paginationResult.TotalItemCount = appPMS.PerformanceManagemet.Count();

            if (appPMS.PerformanceManagemet.Count != 0)
                appPMS.PerformanceManagemet = appPMS.PerformanceManagemet.OrderBy(o => o.Date).Skip((request.Pagination.Page - 1) * request.Pagination.PageSize).Take(request.Pagination.PageSize).ToList();

            paginationResult.Items.Add(appPMS);
            return paginationResult;
        }

        public async Task<AddResponseDto> AddPMSAsync(PMSRequestDto request)
        {
            var pms = new PerformanceManagement
            {
                UserId = request.UserId,
                CountryId = request.CountryId,
                BranchId = request.BranchId,
                DataGroupId = request.DataGroupId ?? 0,
                DataTypeId = request.DataTypeId ?? 0,
                Explanation = request.Explanation,
                Value = request.Value ?? 1,
                Date = request.Date,
                CreatedBy = request.UserAuditId,
                CreatedAt = DateTime.Now
            };

            await _unitOfWorkPortalDb.GetRepository<PerformanceManagement>().AddAsync(pms);
            await _unitOfWorkPortalDb.SaveChangesAsync();

            return new AddResponseDto() { Id = pms.Id };
        }
        #endregion

        #region Put
        public async Task<ValidateResponseDto> UpdatePMSAsync(PMSRequestDto request)
        {
            var existingPMS = await _unitOfWorkPortalDb.GetRepository<PerformanceManagement>().Entities
                                        .Where(p => p.IsActive && !p.IsDeleted && p.Id == request.Id)
                                        .FirstOrDefaultAsync();

            if (existingPMS == null)
                throw new NoFoundDataPortalException(nameof(SiteResources.Exception_NoRecordsFound).ToSiteResourcesValue());

            existingPMS.CountryId = request.CountryId;
            existingPMS.BranchId = request.BranchId;
            existingPMS.UserId = request.UserId;
            existingPMS.Date = request.Date;
            existingPMS.DataGroupId = request.DataGroupId ?? 0;
            existingPMS.DataTypeId = request.DataTypeId ?? 0;
            existingPMS.Explanation = request.Explanation;
            existingPMS.Value = request.Value ?? 0;
            existingPMS.UpdatedBy = request.UserId;
            existingPMS.UpdatedAt = DateTime.Now;

            _unitOfWorkPortalDb.GetRepository<PerformanceManagement>().Update(existingPMS);
            await _unitOfWorkPortalDb.SaveChangesAsync();

            return new ValidateResponseDto { Result = true };
        }
        #endregion

        #region Delete
        public async Task<DeleteResponseDto> DeletePMSAsync(int id)
        {
            var pMS = await _unitOfWorkPortalDb.GetRepository<PerformanceManagement>().Entities
                                        .Where(p => !p.IsDeleted && p.Id == id)
                                        .FirstOrDefaultAsync();

            if (pMS == null)
                throw new NoFoundDataPortalException(nameof(SiteResources.Exception_NoRecordsFound).ToSiteResourcesValue());

            _unitOfWorkPortalDb.GetRepository<PerformanceManagement>().MarkAsDeleted(pMS.Id);
            await _unitOfWorkPortalDb.SaveChangesAsync();

            return new DeleteResponseDto()
            {
                Result = true
            };
        }
        #endregion

        #region PrivateMethod

        private async Task<string> GetCreatedUserInformation(int createdById)
        {
            var createdBy = await _unitOfWorkPortalDb.GetRepository<User>().Entities
                .FirstOrDefaultAsync(f => f.IsActive && !f.IsDeleted && f.Id == createdById);

            return $"{createdBy.Name} {createdBy.Surname}";

        }

        #endregion
    }
}





