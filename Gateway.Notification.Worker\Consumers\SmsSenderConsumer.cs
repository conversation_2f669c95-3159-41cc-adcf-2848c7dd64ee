﻿using Gateway.EventBus.Subscribers;
using Gateway.Extensions;
using Gateway.Notification.Application.Consumers.Dto;
using Gateway.Notification.Application.Handlers;
using Gateway.Notification.Application.Handlers.Dto;
using Microsoft.Extensions.Configuration;
using Microsoft.Extensions.DependencyInjection;
using System;
using System.Threading.Tasks;

namespace Gateway.Notification.Application.Consumers
{
    public class SmsSenderConsumer : BaseConsumer
    {
        private readonly IMessageSubscriber _messageSubscriber;
        private readonly string _queueName;
        private readonly IServiceProvider _serviceProvider;

        #region ctor

        public SmsSenderConsumer(IServiceProvider serviceProvider, IMessageSubscriber messageSubscriber, IConfiguration configuration)
        {
            _messageSubscriber = messageSubscriber;
            _queueName = configuration["RabbitMq:SMSQueue"].AddEnvironmentSuffix();
            _serviceProvider = serviceProvider;
        }

        #endregion

        #region Public Methods

        public override async Task Start()
        {
            await _messageSubscriber.SubscribeMessage<SmsModel>(_queueName, true, async (msg, _) =>
            {
                using var scope = _serviceProvider.CreateScope();

                var handler = scope.ServiceProvider.GetRequiredService<ISmsCollectorHandler>();

                await handler.CollectSms(new SmsCollectorRequest { SmsModel = msg });
            });
        }

        #endregion
    }
}
