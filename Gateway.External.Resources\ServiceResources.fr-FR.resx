﻿<?xml version="1.0" encoding="utf-8"?>
<root>
  <!-- 
    Microsoft ResX Schema 
    
    Version 2.0
    
    The primary goals of this format is to allow a simple XML format 
    that is mostly human readable. The generation and parsing of the 
    various data types are done through the TypeConverter classes 
    associated with the data types.
    
    Example:
    
    ... ado.net/XML headers & schema ...
    <resheader name="resmimetype">text/microsoft-resx</resheader>
    <resheader name="version">2.0</resheader>
    <resheader name="reader">System.Resources.ResXResourceReader, System.Windows.Forms, ...</resheader>
    <resheader name="writer">System.Resources.ResXResourceWriter, System.Windows.Forms, ...</resheader>
    <data name="Name1"><value>this is my long string</value><comment>this is a comment</comment></data>
    <data name="Color1" type="System.Drawing.Color, System.Drawing">Blue</data>
    <data name="Bitmap1" mimetype="application/x-microsoft.net.object.binary.base64">
        <value>[base64 mime encoded serialized .NET Framework object]</value>
    </data>
    <data name="Icon1" type="System.Drawing.Icon, System.Drawing" mimetype="application/x-microsoft.net.object.bytearray.base64">
        <value>[base64 mime encoded string representing a byte array form of the .NET Framework object]</value>
        <comment>This is a comment</comment>
    </data>
                
    There are any number of "resheader" rows that contain simple 
    name/value pairs.
    
    Each data row contains a name, and value. The row also contains a 
    type or mimetype. Type corresponds to a .NET class that support 
    text/value conversion through the TypeConverter architecture. 
    Classes that don't support this are serialized and stored with the 
    mimetype set.
    
    The mimetype is used for serialized objects, and tells the 
    ResXResourceReader how to depersist the object. This is currently not 
    extensible. For a given mimetype the value must be set accordingly:
    
    Note - application/x-microsoft.net.object.binary.base64 is the format 
    that the ResXResourceWriter will generate, however the reader can 
    read any of the formats listed below.
    
    mimetype: application/x-microsoft.net.object.binary.base64
    value   : The object must be serialized with 
            : System.Runtime.Serialization.Formatters.Binary.BinaryFormatter
            : and then encoded with base64 encoding.
    
    mimetype: application/x-microsoft.net.object.soap.base64
    value   : The object must be serialized with 
            : System.Runtime.Serialization.Formatters.Soap.SoapFormatter
            : and then encoded with base64 encoding.

    mimetype: application/x-microsoft.net.object.bytearray.base64
    value   : The object must be serialized into a byte array 
            : using a System.ComponentModel.TypeConverter
            : and then encoded with base64 encoding.
    -->
  <xsd:schema id="root" xmlns="" xmlns:xsd="http://www.w3.org/2001/XMLSchema" xmlns:msdata="urn:schemas-microsoft-com:xml-msdata">
    <xsd:import namespace="http://www.w3.org/XML/1998/namespace" />
    <xsd:element name="root" msdata:IsDataSet="true">
      <xsd:complexType>
        <xsd:choice maxOccurs="unbounded">
          <xsd:element name="metadata">
            <xsd:complexType>
              <xsd:sequence>
                <xsd:element name="value" type="xsd:string" minOccurs="0" />
              </xsd:sequence>
              <xsd:attribute name="name" use="required" type="xsd:string" />
              <xsd:attribute name="type" type="xsd:string" />
              <xsd:attribute name="mimetype" type="xsd:string" />
              <xsd:attribute ref="xml:space" />
            </xsd:complexType>
          </xsd:element>
          <xsd:element name="assembly">
            <xsd:complexType>
              <xsd:attribute name="alias" type="xsd:string" />
              <xsd:attribute name="name" type="xsd:string" />
            </xsd:complexType>
          </xsd:element>
          <xsd:element name="data">
            <xsd:complexType>
              <xsd:sequence>
                <xsd:element name="value" type="xsd:string" minOccurs="0" msdata:Ordinal="1" />
                <xsd:element name="comment" type="xsd:string" minOccurs="0" msdata:Ordinal="2" />
              </xsd:sequence>
              <xsd:attribute name="name" type="xsd:string" use="required" msdata:Ordinal="1" />
              <xsd:attribute name="type" type="xsd:string" msdata:Ordinal="3" />
              <xsd:attribute name="mimetype" type="xsd:string" msdata:Ordinal="4" />
              <xsd:attribute ref="xml:space" />
            </xsd:complexType>
          </xsd:element>
          <xsd:element name="resheader">
            <xsd:complexType>
              <xsd:sequence>
                <xsd:element name="value" type="xsd:string" minOccurs="0" msdata:Ordinal="1" />
              </xsd:sequence>
              <xsd:attribute name="name" type="xsd:string" use="required" />
            </xsd:complexType>
          </xsd:element>
        </xsd:choice>
      </xsd:complexType>
    </xsd:element>
  </xsd:schema>
  <resheader name="resmimetype">
    <value>text/microsoft-resx</value>
  </resheader>
  <resheader name="version">
    <value>2.0</value>
  </resheader>
  <resheader name="reader">
    <value>System.Resources.ResXResourceReader, System.Windows.Forms, Version=4.0.0.0, Culture=neutral, PublicKeyToken=b77a5c561934e089</value>
  </resheader>
  <resheader name="writer">
    <value>System.Resources.ResXResourceWriter, System.Windows.Forms, Version=4.0.0.0, Culture=neutral, PublicKeyToken=b77a5c561934e089</value>
  </resheader>
  <data name="BRANCH_DEPARTMENT_NOT_FOUND" xml:space="preserve">
    <value>Branch Department not found</value>
  </data>
  <data name="APPLICANT_NOT_FOUND" xml:space="preserve">
    <value>Applicant not found</value>
  </data>
  <data name="APPOINTMENT_NOT_FOUND" xml:space="preserve">
    <value>Appointment not found</value>
  </data>
  <data name="BRANCH_NOT_FOUND" xml:space="preserve">
    <value>Branch not found</value>
  </data>
  <data name="FAILED" xml:space="preserve">
    <value>FAILED</value>
  </data>
  <data name="FAMILY_APPLICANT_COUNT_ERROR" xml:space="preserve">
    <value>The family applicant counts can be more than 1 item</value>
  </data>
  <data name="GROUP_APPLICANT_COUNT_ERROR" xml:space="preserve">
    <value>The group applicant counts can be more than 1 item</value>
  </data>
  <data name="INDIVIDUAL_APPLICANT_COUNT_ERROR" xml:space="preserve">
    <value>The individual applicant counts can not be more than 1 item</value>
  </data>
  <data name="INPUT_ERROR" xml:space="preserve">
    <value>INPUT_ERROR</value>
  </data>
  <data name="INTERNAL_SERVER_ERROR" xml:space="preserve">
    <value>INTERNAL_SERVER_ERROR</value>
  </data>
  <data name="INVALID_INPUT_ERROR" xml:space="preserve">
    <value>Invalid input error</value>
  </data>
  <data name="INVALID_REQUEST" xml:space="preserve">
    <value>Invalid request</value>
  </data>
  <data name="PROPERTY_MAX_LENGTH_ERROR" xml:space="preserve">
    <value>The property {0} can not be more than {1} characters</value>
  </data>
  <data name="PROPERTY_REQUIRED" xml:space="preserve">
    <value>{0} is required</value>
  </data>
  <data name="RESOURCE_ALREADY_REGISTERED" xml:space="preserve">
    <value>Resource is already registered</value>
  </data>
  <data name="RESOURCE_CREATED" xml:space="preserve">
    <value>Resource created</value>
  </data>
  <data name="RESOURCE_DELETED" xml:space="preserve">
    <value>Resource deleted</value>
  </data>
  <data name="RESOURCE_FOUND" xml:space="preserve">
    <value>Enregistrement trouvé</value>
  </data>
  <data name="RESOURCE_NOT_FOUND" xml:space="preserve">
    <value>Aucun enregistrement trouvé</value>
  </data>
  <data name="RESOURCE_RETRIEVED" xml:space="preserve">
    <value>Inscrit</value>
  </data>
  <data name="RESOURCE_UPDATED" xml:space="preserve">
    <value>Resource updated</value>
  </data>
  <data name="SUCCESS" xml:space="preserve">
    <value>SUCCESS</value>
  </data>
  <data name="SLOT_NOT_FOUND" xml:space="preserve">
    <value>Slot not found</value>
  </data>
  <data name="NO_AVAILABLE_SLOTS_FOUND" xml:space="preserve">
    <value>No available slots found</value>
  </data>
  <data name="FIRST_AVAILABLE_SLOT_FOUND" xml:space="preserve">
    <value>First available slot found</value>
  </data>
  <data name="COUNTRY_NOT_FOUND" xml:space="preserve">
    <value>Country not found</value>
  </data>
  <data name="INVALID_EMAIL_ADDRESS" xml:space="preserve">
    <value>Invalid email address</value>
  </data>
  <data name="REPEATED_APPOINTMENT_FOUND" xml:space="preserve">
    <value>There is an appointment with the passport</value>
  </data>
  <data name="SLOT_QUOTA_NOT_FOUND" xml:space="preserve">
    <value>Slot quota not found</value>
  </data>
  <data name="APPLICATION_STATUS_NOT_FOUND" xml:space="preserve">
    <value>État de la demande introuvable</value>
  </data>
  <data name="APPLICANT_COUNT_VALIDATION_ERROR" xml:space="preserve">
    <value>The applicant count can not be more than 1 on indvidual applicant type</value>
  </data>
  <data name="APPLICATION_NOT_FOUND" xml:space="preserve">
    <value>Application non trouvée</value>
  </data>
  <data name="PROPERTY_FORMAT_ERROR" xml:space="preserve">
    <value>The property {0} format not valid</value>
  </data>
  <data name="APPLICANT_ONLY_HAS_ONE_WİFEORHUSBAND" xml:space="preserve">
    <value>Applicant only has one wife or husband</value>
  </data>
  <data name="ONLY_ONE_RECORD_CANBE_SET_AS_WIFEORHUSBAND_RECORD_FOR_APPOINTMENT" xml:space="preserve">
    <value>Only one record can be set as wife or husband record for appointment</value>
  </data>
  <data name="METHOD_REQUIREMENT_ERROR" xml:space="preserve">
    <value>This method is not valid for this parameter combinations</value>
  </data>
  <data name="APPOINTMENT_NOT_CONVERTED_APPLICATION" xml:space="preserve">
    <value>Appointment has not been converted to an application</value>
  </data>
  <data name="APPOINTMENT_HAS_CONVERTED_APPLICATION_NOT_FOUND" xml:space="preserve">
    <value>Appointment converted to application not found</value>
  </data>
  <data name="FILE_OPERATION_FAILED" xml:space="preserve">
    <value>File operation failed</value>
  </data>
  <data name="BRANCH_CHANGE_NOT_ALLOWED" xml:space="preserve">
    <value>Branch change not allowed</value>
  </data>
  <data name="NEW_APPLICANT_NOT_ALLOWED" xml:space="preserve">
    <value>New applicant not allowed</value>
  </data>
  <data name="SLOT_NOT_IN_THIS_BRANCH" xml:space="preserve">
    <value>Slot not in this branch</value>
  </data>
  <data name="PASSPORT_VALIDITY_PERIOD_ERROR" xml:space="preserve">
    <value>La période de validité du passeport ne peut pas être inférieure à 180 jours</value>
  </data>
  <data name="BIRTHDATE_MUST_BE_PAST_TENSE" xml:space="preserve">
    <value>Birth date must be past tense</value>
  </data>
  <data name="USER_ALREADY_REGISTERED" xml:space="preserve">
    <value>User already registered</value>
  </data>
  <data name="PASSWORD_MISMATCH" xml:space="preserve">
    <value>Password mismatch</value>
  </data>
  <data name="ALREADY_HAS_UPDATE_REQUEST" xml:space="preserve">
    <value>Already has update request</value>
  </data>
  <data name="USER_NOT_FOUND" xml:space="preserve">
    <value>User not found</value>
  </data>
  <data name="AUTHORIZED_USER_NOT_FOUND" xml:space="preserve">
    <value>Authorized user not found</value>
  </data>
  <data name="INVALID_PHONE_NUMBER" xml:space="preserve">
    <value>Invalid phone number</value>
  </data>
  <data name="PROPERTY_MUST_NOT_HAVE_NUMERIC_CHARACTER" xml:space="preserve">
    <value>Must not have numeric characters</value>
  </data>
  <data name="PASSWORD_MUST_NOT_SAME" xml:space="preserve">
    <value>Password must not same</value>
  </data>
  <data name="TRANSLATION_NOT_FOUND" xml:space="preserve">
    <value>traduction non trouvée</value>
  </data>
  <data name="COMPANY_NOT_FOUND" xml:space="preserve">
    <value>Company not found</value>
  </data>
  <data name="EXTERNAL_REGISTER_NOTIFICATION" xml:space="preserve">
    <value>Your registration has been completed. Your temporary password is [PASSWORD] You can change it from the system after logging in with your temporary password.</value>
  </data>
  <data name="NO_DATA_FOUND_TO_REPORT" xml:space="preserve">
    <value>No data found to report</value>
  </data>
  <data name="CREATED_BY" xml:space="preserve">
    <value>Created by</value>
  </data>
  <data name="ORDER" xml:space="preserve">
    <value>Order</value>
  </data>
  <data name="REPORT_DATE" xml:space="preserve">
    <value>Report date</value>
  </data>
  <data name="SAME_PASSPORT_USED_BETWEEN_APPLICANTS" xml:space="preserve">
    <value>Same passport used between applicants</value>
  </data>
  <data name="COMPANY_USER_REPORT" xml:space="preserve">
    <value>Company User Report</value>
  </data>
  <data name="COMPANY_SLOT_DEMAND_REPORT" xml:space="preserve">
    <value>Company Slot Demand Report</value>
  </data>
  <data name="COMPANY_APPOINTMENT_DEMAND_REPORT" xml:space="preserve">
    <value>Company Appointment Demand Report</value>
  </data>
  <data name="APPOINTMENT_DEMAND_NOT_FOUND" xml:space="preserve">
    <value>Appointment demand not found</value>
  </data>
  <data name="COMPANY_APPLICATION_REPORT" xml:space="preserve">
    <value>Company Application Report</value>
  </data>
  <data name="PNL_REPORT" xml:space="preserve">
    <value>PNL Report</value>
  </data>
  <data name="PNL_ALREADY_REGISTERED" xml:space="preserve">
    <value>PNL already registered</value>
  </data>
  <data name="INVALID_PARAMETER" xml:space="preserve">
    <value>Invalid parameter {0}</value>
  </data>
  <data name="INVALID_PASSPORT_NUMBER" xml:space="preserve">
    <value>Invalid passport number</value>
  </data>
  <data name="DATE_PARAMETERS_MISMATCH" xml:space="preserve">
    <value>Date parameters mismatch</value>
  </data>
  <data name="FILE_SIZE_LIMIT_EXCEEDED" xml:space="preserve">
    <value>File size limit exceeded</value>
  </data>
  <data name="INVALID_FILE_EXTENSION" xml:space="preserve">
    <value>Invalid file extension</value>
  </data>
  <data name="EMAIL_MISMATCH_WITH_TOKEN" xml:space="preserve">
    <value>L'e-mail doit être le même que celui utilisé lors de la connexion</value>
  </data>
  <data name="APPOINTMENT_FOUND_WITH_SAME_PASSPORT" xml:space="preserve">
    <value>Appointment found with same passport number</value>
  </data>
  <data name="BAD_REQUEST" xml:space="preserve">
    <value>BAD_REQUEST</value>
  </data>
  <data name="CANNOT_CONTINUE_APPOINTMENT" xml:space="preserve">
    <value>Vous ne pouvez pas continuer le rendez-vous avec ce numéro de passeport</value>
  </data>
  <data name="PRE_CONDITION_FAILED" xml:space="preserve">
    <value>La condition préalable a échoué</value>
  </data>
  <data name="INVALID_PARAMETER_LOWER" xml:space="preserve">
    <value>Paramètre non valide : {0} doit être inférieur à {1}</value>
  </data>
  <data name="CANNOT_UPDATE_WALKIN" xml:space="preserve">
    <value>Impossible de mettre à jour les rendez-vous sans rendez-vous</value>
  </data>
  <data name="MOBILE_SLOT_NOT_FOUND" xml:space="preserve">
    <value>Aucun rendez-vous disponible pour la date sélectionnée. Veuillez essayer une autre date.</value>
  </data>
</root>