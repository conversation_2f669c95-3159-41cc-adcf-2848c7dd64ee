﻿using Portal.Gateway.ApiModel;
using System.Collections.Generic;

namespace Portal.Gateway.Entity.Entities.Portal
{
    public class BranchApplicationCountryExtraFee : AuditableEntity
    {
        public BranchApplicationCountryExtraFee()
        {
            BranchApplicationCountryExtraFeeCommissions = new HashSet<BranchApplicationCountryExtraFeeCommission>();
            BranchApplicationCountryExtraFeeCosts = new HashSet<BranchApplicationCountryExtraFeeCost>();
            IsActive = true;
            IsDeleted = false;
        }
        public int Id { get; set; }
        public int BranchApplicationCountryId { get; set; }
        public int ExtraFeeId { get; set; }
        public bool IsAutoChecked { get; set; }
        public decimal Price { get; set; }
        public decimal Tax { get; set; }
        public int CurrencyId { get; set; }
        public decimal TaxRatio { get; set; }
        public bool ShowInICR { get; set; }
        public bool ShowInSummary { get; set; }
        public decimal BasePrice { get; set; }
        public decimal ServiceTax { get; set; }
        public bool IsShowInReport { get; set; }
        public bool IsGroupInIcr { get; set; }
        public bool IsShowInRejectionList { get; set; }
        public bool IsShowInAllApplicationsReport { get; set; }
        public bool IsShowInApplicationAfterRejection { get; set; }
        public virtual BranchApplicationCountry BranchApplicationCountry { get; set; }
        public virtual ExtraFee ExtraFee { get; set; }
        public virtual SapExtraFee SapExtraFee { get; set; }
        public virtual ICollection<BranchApplicationCountryExtraFeeCommission> BranchApplicationCountryExtraFeeCommissions { get; set; }
        public virtual ICollection<BranchApplicationCountryExtraFeeCost> BranchApplicationCountryExtraFeeCosts { get; set; }
        public virtual ICollection<BranchApplicationCountryCompanyExtraFee> BranchApplicationCountryCompanyExtraFee { get; set; }
    }
}
