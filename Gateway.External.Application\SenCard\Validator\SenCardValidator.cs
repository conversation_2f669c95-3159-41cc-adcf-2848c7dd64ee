﻿using FluentValidation;
using Gateway.External.Application.SenCard.Dto.Request;

namespace Gateway.External.Application.SenCard.Validator
{
    internal class GetSenCardDistrictValidator : AbstractValidator<SenCardDistrictRequest>
    {
        public GetSenCardDistrictValidator()
        {
            RuleFor(x => x.ProvinceId)
                .NotEmpty()
                .Length(3).WithMessage("Must be exactly 3 characters")
                .Must(Gateway.Extensions.StringExtensions.BeThreeDigitNumber).WithMessage("Must be between 000 and 999");
        }

    }
    internal class GetSenCardOrganizationValidator : AbstractValidator<SenCardOrganizationRequest>
    {
        public GetSenCardOrganizationValidator()
        {
            RuleFor(x => x.ProvinceId)
                .NotEmpty()
                .Length(3).WithMessage($"ProvinceId Must be exactly 3 characters")
                .Must(Gateway.Extensions.StringExtensions.BeThreeDigitNumber).WithMessage("ProvinceId Must be between 000 and 999");
        }
    }
}
