﻿@using Kendo.Mvc.TagHelpers
@using Microsoft.AspNetCore.Mvc.TagHelpers
@model AddUpdateApplicationViewModel
@Html.HiddenFor(m => m.IsRequiredApplicationTogether)
@{
    var isRequiredTotalYearInCountry = Model.ApplicationFormElements.Any(p => p.FormElementId == (int)ApplicationFormElement.TotalYearInCountry && p.IsRequired);
    var isRequiredReimbursementType = Model.ApplicationFormElements.Any(p => p.FormElementId == (int)ApplicationFormElement.ReimbursementType && p.IsRequired);
    var isRequiredReimbursementSponsorDetail = Model.ApplicationFormElements.Any(p => p.FormElementId == (int)ApplicationFormElement.ReimbursementSponsorDetail && p.IsRequired);
    var isRequiredJob = Model.ApplicationFormElements.Any(p => p.FormElementId == (int)ApplicationFormElement.Job && p.IsRequired);
    var isRequiredCompanyName = Model.ApplicationFormElements.Any(p => p.FormElementId == (int)ApplicationFormElement.CompanyName && p.IsRequired);
    var isRequiredTotalYearInCompany = Model.ApplicationFormElements.Any(p => p.FormElementId == (int)ApplicationFormElement.TotalYearInCompany && p.IsRequired);
    var isRequiredMonthlySalary = Model.ApplicationFormElements.Any(p => p.FormElementId == (int)ApplicationFormElement.MonthlySalary && p.IsRequired);
    var isRequiredMonthlySalaryCurrency = Model.ApplicationFormElements.Any(p => p.FormElementId == (int)ApplicationFormElement.MonthlySalaryCurrency && p.IsRequired);
    var isRequiredBankBalance = Model.ApplicationFormElements.Any(p => p.FormElementId == (int)ApplicationFormElement.BankBalance && p.IsRequired);
    var isRequiredBankBalanceCurrency = Model.ApplicationFormElements.Any(p => p.FormElementId == (int)ApplicationFormElement.BankBalanceCurrency && p.IsRequired);
    var isRequiredCityName = Model.ApplicationFormElements.Any(p => p.FormElementId == (int)ApplicationFormElement.CityName && p.IsRequired);
    var isRequiredAccomodationDetail = Model.ApplicationFormElements.Any(p => p.FormElementId == (int)ApplicationFormElement.AccomodationDetail && p.IsRequired);
    var isRequiredRelativeLocation = Model.ApplicationFormElements.Any(p => p.FormElementId == (int)ApplicationFormElement.RelativeLocation && p.IsRequired);
    var isRequiredPersonTravelWith = Model.ApplicationFormElements.Any(p => p.FormElementId == (int)ApplicationFormElement.PersonTravelWith && p.IsRequired);
    var isRequiredApplicationTogether = Model.ApplicationFormElements.Any(p => p.FormElementId == (int)ApplicationFormElement.ApplicationTogether && p.IsRequired && Model.CountryCallingCode == "964"); //Iraq
    var isRelationApplicationId = Model.RelationalApplicationId.HasValue;
    var isApplicantTypeId = Model.ApplicantTypeId;
    var isCountryCode = Model.CountryCallingCode;
    var isJobText = Model.Job != null ? true : false;
    var isShowReleatedInvidual = Model.ShowReleatedInvidual;
    var defaultMailAddress = "<EMAIL>";
}

<div id="divStep3DocumentInformation" class="pb-5" data-wizard-type="step-content">
    @Html.HiddenFor(m => m.ApplicantId)
    <h4 class="mb-10 font-weight-bold text-dark">@SiteResources.ApplicationStep3DocumentInformation.ToTitleCase()</h4>
    <div class="row">
        <div class="col-xl-12">
            <div class="form-group row">
                <div class="col-lg-3 col-md-6">
                    <label class="font-weight-bold">@SiteResources.TotalYearInCountry.ToTitleCase()</label>
                    @(Html.Kendo().NumericTextBoxFor(m => m.TotalYearInCountry).Min(0).Decimals(0).Format("0"))
                    <a href="javascript:void(0)" class="form-text small" onclick="calculateTotalYearInCountrySinceBorn();">(@SiteResources.SinceBorn.ToTitleCase())</a>
                    <span asp-validation-for="TotalYearInCountry"></span>
                </div>
                <div class="col-lg-3 col-md-6">
                    <label class="font-weight-bold">@SiteResources.ReimbursementType.ToTitleCase()</label>
                    @(Html.Kendo().DropDownListFor(m => m.ReimbursementTypeId)
                        .HtmlAttributes(new { @class = "form-control" })
                        .Events(events => events.Change("onChangeReimbursementType"))
                        .Filter(FilterType.Contains)
                        .OptionLabel(SiteResources.Select)
                        .DataTextField("Text")
                        .DataValueField("Value")
                        .DataSource(source =>
                        {
                            source.Read(read =>
                            {
                                read.Action("GetReimbursementTypeSelectList", "Parameter", new { Area = "" });
                            });
                        }))
                    <span asp-validation-for="ReimbursementTypeId"></span>
                </div>
                <div class="applicationReimbursementSponsorDetail col-lg-6 col-md-12">
                    <label class="font-weight-bold">@SiteResources.ReimbursementSponsorDetail.ToTitleCase()</label>
                    <input type="text" asp-for="ReimbursementSponsorDetail" class="form-control" autocomplete="off" oninput="let p=this.selectionStart;this.value=this.value.toUpperCase();this.setSelectionRange(p, p);" onfocusout="this.value=this.value.trim();" />
                    <span asp-validation-for="ReimbursementSponsorDetail"></span>
                </div>
            </div>
            <div class="form-group row">
                <div class="col-lg-3 col-md-6">
                    <label class="font-weight-bold">@SiteResources.Occupation.ToTitleCase()</label>
                    @(Html.Kendo().DropDownListFor(m => m.OccupationId)
                        .HtmlAttributes(new { @class = "form-control" })
                        .Events(events => events.Change("onChangeOccupation"))
                        .Filter(FilterType.Contains)
                        .OptionLabel(SiteResources.Select)
                        .DataTextField("Text")
                        .DataValueField("Value")
                        .DataSource(source =>
                        {
                            source.Read(read =>
                            {
                                read.Action("GetDataOccupationTypeSelectList", "Parameter", new { Area = "" });
                            });
                        }))
                    <span asp-validation-for="OccupationId"></span>

                    <div class="applicationOccupationTextName">
                        <input type="text" asp-for="Job" class="form-control" autocomplete="off" oninput="let p=this.selectionStart;this.value=this.value.toUpperCase();this.setSelectionRange(p, p);" onfocusout="this.value=this.value.trim();" />
                    </div>
                </div>
                <div class="applicationJobDetail col-lg-3 col-md-6">
                    <label class="font-weight-bold">@SiteResources.CompanyName.ToTitleCase()</label>
                    <input type="text" asp-for="CompanyName" class="form-control" autocomplete="off" oninput="let p=this.selectionStart;this.value=this.value.toUpperCase();this.setSelectionRange(p, p);" onfocusout="this.value=this.value.trim();" />
                    <span asp-validation-for="CompanyName"></span>
                </div>
                <div class="applicationJobDetail col-lg-3 col-md-6">
                    <label class="font-weight-bold">@SiteResources.TotalYearInCompany.ToTitleCase()</label>
                    @(Html.Kendo().NumericTextBoxFor(m => m.TotalYearInCompany).Min(0).Decimals(0).Format("0"))
                    <span asp-validation-for="TotalYearInCompany"></span>
                </div>
                <div class="applicationJobDetail col-lg-3 col-md-6">
                    <label class="font-weight-bold">@SiteResources.MonthlySalary.ToTitleCase()</label>
                    <div class="d-flex">
                        @(Html.Kendo().NumericTextBoxFor(m => m.MonthlySalary).Min(0).Decimals(0).Format("0").HtmlAttributes(new { style = "width: 100px;" }))
                        @(Html.Kendo().DropDownListFor(m => m.MonthlySalaryCurrencyId)
                            .HtmlAttributes(new { @class = "form-control", style = "width: 100px;" })
                            .Filter(FilterType.Contains)
                            .OptionLabel(SiteResources.Select)
                            .DataTextField("Text")
                            .DataValueField("Value")
                            .DataSource(source =>
                            {
                                source.Read(read =>
                                {
                                    read.Action("GetCurrencyTypeSelectList", "Parameter", new { Area = "" });
                                });
                            }))
                    </div>
                    <span asp-validation-for="MonthlySalary"></span>
                    <span asp-validation-for="MonthlySalaryCurrencyId"></span>
                </div>
            </div>
            <div class="form-group row">
                <div class="col-lg-3 col-md-6">
                    <label class="font-weight-bold k-display-block">@SiteResources.HasDeed.ToTitleCase()</label>
                    @(Html.Kendo().CheckBoxFor(m => m.HasDeed).HtmlAttributes(new { @class = "checkbox-square" }).Label(SiteResources.Yes.ToTitleCase()))
                </div>
                <div class="col-lg-3 col-md-6">
                    <label class="font-weight-bold k-display-block">@SiteResources.HasBankAccount.ToTitleCase()</label>
                    @(Html.Kendo().CheckBoxFor(m => m.HasBankAccount).HtmlAttributes(new { @class = "checkbox-square" }).Label(SiteResources.Yes.ToTitleCase()))
                </div>
                <div class="applicationBankBalance col-lg-3 col-md-6">
                    <label class="font-weight-bold">@SiteResources.BankBalance.ToTitleCase()</label>
                    <div class="d-flex">
                        @(Html.Kendo().NumericTextBoxFor(m => m.BankBalance).Min(0).Decimals(0).Format("0").HtmlAttributes(new { style = "width: 100px;" }))
                        @(Html.Kendo().DropDownListFor(m => m.BankBalanceCurrencyId)
                            .HtmlAttributes(new { @class = "form-control", style = "width: 100px;" })
                            .Filter(FilterType.Contains)
                            .OptionLabel(SiteResources.Select)
                            .DataTextField("Text")
                            .DataValueField("Value")
                            .DataSource(source =>
                            {
                                source.Read(read =>
                                {
                                    read.Action("GetCurrencyTypeSelectList", "Parameter", new { Area = "" });
                                });
                            }))
                    </div>
                    <span asp-validation-for="BankBalance"></span>
                    <span asp-validation-for="BankBalanceCurrencyId"></span>
                </div>
                <div class="col-lg-3 col-md-6">
                    <label class="font-weight-bold k-display-block">@SiteResources.HasEntryBan.ToTitleCase()</label>
                    @(Html.Kendo().CheckBoxFor(m => m.HasEntryBan).HtmlAttributes(new { @class = "checkbox-square" }).Label(SiteResources.Yes.ToTitleCase()))
                </div>
            </div>
            <div class="form-group row">
                <div class="visaCategory col-lg-3 col-md-6">
                    <label class="font-weight-bold">@SiteResources.VisaCategory.ToTitleCase()</label>
                    @(Html.Kendo().DropDownListFor(m => m.VisaCategoryId)
                        .HtmlAttributes(new { @class = "form-control" })
                        .Events(events =>
                        {
                            if (Model.CountryCallingCode == "993")
                            {
                                events.Change("onChangeVisaCategoryType");
                            }
                        })
                        .Filter(FilterType.Contains)
                        .OptionLabel(SiteResources.Select)
                        .DataTextField("Text")
                        .DataValueField("Value")
                        .DataSource(source =>
                        {
                            source.Read(read =>
                            {
                                read.Action("GetBranchApplicationCountryVisaCategorySelectList", "Application", new { Area = "Appointment", branchApplicationCountryId = Model.BranchApplicationCountryId });
                            });
                        }))
                </div>
                <div class="additionalServiceType col-lg-3 col-md-6">
                    <label class="font-weight-bold">@SiteResources.AdditionalServiceType.ToTitleCase()</label>
                    @(Html.Kendo().DropDownListFor(m => m.AdditionalServiceTypeId)
                        .HtmlAttributes(new { @class = "form-control" })
                        .Events(events => events.Change("onChangeAdditionalServiceType"))
                        .Filter(FilterType.Contains)
                        .OptionLabel(SiteResources.Select)
                        .DataTextField("Text")
                        .DataValueField("Value")
                        .DataSource(source =>
                        {
                            source.Read(read =>
                            {
                                read.Action("GetAdditionalServiceTypeSelectList", "Parameter", new { Area = "" });
                            });
                        }))
                    <span asp-validation-for="AdditionalServiceTypeId"></span>
                </div>
                @if (Model.CountryCallingCode == "966")
                { // Saudi Arabia
                    <div class="col-lg-3 col-md-6">
                        <label class="font-weight-bold">@SiteResources.NumberOfEntry.ToTitleCase()</label>
                        @(Html.Kendo().DropDownListFor(m => m.NumberOfEntryId)
                            .HtmlAttributes(new { @class = "form-control" })
                            .Filter(FilterType.Contains)
                            .OptionLabel(SiteResources.Select)
                            .DataTextField("Text")
                            .DataValueField("Value")
                            .DataSource(source =>
                            {
                                source.Read(read =>
                                {
                                    read.Action("GetApplicationNumberOfEntrySelectList", "Application", new { Area = "Appointment" });
                                });
                            }))
                    </div>
                }
                <div class="col-lg-3 col-md-6" @* onclick="checkEntryDateIsAvailable()"*@>
                    <label class="font-weight-bold">@SiteResources.EntryDate.ToTitleCase()</label>
                    @(Html.Kendo().DatePickerFor(m => m.EntryDate).Format(SiteResources.DatePickerFormatView).Value(Model.IsExistingApplication ?
                        Model.EntryDate :
                        (Model.RelationalApplicationDetail != null && Model.RelationalApplicationDetail.RelationalApplicationDocumentDetail != null) ?
                        Model.RelationalApplicationDetail.RelationalApplicationDocumentDetail.EntryDate :
                        DateTime.Today))
                    <span asp-validation-for="EntryDate"></span>
                </div>
                <div class="col-lg-3 col-md-6">
                    <label class="font-weight-bold">@SiteResources.ExitDate.ToTitleCase()</label>
                    @(Html.Kendo().DatePickerFor(m => m.ExitDate).Format(SiteResources.DatePickerFormatView).Value(Model.IsExistingApplication ?
                        Model.ExitDate :
                        (Model.RelationalApplicationDetail != null && Model.RelationalApplicationDetail.RelationalApplicationDocumentDetail != null) ?
                        Model.RelationalApplicationDetail.RelationalApplicationDocumentDetail.ExitDate :
                        DateTime.Today))
                    <span asp-validation-for="ExitDate"></span>
                </div>
            </div>
            <div class="form-group row">
                <div class="col-lg-3 col-md-6">
                    <label class="font-weight-bold">@SiteResources.CityToVisit.ToTitleCase()</label>
                    <input type="text" asp-for="CityName" class="form-control" autocomplete="off" oninput="let p=this.selectionStart;this.value=this.value.toUpperCase();this.setSelectionRange(p, p);" onfocusout="this.value=this.value.trim();" />
                    <span asp-validation-for="CityName"></span>
                </div>
                <div class="col-lg-3 col-md-6">
                    <label class="font-weight-bold">@SiteResources.AccomodationDetail.ToTitleCase()</label>
                    <input type="text" asp-for="AccomodationDetail" class="form-control" autocomplete="off" oninput="let p=this.selectionStart;this.value=this.value.toUpperCase();this.setSelectionRange(p, p);" onfocusout="this.value=this.value.trim();" />
                    <span asp-validation-for="AccomodationDetail"></span>
                </div>
                <div class="col-lg-3 col-md-6">
                    <label class="font-weight-bold k-display-block">@SiteResources.HasRelativeAbroad.ToTitleCase()</label>
                    @(Html.Kendo().CheckBoxFor(m => m.HasRelativeAbroad).HtmlAttributes(new { @class = "checkbox-square" }).Label(SiteResources.Yes.ToTitleCase()))
                </div>
                <div class="applicationRelative col-lg-3 col-md-6">
                    <label class="font-weight-bold">@SiteResources.RelativeLocation.ToTitleCase()</label>
                    <input type="text" asp-for="RelativeLocation" class="form-control" autocomplete="off" oninput="let p=this.selectionStart;this.value=this.value.toUpperCase();this.setSelectionRange(p, p);" onfocusout="this.value=this.value.trim();" />
                    <span asp-validation-for="RelativeLocation"></span>
                </div>
            </div>
            <div class="tsFormGroup form-group row">
                <div class="col-lg-3 col-md-6">
                    <label class="font-weight-bold">@SiteResources.VehicleType.ToTitleCase()</label>
                    @(Html.Kendo().DropDownListFor(m => m.VehicleTypeId)
                        .HtmlAttributes(new { @class = "form-control" })
                        .AutoWidth(true)
                        .Filter(FilterType.Contains)
                        .OptionLabel(SiteResources.Select)
                        .DataTextField("Text")
                        .DataValueField("Value")
                        .DataSource(source =>
                        {
                            source.Read(read =>
                            {
                                read.Action("GetVehicleTypeSelectList", "Parameter", new { Area = "" });
                            });
                        }))
                    <span asp-validation-for="VehicleTypeId"></span>
                </div>
                @Html.HiddenFor(m => Model.VehicleType)
                <div class="col-lg-3 col-md-6">
                    <label class="font-weight-bold">@SiteResources.BrandModel.ToTitleCase()</label>
                    @(Html.Kendo().DropDownListFor(m => m.BrandModelId)
                        .HtmlAttributes(new { @class = "form-control" })
                        .Events(events => events.Change("onChangeBrandModel"))
                        .Filter(FilterType.Contains)
                        .OptionLabel(SiteResources.Select)
                        .DataTextField("Text")
                        .DataValueField("Value")
                        .DataSource(source =>
                        {
                            source.Read(read =>
                            {
                                read.Action("GetApplicationBrandModelsDataSelectList", "Parameter", new { Area = "" });
                            });
                        }))
                    <span asp-validation-for="BrandModelId"></span>
                </div>
                <div class="tsBrandModel col-lg-3 col-md-6">
                    <label class="font-weight-bold">@SiteResources.Brand.ToTitleCase()</label>
                    <input type="text" asp-for="BrandText" class="form-control" autocomplete="off" oninput="let p=this.selectionStart;this.value=this.value.toUpperCase();this.setSelectionRange(p, p);" onfocusout="this.value=this.value.trim();" />
                    <span asp-validation-for="BrandText"></span>
                </div>
                <div class="tsBrandModel col-lg-3 col-md-6">
                    <label class="font-weight-bold">@SiteResources.Model.ToTitleCase()</label>
                    <input type="text" asp-for="ModelText" class="form-control" autocomplete="off" oninput="let p=this.selectionStart;this.value=this.value.toUpperCase();this.setSelectionRange(p, p);" onfocusout="this.value=this.value.trim();" />
                    <span asp-validation-for="ModelText"></span>
                </div>
                <div class="col-lg-3 col-md-6">
                    <label class="font-weight-bold">@SiteResources.PlateNo.ToTitleCase()</label>
                    <input type="text" asp-for="PlateNo" class="form-control" autocomplete="off" oninput="let p=this.selectionStart;this.value=this.value.toUpperCase();this.setSelectionRange(p, p);" onfocusout="this.value=this.value.trim();" />
                    <span asp-validation-for="PlateNo"></span>
                </div>
                <div class="applicationJobDetail col-lg-3 col-md-6">
                    <label class="font-weight-bold">@SiteResources.ModelYear.ToTitleCase()</label>
                    @(Html.Kendo().NumericTextBoxFor(m => m.ModelYear).Min(0).Decimals(0).Format("0"))
                    <span asp-validation-for="ModelYear"></span>
                </div>
                <div class="col-lg-3 col-md-6">
                    <label class="font-weight-bold">@SiteResources.ChassisNumber.ToTitleCase()</label>
                    <input type="text" asp-for="ChassisNumber" class="form-control" autocomplete="off" oninput="let p=this.selectionStart;this.value=this.value.toUpperCase();this.setSelectionRange(p, p);" onfocusout="this.value=this.value.trim();" />
                    <span asp-validation-for="ChassisNumber"></span>
                </div>
            </div>
            <div class="form-group row">
                <div class="col-lg-3 col-md-6">
                    <label class="font-weight-bold k-display-block">@SiteResources.ResidenceApplicationToBeMade.ToTitleCase()</label>
                    @(Html.Kendo().DropDownListFor(m => m.ResidenceApplication)
                        .HtmlAttributes(new { @class = "form-control" })
                        .AutoWidth(true)
                        .SelectedIndex(2)
                        .DataTextField("Text")
                        .DataValueField("Value")
                        .DataSource(source =>
                        {
                            source.Read(read =>
                            {
                                read.Action("GetYesNoQuestionSelectList", "Parameter", new { Area = "", hasUnspecified = true });
                            });
                        }))
                </div>
                <div class="col-lg-3 col-md-6">
                    <label class="font-weight-bold k-display-block">@SiteResources.ApplicationTogether.ToTitleCase()</label>
                    @(Html.Kendo().DropDownListFor(m => m.ApplicationTogetherId)
                        .HtmlAttributes(new { @class = "form-control" })
                        .AutoWidth(true)
                        .OptionLabel(SiteResources.Select)
                        .DataTextField("Text")
                        .DataValueField("Value")
                        .DataSource(source =>
                        {
                            source.Read(read =>
                            {
                                read.Action("GetYesNoQuestionSelectList", "Parameter", new { Area = "", hasUnspecified = false });
                            });
                        }))
                </div>
                <div class="applicationTogetherWith col-lg col-md">
                    <label class="font-weight-bold k-display-block">50 @SiteResources.Year.ToTitleCase() +</label>
                    @(Html.Kendo().NumericTextBoxFor(m => m.ApplicationTogetherFiftyYearCount).Min(0).Decimals(0).Format("0").HtmlAttributes(new { style = "width: 100px;" }))
                </div>
                <div class="applicationTogetherWith col-lg col-md">
                    <label class="font-weight-bold k-display-block">15 @SiteResources.Year.ToTitleCase() -</label>
                    @(Html.Kendo().NumericTextBoxFor(m => m.ApplicationTogetherFifteenYearCount).Min(0).Decimals(0).Format("0").HtmlAttributes(new { style = "width: 100px;" }))
                </div>
                <div class="applicationPersonTravelWith col-lg-3 col-md-6">
                    <label class="font-weight-bold">@SiteResources.PersonTravelWith.ToTitleCase()</label>
                    <input type="text" asp-for="PersonTravelWith" class="form-control" autocomplete="off" oninput="let p=this.selectionStart;this.value=this.value.toUpperCase();this.setSelectionRange(p, p);" onfocusout="this.value=this.value.trim();" />
                    <span asp-validation-for="PersonTravelWith"></span>
                </div>
                <div class="applicationPersonTravelWith col-lg-3 col-md-6">
                    <label class="font-weight-bold k-display-block">@SiteResources.PersonTravelWithHasVisa.ToTitleCase()</label>
                    @(Html.Kendo().CheckBoxFor(m => m.PersonTravelWithHasVisa).HtmlAttributes(new { @class = "checkbox-square" }).Label(SiteResources.Yes.ToTitleCase()))
                </div>
                <div class="releatedInvidualInsurance col-lg-3 col-md-6">
                    <label class="font-weight-bold k-display-block">@SiteResources.ReleatedInvidualInsurance.ToTitleCase()</label>
                    @(Html.Kendo().DropDownListFor(m => m.ProvidedWithHasRelatedInsuranceId)
                        .HtmlAttributes(new { @class = "form-control" })
                        .AutoWidth(true)
                        .OptionLabel(SiteResources.Select)
                        .DataTextField("Text")
                        .DataValueField("Value")
                        .DataSource(source =>
                        {
                            source.Read(read =>
                            {
                                read.Action("GetYesNoQuestionSelectList", "Parameter", new { Area = "", hasUnspecified = false });
                            });
                        }))
                </div>
                <div class="releatedInvidualInsuranceEditGrid">
                    @(Html.Kendo().Grid(Model.ReleatedInsuranceApplicationDetail)
                        .Name("gridReleatedInsuranceApplication")
                        .Navigatable() // Klavye ile gezinmeyi aktif eder
                        .Events(e => {
                            e.Save("onGridSave");
                        })
                        .Columns(columns =>
                        {
                            columns.Bound(o => o.Id).Visible(false);
                            columns.Bound(o => o.ReleatedInsuranceNationalityId).Visible(false);
                            columns.Bound(o => o.ReleatedInsuranceNationality).ClientTemplateId("nationalityTypeTemplate").Title(SiteResources.Nationality.ToTitleCase()).Sortable(false).Width(250);
                            columns.Bound(o => o.PassportNumber).Title(SiteResources.PassportNo.ToTitleCase());
                            columns.Bound(o => o.Name).Title(SiteResources.Name.ToTitleCase());
                            columns.Bound(o => o.Surname).Title(SiteResources.Surname.ToTitleCase());
							columns.Bound(o => o.BirthDate).Title(SiteResources.BirthDate.ToTitleCase()).Format("{0:dd/MM/yyyy}").EditorTemplateName("Date");
                            columns.Bound(o => o.PhoneNumber1).Title(SiteResources.PhoneNumber.ToTitleCase());
                            columns.Bound(o => o.Email).Title(SiteResources.Email.ToTitleCase());
                            columns.Command(command =>
                            {
                                command.Custom("Sil").Click("onDeleteClick");
                            }).Title(SiteResources.Delete.ToTitleCase());
                        }).ToolBar(toolbar => { toolbar.Create().Text(SiteResources.AddRelatedIndividual); })
                        .Editable(editable => editable.Mode(GridEditMode.InCell))
                        .Scrollable(s => s.Height("auto"))
                        .DataSource(dataSource => dataSource
                        .Ajax()
                        .Batch(true)
                        .Model(model =>
                        {
                            model.Id(p => p.Id);
                            model.Field(p => p.ReleatedInsuranceNationality).DefaultValue(ViewData["defaultNationalities"] as AddUpdateApplicationViewModel.NationalityViewModel).Editable(true); // Varsayılan değer
                            model.Field(o => o.PassportNumber).Editable(true); // Varsayılan değer
                            model.Field(o => o.Name).Editable(true); // Varsayılan değer
                            model.Field(o => o.Surname).Editable(true); // Varsayılan değer
                            model.Field(o => o.BirthDate).Editable(true).DefaultValue(null); // Varsayılan tarih
                            model.Field(o => o.PhoneNumber1).Editable(true);
                            model.Field(o => o.Email).Editable(true).DefaultValue(defaultMailAddress);
                        })
                        .Read(read => read.Action("GetRelatedInsuranceApplications", "Application", new { Area = "Appointment", EncryptedId = Model.EncryptedApplicationId }))
                        )
                        )
                </div>


            </div>
            @if (Model.CountryCallingCode == "993")
            {
                <div class="form-group row">
                    <div class="col-lg-3 col-md-6">
                        <label class="font-weight-bold k-display-block">@SiteResources.HasPersonVisitedTurkeyBefore.ToTitleCase()</label>
                        @(Html.Kendo().CheckBoxFor(m => m.HasPersonVisitedTurkeyBefore).HtmlAttributes(new { @class = "checkbox-square" }).Label(SiteResources.Yes.ToTitleCase()))
                    </div>
                </div>
            }
        </div>
    </div>
    <div class="separator separator-dashed my-5"></div>
    <div class="row">
        <div class="col-xl-12">
            <h3 class="font-size-lg text-dark font-weight-bold mb-6">@SiteResources.PreviouslyObtainedVisas.ToTitleCase()</h3>
            @for (int i = 0; i < 5; i++)
            {
                Model.VisaHistories.Add(new AddUpdateApplicationViewModel.VisaHistory());
                @Html.HiddenFor(m => Model.VisaHistories[i].ApplicationVisaHistoryId)

                <div class="form-group row">
                    @if (Model.CountryCallingCode != "965")
                    {
                        <div class="col-lg-3 col-md-6">
                            <label class="font-weight-bold">@SiteResources.Country.ToTitleCase()</label>
                            @(Html.Kendo().DropDownListFor(m => Model.VisaHistories[i].CountryId)
                                .HtmlAttributes(new { @class = "form-control" })
                                .Filter(FilterType.Contains)
                                .OptionLabel(SiteResources.Select)
                                .DataTextField("Text")
                                .DataValueField("Value")
                                .DataSource(source =>
                                {
                                    source.Read(read =>
                                    {
                                        read.Action("GetCachedCountrySelectList", "Parameter", new { Area = "" });
                                    });
                                }))
                        </div>
                        <div class="col-lg-3 col-md-6">
                            <label class="font-weight-bold">@SiteResources.VisaFromDate.ToTitleCase()</label>
                            @(Html.Kendo().DatePickerFor(m => Model.VisaHistories[i].FromDate)
                                .Format(SiteResources.DatePickerFormatView))
                        </div>
                        <div class="col-lg-3 col-md-6">
                            <label class="font-weight-bold">@SiteResources.VisaUntilDate.ToTitleCase()</label>
                            @(Html.Kendo().DatePickerFor(m => Model.VisaHistories[i].UntilDate)
                                .Format(SiteResources.DatePickerFormatView))
                        </div>
                        <div class="col-lg-3 col-md-6">
                            <label class="font-weight-bold k-display-block">&nbsp;</label>
                            @(Html.Kendo().CheckBoxFor(m => Model.VisaHistories[i].IsUsed).HtmlAttributes(new { @class = "checkbox-square" }).Label(SiteResources.IsVisaUsed))
                        </div>
                    }
                    @if (Model.CountryCallingCode == "965")
                    {
                        <div class="col-lg-2 col-md-1">
                            <label class="font-weight-bold">@SiteResources.Country.ToTitleCase()</label>
                            @(Html.Kendo().DropDownListFor(m => Model.VisaHistories[i].CountryId)
                                .HtmlAttributes(new { @class = "form-control" })
                                .Filter(FilterType.Contains)
                                .OptionLabel(SiteResources.Select)
                                .DataTextField("Text")
                                .DataValueField("Value")
                                .DataSource(source =>
                                {
                                    source.Read(read =>
                                    {
                                        read.Action("GetCachedCountrySelectList", "Parameter", new { Area = "" });
                                    });
                                }))
                        </div>
                        <div class="col-lg-2 col-md-1">
                            <label class="font-weight-bold">@SiteResources.VisaFromDate.ToTitleCase()</label>
                            @(Html.Kendo().DatePickerFor(m => Model.VisaHistories[i].FromDate)
                                .Format(SiteResources.DatePickerFormatView))
                        </div>
                        <div class="col-lg-2 col-md-1">
                            <label class="font-weight-bold">@SiteResources.VisaUntilDate.ToTitleCase()</label>
                            @(Html.Kendo().DatePickerFor(m => Model.VisaHistories[i].UntilDate)
                                .Format(SiteResources.DatePickerFormatView))
                        </div>
                        <div class="col-lg-1 col-md-1">
                            <label class="font-weight-bold k-display-block">&nbsp;</label>
                            @(Html.Kendo().CheckBoxFor(m => Model.VisaHistories[i].IsUsed).HtmlAttributes(new { @class = "checkbox-square" }).Label(SiteResources.IsVisaUsed))
                        </div>
                        <div class="col-lg-2 col-md-1">
                            <label class="font-weight-bold">@SiteResources.NumberOfEntry.ToTitleCase()</label>
                            @(Html.Kendo().DropDownListFor(m => Model.VisaHistories[i].NumberOfEntryId)
                                .HtmlAttributes(new { @class = "form-control" })
                                .Filter(FilterType.Contains)
                                .OptionLabel(SiteResources.Select)
                                .DataTextField("Text")
                                .DataValueField("Value")
                                .DataSource(source =>
                                {
                                    source.Read(read =>
                                    {
                                        read.Action("GetApplicationNumberOfEntrySelectList", "Application", new { Area = "Appointment" });
                                    });
                                }))
                        </div>
                        <div class="col-lg-2 col-md-1">
                            <label class="font-weight-bold">@SiteResources.VisaIsUsedYear.ToTitleCase()</label>
                            @(Html.Kendo().DropDownListFor(m => Model.VisaHistories[i].VisaIsUsedYear)
                                .HtmlAttributes(new { @class = "form-control" })
                                .Filter(FilterType.Contains)
                                .OptionLabel(SiteResources.Select)
                                .DataTextField("Text")
                                .DataValueField("Value")
                                .DataSource(source =>
                                {
                                    source.Read(read =>
                                    {
                                        read.Action("GetApplicationVisaUsedYearSelectList", "Application", new { Area = "Appointment" });
                                    });
                                }))
                        </div>
                        <div class="col-lg-1 col-md-1">
                            <label class="font-weight-bold">@SiteResources.VisaDecision.ToTitleCase()</label>
                            @(Html.Kendo().DropDownListFor(m => Model.VisaHistories[i].OldVisaDecisionId)
                                .HtmlAttributes(new { @class = "form-control" })
                                .Filter(FilterType.Contains)
                                .OptionLabel(SiteResources.Select)
                                .DataTextField("Text")
                                .DataValueField("Value")
                                .DataSource(source =>
                                {
                                    source.Read(read =>
                                    {
                                        read.Action("GetOldVisaDecisionSelectList", "Parameter", new { Area = "" });
                                    });
                                }))
                        </div>
                    }
                </div>
            }
        </div>
    </div>
</div>


<script>

    $(function () {
        checkConditionalValidations();
        onChangeReimbursementType();
        checkBankBalanceVisibility();
        checkRelativeVisibility();
        checkPersonTravelWithVisibility();
        checkReleatedInvidualInsuranceVisibility();
        //checkEntryDateIsAvailable();
        checkOccupationOtherVisibility();
        checkVisaCategoryRequirement();
        onChangeAdditionalServiceType();
        onChangeBrandModel();

        $('#HasBankAccount').change(function () {
            checkBankBalanceVisibility();
        });

        $('#HasRelativeAbroad').change(function () {
            checkRelativeVisibility();
        });

        $('#ApplicationTogetherId').change(function () {
            checkPersonTravelWithVisibility();
        });
        $('#ProvidedWithHasRelatedInsuranceId').change(function () {
            checkReleatedInvidualInsuranceVisibility();
        });
    });

    function checkVisaCategoryRequirement() {
        var applicationTypeId = $("#ApplicationTypeId").val();

        if (applicationTypeId === '@ApplicationType.NonApplication.ToInt()') {
            $(".additionalServiceType").show();
            $("#AdditionalServiceTypeId").attr("required", true);
            $("#AdditionalServiceTypeId").attr("validationMessage", jsResources.RequiredField);
        }
        else {
            $(".additionalServiceType").hide();
            $("#VisaCategoryId").attr("required", true);
            $("#VisaCategoryId").attr("validationMessage", jsResources.RequiredField);
        }
    }

    function checkConditionalValidations() {

        if ('@isRequiredTotalYearInCountry' === "True") {
            $("#TotalYearInCountry").attr("required", true);
            $("#TotalYearInCountry").attr("validationMessage", jsResources.RequiredField);
        }

        if ('@isRequiredReimbursementType' === "True") {
            $("#ReimbursementTypeId").attr("required", true);
            $("#ReimbursementTypeId").attr("validationMessage", jsResources.RequiredField);
        }

        if ('@isRequiredReimbursementSponsorDetail' === "True") {
            $("#ReimbursementSponsorDetail").attr("required", true);
            $("#ReimbursementSponsorDetail").attr("validationMessage", jsResources.RequiredField);
        }

        if ('@isRequiredJob' === "True") {
            $("#OccupationId").attr("required", true);
            $("#OccupationId").attr("validationMessage", jsResources.RequiredField);
        }

        if ('@isRequiredCompanyName' === "True") {
            $("#CompanyName").attr("required", true);
            $("#CompanyName").attr("validationMessage", jsResources.RequiredField);
        }

        if ('@isRequiredTotalYearInCompany' === "True") {
            $("#TotalYearInCompany").attr("required", true);
            $("#TotalYearInCompany").attr("validationMessage", jsResources.RequiredField);
        }

        if ('@isRequiredMonthlySalary' === "True") {
            $("#MonthlySalary").attr("required", true);
            $("#MonthlySalary").attr("validationMessage", jsResources.RequiredField);
        }

        if ('@isRequiredMonthlySalaryCurrency' === "True") {
            $("#MonthlySalaryCurrencyId").attr("required", true);
            $("#MonthlySalaryCurrencyId").attr("validationMessage", jsResources.RequiredField);
        }

        if ('@isRequiredBankBalance' === "True") {
            $("#BankBalance").attr("required", true);
            $("#BankBalance").attr("validationMessage", jsResources.RequiredField);
        }

        if ('@isRequiredBankBalanceCurrency' === "True") {
            $("#BankBalanceCurrencyId").attr("required", true);
            $("#BankBalanceCurrencyId").attr("validationMessage", jsResources.RequiredField);
        }

        if ('@isRequiredCityName' === "True") {
            $("#CityName").attr("required", true);
            $("#CityName").attr("validationMessage", jsResources.RequiredField);
        }

        if ('@isRequiredAccomodationDetail' === "True") {
            $("#AccomodationDetail").attr("required", true);
            $("#AccomodationDetail").attr("validationMessage", jsResources.RequiredField);
        }

        if ('@isRequiredRelativeLocation' === "True") {
            $("#RelativeLocation").attr("required", true);
            $("#RelativeLocation").attr("validationMessage", jsResources.RequiredField);
        }

        if ('@isRequiredPersonTravelWith' === "True") {
            $("#PersonTravelWith").attr("required", true);
            $("#PersonTravelWith").attr("validationMessage", jsResources.RequiredField);
        }

        if ('@isRequiredApplicationTogether' === "True") {
            $("#ApplicationTogetherId").attr("required", true);
            $("#ApplicationTogetherId").attr("validationMessage", jsResources.RequiredField);
            $("#IsRequiredApplicationTogether").val("true");
        }
    }

    function onChangeReimbursementType() {

        //$("#HasBankAccount").prop('checked', false);
        //$("#BankBalance").data("kendoNumericTextBox").value(null);
        //$("#BankBalanceCurrencyId").data("kendoDropDownList").select(null);
        //$("#HasDeed").prop('checked', false);

        checkBankBalanceVisibility();

        if ($("#ReimbursementTypeId").val() === '@ReimbursementType.NoFinancialDocument.ToInt()') {
            $(".applicationJobDetail").hide();

            $("#CompanyName").val('');
            $("#TotalYearInCompany").data("kendoNumericTextBox").value(null);
            $("#MonthlySalary").data("kendoNumericTextBox").value(null);
            $("#MonthlySalaryCurrencyId").data("kendoDropDownList").select(null);
        }
        else {
            $(".applicationJobDetail").show();
        }

        if ($("#ReimbursementTypeId").val() === '@ReimbursementType.Sponsor.ToInt()') {
            $(".applicationReimbursementSponsorDetail").show();

            var relationalHasBankAccount = $("#RelationalApplicationDetail_RelationalApplicationDocumentDetail_HasBankAccount").val();
            var relationalBankBalance = $("#RelationalApplicationDetail_RelationalApplicationDocumentDetail_BankBalance").val();
            var relationalBankBalanceCurrencyId = $("#RelationalApplicationDetail_RelationalApplicationDocumentDetail_BankBalanceCurrencyId").val();
            var relationalHasDeed = $("#RelationalApplicationDetail_RelationalApplicationDocumentDetail_HasDeed").val();

            if (relationalHasBankAccount === "True")
                $("#HasBankAccount").click();
            $("#BankBalance").data("kendoNumericTextBox").value(relationalBankBalance);
            $("#BankBalanceCurrencyId").data("kendoDropDownList").select(parseInt(relationalBankBalanceCurrencyId));
            if (relationalHasDeed === "True")
                $("#HasDeed").click();

            if ('@isRelationApplicationId' === "True" && '@isApplicantTypeId' === '@ApplicantType.Family.ToInt()' && '@isCountryCode' === "966") { //966 - Suudi Arabia CountryCode
                var relationalJob = $("#RelationalApplicationDetail_RelationalApplicationDocumentDetail_Job").val();
                var relationalOccupationId = $("#RelationalApplicationDetail_RelationalApplicationDocumentDetail_OccupationId").val();
                var relationalCompanyName = $("#RelationalApplicationDetail_RelationalApplicationDocumentDetail_CompanyName").val();
                var relationalMonthlySalary = $("#RelationalApplicationDetail_RelationalApplicationDocumentDetail_MonthlySalary").val();
                var relationalMonthlySalaryCurrencyId = $("#RelationalApplicationDetail_RelationalApplicationDocumentDetail_MonthlySalaryCurrencyId").val();
                var relationalTotalYearInCompany = $("#RelationalApplicationDetail_RelationalApplicationDocumentDetail_TotalYearInCompany").val();
                var reimbursementSponsorDetail = $("#RelationalApplicationDetail_RelationalApplicationDocumentDetail_ReimbursementSponsorDetail").val();

                $("#Job").val(relationalJob);
                $("#OccupationId").val(relationalOccupationId);
                $("#CompanyName").val(relationalCompanyName);
                $("#TotalYearInCompany").data("kendoNumericTextBox").value(relationalTotalYearInCompany);
                $("#MonthlySalary").data("kendoNumericTextBox").value(relationalMonthlySalary);
                $("#MonthlySalaryCurrencyId").data("kendoDropDownList").select(parseInt(relationalMonthlySalaryCurrencyId));
                $("#ReimbursementSponsorDetail").val(reimbursementSponsorDetail);
            }
        }
        else {
            $(".applicationReimbursementSponsorDetail").hide();

            $("#ReimbursementSponsorDetail").val('');
        }
    }

    function checkBankBalanceVisibility() {
        if ($('#HasBankAccount').is(":checked")) {
            $(".applicationBankBalance").show();
        }
        else {
            $(".applicationBankBalance").hide();

            $("#BankBalance").val('');
            $("#BankBalanceCurrencyId").data("kendoDropDownList").select(null);
        }
    }

    function checkRelativeVisibility() {
        if ($('#HasRelativeAbroad').is(":checked")) {
            $(".applicationRelative").show();
        }
        else {
            $(".applicationRelative").hide();

            $("#RelativeLocation").val('');
        }
    }

    function checkPersonTravelWithVisibility() {
        if ($('#ApplicationTogetherId').val() == '@YesNoQuestion.Yes.ToInt()') {

			if ('@isShowReleatedInvidual' === "False") {
                $(".applicationPersonTravelWith").show();
            }
            else {
                $(".releatedInvidualInsurance").show();
                $(".applicationPersonTravelWith").hide();
            }
            if ($('#CountryCallingCode').val() == "964") {
                $(".applicationTogetherWith").show();
            }
        }
        else {
            $(".applicationPersonTravelWith").hide();
            $(".applicationTogetherWith").hide();
            $(".releatedInvidualInsurance").hide();

            $("#PersonTravelWith").val('');
            $("#PersonTravelWithHasVisa").prop('checked', false);

            $("#ApplicationTogetherFiftyYearCount").data("kendoNumericTextBox").value(null);
            $("#ApplicationTogetherFifteenYearCount").data("kendoNumericTextBox").value(null);
        }
    }
    function checkReleatedInvidualInsuranceVisibility() {
        if ($('#ProvidedWithHasRelatedInsuranceId').val() == '@YesNoQuestion.Yes.ToInt()') {
            $(".releatedInvidualInsuranceEditGrid").show();
        }
        else {
            $(".releatedInvidualInsuranceEditGrid").hide();
        }
    }
    function calculateTotalYearInCountrySinceBorn() {
        var todayDate = new Date(new Date().toDateString());
        var birthDate = kendo.parseDate($("#BirthDate").val(), jsResources.DatePickerFormatJs);
        var totalYearInCountrySinceBorn = todayDate.getFullYear() - birthDate.getFullYear();
        $("#TotalYearInCountry").data("kendoNumericTextBox").value(totalYearInCountrySinceBorn);
    }

    //function checkEntryDateIsAvailable() {

    //    $("#EntryDate").on('change', function () {
    //        var todayDate = new Date(new Date().toDateString());
    //        var entryDate = kendo.parseDate($("#EntryDate").val(), jsResources.DatePickerFormatJs);
    //        var exitDate = kendo.parseDate($("#ExitDate").val(), jsResources.DatePickerFormatJs);
    //        var applicationTime = kendo.parseDate($("#ApplicationTime").val(), jsResources.DatePickerFormatJs);
    //        var isExistingApplication = /*$("#IsExistingApplication").val()*/@Model.IsExistingApplication;

    //        if (/*(isExistingApplication === "true" || isExistingApplication === "True" || isExistingApplication === true)*/@Model.IsExistingApplication && entryDate < applicationTime) {
    //            alert('Selected date must be greater than today date');
    //            $(this).val('');
    //        }
    //        else if (entryDate < todayDate) {
    //            alert('Selected date must be greater than today123 date');
    //            $(this).val('');
    //        }
    //    });
    //}
    function onChangeOccupation() {
        if ($("#OccupationId").val() === '@DataOccupationType.Other.ToInt()') {
            $(".applicationOccupationTextName").show();
            $("#Job").attr("required", true);
            $("#Job").attr("validationMessage", jsResources.RequiredField);
        }
        else {
            $(".applicationOccupationTextName").hide();
            $("#Job").attr("required", false);
        }
    }
    function checkOccupationOtherVisibility() {
        if ('@isJobText' === "True") {
            $(".applicationOccupationTextName").show();
        }
        else {
            $(".applicationOccupationTextName").hide();
        }
    }

    function onChangeAdditionalServiceType() {
        if ($("#AdditionalServiceTypeId").val() === '@AdditionalServiceType.TS.ToInt()') {
            $(".tsFormGroup").show();
            $(".tsBrandModel").hide();
            $("#VisaCategoryId").setRequired(false);
            $("#VehicleTypeId").attr("required", true);
            $("#VehicleTypeId").attr("validationMessage", jsResources.RequiredField);
            $("#BrandModelId").attr("required", true);
            $("#BrandModelId").attr("validationMessage", jsResources.RequiredField);
            $("#CityName").attr("required", true);
            $("#CityName").attr("validationMessage", jsResources.RequiredField);
            $("#PlateNo").attr("required", true);
            $("#PlateNo").attr("validationMessage", jsResources.RequiredField);
            $("#ModelYear").attr("required", true);
            $("#ModelYear").attr("validationMessage", jsResources.RequiredField);
            $("#ChassisNumber").attr("required", true);
            $("#ChassisNumber").attr("validationMessage", jsResources.RequiredField);
        }
        else if ($("#AdditionalServiceTypeId").val() === '@AdditionalServiceType.YSS.ToInt()') {
            $(".tsFormGroup").hide();
            $("#VehicleTypeId").attr("required", false);
            $("#VehicleTypeId").data("kendoDropDownList").select(null);
            $("#BrandModelId").attr("required", false);
            $("#BrandModelId").data("kendoDropDownList").select(null);
            //$("#CityName").val("");
            $("#CityName").attr("required", false);
            $("#PlateNo").val("");
            $("#PlateNo").attr("required", false);
            $("#ModelYear").val(null);
            $("#ModelYear").attr("required", false);
            $("#ChassisNumber").val("");
            $("#ChassisNumber").attr("required", false);

        }
        else {
            $(".tsFormGroup").hide();
            $("#AdditionalServiceTypeId").attr("required", false);
            $("#AdditionalServiceTypeId").data("kendoDropDownList").select(null);
            $("#VehicleTypeId").attr("required", false);
            $("#VehicleTypeId").data("kendoDropDownList").select(null);
            $("#BrandModelId").attr("required", false);
            $("#BrandModelId").data("kendoDropDownList").select(null);
            //$("#CityName").val("");
            $("#CityName").attr("required", false);
            $("#PlateNo").val("");
            $("#PlateNo").attr("required", false);
            $("#BrandText").attr("required", false);
            $("#ModelText").attr("required", false);
            $("#ModelYear").val(null);
            $("#ModelYear").attr("required", false);
            $("#ChassisNumber").val("");
            $("#ChassisNumber").attr("required", false);
        }

    }
    function onChangeBrandModel() {
        if ($("#BrandModelId").val() == 21174) {
            $(".tsBrandModel").show();
            $("#BrandText").attr("required", true);
            $("#BrandText").attr("validationMessage", jsResources.RequiredField);
            $("#ModelText").attr("required", true);
            $("#ModelText").attr("validationMessage", jsResources.RequiredField);
        }
    }

    function onChangeVisaCategoryType() {
        var visaCategoryId = $("#VisaCategoryId").val();
        var dropDownList = $("#ResidenceApplication").data("kendoDropDownList");
        if (visaCategoryId === "40" || visaCategoryId === "41") {
            dropDownList.value('@YesNoQuestion.Yes.ToInt()');
            dropDownList.readonly();
            $("#CityName").attr("required", true);
            $("#ResidenceApplication").attr("validationMessage", jsResources.RequiredField);
        } else {
            dropDownList.value('@YesNoQuestion.Unspecified.ToInt()');
            dropDownList.readonly(false);

             if ($("#AdditionalServiceTypeId").val() !== '@AdditionalServiceType.TS.ToInt()'){
                 $("#CityName").attr("required", false);
                 $("#CityName").removeAttr("validationMessage");
             }
        }
        
        dropDownList.refresh();
    }

    function onDeleteClick(e) {
        e.preventDefault(); // Varsayılan işlevi engelle
        var grid = $("#gridReleatedInsuranceApplication").data("kendoGrid");
        var row = $(e.currentTarget).closest("tr"); // Butonun bulunduğu satır
        removeRow(grid, row); // Satırı sil
    }

    function removeRow(grid, row) {
        var dataItem = grid.dataItem(row); // Seçilen satırın verisini alır
        grid.dataSource.remove(dataItem); // Grid'in veri kaynağından kaldırır
    }

    function onGridSave(e) {
        if (e.values.Email !== undefined) {
            // Kullanıcının yeni girdiği değeri al ve temizle
            let rawEmail = e.values.Email || "";
            let trimmedEmail = rawEmail.trim();
            
            // Sadece boşluk karakterleri girilmişse, sessizce boş değere çevirelim
            if (trimmedEmail === "") {
                setTimeout(() => {
                    e.model.set("Email", "");
                    e.container.find("input[name='Email']").val("");
                    e.model.dirty = true;
                }, 100);
                return; // Hata vermeden çık
            }

            // Sayısal olmayan karakterleri temizle
            let cleanedEmail = trimmedEmail.replace(/[\s\u00A0]+/g, ' ');
            
            // Hatalıysa
            if (!emailFormatCheckParam(cleanedEmail)) {
                alert("Geçersiz e-posta: " + cleanedEmail);

                e.preventDefault(); // Kaydı engelle
                
                // Sadece grid'deki input'u temizle
                setTimeout(() => {
                    e.model.set("Email", "");
                    e.container.find("input[name='Email']").val("");
                }, 100);

                return;
            }

            // Hatalı değilse modeli ve input'u güncelle
            setTimeout(() => {
                e.model.set("Email", cleanedEmail);
                e.container.find("input[name='Email']").val(cleanedEmail);
                e.model.dirty = true; // 👈 önemli
            }, 100);

        }

        // Telefon numarası kontrolü
        if (e.values.PhoneNumber1 !== undefined) {
            let rawPhone = e.values.PhoneNumber1 || "";
            // Önce tüm boşlukları temizleyelim
            let trimmedPhone = rawPhone.trim();

            // Sadece boşluk karakterleri girilmişse, sessizce boş değere çevirelim
            if (trimmedPhone === "") {
                setTimeout(() => {
                    e.model.set("PhoneNumber1", "");
                    e.container.find("input[name='PhoneNumber1']").val("");
                    e.model.dirty = true;
                }, 100);
                return; // Hata vermeden çık
            }

            // Sayısal olmayan karakterleri temizle
            let cleanedPhone = trimmedPhone.replace(/[^0-9.]/g, '').replace(/(\..*)\./g, '$1');

            // Temizlendikten sonra içerik kalmadıysa (sadece sayısal olmayan karakterler varsa)
            if (cleanedPhone === "") {
                alert("Geçersiz telefon numarası! " + cleanedPhone);
                e.preventDefault();

                setTimeout(() => {
                    e.model.set("PhoneNumber1", "");
                    e.container.find("input[name='PhoneNumber1']").val("");
                }, 100);
                return;
            }

            // Telefon geçerliyse modeli güncelle
            setTimeout(() => {
                e.model.set("PhoneNumber1", cleanedPhone);
                e.container.find("input[name='PhoneNumber1']").val(cleanedPhone);
                e.model.dirty = true;
            }, 100);
        }
    }

    function emailFormatCheckParam(emailValue) {
        let checkValue = false;
        if (emailValue !== null && emailValue !== "") {
            $.ajax({
                type: "GET",
                async: false, // dikkat: senkron istek
                url: "/Appointment/Application/GetEmailFormatCheckTrue?email=" + encodeURIComponent(emailValue),
                success: function (data) {
                    checkValue = data;
                },
                error: function () {
                    console.warn("Sunucu hatası!");
                }
            });
        }
        return checkValue;
    }

</script>

<script id="nationalityTypeTemplate" type="text/x-kendo-template">
    #= ReleatedInsuranceNationality && ReleatedInsuranceNationality.Name ? ReleatedInsuranceNationality.Name : 'N/A' #
</script>