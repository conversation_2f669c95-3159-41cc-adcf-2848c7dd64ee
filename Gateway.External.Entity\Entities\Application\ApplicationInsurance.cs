﻿using System;

namespace Gateway.External.Entity.Entities.Application
{
    public class ApplicationInsurance
    {
        public ApplicationInsurance()
        {
            IsActive = true;
            IsDeleted = false;
        }
        public int Id { get; set; }
        public int ApplicationId { get; set; }
        public int ProviderId { get; set; }
        public string Number { get; set; }
        public DateTime StartDate { get; set; }
        public DateTime EndDate { get; set; }
        public int ApplicationExtraFeeId { get; set; }
        public int RelatedIndividualInsuraneId { get; set; }
        public int? OldRelatedIndividualInsuraneId { get; set; }
        public string DocumentId { get; set; }
        public string Price2 { get; set; }
        public bool IsCancelled { get; set; }
        public DateTime? CancelledDate { get; set; }
        public int? CancelledBy { get; set; }
        public int? CancellationReasonId { get; set; }

        public int? CreatedBy { get; set; }

        public DateTime? CreatedAt { get; set; }

        public int? UpdatedBy { get; set; }

        public DateTime? UpdatedAt { get; set; }
        public bool IsActive { get; set; }

        public bool IsDeleted { get; set; }

        public int? DeletedBy { get; set; }

        public DateTime? DeletedAt { get; set; }
    }
}
