using BarcodeStandard;
using Gateway.Barcode;
using Gateway.Core.Pagination;
using Gateway.Extensions;
using Gateway.External.Application.Appointment.Dto;
using Gateway.External.Application.Appointment.Validator;
using Gateway.External.Application.Enums;
using Gateway.External.Application.Lookup;
using Gateway.External.Application.Notification;
using Gateway.External.Application.Notification.Events;
using Gateway.External.Application.Notification.Requests;
using Gateway.External.Entity.Entities.PreApplications;
using Gateway.External.Persistence;
using Gateway.External.Resources;
using Gateway.Validation;
using HtmlAgilityPack;
using iTextSharp.text;
using iTextSharp.text.pdf;
using Microsoft.EntityFrameworkCore;
using Portal.Gateway.Common.Utility.Extensions;
using Portal.Gateway.Common.Utility.Helpers;
using Serilog;
using SkiaSharp;
using System;
using System.Collections.Generic;
using System.IO;
using System.Linq;
using System.Threading.Tasks;
using static Gateway.External.Application.Enums.Enums;
using EnumExtensions = Gateway.Extensions.EnumExtensions;

namespace Gateway.External.Application.Appointment
{
    public class AppointmentService : IAppointmentService
    {
        private static readonly ILogger Logger = Log.ForContext<AppointmentService>();
        private readonly ApiDbContext _dbContext;
        private readonly IValidationService _validationService;
        private readonly INotificationEventService _notificationEventService;


        #region ctor

        public AppointmentService(IValidationService validationService, INotificationEventService notificationEventService, ApiDbContext dbContext)
        {
            _validationService = validationService;
            _dbContext = dbContext;
            _notificationEventService = notificationEventService;
        }

        #endregion


        #region Public Methods

        public async Task<CreateAppointmentResult> CreateAppointment(CreateAppointmentRequest request)
        {
            var validationResult = _validationService.Validate(typeof(CreateAppointmentValidator), request);

            if (!validationResult.IsValid)
                return new CreateAppointmentResult
                {
                    Status = CreateAppointmentStatus.InvalidInput,
                    Message = ServiceResources.INVALID_INPUT_ERROR,
                    ValidationMessages = validationResult.ErrorMessages
                };

            try
            {
                var samePassportCheck = CheckSamePassportUsed(request);

                if (!samePassportCheck)
                    return new CreateAppointmentResult
                    {
                        Status = CreateAppointmentStatus.RuleSamePassportUsedBetweenApplicants,
                        Message = ServiceResources.SAME_PASSPORT_USED_BETWEEN_APPLICANTS
                    };

                var passportExpireDateCheck = CheckPassportExpireDate(request);

                if (passportExpireDateCheck)
                    return new CreateAppointmentResult
                    {
                        Status = CreateAppointmentStatus.PassportValidityPeriodError,
                        Message = ServiceResources.PASSPORT_VALIDITY_PERIOD_ERROR
                    };

                var birthDateCheck = CheckBirthDate(request);

                if (birthDateCheck)
                    return new CreateAppointmentResult
                    {
                        Status = CreateAppointmentStatus.BirthDateValidityError,
                        Message = ServiceResources.BIRTHDATE_MUST_BE_PAST_TENSE
                    };

                var branchApplicationCountry = await _dbContext.BranchApplicationCountry
                    .Include(i => i.Branch)
                    .ThenInclude(i => i.BranchTranslations)
                    .Include(i => i.Branch)
                    .ThenInclude(i => i.BranchDataTranslation)
                    .Where(p => p.IsActive && !p.IsDeleted && p.BranchId == request.BranchId)
                    .FirstOrDefaultAsync();

                if (branchApplicationCountry == null)
                    return new CreateAppointmentResult
                    {
                        Status = CreateAppointmentStatus.BranchNotFound,
                        Message = ServiceResources.BRANCH_NOT_FOUND
                    };

                var slot = await _dbContext.Slot
                    .Where(p => p.IsActive && !p.IsDeleted && p.Id == request.SlotId)
                    .FirstOrDefaultAsync();

                if (slot == null || slot.SlotTime < DateTime.Now)
                    return new CreateAppointmentResult
                    {
                        Status = CreateAppointmentStatus.SlotNotFound,
                        Message = ServiceResources.SLOT_NOT_FOUND
                    };

                if (slot.BranchApplicationCountryId != branchApplicationCountry.Id)
                    return new CreateAppointmentResult()
                    {
                        Status = CreateAppointmentStatus.WrongSlotBranchCombination,
                        Message = ServiceResources.SLOT_NOT_IN_THIS_BRANCH
                    };

                if (request.Applicants.Count > 1 && request.ApplicantTypeId == (int)Enums.Enums.ApplicantType.Individual)
                    return new CreateAppointmentResult
                    {
                        Status = CreateAppointmentStatus.ApplicantCountError,
                        Message = ServiceResources.APPLICANT_COUNT_VALIDATION_ERROR
                    };

                var isWalkIn = request.SlotId == 0;

                var resultMessages = new List<string>();
                PreApplicationApplicant hasRepeatedAppointment = null;

                for (var i = 0; i < request.Applicants.Count; i++)
                {
                    var repeatedAppointment = await _dbContext.PreApplicationApplicant
                        .Include(p => p.PreApplication)
                        .ThenInclude(a => a.BranchApplicationCountry)
                        .Include(p => p.PreApplication)
                        .ThenInclude(p => p.Slot)
                        .If(request.Context.ChannelTypeId == (int)Enums.Enums.ChannelType.GwInternational, s => s.Where(w => w.NationalityId == request.Applicants[i].NationalityId))
                        .Where(p => p.IsActive && !p.IsDeleted && p.PreApplication.ExternalUserId != null && p.PreApplication.ChannelType == request.Context.ChannelTypeId &&
                                    p.PassportNumber == request.Applicants[i].PassportNumber &&
                                    p.PreApplication.Slot.SlotTime >= DateTime.Today &&
                                    p.PreApplication.BranchApplicationCountry.CountryId ==
                                    branchApplicationCountry.CountryId).FirstOrDefaultAsync();

                    if (repeatedAppointment == null) continue;

                    hasRepeatedAppointment = repeatedAppointment;
                    resultMessages.Add($"{repeatedAppointment.PassportNumber} - {repeatedAppointment.Name} {repeatedAppointment.Surname} / {ServiceResources.AppointmentDateTitle} :{repeatedAppointment.PreApplication.Slot.SlotTime.ToString("dd/MM/yyyy HH:mm")}");
                    break;
                }

                if (hasRepeatedAppointment != null)
                {
                    if (hasRepeatedAppointment.PreApplication.ExternalUserId == request.Context.Identity.ExternalUserId)
                    {
                        return new CreateAppointmentResult
                        {
                            Status = CreateAppointmentStatus.ResourceExists,
                            Message = $"{ServiceResources.REPEATED_APPOINTMENT_FOUND}: {resultMessages.First()}"
                        };
                    }

                    return new CreateAppointmentResult
                    {
                        Status = CreateAppointmentStatus.SamePassportNotAllowed,
                        Message = $"{ServiceResources.APPOINTMENT_FOUND_WITH_SAME_PASSPORT}"
                    };
                }

                if (request.Context.ChannelTypeId == (int)Enums.Enums.ChannelType.GwInternational)
                {
                    // rule 4 : mail && phone control

                    var phoneNumbers = request.Applicants.Select(s => s.PhoneNumber).ToList();

                    var emails = request.Applicants.Select(s => s.Email).ToList();

                    var appointmentApplicantsWithRepeatedInformation = await _dbContext.PreApplicationApplicant
                        .Include(i => i.PreApplication)
                        .ThenInclude(i => i.Slot)
                        .Where(r =>
                            r.IsActive && !r.IsDeleted && r.PreApplication.ExternalUserId != null && r.PreApplication.ChannelType == request.Context.ChannelTypeId &&
                            r.PreApplication.Slot.SlotTime >= DateTime.Now &&
                            r.PreApplication.Slot.SlotTime < DateTime.Now.AddMonths(1) &&
                            (emails.Any(a => a == r.Email) || phoneNumbers.Any(a => a == r.PhoneNumber)))
                        .Select(r => new
                        {
                            r.PreApplicationId,
                            r.Email,
                            r.PhoneNumber,
                        })
                        .ToListAsync();

                    //mail && phoneNumber

                    foreach (var applicant in appointmentApplicantsWithRepeatedInformation.GroupBy(r => r.Email).Where(r => r.Count() >= 2))
                    {
                        if (emails.Exists(e => e == applicant.Key) && applicant.GroupBy(r => r.PreApplicationId).Count() >= 2)
                        {
                            return new CreateAppointmentResult
                            {
                                Status = CreateAppointmentStatus.RuleRepeatedMailNotAllowed,
                                Message = $"{ServiceResources.REPEATED_MAIL_NOT_ALLOWED} / {applicant.Key}"
                            };
                        }
                    }

                    foreach (var applicant in appointmentApplicantsWithRepeatedInformation.GroupBy(r => r.PhoneNumber).Where(r => r.Count() >= 3))
                    {
                        if (phoneNumbers.Exists(e => e == applicant.Key) && applicant.GroupBy(r => r.PreApplicationId).Count() >= 3)
                        {
                            return new CreateAppointmentResult
                            {
                                Status = CreateAppointmentStatus.RuleRepeatedPhoneNumberNotAllowed,
                                Message = $"{ServiceResources.REPEATED_PHONE_NUMBER_NOT_ALLOWED} / {applicant.Key}"
                            };
                        }
                    }
                }

                var updateSlotQuotaList = new SortedDictionary<int, int>();
                var updateSlotQuota = 0;
                var applicantsCount = request.Applicants.Count;
                var remainingSlot = applicantsCount - slot.Quota;
                var utcTime =
                    DateTime.SpecifyKind(new DateTime(slot.SlotTime.Year, slot.SlotTime.Month, slot.SlotTime.Day, 0, 0, 0),
                        DateTimeKind.Local);
                var addDaysSlotTime = utcTime.AddDays(1);

                if (request.Context.ChannelTypeId == (int)Enums.Enums.ChannelType.GwInternational)
                {
                    if (remainingSlot > 0)
                    {
                        return new CreateAppointmentResult
                        {
                            Status = CreateAppointmentStatus.SlotQuotaNotFound,
                            Message = ServiceResources.SLOT_QUOTA_NOT_FOUND
                        };
                    }
                }
                else
                {
                    if (remainingSlot > 0)
                    {
                        var nextSlotQuota = await _dbContext.Slot.Where(p =>
                                p.IsActive && !p.IsDeleted && p.SlotTime >= slot.SlotTime && p.SlotTime < addDaysSlotTime
                                && p.BranchApplicationCountry.BranchId == request.BranchId && (request.Context.ChannelTypeId == (int)Enums.Enums.ChannelType.GwInternational ?
                                    p.SlotTypeId == (int)Enums.Enums.SlotType.CallCenter : (p.SlotTypeId == 1 || p.SlotTypeId == 0))
                            ).OrderBy(p => p.SlotTime)
                            .ToListAsync();
                        var applicantNumber = 0;

                        foreach (var item in nextSlotQuota)
                        {
                            if (applicantsCount > 0)
                            {
                                for (var i = applicantNumber; i < request.Applicants.Count; i++)
                                {
                                    if (item != null && item.Quota != 0 && applicantsCount > 0)
                                    {
                                        request.Applicants[i].SlotId = item.Id;
                                        item.Quota -= 1;
                                        applicantNumber++;
                                        applicantsCount -= 1;
                                        updateSlotQuota += 1;
                                        var updateSlotQuotaListNullCheck = updateSlotQuotaList.Where(p => p.Key == item.Id);
                                        if (updateSlotQuotaListNullCheck.Any())
                                        {
                                            updateSlotQuotaList.Remove(item.Id);
                                        }

                                        updateSlotQuotaList.Add(item.Id, updateSlotQuota);
                                    }
                                    else
                                        break;
                                }

                                updateSlotQuota = 0;
                            }
                            else
                                break;
                        }

                        if (applicantsCount > 0)
                        {
                            return new CreateAppointmentResult
                            {
                                Status = CreateAppointmentStatus.SlotQuotaNotFound,
                                Message = ServiceResources.SLOT_QUOTA_NOT_FOUND
                            };
                        }
                    }
                }

                var wifeOrHusbandCount = request.Applicants.Count(t => t.RelationShipId == 1);

                if (wifeOrHusbandCount > 1)
                {
                    return new CreateAppointmentResult
                    {
                        Status = CreateAppointmentStatus.ApplicantMoreThanOneWifeOrHusband,
                        Message = ServiceResources.ONLY_ONE_RECORD_CANBE_SET_AS_WIFEORHUSBAND_RECORD_FOR_APPOINTMENT
                    };
                }

                var newPreApplication = new PreApplication
                {
                    BranchApplicationCountryId = branchApplicationCountry.Id,
                    ApplicantTypeId = request.ApplicantTypeId,
                    VisaCategoryId = (int)Enums.Enums.VisaCategoryType.Unspecified,
                    SlotId = request.SlotId,
                    ApplicationTypeId = (int)Enums.Enums.ApplicationType.Normal,
                    SlotTypeId = slot.SlotTypeId,
                    Note = request.Note,
                    StatusId = (int)Enums.Enums.ApplicationStatus.Active,
                    CreatedAt = DateTime.Now,
                    VasTypeId = request.VasTypeId,
                    CreatedBy = request.CreatedBy,
                    ExternalUserId = request.Context.Identity.ExternalUserId,
                    ChannelType = (byte)request.Context.ChannelTypeId,
                    PreApplicationApplicants = request.Applicants.Select(p => new PreApplicationApplicant
                    {
                        BirthDate = p.BirthDate.GetValueOrDefault(),
                        Email = request.Context.ChannelTypeId == (int)Enums.Enums.ChannelType.GwInternational ? p.Email ?? string.Empty : p.Email.IsNullOrWhitespace() ? request.Applicants.FirstOrDefault(r => r.Email != null)?.Email : p.Email,
                        Name = p.Name,
                        Surname = p.Surname,
                        PassportExpireDate = p.PassportExpireDate.GetValueOrDefault(),
                        PassportNumber = p.PassportNumber,
                        PhoneNumber = request.Context.ChannelTypeId == (int)Enums.Enums.ChannelType.GwInternational ? p.PhoneNumber ?? string.Empty : p.PhoneNumber.IsNullOrWhitespace() ? request.Applicants.FirstOrDefault(r => r.PhoneNumber != null)?.PhoneNumber : p.PhoneNumber,
                        RelationShipId = p.PassportNumber == request.Applicants.First().PassportNumber ? 99 : p.RelationShipId,
                        NationalityId = p.NationalityId ?? 0,
                        GenderId = p.GenderId ?? 0,
                        CreatedAt = DateTime.Now,
                        SlotId = p.SlotId ?? 0,
                    }).ToList()
                };

                await _dbContext.PreApplication.AddAsync(newPreApplication);

                if (!isWalkIn && updateSlotQuotaList.Count == 0)
                {
                    slot.Quota -= request.Applicants.Count;
                    _dbContext.Slot.Update(slot);
                }

                if (!isWalkIn && updateSlotQuotaList.Count > 0)
                {
                    foreach (var item in updateSlotQuotaList)
                    {
                        var updateSlot = await _dbContext.Slot
                            .Where(p => p.Id == item.Key)
                            .FirstOrDefaultAsync();

                        if (updateSlot != null)
                            _dbContext.Slot.Update(updateSlot);
                    }
                }

                await _dbContext.SaveChangesAsync();

                var appointmentLetter = GetCreateAppointmentLetter(new AppointmentLetterFileRequest()
                {
                    AppointmentNumber = newPreApplication.Id,
                    VasTypeId = newPreApplication.VasTypeId,
                    AppointmentDate = newPreApplication.Slot.SlotTime.Date,
                    SlotTime = newPreApplication.Slot.SlotTime.ToString("HH:mm"),
                    VisaCategoryId = newPreApplication.VisaCategoryId,
                    Applicants = newPreApplication.PreApplicationApplicants.Select(r => new AppointmentLetterFileRequest.ApplicantDto()
                    {
                        Email = r.Email,
                        PassportNumber = r.PassportNumber,
                        BirthDate = r.BirthDate,
                        Name = r.Name,
                        Surname = r.Surname
                    }).ToList(),
                    BranchInformation = new AppointmentLetterFileRequest.BranchDto()
                    {
                        Id = branchApplicationCountry.Branch.Id,
                        CountryId = branchApplicationCountry.Branch.CountryId,
                        Email = branchApplicationCountry.Branch.Email,
                        Address = branchApplicationCountry.Branch.Address,
                        AddressArabic = branchApplicationCountry.Branch.BranchDataTranslation
                            .FirstOrDefault(r => r.LanguageId == (int)Enums.Enums.Language.Arabic)?.Address,
                        CallCenterTelephone = branchApplicationCountry.Branch.Telephone,
                        Name = branchApplicationCountry
                                   .Branch.BranchTranslations
                                   .FirstOrDefault(branchTranslation =>
                                       branchTranslation.LanguageId == request.Context.LanguageId)?.Name
                               ?? branchApplicationCountry
                                   .Branch.BranchTranslations
                                   .FirstOrDefault()
                                   ?.Name,
                        NameEn = branchApplicationCountry.Branch.BranchTranslations
                            .FirstOrDefault(r => r.LanguageId == (int)Enums.Enums.Language.English)?.Name
                    }
                });

                var basicGuidelineAttach = branchApplicationCountry.Branch.BasicGuidelineId.HasValue
                    ? GetBasicGuidelineDocument(branchApplicationCountry.Branch.BasicGuidelineId.Value)
                    : null;

                var htmlText = GetHtmlText(request.Context.LanguageId, request.Context.ChannelTypeId, branchApplicationCountry.Note, true,
                    new NotificationHtmlTextDto
                    {
                        SlotTime = newPreApplication.Slot.SlotTime.ToString("HH:mm"),
                        BranchName = branchApplicationCountry.Branch.BranchTranslations
                            .FirstOrDefault(r => r.LanguageId == (int)Enums.Enums.Language.English)?.Name,
                        AppointmentDate = newPreApplication.Slot.SlotTime.Date,
                        AppointmentNumber = newPreApplication.Id.ToString()
                    });

                await _notificationEventService.Raise(new CreateAppointmentSendNotificationEvent
                {
                    AppointmentLetter = appointmentLetter,
                    BasicGuidelineLetter = basicGuidelineAttach,
                    AppointmentNumber = newPreApplication.Id,
                    AppointmentDate = newPreApplication.Slot.SlotTime.Date,
                    LanguageId = request.Context.LanguageId,
                    SlotTime = newPreApplication.Slot.SlotTime.ToString("HH:mm"),
                    HtmlText = htmlText,
                    Applicants = request.Applicants.Select(r => new SendNotificationApplicantDto()
                    {
                        Email = r.Email,
                    }).ToList(),
                    BranchInformation = new SendNotificationBranchDto()
                    {
                        Id = branchApplicationCountry.Branch.Id,
                        Name = branchApplicationCountry
                                   .Branch.BranchTranslations
                                   .FirstOrDefault(branchTranslation =>
                                       branchTranslation.LanguageId == request.Context.LanguageId)?.Name
                               ?? branchApplicationCountry
                                   .Branch.BranchTranslations
                                   .FirstOrDefault()
                                   ?.Name,
                        NameEn = branchApplicationCountry.Branch.BranchTranslations.FirstOrDefault(r => r.LanguageId == (int)Enums.Enums.Language.English)?.Name
                    }
                });

                return new CreateAppointmentResult
                {
                    Status = CreateAppointmentStatus.Successful,
                    Message = ServiceResources.RESOURCE_CREATED,
                    Id = newPreApplication.Id
                };
            }
            catch (Exception exc)
            {
                Logger.Error($@"Exception:{exc.Message}:{DateTime.Now}");

                return new CreateAppointmentResult
                {
                    Status = CreateAppointmentStatus.InternalServerError,
                    Message = ServiceResources.INTERNAL_SERVER_ERROR,
                };
            }
        }

        public async Task<DeleteAppointmentResult> DeleteAppointment(DeleteAppointmentRequest request)
        {
            var validationResult = _validationService.Validate(typeof(DeleteAppointmentValidator), request);

            if (!validationResult.IsValid)
                return new DeleteAppointmentResult
                {
                    Status = DeleteAppointmentStatus.InvalidInput,
                    Message = ServiceResources.INVALID_INPUT_ERROR,
                    ValidationMessages = validationResult.ErrorMessages
                };

            await using var transaction = await _dbContext.Database.BeginTransactionAsync();

            try
            {
                var appointment = await _dbContext.PreApplication.Include(r => r.Slot)
               .Where(p => !p.IsDeleted && p.IsActive && p.Id == request.AppointmentId && p.ExternalUserId == request.Context.Identity.ExternalUserId).FirstOrDefaultAsync();

                if (appointment == null)
                    return new DeleteAppointmentResult
                    {
                        Status = DeleteAppointmentStatus.ResourceNotFound,
                        Message = ServiceResources.RESOURCE_NOT_FOUND
                    };

                if (request.Context.ChannelTypeId == (int)Enums.Enums.ChannelType.GwInternational && appointment.Slot.SlotTime < DateTime.Now.AddHours(24))
                    return new DeleteAppointmentResult
                    {
                        Status = DeleteAppointmentStatus.RuleDeleteOperationNotAllowed,
                        Message = ServiceResources.DELETE_OPERATION_NOT_ALLOWED,
                    };

                var applicants = await _dbContext.PreApplicationApplicant
                    .Where(p => !p.IsDeleted && p.PreApplicationId == request.AppointmentId).ToListAsync();

                if (applicants.Any())
                {
                    foreach (var applicant in applicants)
                    {
                        var deleteAppointmentApplicantRequest = new DeleteAppointmentApplicantRequest
                        {
                            AppointmentId = appointment.Id,
                            AppointmentApplicantId = applicant.Id,
                            Context = request.Context
                        };

                        var response = await DeleteAppointmentApplicant(deleteAppointmentApplicantRequest);

                        if (response.Status != DeleteAppointmentApplicantStatus.Successful)
                            return new DeleteAppointmentResult
                            {
                                Status = (DeleteAppointmentStatus)response.Status,
                                Message = response.Message
                            };
                    }
                }
                else
                {
                    var slot = await _dbContext.Slot.Where(p => p.Id == appointment.SlotId).FirstOrDefaultAsync();

                    var applicantCount = applicants.Count(q => q.IsActive && !q.IsDeleted);

                    slot.Quota += applicantCount;

                    _dbContext.Slot.Update(slot);
                }

                appointment.DeletedAt = DateTime.Now;
                appointment.IsActive = false;
                appointment.IsDeleted = true;

                _dbContext.PreApplication.Update(appointment);

                await _dbContext.SaveChangesAsync();

                await transaction.CommitAsync();

                return new DeleteAppointmentResult
                {
                    Status = DeleteAppointmentStatus.Successful,
                    Message = ServiceResources.RESOURCE_DELETED
                };
            }
            catch (Exception exc)
            {
                Logger.Error($@"Exception:{exc.Message}:{DateTime.Now}");

                await transaction.RollbackAsync();
                return new DeleteAppointmentResult
                {
                    Status = DeleteAppointmentStatus.InternalServerError,
                    Message = ServiceResources.INTERNAL_SERVER_ERROR
                };
            }
        }

        public async Task<DeleteAppointmentApplicantResult> DeleteAppointmentApplicant(DeleteAppointmentApplicantRequest request)
        {
            var validationResult = _validationService.Validate(typeof(DeleteAppointmentApplicantValidator), request);

            if (!validationResult.IsValid)
                return new DeleteAppointmentApplicantResult
                {
                    Status = DeleteAppointmentApplicantStatus.InvalidInput,
                    Message = ServiceResources.INVALID_INPUT_ERROR,
                    ValidationMessages = validationResult.ErrorMessages
                };

            try
            {
                var applicant = await _dbContext.PreApplicationApplicant
            .Where(p => !p.IsDeleted && p.IsActive && p.Id == request.AppointmentApplicantId).FirstOrDefaultAsync();

                if (applicant == null)
                    return new DeleteAppointmentApplicantResult
                    {
                        Status = DeleteAppointmentApplicantStatus.ApplicantNotFound,
                        Message = ServiceResources.APPLICANT_NOT_FOUND
                    };

                var appointment = await _dbContext.PreApplication.Include(i => i.Slot)
                    .Where(p => !p.IsDeleted && p.IsActive && p.Id == request.AppointmentId && p.ExternalUserId == request.Context.Identity.ExternalUserId).FirstOrDefaultAsync();

                if (appointment == null)
                    return new DeleteAppointmentApplicantResult
                    {
                        Status = DeleteAppointmentApplicantStatus.AppointmentNotFound,
                        Message = ServiceResources.APPOINTMENT_NOT_FOUND
                    };

                if (applicant.PreApplicationId != appointment.Id)
                    return new DeleteAppointmentApplicantResult
                    {
                        Status = DeleteAppointmentApplicantStatus.ApplicantNotMatchWithAppointment,
                        Message = ServiceResources.APPLICANT_NOT_MATCH_WITH_APPOINTMENT
                    };

                if (request.Context.ChannelTypeId == (int)Enums.Enums.ChannelType.GwInternational && appointment.Slot.SlotTime < DateTime.Now.AddHours(24))
                    return new DeleteAppointmentApplicantResult
                    {
                        Status = DeleteAppointmentApplicantStatus.RuleDeleteOperationNotAllowed,
                        Message = ServiceResources.DELETE_OPERATION_NOT_ALLOWED,
                    };

                applicant.DeletedAt = DateTime.Now;
                applicant.IsActive = false;
                applicant.IsDeleted = true;
                _dbContext.PreApplicationApplicant.Update(applicant);

                var slotId = applicant.SlotId != 0
                    ? applicant.SlotId
                    : appointment.SlotId;

                var slot = await _dbContext.Slot.Where(p => p.Id == slotId).FirstOrDefaultAsync();

                slot.Quota += 1;

                _dbContext.Slot.Update(slot);

                await _dbContext.SaveChangesAsync();

                return new DeleteAppointmentApplicantResult
                {
                    Message = ServiceResources.RESOURCE_DELETED,
                    Status = DeleteAppointmentApplicantStatus.Successful
                };
            }
            catch (Exception exc)
            {
                Logger.Error($@"Exception:{exc.Message}:{DateTime.Now}");

                return new DeleteAppointmentApplicantResult
                {
                    Status = DeleteAppointmentApplicantStatus.InternalServerError,
                    Message = ServiceResources.INTERNAL_SERVER_ERROR
                };
            }
        }

        public async Task<UpdateAppointmentResult> UpdateAppointment(UpdateAppointmentRequest request)
        {
            var validationResult = _validationService.Validate(typeof(UpdateAppointmentValidator), request);

            if (!validationResult.IsValid)
                return new UpdateAppointmentResult
                {
                    Status = UpdateAppointmentStatus.InvalidInput,
                    Message = ServiceResources.INVALID_INPUT_ERROR,
                    ValidationMessages = validationResult.ErrorMessages
                };

            try
            {
                var samePassportCheck = CheckSamePassportUsed(request);

                if (!samePassportCheck)
                    return new UpdateAppointmentResult
                    {
                        Status = UpdateAppointmentStatus.RuleSamePassportUsedBetweenApplicants,
                        Message = ServiceResources.SAME_PASSPORT_USED_BETWEEN_APPLICANTS
                    };

                var passportExpireDateCheck = CheckPassportExpireDate(request);

                if (passportExpireDateCheck)
                    return new UpdateAppointmentResult
                    {
                        Status = UpdateAppointmentStatus.PassportValidityPeriodError,
                        Message = ServiceResources.PASSPORT_VALIDITY_PERIOD_ERROR
                    };

                var birthDateCheck = CheckBirthDate(request);

                if (birthDateCheck)
                    return new UpdateAppointmentResult
                    {
                        Status = UpdateAppointmentStatus.BirthDateValidityError,
                        Message = ServiceResources.BIRTHDATE_MUST_BE_PAST_TENSE
                    };

                foreach (var appointmentApplicant in request.Applicants)
                {
                    appointmentApplicant.SlotId ??= 0;
                }

                var appointment = await _dbContext.PreApplication
                    .Include(q => q.BranchApplicationCountry)
                    .Include(q => q.PreApplicationHistories)
                    .Include(q => q.Slot)
                    .Where(q => q.Id == request.AppointmentId && q.ExternalUserId == request.Context.Identity.ExternalUserId).FirstOrDefaultAsync();

                if (appointment == null)
                    return new UpdateAppointmentResult
                    {
                        Status = UpdateAppointmentStatus.AppointmentNotFound,
                        Message = ServiceResources.APPOINTMENT_NOT_FOUND,
                    };

                var currentSlotId = appointment.SlotId;

                var appointmentApplicants = await _dbContext.PreApplicationApplicant
                    .Include(a => a.PreApplicationApplicantHistories)
                    .Where(a => a.PreApplicationId == request.AppointmentId).ToListAsync();

                if (!appointmentApplicants.Any())
                    return new UpdateAppointmentResult
                    {
                        Status = UpdateAppointmentStatus.ApplicantNotFound,
                        Message = ServiceResources.APPLICANT_NOT_FOUND,
                    };

                var branchApplicationCountry = await _dbContext.BranchApplicationCountry.Include(i => i.Branch)
                    .ThenInclude(i => i.BranchTranslations)
                    .Include(i => i.Branch)
                    .ThenInclude(i => i.BranchDataTranslation)
                    .Where(q => q.IsActive && !q.IsDeleted && q.BranchId == request.BranchId)
                    .FirstOrDefaultAsync();

                if (branchApplicationCountry == null)
                    return new UpdateAppointmentResult
                    {
                        Status = UpdateAppointmentStatus.BranchNotFound,
                        Message = ServiceResources.BRANCH_NOT_FOUND,
                    };

                var resultMessages = new List<string>();
                PreApplicationApplicant hasRepeatedAppointment = null;
                var remainingCurrentSlotStatus = false;

                for (var i = 0; i < request.Applicants.Count; i++)
                {
                    var repeatedAppointment = await _dbContext.PreApplicationApplicant
                        .Include(p => p.PreApplication)
                        .ThenInclude(a => a.BranchApplicationCountry)
                        .Include(p => p.PreApplication)
                        .ThenInclude(p => p.Slot)
                        .If(request.Context.ChannelTypeId == (int)Enums.Enums.ChannelType.GwInternational, s => s.Where(w => w.NationalityId == request.Applicants[i].NationalityId))
                        .Where(p => p.IsActive && !p.IsDeleted && p.PreApplication.ExternalUserId != null && p.PreApplication.ChannelType == request.Context.ChannelTypeId &&
                                    p.PassportNumber == request.Applicants[i].PassportNumber &&
                                    p.PreApplication.Slot.SlotTime >= DateTime.Today &&
                                    p.PreApplication.BranchApplicationCountry.CountryId ==
                                    branchApplicationCountry.CountryId &&
                                    p.PreApplicationId != request.AppointmentId).FirstOrDefaultAsync();

                    if (repeatedAppointment == null) continue;

                    hasRepeatedAppointment = repeatedAppointment;
                    resultMessages.Add($"{repeatedAppointment.PassportNumber} - {repeatedAppointment.Name} {repeatedAppointment.Surname} / {ServiceResources.AppointmentDateTitle} : {repeatedAppointment.PreApplication.Slot.SlotTime.ToString("dd/MM/yyyy HH:mm")}");
                }

                if (hasRepeatedAppointment != null)
                {
                    if (hasRepeatedAppointment.PreApplication.ExternalUserId == request.Context.Identity.ExternalUserId)
                    {
                        return new UpdateAppointmentResult
                        {
                            Status = UpdateAppointmentStatus.RepeatedAppointmentFound,
                            Message = $"{ServiceResources.REPEATED_APPOINTMENT_FOUND}: {resultMessages.First()}"
                        };
                    }

                    return new UpdateAppointmentResult
                    {
                        Status = UpdateAppointmentStatus.SamePassportNotAllowed,
                        Message = $"{ServiceResources.APPOINTMENT_FOUND_WITH_SAME_PASSPORT}"
                    };
                }

                var newSlot = await _dbContext.Slot.Where(p => p.Id == request.SlotId).FirstOrDefaultAsync();

                if (newSlot == null || newSlot.SlotTime < DateTime.Now)
                    return new UpdateAppointmentResult
                    {
                        Status = UpdateAppointmentStatus.SlotNotFound,
                        Message = ServiceResources.SLOT_NOT_FOUND,
                    };

                if (newSlot.BranchApplicationCountryId != branchApplicationCountry.Id)
                    return new UpdateAppointmentResult
                    {
                        Status = UpdateAppointmentStatus.WrongSlotBranchCombination,
                        Message = ServiceResources.SLOT_NOT_IN_THIS_BRANCH,
                    };

                if (request.Context.ChannelTypeId == (int)Enums.Enums.ChannelType.GwInternational)
                {
                    // rule 1 : all update operations can made after 48 hours

                    if (appointment.Slot.SlotTime < DateTime.Now.AddHours(48))
                        return new UpdateAppointmentResult
                        {
                            Status = UpdateAppointmentStatus.RuleAppointmentUpdateNotAllowed,
                            Message = ServiceResources.APPOINTMENT_UPDATE_NOT_ALLOWED,
                        };

                    // rule 2 : changing branch for appointment update limited to 1

                    if (request.BranchId != appointment.BranchApplicationCountry.BranchId)
                    {
                        if (appointment.PreApplicationHistories.Any(r => r.PropertyName == "BranchApplicationCountryId"))
                            return new UpdateAppointmentResult
                            {
                                Status = UpdateAppointmentStatus.RuleBranchChangeNotAllowed,
                                Message = ServiceResources.BRANCH_CHANGE_NOT_ALLOWED
                            };
                    }

                    // rule 3 : slot updates for appointment update limited to 2

                    if (newSlot.Id != currentSlotId)
                    {
                        if (appointment.PreApplicationHistories.Count(r => r.PropertyName == "SlotId") >= 2)
                        {
                            return new UpdateAppointmentResult
                            {
                                Status = UpdateAppointmentStatus.RuleAppointmentSlotUpdatesLimit,
                                Message = ServiceResources.APPOINTMENT_SLOT_LIMIT,
                            };
                        }
                    }
                }

                var activeApplicantCount = appointmentApplicants.Where(r => r.IsActive && !r.IsDeleted).ToList().Count;

                if (newSlot.Id == currentSlotId)
                    newSlot.Quota += activeApplicantCount;

                var updateSlotQuotaList = new SortedDictionary<int, int>();
                var updateSlotQuota = 0;
                var applicantsCount = request.Applicants.Count;
                var remainingSlot = applicantsCount - newSlot.Quota;

                if (request.Context.ChannelTypeId == (int)Enums.Enums.ChannelType.GwInternational)
                {
                    if (remainingSlot > 0)
                        return new UpdateAppointmentResult
                        {
                            Status = UpdateAppointmentStatus.SlotQuotaNotFound,
                            Message = ServiceResources.SLOT_QUOTA_NOT_FOUND,
                        };
                }
                else
                {
                    if (remainingSlot > 0)
                    {
                        var nextSlotQuota = await _dbContext.Slot.Where(p =>
                            p.IsActive && !p.IsDeleted && p.SlotTime >= newSlot.SlotTime &&
                            p.SlotTime < newSlot.SlotTime.AddDays(1)
                            && p.BranchApplicationCountryId == branchApplicationCountry.Id && (request.Context.ChannelTypeId == (int)Enums.Enums.ChannelType.GwInternational ?
                                p.SlotTypeId == (int)Enums.Enums.SlotType.CallCenter : (p.SlotTypeId == 1 || p.SlotTypeId == 0))).OrderBy(p => p.SlotTime).ToListAsync();
                        var applicantNumber = 0;
                        for (var v = 0; v < nextSlotQuota.Count; v++)
                        {
                            if (applicantsCount > 0)
                            {
                                for (var i = applicantNumber; i < request.Applicants.Count; i++)
                                {
                                    if (nextSlotQuota[v] != null && nextSlotQuota[v].Quota != 0 && applicantsCount > 0)
                                    {
                                        request.Applicants[i].SlotId = nextSlotQuota[v].Id;
                                        nextSlotQuota[v].Quota -= 1;

                                        applicantNumber++;
                                        applicantsCount -= 1;
                                        updateSlotQuota += 1;

                                        var updateSlotQuotaListNullCheck = updateSlotQuotaList.Where(p => p.Key == nextSlotQuota[v].Id);

                                        if (updateSlotQuotaListNullCheck.Any())
                                            updateSlotQuotaList.Remove(nextSlotQuota[v].Id);

                                        updateSlotQuotaList.Add(nextSlotQuota[v].Id, updateSlotQuota);
                                    }
                                    else
                                        break;
                                }

                                updateSlotQuota = 0;
                            }
                            else
                                break;
                        }

                        if (applicantsCount > 0)
                            return new UpdateAppointmentResult
                            {
                                Status = UpdateAppointmentStatus.SlotQuotaNotFound,
                                Message = ServiceResources.SLOT_QUOTA_NOT_FOUND,
                            };
                    }
                }

                var wifeOrHusbandCount = request.Applicants.Count(t => t.RelationShipId == 1);

                if (wifeOrHusbandCount > 1)
                {
                    return new UpdateAppointmentResult
                    {
                        Status = UpdateAppointmentStatus.ApplicantMoreThanOneWifeOrHusband,
                        Message = ServiceResources.APPLICANT_ONLY_HAS_ONE_WİFEORHUSBAND
                    };
                }

                appointment.BranchApplicationCountryId = branchApplicationCountry.Id;
                appointment.ApplicantTypeId = request.ApplicantTypeId;
                appointment.SlotId = request.SlotId;
                appointment.VasTypeId = request.VasTypeId;
                appointment.IsActive = true;
                appointment.IsDeleted = false;
                appointment.SlotTypeId = newSlot.SlotTypeId;
                appointment.Note = request.Note;
                appointment.UpdatedAt = DateTime.Now;

                _dbContext.PreApplication.Update(appointment);

                var entityApplicantDeactivated = appointmentApplicants
                    .Where(p => request.Applicants.All(q => q.ApplicantId != p.Id)).AsEnumerable();

                var preApplicationApplicants = entityApplicantDeactivated.ToList();
                if (preApplicationApplicants.Any())
                {
                    preApplicationApplicants.ToList().ForEach(p => p.IsDeleted = true);
                    preApplicationApplicants.ToList().ForEach(p => p.IsActive = false);
                    preApplicationApplicants.ToList().ForEach(p => p.DeletedAt = DateTime.Now);

                    _dbContext.PreApplicationApplicant.UpdateRange(preApplicationApplicants);
                }

                var updatedApplicants = appointmentApplicants.Where(p => request.Applicants.Any(q => q.ApplicantId == p.Id)).ToList();

                //var isSlotExist = false;

                if (updatedApplicants.Any())
                {
                    var firstApplicant = request.Applicants.FirstOrDefault(q => q.ApplicantId == updatedApplicants.First().Id);

                    foreach (var updatedApplicant in updatedApplicants)
                    {
                        var applicant = request.Applicants.FirstOrDefault(q => q.ApplicantId == updatedApplicant.Id);

                        if (applicant == null) continue;

                        updatedApplicant.PassportNumber = applicant.PassportNumber;
                        updatedApplicant.PassportExpireDate = applicant.PassportExpireDate.GetValueOrDefault();
                        updatedApplicant.Name = applicant.Name;
                        updatedApplicant.Surname = applicant.Surname;
                        updatedApplicant.PassportExpireDate = applicant.PassportExpireDate.GetValueOrDefault();
                        updatedApplicant.BirthDate = applicant.BirthDate.GetValueOrDefault();
                        updatedApplicant.Email = request.Context.ChannelTypeId == (int)Enums.Enums.ChannelType.GwInternational ? applicant.Email ?? string.Empty : applicant.Email.IsNullOrWhitespace() ? firstApplicant?.Email : applicant.Email;
                        updatedApplicant.PhoneNumber = request.Context.ChannelTypeId == (int)Enums.Enums.ChannelType.GwInternational ? applicant.PhoneNumber ?? string.Empty : applicant.PhoneNumber.IsNullOrWhitespace() ? firstApplicant?.PhoneNumber : applicant.PhoneNumber;
                        updatedApplicant.RelationShipId = applicant.PassportNumber == firstApplicant?.PassportNumber ? 99 : applicant.RelationShipId.GetValueOrDefault();
                        updatedApplicant.GenderId = applicant.GenderId ?? 0;
                        updatedApplicant.NationalityId = applicant.NationalityId ?? 0;

                        var remainingPreApplicationApplicant = await _dbContext.PreApplicationApplicant
                            .Where(q => q.Id == updatedApplicant.Id).FirstOrDefaultAsync();

                        var remainingCurrentSlotId = remainingPreApplicationApplicant.SlotId;
                        if (remainingCurrentSlotId != null && remainingCurrentSlotId != applicant.SlotId)
                        {
                            var newCurrentSlot = await _dbContext.Slot.Where(p => p.Id == remainingCurrentSlotId).FirstOrDefaultAsync();
                            newCurrentSlot.Quota += 1;
                            remainingCurrentSlotStatus = true;
                            _dbContext.Slot.Update(newCurrentSlot);
                        }

                        updatedApplicant.SlotId = applicant.SlotId;

                        _dbContext.PreApplicationApplicant.Update(updatedApplicant);
                    }
                }

                var newApplicants = request.Applicants
                    .Where(p => !appointmentApplicants.Exists(m => m.Id == p.ApplicantId))
                    .Select(p => new PreApplicationApplicant
                    {
                        PreApplicationId = request.AppointmentId,
                        BirthDate = p.BirthDate.GetValueOrDefault(),
                        Email = request.Context.ChannelTypeId == (int)Enums.Enums.ChannelType.GwInternational ? p.Email ?? string.Empty : p.Email.IsNullOrWhitespace() ? request.Applicants.FirstOrDefault(i => i.Email != null)?.Email : p.Email,
                        Name = p.Name,
                        PassportExpireDate = p.PassportExpireDate.GetValueOrDefault(),
                        PassportNumber = p.PassportNumber,
                        PhoneNumber = request.Context.ChannelTypeId == (int)Enums.Enums.ChannelType.GwInternational ? p.PhoneNumber ?? string.Empty : p.PhoneNumber.IsNullOrWhitespace() ? request.Applicants.FirstOrDefault(i => i.PhoneNumber != null)?.PhoneNumber : p.PhoneNumber,
                        Surname = p.Surname,
                        RelationShipId = p.RelationShipId.GetValueOrDefault(),
                        SlotId = p.SlotId ?? 0,
                        GenderId = p.GenderId ?? 0,
                        NationalityId = p.NationalityId ?? 0,
                        IsActive = true,
                        IsDeleted = false
                    }).AsEnumerable();

                var applicationApplicants = newApplicants.ToList();

                if (applicationApplicants.Any())
                {
                    if (request.Context.ChannelTypeId == (int)Enums.Enums.ChannelType.GwInternational)
                    {
                        // rule 4 : mail && phone control

                        var phoneNumbers = applicationApplicants.Select(s => s.PhoneNumber).ToList();

                        var emails = applicationApplicants.Select(s => s.Email).ToList();

                        var appointmentApplicantsWithRepeatedInformation = await _dbContext.PreApplicationApplicant
                            .Include(i => i.PreApplication)
                            .ThenInclude(i => i.Slot)
                            .Where(r =>
                                r.IsActive && !r.IsDeleted && r.PreApplication.ExternalUserId != null && r.PreApplication.ChannelType == request.Context.ChannelTypeId &&
                                r.PreApplication.Slot.SlotTime >= DateTime.Now &&
                                r.PreApplication.Slot.SlotTime < DateTime.Now.AddMonths(1) && r.PreApplicationId != request.AppointmentId &&
                                (emails.Any(a => a == r.Email) || phoneNumbers.Any(a => a == r.PhoneNumber)))
                            .Select(r => new
                            {
                                r.PreApplicationId,
                                r.Email,
                                r.PhoneNumber,
                            })
                            .ToListAsync();

                        //mail && phoneNumber

                        foreach (var applicant in appointmentApplicantsWithRepeatedInformation.GroupBy(r => r.Email).Where(r => r.Count() >= 2))
                        {
                            if (emails.Exists(e => e == applicant.Key) && applicant.GroupBy(r => r.PreApplicationId).Count() >= 2)
                            {
                                return new UpdateAppointmentResult
                                {
                                    Status = UpdateAppointmentStatus.RuleRepeatedMailNotAllowed,
                                    Message = $"{ServiceResources.REPEATED_MAIL_NOT_ALLOWED} / {applicant.Key}"
                                };
                            }
                        }

                        foreach (var applicant in appointmentApplicantsWithRepeatedInformation.GroupBy(r => r.PhoneNumber).Where(r => r.Count() >= 3))
                        {
                            if (phoneNumbers.Exists(e => e == applicant.Key) && applicant.GroupBy(r => r.PreApplicationId).Count() >= 3)
                            {
                                return new UpdateAppointmentResult
                                {
                                    Status = UpdateAppointmentStatus.RuleRepeatedPhoneNumberNotAllowed,
                                    Message = $"{ServiceResources.REPEATED_PHONE_NUMBER_NOT_ALLOWED} / {applicant.Key}"
                                };
                            }
                        }
                    }

                    await _dbContext.PreApplicationApplicant.AddRangeAsync(applicationApplicants);
                }

                if (updateSlotQuotaList.Count > 0)
                {
                    foreach (var item in updateSlotQuotaList)
                    {
                        var updateSlot = await _dbContext.Slot.Where(p => p.Id == item.Key).FirstOrDefaultAsync();

                        _dbContext.Slot.Update(updateSlot);
                    }
                }
                else
                {
                    newSlot.Quota -= request.Applicants.Count;
                    _dbContext.Slot.Update(newSlot);
                }

                if (!remainingCurrentSlotStatus && newSlot.Id != currentSlotId)
                {
                    var currentSlot = await _dbContext.Slot.Where(p => p.Id == currentSlotId).FirstOrDefaultAsync();

                    currentSlot.Quota += activeApplicantCount;

                    _dbContext.Slot.Update(currentSlot);
                }

                await _dbContext.SaveChangesAsync();

                if (newSlot.Id != currentSlotId)
                {
                    var file = GetUpdateAppointmentLetter(new AppointmentLetterFileRequest()
                    {
                        AppointmentNumber = request.AppointmentId,
                        VasTypeId = appointment.VasTypeId,
                        AppointmentDate = appointment.Slot.SlotTime.Date,
                        SlotDateAndTime = appointment.Slot.SlotTime,
                        SlotTime = appointment.Slot.SlotTime.ToString("HH:mm"),
                        VisaCategoryId = appointment.VisaCategoryId,
                        Applicants = request.Applicants.Select(r => new AppointmentLetterFileRequest.ApplicantDto()
                        {
                            Email = r.Email,
                            PassportNumber = r.PassportNumber,
                            BirthDate = r.BirthDate,
                            Name = r.Name,
                            Surname = r.Surname
                        }).ToList(),
                        BranchInformation = new AppointmentLetterFileRequest.BranchDto()
                        {
                            Id = branchApplicationCountry.Branch.Id,
                            CountryId = branchApplicationCountry.Branch.CountryId,
                            Email = branchApplicationCountry.Branch.Email,
                            Address = branchApplicationCountry.Branch.Address,
                            AddressArabic = branchApplicationCountry.Branch.BranchDataTranslation
                                .FirstOrDefault(r => r.LanguageId == (int)Enums.Enums.Language.Arabic)?.Address,
                            CallCenterTelephone = branchApplicationCountry.Branch.Telephone,
                            Name = branchApplicationCountry
                                       .Branch.BranchTranslations
                                       .FirstOrDefault(branchTranslation =>
                                           branchTranslation.LanguageId == request.Context.LanguageId)?.Name
                                   ?? branchApplicationCountry
                                       .Branch.BranchTranslations
                                       .FirstOrDefault()
                                       ?.Name,
                            NameEn = branchApplicationCountry.Branch.BranchTranslations
                                .FirstOrDefault(r => r.LanguageId == (int)Enums.Enums.Language.English)?.Name
                        },
                    });

                    var htmlText = GetHtmlText(request.Context.LanguageId, request.Context.ChannelTypeId, branchApplicationCountry.Note, false,
                        new NotificationHtmlTextDto
                        {
                            SlotTime = appointment.Slot.SlotTime.ToString("HH:mm"),
                            BranchName = branchApplicationCountry.Branch.BranchTranslations
                                .FirstOrDefault(r => r.LanguageId == (int)Enums.Enums.Language.English)?.Name,
                            AppointmentDate = appointment.Slot.SlotTime.Date,
                            AppointmentNumber = request.AppointmentId.ToString()
                        });

                    await _notificationEventService.Raise(new UpdateAppointmentSendNotificationEvent
                    {
                        AppointmentLetter = file,
                        AppointmentNumber = request.AppointmentId,
                        AppointmentDate = appointment.Slot.SlotTime.Date,
                        LanguageId = request.Context.LanguageId,
                        SlotDateAndTime = appointment.Slot.SlotTime,
                        HtmlText = htmlText,
                        SlotTime = appointment.Slot.SlotTime.ToString("HH:mm"),
                        Applicants = request.Applicants.Select(r => new SendNotificationApplicantDto
                        {
                            Email = r.Email,
                        }).ToList(),
                        BranchInformation = new SendNotificationBranchDto()
                        {
                            Id = branchApplicationCountry.Branch.Id,
                            Name = branchApplicationCountry
                                       .Branch.BranchTranslations
                                       .FirstOrDefault(branchTranslation =>
                                           branchTranslation.LanguageId == request.Context.LanguageId)?.Name
                                   ?? branchApplicationCountry
                                       .Branch.BranchTranslations
                                       .FirstOrDefault()
                                       ?.Name,
                            NameEn = branchApplicationCountry.Branch.BranchTranslations.FirstOrDefault(r => r.LanguageId == (int)Enums.Enums.Language.English)?.Name
                        }
                    });

                }

                return new UpdateAppointmentResult
                {
                    Status = UpdateAppointmentStatus.Successful,
                    Message = ServiceResources.RESOURCE_UPDATED,
                    Id = appointment.Id
                };
            }
            catch (Exception exc)
            {
                Logger.Error($@"Exception:{exc.Message}:{DateTime.Now}");

                return new UpdateAppointmentResult
                {
                    Status = UpdateAppointmentStatus.InternalServerError,
                    Message = ServiceResources.INTERNAL_SERVER_ERROR
                };
            }
        }

        public async Task<GetPaginatedAppointmentsResult> GetPaginatedAppointments(GetPaginatedAppointmentsRequest request)
        {
            var validationResult = _validationService.Validate(typeof(GetPaginatedAppointmentsValidator), request);

            if (!validationResult.IsValid)
                return new GetPaginatedAppointmentsResult
                {
                    Status = GetPaginatedAppointmentsStatus.InvalidInput,
                    Message = ServiceResources.INVALID_INPUT_ERROR,
                    ValidationMessages = validationResult.ErrorMessages
                };

            try
            {
                var countries = await _dbContext.Country.ToListAsync();

                var queryAppointments = _dbContext.PreApplication
                    .Include(i => i.PreApplicationApplicants)
                    .Include(i => i.BranchApplicationCountry)
                    .ThenInclude(i => i.Branch)
                    .ThenInclude(i => i.BranchTranslations)
                    .Include(i => i.Slot)
                    .Where(p => p.IsActive && !p.IsDeleted && p.Id > 0 && p.Slot.SlotTime >= DateTime.Today.Date
                                && p.ExternalUserId == request.Context.Identity.ExternalUserId && p.PreApplicationApplicants.Any(w => w.IsActive && !w.IsDeleted) && p.ChannelType == request.Context.ChannelTypeId).AsQueryable();

                if (request.Filters != null)
                {
                    if (!request.Filters.ReferenceNumber.IsNullOrWhitespace())
                        queryAppointments = queryAppointments.Where(r =>
                            r.Id.ToString().PadLeft(13, '0').Contains(request.Filters.ReferenceNumber));

                    if (!request.Filters.Email.IsNullOrWhitespace())
                        queryAppointments = queryAppointments.Where(r =>
                            r.PreApplicationApplicants.Any(s => s.Email.Contains(request.Filters.Email)));

                    if (!request.Filters.PassportNumber.IsNullOrWhitespace())
                        queryAppointments = queryAppointments.Where(r =>
                            r.PreApplicationApplicants.Any(s => s.PassportNumber.Contains(request.Filters.PassportNumber)));

                    if (!request.Filters.PhoneNumber.IsNullOrWhitespace())
                        queryAppointments = queryAppointments.Where(r =>
                            r.PreApplicationApplicants.Any(s => s.PhoneNumber.Contains(request.Filters.PhoneNumber)));
                }

                var appointmentList = await queryAppointments.ToListAsync();

                if (!appointmentList.Any())
                    return new GetPaginatedAppointmentsResult
                    {
                        Status = GetPaginatedAppointmentsStatus.ResourceNotFound,
                        Message = ServiceResources.APPOINTMENT_NOT_FOUND
                    };

                var appointments = new GetPaginatedAppointmentsResult
                {
                    Appointments = appointmentList.Select(p => new AppointmentDto
                    {
                        Id = p.Id,
                        ReferenceNumber = p.Id.ToApplicationNumber(),
                        ApplicantType = GetLookupValue((int)LookupType.ApplicantType, p.ApplicantTypeId),
                        SlotType = GetLookupValue((int)LookupType.SlotType, p.SlotTypeId),
                        VasType = GetLookupValue((int)LookupType.VasType, p.VasTypeId),
                        SlotId = p.SlotId,
                        Slot = p.Slot.SlotTime,
                        SlotText = p.Slot.SlotTime.ToString("dd/MM/yyyy HH:mm"),
                        CreatedAt = p.CreatedAt,
                        BranchId = p.BranchApplicationCountry.Branch.Id,
                        BranchName = p.BranchApplicationCountry
                                         .Branch.BranchTranslations
                                         .FirstOrDefault(branchTranslation =>
                                             branchTranslation.LanguageId == request.Context.LanguageId)?.Name
                                     ?? p.BranchApplicationCountry
                                         .Branch.BranchTranslations
                                         .FirstOrDefault()
                                         ?.Name,
                        VisitingCountryId = p.BranchApplicationCountry.CountryId,
                        ResidingCountryId = p.BranchApplicationCountry.Branch.CountryId,
                        IsBranchDeactivated = request.Context.ChannelTypeId == (int)ChannelType.GwInternational ? !p.BranchApplicationCountry.Branch.ShowInB2c : false,
                        Applicants = p.PreApplicationApplicants.Where(w => w.IsActive && !w.IsDeleted).Select(r => new ApplicantDto
                        {
                            ApplicantId = r.Id,
                            ApplicantReferenceNumber = r.Id.ToApplicationNumber(),
                            PassportNumber = r.PassportNumber,
                            NameSurname = $"{r.Name} {r.Surname}",
                            Name = r.Name,
                            Surname = r.Surname,
                            PhoneNumber = r.PhoneNumber,
                            Email = r.Email,
                            NationalityId = r.NationalityId,
                            Nationality = r.NationalityId.IsNumericAndGreaterThenZero() ?
                                request.Context.LanguageId == (int)Enums.Enums.Language.Turkish ?
                                    countries.First(k => k.Id == r.NationalityId).NameTr :
                                    countries.First(k => k.Id == r.NationalityId).Name :
                                string.Empty,
                            Gender = GetLookupValue((int)LookupType.Gender, r.GenderId),
                            PassportExpireDate = r.PassportExpireDate,
                            BirthDate = r.BirthDate,
                            RelationShip = r.RelationShipId.HasValue ? GetLookupValue((int)LookupType.RelationShip, r.RelationShipId) : null,
                        }).ToList()
                    }).ToList()
                };

                var paginationResult = PagedResultsFactory.CreatePagedResult(
                    appointments.Appointments.AsQueryable(), request.Pagination.PageNumber, request.Pagination.PageSize,
                    request.Pagination.OrderBy, request.Pagination.Ascending);

                return paginationResult == null
                    ? new GetPaginatedAppointmentsResult
                    {
                        Appointments = null,
                        Status = GetPaginatedAppointmentsStatus.ResourceNotFound,
                        Message = ServiceResources.INVALID_INPUT_ERROR
                    }
                    : new GetPaginatedAppointmentsResult
                    {
                        Appointments = paginationResult.Results,
                        TotalNumberOfRecords = paginationResult.TotalNumberOfRecords,
                        TotalNumberOfPages = paginationResult.TotalNumberOfPages,
                        Status = GetPaginatedAppointmentsStatus.Successful,
                        Message = ServiceResources.RESOURCE_RETRIEVED
                    };
            }
            catch (Exception exc)
            {
                Logger.Error($@"Exception:{exc.Message}:{DateTime.Now}");

                return new GetPaginatedAppointmentsResult
                {
                    Status = GetPaginatedAppointmentsStatus.InternalServerError,
                    Message = ServiceResources.INTERNAL_SERVER_ERROR
                };
            }
        }

        public async Task<GetPaginatedAppointmentCollectionsResult> GetPaginatedAppointmentCollections(GetPaginatedAppointmentCollectionsRequest request)
        {
            var validationResult = _validationService.Validate(typeof(GetPaginatedAppointmentCollectionsValidator), request);

            if (!validationResult.IsValid)
                return new GetPaginatedAppointmentCollectionsResult
                {
                    Status = GetPaginatedAppointmentCollectionsStatus.InvalidInput,
                    Message = ServiceResources.INVALID_INPUT_ERROR,
                    ValidationMessages = validationResult.ErrorMessages
                };

            try
            {
                var countries = await _dbContext.Country.ToListAsync();

                var queryAppointments = _dbContext.PreApplication
                    .Include(i => i.PreApplicationApplicants)
                    .Include(i => i.BranchApplicationCountry)
                    .ThenInclude(i => i.Branch)
                    .ThenInclude(i => i.BranchTranslations)
                    .Include(i => i.Slot)
                    .Where(p => p.IsActive && !p.IsDeleted && p.ExternalUserId == request.Context.Identity.ExternalUserId && p.Slot.SlotTime > DateTime.Now.AddYears(-1) && p.ChannelType == request.Context.ChannelTypeId
                     && p.PreApplicationApplicants.Any(s => s.IsActive && !s.IsDeleted)).AsQueryable();

                if (request.Filters != null)
                {
                    if (!request.Filters.ReferenceNumber.IsNullOrWhitespace())
                        queryAppointments = queryAppointments.Where(r =>
                            r.Id.ToString().PadLeft(13, '0').Contains(request.Filters.ReferenceNumber));

                    if (!request.Filters.Email.IsNullOrWhitespace())
                        queryAppointments = queryAppointments.Where(r =>
                            r.PreApplicationApplicants.Any(s => s.Email.Contains(request.Filters.Email)));

                    if (!request.Filters.PassportNumber.IsNullOrWhitespace())
                        queryAppointments = queryAppointments.Where(r =>
                            r.PreApplicationApplicants.Any(s => s.PassportNumber.Contains(request.Filters.PassportNumber)));

                    if (!request.Filters.PhoneNumber.IsNullOrWhitespace())
                        queryAppointments = queryAppointments.Where(r =>
                            r.PreApplicationApplicants.Any(s => s.PhoneNumber.Contains(request.Filters.PhoneNumber)));
                }

                var appointmentList = await queryAppointments.ToListAsync();

                if (!appointmentList.Any())
                    return new GetPaginatedAppointmentCollectionsResult
                    {
                        Status = GetPaginatedAppointmentCollectionsStatus.ResourceNotFound,
                        Message = ServiceResources.APPOINTMENT_NOT_FOUND
                    };

                var appointments = new GetPaginatedAppointmentCollectionsResult
                {
                    Appointments = appointmentList.Select(p => new AppointmentDto
                    {
                        Id = p.Id,
                        ReferenceNumber = p.Id.ToApplicationNumber(),
                        ApplicantType = GetLookupValue((int)LookupType.ApplicantType, p.ApplicantTypeId),
                        SlotType = GetLookupValue((int)LookupType.SlotType, p.SlotTypeId),
                        VasType = GetLookupValue((int)LookupType.VasType, p.VasTypeId),
                        SlotId = p.SlotId,
                        Slot = p.Slot.SlotTime,
                        SlotText = p.Slot.SlotTime.ToString("dd/MM/yyyy HH:mm"),
                        CreatedAt = p.CreatedAt,
                        BranchId = p.BranchApplicationCountry.Branch.Id,
                        BranchName = p.BranchApplicationCountry
                                         .Branch.BranchTranslations
                                         .FirstOrDefault(branchTranslation =>
                                             branchTranslation.LanguageId == request.Context.LanguageId)?.Name
                                     ?? p.BranchApplicationCountry
                                         .Branch.BranchTranslations
                                         .FirstOrDefault()
                                         ?.Name,
                        VisitingCountryId = p.BranchApplicationCountry.CountryId,
                        ResidingCountryId = p.BranchApplicationCountry.Branch.CountryId,
                        IsBranchDeactivated = request.Context.ChannelTypeId == (int)ChannelType.GwInternational ? !p.BranchApplicationCountry.Branch.ShowInB2c : false,
                        Applicants = p.PreApplicationApplicants.Where(w => w.IsActive && !w.IsDeleted).Select(r => new ApplicantDto
                        {
                            ApplicantId = r.Id,
                            ApplicantReferenceNumber = r.Id.ToApplicationNumber(),
                            PassportNumber = r.PassportNumber,
                            NameSurname = $"{r.Name} {r.Surname}",
                            Name = r.Name,
                            Surname = r.Surname,
                            PhoneNumber = r.PhoneNumber,
                            Email = r.Email,
                            NationalityId = r.NationalityId,
                            Nationality = r.NationalityId.IsNumericAndGreaterThenZero() ?
                                request.Context.LanguageId == (int)Enums.Enums.Language.Turkish ?
                                    countries.First(k => k.Id == r.NationalityId).NameTr :
                                    countries.First(k => k.Id == r.NationalityId).Name :
                                string.Empty,
                            Gender = GetLookupValue((int)LookupType.Gender, r.GenderId),
                            PassportExpireDate = r.PassportExpireDate,
                            BirthDate = r.BirthDate,
                            RelationShip = r.RelationShipId.HasValue ? GetLookupValue((int)LookupType.RelationShip, r.RelationShipId) : null,
                        }).ToList()
                    }).ToList()
                };

                var paginationResult = PagedResultsFactory.CreatePagedResult(
                    appointments.Appointments.AsQueryable(), request.Pagination.PageNumber, request.Pagination.PageSize,
                    request.Pagination.OrderBy, request.Pagination.Ascending);

                return paginationResult == null
                    ? new GetPaginatedAppointmentCollectionsResult
                    {
                        Appointments = null,
                        Status = GetPaginatedAppointmentCollectionsStatus.ResourceNotFound,
                        Message = ServiceResources.INVALID_INPUT_ERROR
                    }
                    : new GetPaginatedAppointmentCollectionsResult
                    {
                        Appointments = paginationResult.Results,
                        TotalNumberOfRecords = paginationResult.TotalNumberOfRecords,
                        TotalNumberOfPages = paginationResult.TotalNumberOfPages,
                        Status = GetPaginatedAppointmentCollectionsStatus.Successful,
                        Message = ServiceResources.RESOURCE_RETRIEVED
                    };
            }
            catch (Exception exc)
            {
                Logger.Error($@"Exception:{exc.Message}:{DateTime.Now}");

                return new GetPaginatedAppointmentCollectionsResult
                {
                    Status = GetPaginatedAppointmentCollectionsStatus.InternalServerError,
                    Message = ServiceResources.INTERNAL_SERVER_ERROR
                };
            }
        }

        public async Task<GetAppointmentsConvertedToApplicationResult> GetAppointmentsConvertedToApplication(GetAppointmentsConvertedToApplicationRequest request)
        {
            try
            {
                var queryAppointments = await _dbContext.PreApplication
                    .Include(r => r.Slot)
                    .Include(r => r.PreApplicationApplicants)
                    .ThenInclude(r => r.Applications)
                    .Where(p => p.IsActive && !p.IsDeleted && p.ExternalUserId == request.Context.Identity.ExternalUserId && p.ChannelType == request.Context.ChannelTypeId)
                    .ToListAsync();

                if (!queryAppointments.Any())
                    return new GetAppointmentsConvertedToApplicationResult
                    {
                        Status = GetAppointmentsConvertedToApplicationStatus.ResourceNotFound,
                        Message = ServiceResources.APPOINTMENT_HAS_CONVERTED_APPLICATION_NOT_FOUND
                    };

                queryAppointments = queryAppointments.Where(s =>
                    s.PreApplicationApplicants.Any(p =>
                       p.Applications.Any(a => a.IsActive && !a.IsDeleted && a.StatusId != (int)Enums.Enums.ApplicationStatus.Cancelled))).ToList();

                if (!queryAppointments.Any())
                    return new GetAppointmentsConvertedToApplicationResult
                    {
                        Status = GetAppointmentsConvertedToApplicationStatus.ResourceNotFound,
                        Message = ServiceResources.APPLICATION_NOT_FOUND
                    };

                var result = new GetAppointmentsConvertedToApplicationResult
                {
                    Appointments = queryAppointments.Select(r => new GetAppointmentsConvertedToApplicationResult.AppointmentConvertedToApplicationDto
                    {
                        Id = r.Id,
                        SlotTime = r.Slot.SlotTime,
                        SlotTimeText = r.Slot.SlotTime.ToString("dd/MM/yyyy HH:mm"),
                        ApplicantType = GetLookupValue((int)LookupType.ApplicantType, r.ApplicantTypeId),
                        Applicants = r.PreApplicationApplicants.Where(s => s.IsActive && !s.IsDeleted).Select(p => new ApplicantDto()
                        {
                            ApplicantId = p.Id,
                            NameSurname = $"{p.Name} {p.Surname}",
                            PassportNumber = p.PassportNumber
                        }).ToList()
                    })
                };

                return new GetAppointmentsConvertedToApplicationResult()
                {
                    Appointments = result.Appointments,
                    Status = GetAppointmentsConvertedToApplicationStatus.Successful,
                    Message = ServiceResources.RESOURCE_RETRIEVED
                };
            }
            catch (Exception exc)
            {
                Logger.Error($@"Exception:{exc.Message}:{DateTime.Now}");

                return new GetAppointmentsConvertedToApplicationResult
                {
                    Status = GetAppointmentsConvertedToApplicationStatus.InternalServerError,
                    Message = ServiceResources.INTERNAL_SERVER_ERROR
                };
            }
        }

        public async Task<GetAppointmentUpdateHistoryResult> GetAppointmentUpdateHistory(GetAppointmentUpdateHistoryRequest request)
        {
            var validationResult = _validationService.Validate(typeof(GetAppointmentUpdateHistoryValidator), request);

            if (!validationResult.IsValid)
                return new GetAppointmentUpdateHistoryResult
                {
                    Status = GetAppointmentUpdateHistoryStatus.InvalidInput,
                    Message = ServiceResources.INVALID_INPUT_ERROR,
                    ValidationMessages = validationResult.ErrorMessages
                };

            try
            {

                var appointment = await _dbContext.PreApplication
                    .Include(i => i.PreApplicationHistories)
                    .Include(i => i.PreApplicationApplicants)
                    .ThenInclude(i => i.PreApplicationApplicantHistories)
                    .Where(r => r.Id == request.ResourceId && r.ExternalUserId == request.Context.Identity.ExternalUserId).FirstOrDefaultAsync();

                if (appointment == null)
                    return new GetAppointmentUpdateHistoryResult
                    {
                        Status = GetAppointmentUpdateHistoryStatus.ResourceNotFound,
                        Message = ServiceResources.RESOURCE_NOT_FOUND
                    };

                return new GetAppointmentUpdateHistoryResult
                {
                    Id = appointment.Id,
                    UpdateHistories = appointment.PreApplicationHistories.Select(r => new HistoryDto()
                    {
                        CurrentValue = r.CurrentValue,
                        PreviousValue = r.PreviousValue,
                        PropertyName = r.PropertyName,
                        CreatedAt = r.CreatedAt
                    }).ToList(),
                    Applicants = appointment.PreApplicationApplicants.Select(s => new AppointmentApplicantHistoryDto
                    {
                        Id = s.Id,
                        UpdateHistories = s.PreApplicationApplicantHistories.Select(k => new HistoryDto()
                        {
                            CurrentValue = k.CurrentValue,
                            PreviousValue = k.PreviousValue,
                            PropertyName = k.PropertyName,
                            CreatedAt = k.CreatedAt
                        }).ToList()
                    }).ToList(),
                    Status = GetAppointmentUpdateHistoryStatus.Successful,
                    Message = ServiceResources.SUCCESS,
                };
            }
            catch (Exception exc)
            {
                Logger.Error($@"Exception:{exc.Message}:{DateTime.Now}");

                return new GetAppointmentUpdateHistoryResult
                {
                    Status = GetAppointmentUpdateHistoryStatus.InternalServerError,
                    Message = ServiceResources.INTERNAL_SERVER_ERROR
                };
            }
        }

        public async Task<GetAppointmentLetterResult> GetAppointmentLetter(GetAppointmentLetterRequest request)
        {
            var validationResult = _validationService.Validate(typeof(GetAppointmentLetterValidator), request);

            if (!validationResult.IsValid)
                return new GetAppointmentLetterResult
                {
                    Status = GetAppointmentLetterStatus.InvalidInput,
                    Message = ServiceResources.INVALID_INPUT_ERROR,
                    ValidationMessages = validationResult.ErrorMessages
                };

            try
            {
                var appointment = await _dbContext.PreApplication
            .Include(r => r.Slot)
            .Include(r => r.BranchApplicationCountry)
            .ThenInclude(r => r.Branch)
            .ThenInclude(r => r.BranchTranslations)
            .Include(r => r.BranchApplicationCountry)
            .ThenInclude(r => r.Branch)
            .ThenInclude(r => r.BranchDataTranslation)
            .Include(r => r.PreApplicationApplicants)
            .Where(r => r.IsActive && !r.IsDeleted && r.Id == request.ResourceId && r.ExternalUserId == request.Context.Identity.ExternalUserId).FirstOrDefaultAsync();

                if (appointment == null)
                    return new GetAppointmentLetterResult
                    {
                        Status = GetAppointmentLetterStatus.ResourceNotFound,
                        Message = ServiceResources.RESOURCE_NOT_FOUND
                    };

                var file = GetCreateAppointmentLetter(new AppointmentLetterFileRequest()
                {
                    AppointmentNumber = appointment.Id,
                    VasTypeId = appointment.VasTypeId,
                    AppointmentDate = appointment.Slot.SlotTime.Date,
                    SlotTime = appointment.Slot.SlotTime.ToString("HH:mm"),
                    VisaCategoryId = appointment.VisaCategoryId,
                    Applicants = appointment.PreApplicationApplicants.Where(w => w.IsActive && !w.IsDeleted).Select(r => new AppointmentLetterFileRequest.ApplicantDto()
                    {
                        Email = r.Email,
                        PassportNumber = r.PassportNumber,
                        BirthDate = r.BirthDate,
                        Name = r.Name,
                        Surname = r.Surname
                    }).ToList(),
                    BranchInformation = new AppointmentLetterFileRequest.BranchDto()
                    {
                        Id = appointment.BranchApplicationCountry.Branch.Id,
                        CountryId = appointment.BranchApplicationCountry.Branch.CountryId,
                        Email = appointment.BranchApplicationCountry.Branch.Email,
                        Address = appointment.BranchApplicationCountry.Branch.Address,
                        AddressArabic = appointment.BranchApplicationCountry.Branch.BranchDataTranslation
                            .FirstOrDefault(r => r.LanguageId == (int)Enums.Enums.Language.Arabic)?.Address,
                        CallCenterTelephone = appointment.BranchApplicationCountry.Branch.Telephone,
                        Name = appointment.BranchApplicationCountry
                                   .Branch.BranchTranslations
                                   .FirstOrDefault(branchTranslation =>
                                       branchTranslation.LanguageId == request.Context.LanguageId)?.Name
                               ?? appointment.BranchApplicationCountry
                                   .Branch.BranchTranslations
                                   .FirstOrDefault()
                                   ?.Name,
                        NameEn = appointment.BranchApplicationCountry.Branch.BranchTranslations
                            .FirstOrDefault(r => r.LanguageId == (int)Enums.Enums.Language.English)?.Name
                    }
                });

                if (file == null)
                    return new GetAppointmentLetterResult
                    {
                        Status = GetAppointmentLetterStatus.InternalServerError,
                        Message = ServiceResources.INTERNAL_SERVER_ERROR
                    };

                return new GetAppointmentLetterResult
                {
                    File = Convert.FromBase64String(file.Content),
                    Status = GetAppointmentLetterStatus.Successful,
                    Message = ServiceResources.SUCCESS
                };
            }
            catch (Exception exc)
            {
                Logger.Error($@"Exception:{exc.Message}:{DateTime.Now}");

                return new GetAppointmentLetterResult
                {
                    Status = GetAppointmentLetterStatus.InternalServerError,
                    Message = ServiceResources.INTERNAL_SERVER_ERROR
                };
            }
        }

        public async Task<GetAppointmentResult> GetAppointment(GetAppointmentRequest request)
        {
            var validationResult = _validationService.Validate(typeof(GetAppointmentValidator), request);

            if (!validationResult.IsValid)
                return new GetAppointmentResult
                {
                    Status = GetAppointmentStatus.InvalidInput,
                    Message = ServiceResources.INVALID_INPUT_ERROR,
                    ValidationMessages = validationResult.ErrorMessages
                };

            try
            {
                var countries = await _dbContext.Country.ToListAsync();

                var appointment = await _dbContext.PreApplication
                    .Include(i => i.PreApplicationApplicants)
                    .Include(i => i.BranchApplicationCountry)
                    .ThenInclude(i => i.Branch)
                    .ThenInclude(i => i.BranchTranslations)
                    .Include(i => i.Slot)
                    .Where(p => p.IsActive && !p.IsDeleted && p.Id == request.ResourceId
                                && p.ExternalUserId == request.Context.Identity.ExternalUserId).FirstOrDefaultAsync();

                if (appointment == null)
                    return new GetAppointmentResult
                    {
                        Status = GetAppointmentStatus.ResourceNotFound,
                        Message = ServiceResources.RESOURCE_NOT_FOUND
                    };

                return new GetAppointmentResult
                {
                    Status = GetAppointmentStatus.Successful,
                    Message = ServiceResources.SUCCESS,
                    Appointment = new AppointmentDto
                    {
                        Id = appointment.Id,
                        ReferenceNumber = appointment.Id.ToApplicationNumber(),
                        ApplicantType = GetLookupValue((int)LookupType.ApplicantType, appointment.ApplicantTypeId),
                        SlotType = GetLookupValue((int)LookupType.SlotType, appointment.SlotTypeId),
                        VasType = GetLookupValue((int)LookupType.VasType, appointment.VasTypeId),
                        SlotId = appointment.SlotId,
                        Slot = appointment.Slot.SlotTime,
                        SlotText = appointment.Slot.SlotTime.ToString("dd/MM/yyyy HH:mm"),
                        CreatedAt = appointment.CreatedAt,
                        BranchId = appointment.BranchApplicationCountry.Branch.Id,
                        BranchName = appointment.BranchApplicationCountry
                                         .Branch.BranchTranslations
                                         .FirstOrDefault(branchTranslation =>
                                             branchTranslation.LanguageId == request.Context.LanguageId)?.Name
                                     ?? appointment.BranchApplicationCountry
                                         .Branch.BranchTranslations
                                         .FirstOrDefault()
                                         ?.Name,
                        VisitingCountryId = appointment.BranchApplicationCountry.CountryId,
                        ResidingCountryId = appointment.BranchApplicationCountry.Branch.CountryId,
                        Applicants = appointment.PreApplicationApplicants.Where(w => w.IsActive && !w.IsDeleted).Select(r => new ApplicantDto
                        {
                            ApplicantId = r.Id,
                            ApplicantReferenceNumber = r.Id.ToApplicationNumber(),
                            PassportNumber = r.PassportNumber,
                            NameSurname = $"{r.Name} {r.Surname}",
                            Name = r.Name,
                            Surname = r.Surname,
                            PhoneNumber = r.PhoneNumber,
                            Email = r.Email,
                            NationalityId = r.NationalityId,
                            Nationality = r.NationalityId.IsNumericAndGreaterThenZero()
                                ? request.Context.LanguageId == (int)Enums.Enums.Language.Turkish
                                    ? countries.First(k => k.Id == r.NationalityId).NameTr
                                    : countries.First(k => k.Id == r.NationalityId).Name
                                : string.Empty,
                            Gender = GetLookupValue((int)LookupType.Gender, r.GenderId),
                            PassportExpireDate = r.PassportExpireDate,
                            BirthDate = r.BirthDate,
                            RelationShip = r.RelationShipId.HasValue ? GetLookupValue((int)LookupType.RelationShip, r.RelationShipId) : null,
                        }).ToList()
                    }
                };

            }
            catch (Exception exc)
            {
                Logger.Error($@"Exception:{exc.Message}:{DateTime.Now}");

                return new GetAppointmentResult
                {
                    Status = GetAppointmentStatus.InternalServerError,
                    Message = ServiceResources.INTERNAL_SERVER_ERROR
                };
            }
        }

        public async Task<ValidateApplicantInformationResult> ValidateApplicantInformation(
            ValidateApplicantInformationRequest request)
        {
            var validationResult = _validationService.Validate(typeof(ValidateApplicantInformationValidator), request);

            if (!validationResult.IsValid)
                return new ValidateApplicantInformationResult
                {
                    Status = ValidateApplicantInformationStatus.InvalidInput,
                    Message = ServiceResources.INVALID_INPUT_ERROR,
                    ValidationMessages = validationResult.ErrorMessages
                };

            try
            {

                var branchApplicationCountry = await _dbContext.BranchApplicationCountry.Include(i => i.Branch)
                    .Where(q => q.IsActive && !q.IsDeleted && q.BranchId == request.BranchId)
                    .FirstOrDefaultAsync();

                if (branchApplicationCountry == null)
                    return new ValidateApplicantInformationResult
                    {
                        Status = ValidateApplicantInformationStatus.BranchNotFound,
                        Message = ServiceResources.BRANCH_NOT_FOUND,
                    };

                var resultMessages = new List<string>();
                var resultAppointmentId = new List<int>();
                var resultApplicantId = new List<int>();
                PreApplicationApplicant hasRepeatedAppointment = null;

                for (var i = 0; i < request.Applicants.Count; i++) //passport Control
                {
                    var hasAppointmentSamePassport = await _dbContext.PreApplicationApplicant
                        .Include(p => p.PreApplication)
                        .ThenInclude(a => a.BranchApplicationCountry)
                        .Include(p => p.PreApplication)
                        .ThenInclude(p => p.Slot)
                        .If(request.Context.ChannelTypeId == (int)Enums.Enums.ChannelType.GwInternational, s => s.Where(w => w.NationalityId == request.Applicants[i].NationalityId))
                        .Where(p => p.IsActive && !p.IsDeleted && p.PreApplication.ExternalUserId != null && p.PreApplication.ChannelType == request.Context.ChannelTypeId &&
                                    p.PassportNumber == request.Applicants[i].PassportNumber &&
                                    p.PreApplication.Slot.SlotTime >= DateTime.Today &&
                                    p.PreApplication.BranchApplicationCountry.CountryId ==
                                    branchApplicationCountry.CountryId).FirstOrDefaultAsync();

                    if (hasAppointmentSamePassport == null) continue;

                    hasRepeatedAppointment = hasAppointmentSamePassport;
                    resultMessages.Add(
                        $"{hasAppointmentSamePassport.PassportNumber} - {hasAppointmentSamePassport.Name} {hasAppointmentSamePassport.Surname} / {ServiceResources.AppointmentDateTitle} : {hasAppointmentSamePassport.PreApplication.Slot.SlotTime.ToString("dd/MM/yyyy HH:mm")}");

                    resultAppointmentId.Add(hasAppointmentSamePassport.PreApplicationId);
                    resultApplicantId.Add(hasAppointmentSamePassport.Id);

                    break;
                }

                if (hasRepeatedAppointment != null)
                {
                    if (hasRepeatedAppointment.PreApplication.ExternalUserId == request.Context.Identity.ExternalUserId)
                    {
                        return new ValidateApplicantInformationResult
                        {
                            Status = ValidateApplicantInformationStatus.RepeatedAppointmentFound,
                            Message = $"{ServiceResources.REPEATED_APPOINTMENT_FOUND}: {resultMessages.First()}",
                            AppointmentId = resultAppointmentId.First(),
                            ApplicantId = resultApplicantId.First()
                        };
                    }

                    return new ValidateApplicantInformationResult
                    {
                        Status = ValidateApplicantInformationStatus.SamePassportNotAllowed,
                        Message = $"{ServiceResources.APPOINTMENT_FOUND_WITH_SAME_PASSPORT}"
                    };
                }

                var phoneNumbers = request.Applicants.Select(r => r.PhoneNumber).ToList();

                var emails = request.Applicants.Select(r => r.Email).ToList();

                var appointmentApplicants = await _dbContext.PreApplicationApplicant
                    .Include(i => i.PreApplication)
                    .ThenInclude(i => i.Slot)
                    .If(request.AppointmentId.HasValue, filter => filter.Where(r => r.PreApplicationId != request.AppointmentId))
                    .Where(r =>
                        r.IsActive && !r.IsDeleted && r.PreApplication.ExternalUserId != null && r.PreApplication.ChannelType == request.Context.ChannelTypeId &&
                        r.PreApplication.Slot.SlotTime >= DateTime.Now &&
                        r.PreApplication.Slot.SlotTime < DateTime.Now.AddMonths(1) &&
                        (emails.Any(a => a == r.Email) || phoneNumbers.Any(a => a == r.PhoneNumber)))
                    .Select(r => new
                    {
                        r.PreApplicationId,
                        r.Email,
                        r.PhoneNumber,
                    })
                    .ToListAsync();

                //mail && phoneNumber

                foreach (var applicant in appointmentApplicants.GroupBy(r => r.Email).Where(r => r.Count() >= 2))
                {
                    if (emails.Exists(e => e == applicant.Key) && applicant.GroupBy(r => r.PreApplicationId).Count() >= 2)
                    {
                        return new ValidateApplicantInformationResult
                        {
                            Status = ValidateApplicantInformationStatus.RuleRepeatedMailNotAllowed,
                            Message = $"{ServiceResources.REPEATED_MAIL_NOT_ALLOWED} / {applicant.Key}"
                        };
                    }
                }

                foreach (var applicant in appointmentApplicants.GroupBy(r => r.PhoneNumber).Where(r => r.Count() >= 3))
                {
                    if (phoneNumbers.Exists(e => e == applicant.Key) && applicant.GroupBy(r => r.PreApplicationId).Count() >= 3)
                    {
                        return new ValidateApplicantInformationResult
                        {
                            Status = ValidateApplicantInformationStatus.RuleRepeatedPhoneNumberNotAllowed,
                            Message = $"{ServiceResources.REPEATED_PHONE_NUMBER_NOT_ALLOWED} / {applicant.Key}"
                        };
                    }
                }

                return new ValidateApplicantInformationResult
                {
                    Status = ValidateApplicantInformationStatus.Successful,
                    Message = ServiceResources.SUCCESS,
                };
            }
            catch (Exception exc)
            {
                Logger.Error($@"Exception:{exc.Message}:{DateTime.Now}");

                return new ValidateApplicantInformationResult
                {
                    Status = ValidateApplicantInformationStatus.InternalServerError,
                    Message = ServiceResources.INTERNAL_SERVER_ERROR
                };
            }
        }


        public async Task<UpdateAppointmentSlotTodayResult> UpdateAppointmentSlotToday(UpdateAppointmentSlotTodayRequest request)
        {
            var validationResult = _validationService.Validate(typeof(UpdateAppointmentSlotTodayValidator), request);

            if (!validationResult.IsValid)
                return new UpdateAppointmentSlotTodayResult
                {
                    Status = UpdateAppointmentSlotTodayStatus.InvalidInput,
                    Message = ServiceResources.INVALID_INPUT_ERROR,
                    ValidationMessages = validationResult.ErrorMessages
                };

            try
            {
                var applicant = _dbContext.PreApplicationApplicant
              .Include(i => i.PreApplication)
              .ThenInclude(i => i.Slot)
              .Where(s => s.Id == request.ApplicantId && !s.IsDeleted && s.IsActive)
              .FirstOrDefault();

                if (applicant == null)
                {
                    return new UpdateAppointmentSlotTodayResult
                    {
                        Message = ServiceResources.APPLICANT_NOT_FOUND,
                        Status = UpdateAppointmentSlotTodayStatus.NotFound,
                    };
                }

                var slotData = applicant.PreApplication.Slot;

                if (slotData == null)
                {
                    return new UpdateAppointmentSlotTodayResult
                    {
                        Message = ServiceResources.SLOT_NOT_FOUND,
                        Status = UpdateAppointmentSlotTodayStatus.NotFound,
                    };
                }

                if(slotData.Id == 0)
                {
                    return new UpdateAppointmentSlotTodayResult
                    {
                        Message = ServiceResources.CANNOT_UPDATE_WALKIN,
                        Status = UpdateAppointmentSlotTodayStatus.PreConditionFailed,
                    };
                }

                DateTime currentDate = DateTime.Now;
                DateTime slotTime = new DateTime(currentDate.Year, currentDate.Month, currentDate.Day, request.Hour, request.Minute, 0);

                DateTimeOffset slotTimeOffset = new DateTimeOffset(slotTime, TimeZoneInfo.Local.GetUtcOffset(slotTime));

                slotData.SlotTime = slotTimeOffset;

                _dbContext.Entry(slotData).Property(e => e.SlotTime).IsModified = true;
                await _dbContext.SaveChangesAsync();

                return new UpdateAppointmentSlotTodayResult
                {
                    Message = ServiceResources.RESOURCE_UPDATED,
                    Status = UpdateAppointmentSlotTodayStatus.Successful,
                    Id = slotData.Id
                };
            }
            catch (Exception exc)
            {
                Logger.Error($@"Exception:{exc.Message}:{DateTime.Now}");

                return new UpdateAppointmentSlotTodayResult
                {
                    Status = UpdateAppointmentSlotTodayStatus.InternalServerError,
                    Message = ServiceResources.INTERNAL_SERVER_ERROR
                };
            }
        }

        public async Task<ValidateAppointmentResult> ValidateAppointment(ValidateAppointmentRequest request)
        {
            var validationResult = _validationService.Validate(typeof(ValidateAppointmentValidator), request);

            if (!validationResult.IsValid)
                return new ValidateAppointmentResult
                {
                    Status = ValidateAppointmentStatus.InvalidInput,
                    Message = ServiceResources.INVALID_INPUT_ERROR,
                    ValidationMessages = validationResult.ErrorMessages
                };

            var branchApplicationCountry = await _dbContext.BranchApplicationCountry.Include(i => i.Branch)
                .Where(q => q.IsActive && !q.IsDeleted && q.BranchId == request.BranchId)
                .FirstOrDefaultAsync();

            if (branchApplicationCountry == null)
                return new ValidateAppointmentResult
                {
                    Status = ValidateAppointmentStatus.NotFound,
                    Message = ServiceResources.BRANCH_NOT_FOUND,
                };

            var requestedSlot = await _dbContext.Slot
                .Where(s => s.IsActive && !s.IsDeleted && s.Id == request.SlotId).FirstOrDefaultAsync();

            if (requestedSlot == null)
                return new ValidateAppointmentResult
                {
                    Status = ValidateAppointmentStatus.NotFound,
                    Message = ServiceResources.SLOT_NOT_FOUND,
                };

            if (request.AppointmentId.HasValue)
            {
                var appointmentNotIncluded = await _dbContext.PreApplicationApplicant.Where(s =>
                    s.IsActive && !s.IsDeleted && s.PreApplicationId == request.AppointmentId.Value).ToListAsync();

                foreach (var requestedSameApplicant in appointmentNotIncluded.Select(applicant => request.Applicants.Find(s =>
                             s.PassportNumber == applicant.PassportNumber && s.NationalityId == applicant.NationalityId)).Where(requestedSameApplicant => requestedSameApplicant != null))
                {
                    request.Applicants.Remove(requestedSameApplicant);
                }
            }

            var applicationWithSamePassport = await _dbContext.Application.Include(i => i.ApplicationStatusHistories).Where(s => s.IsActive && !s.IsDeleted &&
                    request.Applicants.Select(l => l.PassportNumber)
                        .Any(r => r == s.PassportNumber) && request.Applicants.Select(k => k.NationalityId).Contains(s.NationalityId
                )).ToListAsync();

            if (!applicationWithSamePassport.Any())
                return new ValidateAppointmentResult
                {
                    Status = ValidateAppointmentStatus.Successful,
                    Message = ServiceResources.SUCCESS,
                };

            if (branchApplicationCountry.Branch.CheckRejectedStatus)
            {
                var response = CheckApplicationStatusExpiryRule(
                     new[]
                     {
                         (int)ApplicationStatusType.Rejection,
                         (int)ApplicationStatusType.IstizanRejection,
                         (int)ApplicationStatusType.RejectionWithCountryEntryBanned
                     },
                     branchApplicationCountry.Branch.CheckRejectedStatusPeriod,
                     requestedSlot.SlotTime,
                     applicationWithSamePassport,
                     request.Applicants
                 );

                if (response != null)
                {
                    return new ValidateAppointmentResult()
                    {
                        Status = ValidateAppointmentStatus.PreConditionFailed,
                        Message =
                            $"{ServiceResources.CANNOT_CONTINUE_APPOINTMENT} / {response}"
                    };
                }
            }

            if (branchApplicationCountry.Branch.CheckUnrealDocumentStatus)
            {
                var response = CheckApplicationStatusExpiryRule(
                    new[]
                    {
                        (int)ApplicationStatusType.UnrealDocument
                    },
                    branchApplicationCountry.Branch.CheckUnrealDocumentStatusPeriod,
                    requestedSlot.SlotTime,
                    applicationWithSamePassport,
                    request.Applicants
                );

                if (response != null)
                {
                    return new ValidateAppointmentResult()
                    {
                        Status = ValidateAppointmentStatus.PreConditionFailed,
                        Message =
                            $"{ServiceResources.CANNOT_CONTINUE_APPOINTMENT} / {response}"
                    };
                }
            }

            return new ValidateAppointmentResult
            {
                Status = ValidateAppointmentStatus.Successful,
                Message = ServiceResources.SUCCESS
            };
        }

        #endregion

        #region Private Methods

        private static LookupValue GetLookupValue(int enumType, int? value)
        {
            return EnumExtensions.GetEnumAsDictionary(LookupTypeFactory.GetInstance(enumType))
                .Select(x => new LookupValue { Id = x.Key.ToString(), DisplayValue = x.Value })
                .FirstOrDefault(p => p.Id == value.ToString());
        }

        private static bool CheckSamePassportUsed(BaseAppointmentRequest request)
        {
            return request.Applicants.GroupBy(r => r.PassportNumber).All(passport => passport.Count() < 2);
        }

        private static bool CheckPassportExpireDate(BaseAppointmentRequest request)
        {
            return request.Applicants.Any(r => r.PassportExpireDate < DateTime.Now.AddDays(180));
        }

        private static bool CheckBirthDate(BaseAppointmentRequest request)
        {
            return request.Applicants.Any(r => r.BirthDate > DateTime.Now);
        }

        private Attachment GetUpdateAppointmentLetter(AppointmentLetterFileRequest args)
        {
            string webSite = "https://gatewayinternational.com.tr";
            if (args.BranchInformation.Id == 43 || args.BranchInformation.Id == 20 || args.BranchInformation.Id == 24 || args.BranchInformation.Id == 25 || args.BranchInformation.Id == 39 || args.BranchInformation.Id == 38 || args.BranchInformation.Id == 22 || args.BranchInformation.Id == 44 || args.BranchInformation.Id == 21 || args.BranchInformation.Id == 23 || args.BranchInformation.Id == 45 || args.BranchInformation.Id == 26) //Algeria
                webSite = "https://gatewayinternational.com.tr/en/algeria";
            else if (args.BranchInformation.Id == 35 || args.BranchInformation.Id == 16 || args.BranchInformation.Id == 48 || args.BranchInformation.Id == 17 || args.BranchInformation.Id == 18 || args.BranchInformation.Id == 53 || args.BranchInformation.Id == 46 || args.BranchInformation.Id == 19 || args.BranchInformation.Id == 49 || args.BranchInformation.Id == 50 || args.BranchInformation.Id == 34 || args.BranchInformation.Id == 15 || args.BranchInformation.Id == 14 || args.BranchInformation.Id == 52 || args.BranchInformation.Id == 36 || args.BranchInformation.Id == 51) //India
                webSite = "https://gatewayinternational.com.tr/en/india";
            else if (args.BranchInformation.Id == 33 || args.BranchInformation.Id == 8 || args.BranchInformation.Id == 5 || args.BranchInformation.Id == 3 || args.BranchInformation.Id == 42 || args.BranchInformation.Id == 9 || args.BranchInformation.Id == 7 || args.BranchInformation.Id == 11 || args.BranchInformation.Id == 41 || args.BranchInformation.Id == 40 || args.BranchInformation.Id == 4 || args.BranchInformation.Id == 13 || args.BranchInformation.Id == 6 || args.BranchInformation.Id == 10) //Iraq
                webSite = "https://gatewayinternational.com.tr/en/iraq";
            else if (args.BranchInformation.Id == 1) //Kuwait
                webSite = "https://gatewayinternational.com.tr/en/kuwait";
            else if (args.BranchInformation.Id == 12 || args.BranchInformation.Id == 2) //Libya
                webSite = "https://gatewayinternational.com.tr/en/libya";
            else if (args.BranchInformation.Id == 27) //Nepal
                webSite = "https://gatewayinternational.com.tr/en/nepal";
            else if (args.BranchInformation.Id == 30 || args.BranchInformation.Id == 29 || args.BranchInformation.Id == 28) //Saudi Arabia
                webSite = "https://gatewayinternational.com.tr/en/saudi-arabia";
            else if (args.BranchInformation.Id == 32 || args.BranchInformation.Id == 31 || args.BranchInformation.Id == 47) //UAE
                webSite = "https://gatewayinternational.com.tr/en/uae";
            else if (args.BranchInformation.Id == 37) //Askabat
                webSite = "https://gatewayinternational.com.tr/en/turkmenistan";

            string text_english = "Dear Applicant,\nYour appointment has been confirmed for the date and time reflecting against your name and appointment number. Please be available at the following address not more that 15 minutes before the appointed time.\n\nTurkey Visa Application Center - " + args.BranchInformation.NameEn + "\n\n" + args.BranchInformation.Address + "\n\nFor more information, please visit our website:\n\n" + webSite + "\n\n\nYou can reach us by e-mail and call center number at the below mentioned details.\n\ne-mail: " + args.BranchInformation.Email + "\n\nCall Center: " + args.BranchInformation.CallCenterTelephone + "\n ";
            string text_french = "Cher candidat,\nVotre rendez-vous a été confirmé pour l'heure et la date correspondant à votre nom et numéro de rendez-vous. Veuillez-vous présenter à l'adresse mentionnée ci-dessous 15 minutes avant l'heure de votre rendez-vous.\n\nLe centre de demande de visa pour la Turkiye - " + args.BranchInformation.NameEn + "\n\n" + args.BranchInformation.Address + "\n\nPour plus d'information, veuillez visiter notre site web:\n\n" + webSite + "\n\n\nVous pouvez nous joindre par e-mails ou par nos numéros du centre d'appels suivants.\n\nCourrier Electronique: " + args.BranchInformation.Email + "\n\nCentre d'appel: " + args.BranchInformation.CallCenterTelephone + "\n ";
            string text_arabic = "عزيزنا المراجع الكريم،" + "\n" + "لقد تمت الموافقة على تحديد الموعد في الساعة والتاريخ المتقابلين لمعلومات الاسم ورقم الموعد الخاصة بك. نرجو تواجدك في العنوان المبين أدناه قبل 15 دقيقة من الموعد." + "\n\n" + "مركز تقديم التأشيرات التركية" + " - " + args.BranchInformation.NameEn + "\n\n" + args.BranchInformation.AddressArabic + "\n\n" + "لمزيداً من المعلومات يرجى زيارة موقعنا الالكتروني" + "\n\n" + webSite + "\n\n\n" + "يمكنك التواصل معنا عبر عنوان البريد الالكتروني ورقم خدمة العملاء المبينين أدناه." + "\n\n" + "البريد الإلكتروني" + args.BranchInformation.Email + "\n\n" + "هاتف" + args.BranchInformation.CallCenterTelephone + "\n ";
            string text_english_Askabat = "Your appointment has been confirmed for the date and time reflecting against your name and appointment number. Please be available at the following address not more that 15 minutes before the appointed time.\n\nTurkey Visa Application Center - " + args.BranchInformation.NameEn + "\n\n" + args.BranchInformation.Address + "\n\nFor more information, please visit our website:\n\n" + webSite + "\n ";
            string text_turkoman_Askabat = "Siziň bellenmegiňiz, adyňyzy we salgy belgiňizi görkezýän sene we wagt üçin tassyklandy. Bellenen wagtdan 15 minut öň aşakdaky salgyda elýeterli bolmagyňyzy haýyş edýäris.\n\nTürkiye wiza anketasy - " + args.BranchInformation.NameEn + "\n\n" + args.BranchInformation.Address + "\n\nMaglumat üçin, web sahypamyza girmegiňizi haýyş edýäris:\n\n" + webSite + "\n "; ;
            string text_russian_Askabat = "Ваша встреча была подтверждена на дату и время, отражающие ваше имя и регистрационный номер. Пожалуйста, будьте доступны по следующему адресу не более чем за 15 минут до назначенного времени.\n\nВизовый Центр Турции - " + args.BranchInformation.NameEn + "\n\n" + args.BranchInformation.Address + "\n\nДля получения информации посетите наш веб-:\n\n" + webSite + "\n "; ;

            System.Text.Encoding.RegisterProvider(System.Text.CodePagesEncodingProvider.Instance);
            iTextSharp.text.Document document = new iTextSharp.text.Document(PageSize.A4, 20, 20, 20, 20);
            var filePathArial = Path.Combine(Directory.GetCurrentDirectory(), "Documents", "Fonts", "arial.ttf");
            System.IO.File.ReadAllText(filePathArial);
            BaseFont georgia = BaseFont.CreateFont(filePathArial, BaseFont.IDENTITY_H, BaseFont.EMBEDDED);
            iTextSharp.text.Font fontGeneral = new iTextSharp.text.Font(georgia, 11, iTextSharp.text.Font.NORMAL, iTextSharp.text.BaseColor.BLACK);
            iTextSharp.text.Font fontHeader = new iTextSharp.text.Font(georgia, 12, iTextSharp.text.Font.BOLD, iTextSharp.text.BaseColor.BLACK);

            MemoryStream memoryStream = new MemoryStream();
            PdfWriter writer = PdfWriter.GetInstance(document, memoryStream);
            document.AddTitle("Appointment Confirmation");
            document.Open();

            PdfPTable table = new PdfPTable(2);
            table.DefaultCell.BorderColor = iTextSharp.text.BaseColor.BLACK;

            PdfPCell cell = new PdfPCell(new Phrase(DateTime.Now.Date.ToString("dd/MM/yyyy"), fontGeneral));
            cell.Colspan = 2;
            cell.HorizontalAlignment = PdfPCell.ALIGN_RIGHT;
            cell.VerticalAlignment = PdfPCell.ALIGN_MIDDLE;
            table.AddCell(cell);

            cell = new PdfPCell(new Phrase("Appointment Details", fontHeader));
            cell.Colspan = 2;
            cell.HorizontalAlignment = PdfPCell.ALIGN_CENTER;
            cell.VerticalAlignment = PdfPCell.ALIGN_MIDDLE;
            cell.BackgroundColor = iTextSharp.text.BaseColor.LIGHT_GRAY;
            table.AddCell(cell);

            cell = new PdfPCell(new Phrase("Applicant Name", fontGeneral));
            cell.Colspan = 1;
            cell.HorizontalAlignment = PdfPCell.ALIGN_CENTER;
            table.AddCell(cell);
            string name = "";
            for (int i = 0; i < args.Applicants.Count; i++)
            {
                if (i != args.Applicants.Count - 1)
                    name += args.Applicants[i].Name + " " + args.Applicants[i].Surname + ",";
                else
                    name += args.Applicants[i].Name + " " + args.Applicants[i].Surname;
            }
            cell = new PdfPCell(new Phrase(name, fontGeneral));
            cell.Colspan = 1;
            cell.HorizontalAlignment = PdfPCell.ALIGN_CENTER;
            table.AddCell(cell);

            cell = new PdfPCell(new Phrase("Passport Number", fontGeneral));
            cell.Colspan = 1;
            cell.HorizontalAlignment = PdfPCell.ALIGN_CENTER;
            table.AddCell(cell);
            string passportNo = "";
            for (int i = 0; i < args.Applicants.Count; i++)
            {
                if (i != args.Applicants.Count - 1)
                    passportNo += args.Applicants[i].PassportNumber + ",";
                else
                    passportNo += args.Applicants[i].PassportNumber;
            }
            cell = new PdfPCell(new Phrase(passportNo, fontGeneral));
            cell.Colspan = 1;
            cell.HorizontalAlignment = PdfPCell.ALIGN_CENTER;
            table.AddCell(cell);

            cell = new PdfPCell(new Phrase("Birth Date", fontGeneral));
            cell.Colspan = 1;
            cell.HorizontalAlignment = PdfPCell.ALIGN_CENTER;
            table.AddCell(cell);
            string birthOfDate = "";
            for (int i = 0; i < args.Applicants.Count; i++)
            {
                if (i != args.Applicants.Count - 1)
                {
                    if (args.Applicants[i].BirthDate == DateTime.MinValue || args.Applicants[i].BirthDate == null)
                        birthOfDate += "N/A" + ",";
                    else
                        birthOfDate += args.Applicants[i].BirthDate.Value.Date.ToString("dd/MM/yyyy") + ",";
                }
                else
                {
                    if (args.Applicants[i].BirthDate == DateTime.MinValue || args.Applicants[i].BirthDate == null)
                        birthOfDate += "N/A";
                    else
                        birthOfDate += args.Applicants[i].BirthDate.Value.Date.ToString("dd/MM/yyyy");
                }
            }
            cell = new PdfPCell(new Phrase(birthOfDate, fontGeneral));
            cell.Colspan = 1;
            cell.HorizontalAlignment = PdfPCell.ALIGN_CENTER;
            table.AddCell(cell);

            if (args.VasTypeId != null)
            {
                cell = new PdfPCell(new Phrase("Appointment Category Type", fontGeneral));
                cell.Colspan = 1;
                cell.HorizontalAlignment = PdfPCell.ALIGN_CENTER;
                table.AddCell(cell);
                cell = new PdfPCell(new Phrase(EnumHelper.GetEnumDescription(typeof(Enums.Enums.VasType), args.VasTypeId.ToString()), fontGeneral));
                cell.Colspan = 1;
                cell.HorizontalAlignment = PdfPCell.ALIGN_CENTER;
                table.AddCell(cell);
            }

            cell = new PdfPCell(new Phrase("Appointment Date and Appointment Time", fontGeneral));
            cell.Colspan = 1;
            cell.HorizontalAlignment = PdfPCell.ALIGN_CENTER;
            table.AddCell(cell);
            cell = new PdfPCell(new Phrase(args.SlotDateAndTime.Date.ToString("dd/MM/yyyy") + " " + args.SlotTime, fontGeneral));
            cell.Colspan = 1;
            cell.HorizontalAlignment = PdfPCell.ALIGN_CENTER;
            table.AddCell(cell);

            cell = new PdfPCell(new Phrase("Appointment Number", fontGeneral));
            cell.Colspan = 1;
            cell.HorizontalAlignment = PdfPCell.ALIGN_CENTER;
            table.AddCell(cell);
            var barcode = CreateBarcode(args.AppointmentNumber);
            iTextSharp.text.Image png = iTextSharp.text.Image.GetInstance(barcode);
            png.ScalePercent(50f);
            cell = new PdfPCell(png);
            cell.Colspan = 1;
            cell.FixedHeight = 70f;
            cell.HorizontalAlignment = PdfPCell.ALIGN_CENTER;
            cell.VerticalAlignment = PdfPCell.ALIGN_MIDDLE;
            table.AddCell(cell);

            if (args.BranchInformation.Id != 37)
            {
                cell = new PdfPCell(new Phrase(text_english, fontGeneral));
                cell.Colspan = 2;
                cell.HorizontalAlignment = PdfPCell.ALIGN_LEFT;
                table.AddCell(cell);

                if (args.BranchInformation.CountryId != 80 && args.BranchInformation.CountryId != 93)
                {
                    cell = new PdfPCell(new Phrase(text_french, fontGeneral));
                    cell.Colspan = 2;
                    cell.HorizontalAlignment = PdfPCell.ALIGN_LEFT;
                    table.AddCell(cell);
                }

                var arabicFont = BaseFont.CreateFont(filePathArial, BaseFont.IDENTITY_H, true);
                var el = new Chunk();
                var f2 = new Font(arabicFont, el.Font.Size, el.Font.Style, el.Font.Color);
                el.Font = f2;

                cell = new PdfPCell(new Phrase(11, text_arabic, el.Font));
                cell.RunDirection = PdfWriter.RUN_DIRECTION_RTL;
                cell.Colspan = 2;
                table.AddCell(cell);
            }
            else
            {
                cell = new PdfPCell(new Phrase(text_english_Askabat, fontGeneral));
                cell.Colspan = 2;
                cell.HorizontalAlignment = PdfPCell.ALIGN_LEFT;
                table.AddCell(cell);
                cell = new PdfPCell(new Phrase(text_turkoman_Askabat, fontGeneral));
                cell.Colspan = 2;
                cell.HorizontalAlignment = PdfPCell.ALIGN_LEFT;
                table.AddCell(cell);

                var russianFont = BaseFont.CreateFont(filePathArial, BaseFont.IDENTITY_H, true);
                var el = new Chunk();
                var f2 = new Font(russianFont, el.Font.Size, el.Font.Style, el.Font.Color);
                el.Font = f2;
                cell = new PdfPCell(new Phrase(11, text_russian_Askabat, el.Font));
                cell.Colspan = 2;
                cell.HorizontalAlignment = PdfPCell.ALIGN_LEFT;
                table.AddCell(cell);
            }

            document.Add(table);
            document.AddAuthor("Appointment Confirmation");
            document.Close();
            writer.Close();
            byte[] pdfFile = memoryStream.ToArray();

            memoryStream.Close();

            return new Attachment()
            {
                Content = Convert.ToBase64String(pdfFile),
                FileName = $"{ServiceResources.APPOINTMENT_UPDATE_CONFIRMATION_FILE_NAME}.pdf",
            };
        }

        private Attachment GetCreateAppointmentLetter(AppointmentLetterFileRequest args)
        {
            string webSite = "https://gatewayinternational.com.tr";
            if (args.BranchInformation.Id == 43 || args.BranchInformation.Id == 20 || args.BranchInformation.Id == 24 || args.BranchInformation.Id == 25 || args.BranchInformation.Id == 39 || args.BranchInformation.Id == 38 || args.BranchInformation.Id == 22 || args.BranchInformation.Id == 44 || args.BranchInformation.Id == 21 || args.BranchInformation.Id == 23 || args.BranchInformation.Id == 45 || args.BranchInformation.Id == 26) //Algeria
                webSite = "https://gatewayinternational.com.tr/en/algeria";
            else if (args.BranchInformation.Id == 35 || args.BranchInformation.Id == 16 || args.BranchInformation.Id == 48 || args.BranchInformation.Id == 17 || args.BranchInformation.Id == 18 || args.BranchInformation.Id == 53 || args.BranchInformation.Id == 46 || args.BranchInformation.Id == 19 || args.BranchInformation.Id == 49 || args.BranchInformation.Id == 50 || args.BranchInformation.Id == 34 || args.BranchInformation.Id == 15 || args.BranchInformation.Id == 14 || args.BranchInformation.Id == 52 || args.BranchInformation.Id == 36 || args.BranchInformation.Id == 51) //India
                webSite = "https://gatewayinternational.com.tr/en/india";
            else if (args.BranchInformation.Id == 33 || args.BranchInformation.Id is 8 or 5 || args.BranchInformation.Id == 3 || args.BranchInformation.Id == 42 || args.BranchInformation.Id == 9 || args.BranchInformation.Id == 7 || args.BranchInformation.Id == 11 || args.BranchInformation.Id == 41 || args.BranchInformation.Id == 40 || args.BranchInformation.Id == 4 || args.BranchInformation.Id == 13 || args.BranchInformation.Id == 6 || args.BranchInformation.Id == 10) //Iraq
                webSite = "https://gatewayinternational.com.tr/en/iraq";
            else if (args.BranchInformation.Id == 1) //Kuwait
                webSite = "https://gatewayinternational.com.tr/en/kuwait";
            else if (args.BranchInformation.Id == 12 || args.BranchInformation.Id == 2) //Libya
                webSite = "https://gatewayinternational.com.tr/en/libya";
            else if (args.BranchInformation.Id == 27) //Nepal
                webSite = "https://gatewayinternational.com.tr/en/nepal";
            else if (args.BranchInformation.Id == 30 || args.BranchInformation.Id is 29 or 28) //Saudi Arabia
                webSite = "https://gatewayinternational.com.tr/en/saudi-arabia";
            else if (args.BranchInformation.Id == 32 || args.BranchInformation.Id is 31 or 47) //UAE
                webSite = "https://gatewayinternational.com.tr/en/uae";
            else if (args.BranchInformation.Id == 37) //Askabat
                webSite = "https://gatewayinternational.com.tr/en/turkmenistan";

            string text_english = "Dear Applicant,\nYour appointment has been confirmed for the date and time reflecting against your name and appointment number. Please be available at the following address not more that 15 minutes before the appointed time.\n\nTurkey Visa Application Center - " + args.BranchInformation.NameEn + "\n\n" + args.BranchInformation.Address + "\n\nFor more information, please visit our website:\n\n" + webSite + "\n\n\nYou can reach us by e-mail and call center number at the below mentioned details.\n\ne-mail: " + args.BranchInformation.Email + "\n\nCall Center: " + args.BranchInformation.CallCenterTelephone + "\n ";
            string text_french = "Cher candidat,\nVotre rendez-vous a été confirmé pour l'heure et la date correspondant à votre nom et numéro de rendez-vous. Veuillez-vous présenter à l'adresse mentionnée ci-dessous 15 minutes avant l'heure de votre rendez-vous.\n\nLe centre de demande de visa pour la Turkiye - " + args.BranchInformation.NameEn + "\n\n" + args.BranchInformation.Address + "\n\nPour plus d'information, veuillez visiter notre site web:\n\n" + webSite + "\n\n\nVous pouvez nous joindre par e-mails ou par nos numéros du centre d'appels suivants.\n\nCourrier Electronique: " + args.BranchInformation.Email + "\n\nCentre d'appel: " + args.BranchInformation.CallCenterTelephone + "\n ";
            string text_arabic = "عزيزنا المراجع الكريم،" + "\n" + "لقد تمت الموافقة على تحديد الموعد في الساعة والتاريخ المتقابلين لمعلومات الاسم ورقم الموعد الخاصة بك. نرجو تواجدك في العنوان المبين أدناه قبل 15 دقيقة من الموعد." + "\n\n" + "مركز تقديم التأشيرات التركية" + " - " + args.BranchInformation.NameEn + "\n\n" + args.BranchInformation.AddressArabic + "\n\n" + "لمزيداً من المعلومات يرجى زيارة موقعنا الالكتروني" + "\n\n" + webSite + "\n\n\n" + "يمكنك التواصل معنا عبر عنوان البريد الالكتروني ورقم خدمة العملاء المبينين أدناه." + "\n\n" + "البريد الإلكتروني" + args.BranchInformation.Email + "\n\n" + "هاتف" + args.BranchInformation.CallCenterTelephone + "\n ";
            string text_english_Askabat = "Your appointment has been confirmed for the date and time reflecting against your name and appointment number. Please be available at the following address not more that 15 minutes before the appointed time.\n\nTurkey Visa Application Center - " + args.BranchInformation.NameEn + "\n\n" + args.BranchInformation.Address + "\n\nFor more information, please visit our website:\n\n" + webSite + "\n ";
            string text_turkoman_Askabat = "Siziň bellenmegiňiz, adyňyzy we salgy belgiňizi görkezýän sene we wagt üçin tassyklandy. Bellenen wagtdan 15 minut öň aşakdaky salgyda elýeterli bolmagyňyzy haýyş edýäris.\n\nTürkiye wiza anketasy - " + args.BranchInformation.NameEn + "\n\n" + args.BranchInformation.Address + "\n\nMaglumat üçin, web sahypamyza girmegiňizi haýyş edýäris:\n\n" + webSite + "\n "; ;
            string text_russian_Askabat = "Ваша встреча была подтверждена на дату и время, отражающие ваше имя и регистрационный номер. Пожалуйста, будьте доступны по следующему адресу не более чем за 15 минут до назначенного времени.\n\nВизовый Центр Турции - " + args.BranchInformation.NameEn + "\n\n" + args.BranchInformation.Address + "\n\nДля получения информации посетите наш веб-:\n\n" + webSite + "\n "; ;

            System.Text.Encoding.RegisterProvider(System.Text.CodePagesEncodingProvider.Instance);
            iTextSharp.text.Document document = new iTextSharp.text.Document(PageSize.A4, 20, 20, 20, 20);
            var filePathArial = Path.Combine(Directory.GetCurrentDirectory(), "Documents", "Fonts", "arial.ttf");
            System.IO.File.ReadAllText(filePathArial);
            BaseFont georgia = BaseFont.CreateFont(filePathArial, BaseFont.IDENTITY_H, BaseFont.EMBEDDED);
            iTextSharp.text.Font fontGeneral = new iTextSharp.text.Font(georgia, 11, iTextSharp.text.Font.NORMAL, iTextSharp.text.BaseColor.BLACK);
            iTextSharp.text.Font fontHeader = new iTextSharp.text.Font(georgia, 12, iTextSharp.text.Font.BOLD, iTextSharp.text.BaseColor.BLACK);

            MemoryStream memoryStream = new MemoryStream();
            PdfWriter writer = PdfWriter.GetInstance(document, memoryStream);
            document.AddTitle("Appointment Confirmation");
            document.Open();

            PdfPTable table = new PdfPTable(2);
            table.DefaultCell.BorderColor = iTextSharp.text.BaseColor.BLACK;

            PdfPCell cell = new PdfPCell(new Phrase(DateTime.Now.Date.ToString("dd/MM/yyyy"), fontGeneral));
            cell.Colspan = 2;
            cell.HorizontalAlignment = PdfPCell.ALIGN_RIGHT;
            cell.VerticalAlignment = PdfPCell.ALIGN_MIDDLE;
            table.AddCell(cell);

            cell = new PdfPCell(new Phrase("Appointment Details", fontHeader));
            cell.Colspan = 2;
            cell.HorizontalAlignment = PdfPCell.ALIGN_CENTER;
            cell.VerticalAlignment = PdfPCell.ALIGN_MIDDLE;
            cell.BackgroundColor = iTextSharp.text.BaseColor.LIGHT_GRAY;
            table.AddCell(cell);

            cell = new PdfPCell(new Phrase("Applicant Name", fontGeneral));
            cell.Colspan = 1;
            cell.HorizontalAlignment = PdfPCell.ALIGN_CENTER;
            table.AddCell(cell);
            string name = "";
            for (int i = 0; i < args.Applicants.Count; i++)
            {
                if (i != args.Applicants.Count - 1)
                    name += args.Applicants[i].Name + " " + args.Applicants[i].Surname + ",";
                else
                    name += args.Applicants[i].Name + " " + args.Applicants[i].Surname;
            }
            cell = new PdfPCell(new Phrase(name, fontGeneral));
            cell.Colspan = 1;
            cell.HorizontalAlignment = PdfPCell.ALIGN_CENTER;
            table.AddCell(cell);

            cell = new PdfPCell(new Phrase("Passport Number", fontGeneral));
            cell.Colspan = 1;
            cell.HorizontalAlignment = PdfPCell.ALIGN_CENTER;
            table.AddCell(cell);
            string passportNo = "";
            for (int i = 0; i < args.Applicants.Count; i++)
            {
                if (i != args.Applicants.Count - 1)
                    passportNo += args.Applicants[i].PassportNumber + ",";
                else
                    passportNo += args.Applicants[i].PassportNumber;
            }
            cell = new PdfPCell(new Phrase(passportNo, fontGeneral));
            cell.Colspan = 1;
            cell.HorizontalAlignment = PdfPCell.ALIGN_CENTER;
            table.AddCell(cell);

            cell = new PdfPCell(new Phrase("Birth Date", fontGeneral));
            cell.Colspan = 1;
            cell.HorizontalAlignment = PdfPCell.ALIGN_CENTER;
            table.AddCell(cell);
            string birthOfDate = "";
            for (int i = 0; i < args.Applicants.Count; i++)
            {
                if (i != args.Applicants.Count - 1)
                {
                    if (args.Applicants[i].BirthDate == DateTime.MinValue || args.Applicants[i].BirthDate == null)
                        birthOfDate += "N/A" + ",";
                    else
                        birthOfDate += args.Applicants[i].BirthDate.Value.Date.ToString("dd/MM/yyyy") + ",";
                }
                else
                {
                    if (args.Applicants[i].BirthDate == DateTime.MinValue || args.Applicants[i].BirthDate == null)
                        birthOfDate += "N/A";
                    else
                        birthOfDate += args.Applicants[i].BirthDate.Value.Date.ToString("dd/MM/yyyy");
                }
            }
            cell = new PdfPCell(new Phrase(birthOfDate, fontGeneral));
            cell.Colspan = 1;
            cell.HorizontalAlignment = PdfPCell.ALIGN_CENTER;
            table.AddCell(cell);

            if (args.VasTypeId != null)
            {
                cell = new PdfPCell(new Phrase("Appointment Category Type", fontGeneral));
                cell.Colspan = 1;
                cell.HorizontalAlignment = PdfPCell.ALIGN_CENTER;
                table.AddCell(cell);
                cell = new PdfPCell(new Phrase(EnumHelper.GetEnumDescription(typeof(Enums.Enums.VasType), args.VasTypeId.ToString()), fontGeneral));
                cell.Colspan = 1;
                cell.HorizontalAlignment = PdfPCell.ALIGN_CENTER;
                table.AddCell(cell);
            }

            cell = new PdfPCell(new Phrase("Appointment Date and Appointment Time", fontGeneral));
            cell.Colspan = 1;
            cell.HorizontalAlignment = PdfPCell.ALIGN_CENTER;
            table.AddCell(cell);
            cell = new PdfPCell(new Phrase(args.AppointmentDate.Date.ToString("dd/MM/yyyy") + " " + args.SlotTime, fontGeneral));
            cell.Colspan = 1;
            cell.HorizontalAlignment = PdfPCell.ALIGN_CENTER;
            table.AddCell(cell);

            cell = new PdfPCell(new Phrase("Appointment Number", fontGeneral));
            cell.Colspan = 1;
            cell.HorizontalAlignment = PdfPCell.ALIGN_CENTER;
            table.AddCell(cell);
            var barcode = CreateBarcode(args.AppointmentNumber);
            iTextSharp.text.Image png = iTextSharp.text.Image.GetInstance(barcode);
            png.ScalePercent(50f);
            cell = new PdfPCell(png);
            cell.Colspan = 1;
            cell.FixedHeight = 70f;
            cell.HorizontalAlignment = PdfPCell.ALIGN_CENTER;
            cell.VerticalAlignment = PdfPCell.ALIGN_MIDDLE;
            table.AddCell(cell);

            if (args.BranchInformation.Id != 37)
            {
                cell = new PdfPCell(new Phrase(text_english, fontGeneral));
                cell.Colspan = 2;
                cell.HorizontalAlignment = PdfPCell.ALIGN_LEFT;
                table.AddCell(cell);

                if (args.BranchInformation.CountryId != 80 && args.BranchInformation.CountryId != 93)
                {
                    cell = new PdfPCell(new Phrase(text_french, fontGeneral));
                    cell.Colspan = 2;
                    cell.HorizontalAlignment = PdfPCell.ALIGN_LEFT;
                    table.AddCell(cell);
                }

                var arabicFont = BaseFont.CreateFont(filePathArial, BaseFont.IDENTITY_H, true);
                var el = new Chunk();
                var f2 = new Font(arabicFont, el.Font.Size, el.Font.Style, el.Font.Color);
                el.Font = f2;
                                
                cell = new PdfPCell(new Phrase(11, text_arabic, el.Font));
                cell.RunDirection = PdfWriter.RUN_DIRECTION_RTL;
                cell.Colspan = 2;
                table.AddCell(cell);
            }
            else
            {
                cell = new PdfPCell(new Phrase(text_english_Askabat, fontGeneral));
                cell.Colspan = 2;
                cell.HorizontalAlignment = PdfPCell.ALIGN_LEFT;
                table.AddCell(cell);
                cell = new PdfPCell(new Phrase(text_turkoman_Askabat, fontGeneral));
                cell.Colspan = 2;
                cell.HorizontalAlignment = PdfPCell.ALIGN_LEFT;
                table.AddCell(cell);

                var russianFont = BaseFont.CreateFont(filePathArial, BaseFont.IDENTITY_H, true);
                var el = new Chunk();
                var f2 = new Font(russianFont, el.Font.Size, el.Font.Style, el.Font.Color);
                el.Font = f2;
                cell = new PdfPCell(new Phrase(11, text_russian_Askabat, el.Font));
                cell.Colspan = 2;
                cell.HorizontalAlignment = PdfPCell.ALIGN_LEFT;
                table.AddCell(cell);
            }

            document.Add(table);
            document.AddAuthor("Appointment Confirmation");
            document.Close();
            writer.Close();
            byte[] pdfFile = memoryStream.ToArray();

            memoryStream.Close();

            return new Attachment()
            {
                Content = Convert.ToBase64String(pdfFile),
                FileName = $"{ServiceResources.APPOINTMENT_CONFIRMATION_FILE_NAME}.pdf",
            };
        }

        public byte[] CreateBarcode(int encryptedId)
        {
            return BarcodeClient.CreateBarcodeByteArray(encryptedId.ToString("0000000000000"), new SKColor(33, 37, 41), BarcodeClient.DefaultWidth, BarcodeClient.DefaultHeight);
        }

        public string GetHtmlText(int languageId, int channelTypeId, string branchNote, bool isNewAppointment, NotificationHtmlTextDto request)
        {
            var htmlText = string.Empty;

            if (channelTypeId is (int)Enums.Enums.ChannelType.GwInternational)
            {
                htmlText = isNewAppointment ?
                    GetNewAppointmentHtml(languageId) :
                    GetUpdateAppointmentHtml(languageId);
            }
            else
            {
                if (isNewAppointment)
                {
                    htmlText = branchNote ?? string.Empty;
                }
            }

            return htmlText.Replace("[APPNUMBER]", request.AppointmentNumber)
                .Replace("[DATE]", request.AppointmentDate.ToString("dd.MM.yyyy"))
                .Replace("[TIME]", request.SlotTime)
                .Replace("[BRANCH]", request.BranchName);
        }

        private string GetNewAppointmentHtml(int languageId)
        {
            string baseHtmlTemplate = @"
                                <html>
                                    <head>
                                        <style>
                                            table {width: 100%; border-collapse: collapse;}
                                            th, td {text-align: center; border: 1px solid black; padding: 10px;}
                                            th {background-color: #B8B0AF;}
                                        </style>
                                    </head>
                                    <body>
                                        <table>
                                            <tr>
                                                <th>%%BRANCH%%</th>
                                                <th>%%DATE%%</th>
                                                <th>%%HOUR%%</th>
                                                <th>%%APPNUMBER%%</th>
                                            </tr>
                                            <tr>
                                                <td>[BRANCH]</td>
                                                <td>[DATE]</td>
                                                <td>[TIME]</td>
                                                <td>[APPNUMBER]</td>
                                            </tr>
                                        </table>
                                    </body>
                                    </html>
                                    <br>";

            string html = languageId switch
            {

                1 => baseHtmlTemplate.Replace("%%BRANCH%%", "Şube")
                                     .Replace("%%DATE%%", "Tarih")
                                     .Replace("%%HOUR%%", "Saat")
                                     .Replace("%%APPNUMBER%%", "Randevu Numarası"),

                2 => baseHtmlTemplate.Replace("%%BRANCH%%", "Branch")
                                     .Replace("%%DATE%%", "Date")
                                     .Replace("%%HOUR%%", "Hour")
                                     .Replace("%%APPNUMBER%%", "Appointment Number"),

                5 => baseHtmlTemplate.Replace("%%BRANCH%%", "Филиал")
                                     .Replace("%%DATE%%", "Дата")
                                     .Replace("%%HOUR%%", "Час")
                                     .Replace("%%APPNUMBER%%", "Номер встречи"),

                _ => baseHtmlTemplate.Replace("%%BRANCH%%", "Branch")
                                     .Replace("%%DATE%%", "Date")
                                     .Replace("%%HOUR%%", "Hour")
                                     .Replace("%%APPNUMBER%%", "Appointment Number")
            };

            var htmlDocument = new HtmlDocument();
            htmlDocument.LoadHtml(html);

            return MinifyHtml(htmlDocument.DocumentNode);
        }


        private string GetUpdateAppointmentHtml(int languageId)
        {
            string baseHtmlTemplate = @"
                                <html>
                                    <head>
                                        <style>
                                            table {width: 100%; border-collapse: collapse;}
                                            th, td {text-align: center; border: 1px solid black; padding: 10px;}
                                            th {background-color: #B8B0AF;}
                                        </style>
                                    </head>
                                    <body>
                                        <table>
                                            <tr>
                                                <th>%%BRANCH%%</th>
                                                <th>%%DATE%%</th>
                                                <th>%%HOUR%%</th>
                                            </tr>
                                            <tr>
                                                <td>[BRANCH]</td>
                                                <td>[DATE]</td>
                                                <td>[TIME]</td>
                                            </tr>
                                        </table>
                                    </body>
                                    </html>
                                    <br>";

            string html = languageId switch
            {
                1 => baseHtmlTemplate.Replace("%%BRANCH%%", "Şube")
                                     .Replace("%%DATE%%", "Tarih")
                                     .Replace("%%HOUR%%", "Saat"),

                2 => baseHtmlTemplate.Replace("%%BRANCH%%", "Branch")
                                     .Replace("%%DATE%%", "Date")
                                     .Replace("%%HOUR%%", "Hour"),

                5 => baseHtmlTemplate.Replace("%%BRANCH%%", "Филиал")
                                     .Replace("%%DATE%%", "Дата")
                                     .Replace("%%HOUR%%", "Час"),

                _ => baseHtmlTemplate.Replace("%%BRANCH%%", "Branch")
                                     .Replace("%%DATE%%", "Date")
                                     .Replace("%%HOUR%%", "Hour")
            }; ;

            var htmlDocument = new HtmlDocument();
            htmlDocument.LoadHtml(html);

            return MinifyHtml(htmlDocument.DocumentNode);

        }

        private static string MinifyHtml(HtmlNode node)
        {
            node.Descendants()
                .Where(n => n.NodeType == HtmlNodeType.Comment || (n.NodeType == HtmlNodeType.Text && string.IsNullOrWhiteSpace(n.InnerHtml)))
                .ToList()
                .ForEach(n => n.Remove());

            return node.OuterHtml;
        }

        private static Attachment GetBasicGuidelineDocument(int basicGuidelineTypeId)
        {
            var basePath = Path.Combine(Directory.GetCurrentDirectory(), "Documents", "AmsBasicGuideline", "Basic Guidelines to apply for Turkiye visa");

            switch (basicGuidelineTypeId)
            {
                case (int)BasicGuidelineType.ArabicFrenchEnglish:
                    basePath += "_ENG_AR_FR.pdf";
                    break;

                case (int)BasicGuidelineType.KurdishArabic:
                    basePath += "_AR_KR.pdf";
                    break;

                case (int)BasicGuidelineType.ArabicEnglish:
                    basePath += "_ENG_AR.pdf";
                    break;

                case (int)BasicGuidelineType.TurkmenRussian:
                    basePath += "_TURKMEN_RUSSIAN.pdf";
                    break;
            }

            var pdfStream = new MemoryStream(System.IO.File.ReadAllBytes(basePath));
            pdfStream.Position = 0;
            pdfStream.Dispose();

            var index = basePath.IndexOf("AmsBasicGuideline\\", StringComparison.Ordinal);

            var fileName = basePath[(index + "AmsBasicGuideline\\".Length)..];

            return new Attachment()
            {
                Content = Convert.ToBase64String(pdfStream.ToArray()),
                FileName = fileName
            };
        }

        private static string CheckApplicationStatusExpiryRule(int[] statuses, int period, DateTimeOffset slotDate, List<Entity.Entities.Application.Application> applicationsSamePassport, List<ValidateAppointmentRequest.ApplicantValidationDto> requestedApplicants)
        {
            var checkRejectedStatusStartDate = DateTime.Today.AddDays(period * -1);
            var checkRejectedStatusEndDate = DateTime.Today;
            var rejectionApplicationStatusIds = statuses;

            foreach (var applicant in requestedApplicants)
            {
                var rejectedAppointment = applicationsSamePassport.Find(p =>
                    p.PassportNumber == applicant.PassportNumber && p.NationalityId == applicant.NationalityId &&
                    p.StatusId != (int)ApplicationStatus.Cancelled &&
                    p.ApplicationTypeId != ApplicationType.NonApplicationInsurance.ToInt() &&
                    p.ApplicationTypeId != ApplicationType.NonApplicationPcr.ToInt() &&
                    p.ApplicationTypeId != ApplicationType.NonApplicationPhotocopy.ToInt() &&
                    p.ApplicationTypeId != ApplicationType.NonApplicationPrintOut.ToInt() &&
                    p.ApplicationTypeId != ApplicationType.NonApplicationPhotograph.ToInt() &&
                    p.ApplicationStatusHistories.Any(p2 =>
                        rejectionApplicationStatusIds.Contains(p2.ApplicationStatusId) &&
                        p2.CreatedAt.Date >= checkRejectedStatusStartDate &&
                        p2.CreatedAt.Date <= checkRejectedStatusEndDate));

                if (rejectedAppointment == null) return null;
                {
                    var rejectedStatusEndDate = rejectedAppointment.ApplicationStatusHistories.First(p2 =>
                            rejectionApplicationStatusIds.Contains(p2.ApplicationStatusId) &&
                            p2.CreatedAt.Date >= checkRejectedStatusStartDate &&
                            p2.CreatedAt.Date <= checkRejectedStatusEndDate).CreatedAt.Date
                        .AddDays(period - 1);
                    return rejectedStatusEndDate > slotDate ? applicant.PassportNumber : null;
                }
            }

            return null;
        }

        #endregion
    }
}