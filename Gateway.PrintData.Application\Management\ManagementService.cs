﻿using Gateway.Extensions;
using Gateway.Redis;
using Gateway.Validation;
using Portal.Gateway.ApiModel;
using Portal.Gateway.Contracts.Entities;
using Portal.Gateway.Contracts.Entities.Dto;
using Portal.Gateway.Contracts.Entities.Dto.Management.Tablet;
using Portal.Gateway.Contracts.Entities.Dto.Management.Tablet.Request;
using Portal.Gateway.Contracts.Entities.Dto.Management.Tablet.Response;
using Portal.Gateway.Contracts.Entities.Enums;
using Portal.Gateway.Contracts.Extensions;
using Portal.Gateway.Contracts.UnitOfWork;
using Portal.Gateway.Entity.Context;
using Portal.Gateway.Entity.Entities.Portal;
using Portal.Gateway.Resources;
using Gateway.PrintData.Application.Management.Dto;

using Serilog;
using Microsoft.EntityFrameworkCore;

namespace Gateway.PrintData.Application.Management
{
    public class ManagementService : IManagementService
    {
        private readonly IUnitOfWork<PortalDbContext> _unitOfWorkPortalDb;
        private readonly IValidationService _validationService;
        private static readonly ILogger Logger = Log.ForContext<ManagementService>();
        private readonly IRedisClient _redisClient;

        public ManagementService(IUnitOfWork<PortalDbContext> unitOfWorkPortalDb, IValidationService validationService, IRedisClient redisClient)
        {
            _unitOfWorkPortalDb = unitOfWorkPortalDb;
            _validationService = validationService;
            _redisClient = redisClient;
        }

        public async Task<TabletResponseDto> GetTabletAsync(int id)
        {
            var existingTablet = await _unitOfWorkPortalDb.GetRepository<DigitalSignatureDevice>().Entities
                .Where(p => p.IsActive && !p.IsDeleted && p.Id == id)
                .FirstOrDefaultAsync();

            if (existingTablet == null)
                throw new NoFoundDataPortalException($"{nameof(SiteResources.Exception_NoRecordsFound).ToSiteResourcesValue()} ({nameof(SiteResources.Tablet).ToSiteResourcesValue()})");

            return new TabletResponseDto
            {
                Id = existingTablet.Id,
                Name = existingTablet.Name,
                Version = existingTablet.Version,
                BranchId = existingTablet.BranchId,
                DeviceId = existingTablet.DeviceId
            };
        }

        public async Task<Pagination<PaginatedTabletsResponseDto>> GetPaginatedTabletsAsync(GetPaginatedTabletsRequestDto request)
        {
            try
            {
                var paginationResult = new Pagination<PaginatedTabletsResponseDto> { Page = request.Pagination.Page, PageSize = request.Pagination.PageSize };

                var queryTablets = _unitOfWorkPortalDb.GetRepository<DigitalSignatureDevice>().Entities
                    .Where(p => p.IsActive && !p.IsDeleted);

                if (!string.IsNullOrEmpty(request.FilterName))
                    queryTablets = queryTablets.Where(p => p.Name.Contains(request.FilterName));

                if (!string.IsNullOrEmpty(request.FilterVersion))
                    queryTablets = queryTablets.Where(p => p.Version.Contains(request.FilterVersion));

                if (!string.IsNullOrEmpty(request.FilterDeviceId))
                    queryTablets = queryTablets.Where(p => p.DeviceId.Contains(request.FilterDeviceId));

                if (request.FilterBranchId.IsNumericAndGreaterThenZero())
                    queryTablets = queryTablets.Where(p => p.BranchId == request.FilterBranchId);

                if (request.FilterLastHeartbeatTime.GetValueOrDefault() == (byte)QMSLastHeartbeatTime.Online || 
                    request.FilterLastHeartbeatTime == (byte)QMSLastHeartbeatTime.Offline)
                {
                    var rawBranchIds = await queryTablets.Select(p => p.BranchId).Distinct().ToListAsync();

                    var branchIds = rawBranchIds.Where(id => id.IsNumericAndGreaterThenZero()).ToList();

                    var allBranchesOnlineTablets = new List<RedisActiveDevice>();

                    foreach (var branchId in branchIds)
                    {
                        var branchOnlineTablets = await _redisClient.GetListAsync<RedisActiveDevice>($"branch:{branchId}:active-devices");

                        allBranchesOnlineTablets.AddRange(branchOnlineTablets);
                    }

                    var onlineDeviceIds = allBranchesOnlineTablets.Select(q => q.DeviceId).ToList();

                    queryTablets = request.FilterLastHeartbeatTime.GetValueOrDefault() == (byte)QMSLastHeartbeatTime.Online
                        ? queryTablets.Where(p => onlineDeviceIds.Contains(p.DeviceId))
                        : queryTablets.Where(p => !onlineDeviceIds.Contains(p.DeviceId));
                }

                List<DigitalSignatureDevice> tablets;

                if (request.Pagination != null && request.Pagination.Page > 0 && request.Pagination.PageSize > 0)
                {
                    tablets = await queryTablets
                        .OrderByDescending(o => o.Id)
                        .Skip((request.Pagination.Page - 1) * request.Pagination.PageSize)
                        .Take(request.Pagination.PageSize)
                        .AsNoTracking().ToListAsync();
                }
                else
                {
                    tablets = await queryTablets
                        .OrderByDescending(o => o.Id)
                        .AsNoTracking().ToListAsync();
                }

                if (tablets == null)
                    throw new NoFoundDataPortalException($"{nameof(SiteResources.Exception_NoRecordsFound).ToSiteResourcesValue(request.LanguageId)}");

                paginationResult.TotalItemCount = queryTablets.Count();

                var tabletListDto = new PaginatedTabletsResponseDto
                {
                    Tablets = tablets.Select(p => new TabletResponseDto
                    {
                        Id = p.Id,
                        Name = p.Name,
                        Version = p.Version,
                        BranchId = p.BranchId,
                        DeviceId = p.DeviceId
                    }).ToList()
                };

                paginationResult.Items.Add(tabletListDto);

                return paginationResult;
            }
            catch (Exception ex)
            {
                Logger.Error(ex.Message);

                throw;
            }
        }

        public async Task<UpdateTabletResponseDto> UpdateTabletAsync(UpdateTabletRequestDto request)
        {
            try
            {
                var existingTablet = await _unitOfWorkPortalDb.GetRepository<DigitalSignatureDevice>().Entities
                    .Where(p => p.IsActive && !p.IsDeleted && p.Id == request.Id)
                .FirstOrDefaultAsync();

                if (existingTablet == null)
                    throw new NoFoundDataPortalException($"{nameof(SiteResources.Exception_NoRecordsFound).ToSiteResourcesValue(request.LanguageId)} ({nameof(SiteResources.Tablet).ToSiteResourcesValue(request.LanguageId)})");

                if (existingTablet.BranchId != request.BranchId)
                {
                    var beforeConnectedTablets = await _redisClient.GetListAsync<RedisActiveDevice>($"branch:{existingTablet.BranchId}:active-devices");

                    if (beforeConnectedTablets != null && beforeConnectedTablets.Exists(p => p.DeviceId == existingTablet.DeviceId))
                    {
                        await _redisClient.RemoveFromListAsync($"branch:{existingTablet.BranchId}:active-devices", beforeConnectedTablets.Find(p => p.DeviceId == existingTablet.DeviceId));

                        var connectionId = beforeConnectedTablets.Find(p => p.DeviceId == existingTablet.DeviceId)?.ConnectionId;

                        if(connectionId != null)
                        {
                            await _redisClient.DeleteAsync(connectionId);

                            await _redisClient.AddToListAsync($"branch:{request.BranchId}:active-devices", new RedisActiveDevice { DeviceId = existingTablet.DeviceId, ConnectionId = connectionId });

                            await _redisClient.HashSetAsync(connectionId, new KeyValuePair<object, object>("BranchId", request.BranchId), new KeyValuePair<object, object>("DeviceId", existingTablet.DeviceId));
                        }
                    }
                    else
                    {
                        var connectionId = await _redisClient.GetHashAsync(existingTablet.DeviceId, "ConnectionId");

                        if (connectionId != null) 
                        {
                            await _redisClient.AddToListAsync($"branch:{request.BranchId}:active-devices", new RedisActiveDevice { DeviceId = existingTablet.DeviceId, ConnectionId = connectionId });

                            await _redisClient.HashSetAsync(connectionId, new KeyValuePair<object, object>("BranchId", request.BranchId), new KeyValuePair<object, object>("DeviceId", existingTablet.DeviceId));

                            await _redisClient.DeleteAsync(connectionId);
                        }
                    }

                    await _redisClient.DeleteAsync(existingTablet.DeviceId);
                }

                existingTablet.Name = request.Name;
                existingTablet.BranchId = request.BranchId;

                _unitOfWorkPortalDb.GetRepository<DigitalSignatureDevice>().Update(existingTablet);
                await _unitOfWorkPortalDb.SaveChangesAsync();

                return new UpdateTabletResponseDto
                {
                    Result = true,
                    Name = existingTablet.Name,
                    DeviceId = existingTablet.DeviceId
                };
            }
            catch (Exception ex)
            {
                Logger.Error(ex.Message);

                return new UpdateTabletResponseDto
                {
                    Result = false,
                    Message = $"TabletId: {request.Id}, Error Message:{ex.Message}"
                };
            }
        }

        public async Task<DeleteResponseDto> DeleteTabletAsync(int id)
        {
            try
            {
                var existingTablet = await _unitOfWorkPortalDb.GetRepository<DigitalSignatureDevice>().Entities
                        .Where(p => p.IsActive && !p.IsDeleted && p.Id == id)
                    .FirstOrDefaultAsync();

                if (existingTablet == null)
                    throw new NoFoundDataPortalException($"{nameof(SiteResources.Exception_NoRecordsFound).ToSiteResourcesValue()} ({nameof(SiteResources.Tablet).ToSiteResourcesValue()})");

                if (existingTablet.BranchId != 0)
                {
                    var beforeConnectedTablets = await _redisClient.GetListAsync<RedisActiveDevice>($"branch:{existingTablet.BranchId}:active-devices");

                    await _redisClient.RemoveFromListAsync($"branch:{existingTablet.BranchId}:active-devices", beforeConnectedTablets.Find(p => p.DeviceId == existingTablet.DeviceId));

                    var connectionId = beforeConnectedTablets.Find(p => p.DeviceId == existingTablet.DeviceId)?.ConnectionId;

                    if(connectionId != null)
                    {
                        await _redisClient.DeleteAsync(connectionId);
                    }
                }
                else
                {
                    var connectionId = await _redisClient.GetHashAsync(existingTablet.DeviceId, "ConnectionId");

                    if (connectionId != null)
                    {
                        await _redisClient.DeleteAsync(connectionId);
                    }
                }

                _unitOfWorkPortalDb.GetRepository<DigitalSignatureDevice>().MarkAsDeleted(existingTablet.Id);
                await _unitOfWorkPortalDb.SaveChangesAsync();

                return new DeleteResponseDto { Result = true };
            }
            catch (Exception ex)
            {
                Logger.Error(ex.Message);

                throw new Exception($"TabletId: {id}, Error Message:{ex.Message}");
            }
        }
    }
}
