﻿<?xml version="1.0" encoding="utf-8"?>
<root>
  <!-- 
    Microsoft ResX Schema 
    
    Version 2.0
    
    The primary goals of this format is to allow a simple XML format 
    that is mostly human readable. The generation and parsing of the 
    various data types are done through the TypeConverter classes 
    associated with the data types.
    
    Example:
    
    ... ado.net/XML headers & schema ...
    <resheader name="resmimetype">text/microsoft-resx</resheader>
    <resheader name="version">2.0</resheader>
    <resheader name="reader">System.Resources.ResXResourceReader, System.Windows.Forms, ...</resheader>
    <resheader name="writer">System.Resources.ResXResourceWriter, System.Windows.Forms, ...</resheader>
    <data name="Name1"><value>this is my long string</value><comment>this is a comment</comment></data>
    <data name="Color1" type="System.Drawing.Color, System.Drawing">Blue</data>
    <data name="Bitmap1" mimetype="application/x-microsoft.net.object.binary.base64">
        <value>[base64 mime encoded serialized .NET Framework object]</value>
    </data>
    <data name="Icon1" type="System.Drawing.Icon, System.Drawing" mimetype="application/x-microsoft.net.object.bytearray.base64">
        <value>[base64 mime encoded string representing a byte array form of the .NET Framework object]</value>
        <comment>This is a comment</comment>
    </data>
                
    There are any number of "resheader" rows that contain simple 
    name/value pairs.
    
    Each data row contains a name, and value. The row also contains a 
    type or mimetype. Type corresponds to a .NET class that support 
    text/value conversion through the TypeConverter architecture. 
    Classes that don't support this are serialized and stored with the 
    mimetype set.
    
    The mimetype is used for serialized objects, and tells the 
    ResXResourceReader how to depersist the object. This is currently not 
    extensible. For a given mimetype the value must be set accordingly:
    
    Note - application/x-microsoft.net.object.binary.base64 is the format 
    that the ResXResourceWriter will generate, however the reader can 
    read any of the formats listed below.
    
    mimetype: application/x-microsoft.net.object.binary.base64
    value   : The object must be serialized with 
            : System.Runtime.Serialization.Formatters.Binary.BinaryFormatter
            : and then encoded with base64 encoding.
    
    mimetype: application/x-microsoft.net.object.soap.base64
    value   : The object must be serialized with 
            : System.Runtime.Serialization.Formatters.Soap.SoapFormatter
            : and then encoded with base64 encoding.

    mimetype: application/x-microsoft.net.object.bytearray.base64
    value   : The object must be serialized into a byte array 
            : using a System.ComponentModel.TypeConverter
            : and then encoded with base64 encoding.
    -->
  <xsd:schema id="root" xmlns="" xmlns:xsd="http://www.w3.org/2001/XMLSchema" xmlns:msdata="urn:schemas-microsoft-com:xml-msdata">
    <xsd:import namespace="http://www.w3.org/XML/1998/namespace" />
    <xsd:element name="root" msdata:IsDataSet="true">
      <xsd:complexType>
        <xsd:choice maxOccurs="unbounded">
          <xsd:element name="metadata">
            <xsd:complexType>
              <xsd:sequence>
                <xsd:element name="value" type="xsd:string" minOccurs="0" />
              </xsd:sequence>
              <xsd:attribute name="name" use="required" type="xsd:string" />
              <xsd:attribute name="type" type="xsd:string" />
              <xsd:attribute name="mimetype" type="xsd:string" />
              <xsd:attribute ref="xml:space" />
            </xsd:complexType>
          </xsd:element>
          <xsd:element name="assembly">
            <xsd:complexType>
              <xsd:attribute name="alias" type="xsd:string" />
              <xsd:attribute name="name" type="xsd:string" />
            </xsd:complexType>
          </xsd:element>
          <xsd:element name="data">
            <xsd:complexType>
              <xsd:sequence>
                <xsd:element name="value" type="xsd:string" minOccurs="0" msdata:Ordinal="1" />
                <xsd:element name="comment" type="xsd:string" minOccurs="0" msdata:Ordinal="2" />
              </xsd:sequence>
              <xsd:attribute name="name" type="xsd:string" use="required" msdata:Ordinal="1" />
              <xsd:attribute name="type" type="xsd:string" msdata:Ordinal="3" />
              <xsd:attribute name="mimetype" type="xsd:string" msdata:Ordinal="4" />
              <xsd:attribute ref="xml:space" />
            </xsd:complexType>
          </xsd:element>
          <xsd:element name="resheader">
            <xsd:complexType>
              <xsd:sequence>
                <xsd:element name="value" type="xsd:string" minOccurs="0" msdata:Ordinal="1" />
              </xsd:sequence>
              <xsd:attribute name="name" type="xsd:string" use="required" />
            </xsd:complexType>
          </xsd:element>
        </xsd:choice>
      </xsd:complexType>
    </xsd:element>
  </xsd:schema>
  <resheader name="resmimetype">
    <value>text/microsoft-resx</value>
  </resheader>
  <resheader name="version">
    <value>2.0</value>
  </resheader>
  <resheader name="reader">
    <value>System.Resources.ResXResourceReader, System.Windows.Forms, Version=4.0.0.0, Culture=neutral, PublicKeyToken=b77a5c561934e089</value>
  </resheader>
  <resheader name="writer">
    <value>System.Resources.ResXResourceWriter, System.Windows.Forms, Version=4.0.0.0, Culture=neutral, PublicKeyToken=b77a5c561934e089</value>
  </resheader>
  <data name="BRANCH_DEPARTMENT_NOT_FOUND" xml:space="preserve">
    <value>Şube Departmanı bulunamadı</value>
  </data>
  <data name="APPLICANT_NOT_FOUND" xml:space="preserve">
    <value>Başvuran bulunamadı</value>
  </data>
  <data name="APPOINTMENT_NOT_FOUND" xml:space="preserve">
    <value>Randevu bulunamadı</value>
  </data>
  <data name="BRANCH_NOT_FOUND" xml:space="preserve">
    <value>Şube bulunamadı</value>
  </data>
  <data name="FAILED" xml:space="preserve">
    <value>FAİLED</value>
  </data>
  <data name="FAMILY_APPLICANT_COUNT_ERROR" xml:space="preserve">
    <value>Aile başvuran sayısı birden fazla olmalıdır</value>
  </data>
  <data name="GROUP_APPLICANT_COUNT_ERROR" xml:space="preserve">
    <value>Grup başvuran sayısı birden fazla olmalıdır</value>
  </data>
  <data name="INPUT_ERROR" xml:space="preserve">
    <value>INPUT_ERROR</value>
  </data>
  <data name="INTERNAL_SERVER_ERROR" xml:space="preserve">
    <value>INTERNAL_SERVER_ERROR</value>
  </data>
  <data name="INVALID_INPUT_ERROR" xml:space="preserve">
    <value>Geçersiz istek parametresi</value>
  </data>
  <data name="INVALID_REQUEST" xml:space="preserve">
    <value>Geçersiz istek</value>
  </data>
  <data name="LINE_NOT_FOUND" xml:space="preserve">
    <value>Hat bulunamadı</value>
  </data>
  <data name="PROPERTY_MAX_LENGTH_ERROR" xml:space="preserve">
    <value>{0} özelliği {1} karakterden fazla olamaz</value>
  </data>
  <data name="PROPERTY_REQUIRED" xml:space="preserve">
    <value>{0} gerekli</value>
  </data>
  <data name="RESOURCE_ALREADY_REGISTERED" xml:space="preserve">
    <value>Önceden kaydedilmiş</value>
  </data>
  <data name="RESOURCE_CREATED" xml:space="preserve">
    <value>Kayıt oluşturuldu</value>
  </data>
  <data name="RESOURCE_DELETED" xml:space="preserve">
    <value>Kayıt silindi</value>
  </data>
  <data name="RESOURCE_FOUND" xml:space="preserve">
    <value>Kayıt bulundu</value>
  </data>
  <data name="RESOURCE_NOT_FOUND" xml:space="preserve">
    <value>Kayıt bulunamadı</value>
  </data>
  <data name="RESOURCE_RETRIEVED" xml:space="preserve">
    <value>Kayıt getirildi</value>
  </data>
  <data name="RESOURCE_UPDATED" xml:space="preserve">
    <value>Kayıt güncellendi</value>
  </data>
  <data name="SUCCESS" xml:space="preserve">
    <value>SUCCESS</value>
  </data>
  <data name="LINE_DEPARTMENT_NOT_FOUND" xml:space="preserve">
    <value>Hat departmanı bulunamadı</value>
  </data>
  <data name="GENERATED_TOKEN_NOT_FOUND" xml:space="preserve">
    <value>Yaratılmış sıra numarası bulunamadı</value>
  </data>
  <data name="TOKEN_NOT_FOUND" xml:space="preserve">
    <value>Sıra numarası bulunamadı</value>
  </data>
  <data name="APPLICANT_IN_BLACKLIST" xml:space="preserve">
    <value>başvuran kara liste içinde</value>
  </data>
  <data name="APPLICANT_IN_WHITELIST" xml:space="preserve">
    <value>başvuran beyaz liste içinde</value>
  </data>
  <data name="BRANCH_APPLICATION_COUNTRY_NOT_FOUND" xml:space="preserve">
    <value>Şubeden başvurulack ülke bulunamadı</value>
  </data>
  <data name="BRANCH_TRANSLATION_NOT_FOUND" xml:space="preserve">
    <value>Şube çevirisi bulunamadı</value>
  </data>
  <data name="DEPARTMENT_NOT_FOUND" xml:space="preserve">
    <value>Departman bulunamadı</value>
  </data>
  <data name="ORDER_ALREADY_REGISTERED" xml:space="preserve">
    <value>önceden aynı sırada kaydedilmiş kayıt bulunmakta</value>
  </data>
  <data name="GENERATED_TOKEN_NOTES_NOT_FOUND" xml:space="preserve">
    <value>Sıra numarası notu bulunamadı</value>
  </data>
  <data name="USER_NOT_FOUND" xml:space="preserve">
    <value>Kullanıcı bilgileri bulunamadı</value>
  </data>
  <data name="COUNTER_NOT_FOUND" xml:space="preserve">
    <value>Kontuar bulunamadı</value>
  </data>
  <data name="APPLICANT_HAS_TICKET_NOT_FOUND" xml:space="preserve">
    <value>Bilete sahip başvuran bulunamadı</value>
  </data>
  <data name="SLOT_NOT_FOUND" xml:space="preserve">
    <value>Slot bulunamadı</value>
  </data>
  <data name="AVAILABLE_QUOTA_NOT_FOUND" xml:space="preserve">
    <value>Boş kota bulunamadı</value>
  </data>
  <data name="ThePassportHasNotYetBeenReceivedAtTheOffice" xml:space="preserve">
    <value>Pasaport başvurana önceden teslim edilmiştir</value>
  </data>
  <data name="COUNTER_BUSY" xml:space="preserve">
    <value>Kontuar meşgul</value>
  </data>
  <data name="ThereIsNoPersonWaitingInTheSelectedDepartment" xml:space="preserve">
    <value>Seçilen departmanda bekleyen kimse bulunmamaktadır</value>
  </data>
  <data name="VipAppointmentTicketsCanOnlyBeGetFromTheVipLine" xml:space="preserve">
    <value>VIP randevu biletleri sadece VIP hattından alınabilir</value>
  </data>
  <data name="GENERATED_TOKEN_HISTORY_NOT_FOUND" xml:space="preserve">
    <value>Yaratılmış sıra numarası geçmişi bulunamadı</value>
  </data>
  <data name="ALL_TICKETS_HAS_HISTORY" xml:space="preserve">
    <value>Tüm biletler qms içinde kullanılmış</value>
  </data>
  <data name="ThereIsNoPersonWaitingOnTheSelectedLine" xml:space="preserve">
    <value>Seçilen hatta bekleyen kimse yok</value>
  </data>
  <data name="SearchOperationNotValidForThisLineDepartment" xml:space="preserve">
    <value>Arama işlemi bu hat departmanı için yetkilendirilmedi</value>
  </data>
  <data name="AlreadyDefinedBefore" xml:space="preserve">
    <value>Daha önceden tanımlı</value>
  </data>
  <data name="InvalidApplicantType" xml:space="preserve">
    <value>Geçersiz başvuran türü</value>
  </data>
  <data name="InvalidEmailFormat" xml:space="preserve">
    <value>Geçersiz mail formatı</value>
  </data>
  <data name="Invalid" xml:space="preserve">
    <value>Geçersiz</value>
  </data>
  <data name="ApplicantType" xml:space="preserve">
    <value>Başvuran türü</value>
  </data>
  <data name="AppointmentDate" xml:space="preserve">
    <value>Randevu zamanı</value>
  </data>
  <data name="BranchName" xml:space="preserve">
    <value>Şube adı</value>
  </data>
  <data name="FirstName" xml:space="preserve">
    <value>Adı</value>
  </data>
  <data name="Gender" xml:space="preserve">
    <value>Cinsiyet</value>
  </data>
  <data name="LastName" xml:space="preserve">
    <value>Soyadı</value>
  </data>
  <data name="Nationality" xml:space="preserve">
    <value>Uyruk</value>
  </data>
  <data name="PassportNumber" xml:space="preserve">
    <value>Pasaport numarası</value>
  </data>
  <data name="AppointmentDateNotForToday" xml:space="preserve">
    <value>Randevu bugüne ait değil</value>
  </data>
  <data name="ReferanceNumber" xml:space="preserve">
    <value>Referans numarası</value>
  </data>
  <data name="BirthDate" xml:space="preserve">
    <value>Doğum tarihi</value>
  </data>
  <data name="PassportExpiryDate" xml:space="preserve">
    <value>Pasaport bitiş tarihi</value>
  </data>
  <data name="OngoingProcess" xml:space="preserve">
    <value>Devam Eden İşlem</value>
  </data>
  <data name="PleaseTryAgain" xml:space="preserve">
    <value>Lütfen tekrar deneyin</value>
  </data>
  <data name="Prime" xml:space="preserve">
    <value>Prime</value>
  </data>
  <data name="Vip" xml:space="preserve">
    <value>Vip</value>
  </data>
  <data name="SAME_PROCESS_COUNTER_NOT_FOUND" xml:space="preserve">
    <value>Line toplu işlem bilgisi bulunamadı</value>
  </data>
  <data name="EarlyAppointmentProcess" xml:space="preserve">
    <value>Randevu tarihi henüz gelmemiştir. Beklenen randevu tarihi:</value>
  </data>
  <data name="LINE_DEPARTMENT_CONNECTION_LIMIT" xml:space="preserve">
    <value>Bağlantı limiti 15 den fazla olamaz</value>
  </data>
  <data name="USER_BUSY" xml:space="preserve">
    <value>Kullanıcı meşgul</value>
  </data>
  <data name="EndwithCannotBeSmallerThanStartwith" xml:space="preserve">
    <value>Endwith startwith'den daha küçük olamaz</value>
  </data>
  <data name="TokenNumberProcessNotFound" xml:space="preserve">
    <value>Token numarası işlemi bulunamadı</value>
  </data>
  <data name="TokenNumberHasReachedUpperLimit" xml:space="preserve">
    <value>Bilet numarası üst limite ulaştı</value>
  </data>
  <data name="ADDING_APPLICANT_WITH_SAME_APPOINTMENT_NUMBER_NOT_ALLOWED" xml:space="preserve">
    <value>Aynı randevu numaralı bir başvuranı tekrar aynı randevuya eklemeye izin verilmemektedir</value>
  </data>
  <data name="NO_PROPERTY_FOUND_TO_UPDATE" xml:space="preserve">
    <value>Güncellenebilecek bir alan bulunamadı</value>
  </data>
  <data name="APPLICATION_NOT_FOUND" xml:space="preserve">
    <value>Başvuru bulunamadı</value>
  </data>
  <data name="PRE_CONDITION_FAILED" xml:space="preserve">
    <value>Ön koşul başarısız</value>
  </data>
  <data name="WRONG_BRANCH_SELECTION" xml:space="preserve">
    <value>Başvuru ilgili şubeden farklı bir şubede bulunmaktadır</value>
  </data>
  <data name="INDIVIUAL_APPLICANT_COUNT_ERROR" xml:space="preserve">
    <value>Bireysel başvuran sayısı birden fazla olamaz</value>
  </data>
  <data name="TOKEN_ALREADY_ADDED_ON_ANOTHER_TOKEN" xml:space="preserve">
    <value>Bilet başka bir işleme eklenmiş</value>
  </data>
  <data name="USER_STATE_REFRESH_MESSAGE" xml:space="preserve">
    <value>Bilet başka bir kullanıcıda işlemdedir, lütfen yeni bilet ile devam edin</value>
  </data>
</root>