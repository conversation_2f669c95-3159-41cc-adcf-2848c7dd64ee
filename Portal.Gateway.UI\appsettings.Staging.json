{
  "AppSettings": {
    "PortalGatewayApiUrl": "https://sta-applb.gateway.com.tr",
    "PortalGatewayApiKey": "Gateway.ApiKey.2021",
    "AppointmentReturnUrl": "https://sta-turkeyvisa.gateway.com.tr/User/Login",
    "GoogleCloudProjectId": "skilful-firefly-315409",
    "GoogleCloudCredentialsUrl": "Credentials/GoogleCloudCredentials.json",
    "Notification": true,
    "QmsChatHubAddress": "https://gwdcstaging01.gateway.com.tr:4445/chathub",
    "PrintDataChatHubAddress": "https://sta-printdatalbpri.gateway.com.tr/digitalsignaturehub",
    "IcrReturnUrl": "https://gatewayinternational.com.tr",
    "PortalGatewayUiUrl": "https://sta-turkeyvisa.gateway.com.tr",
    "EnableQuarterCategoryStatsGraph": false,
    "EnableRejectedApplicationInsuranceCase": true,
    "MinioConfiguration": {
      "EndPoint": "visacdn.gateway.com.tr:443",
      "AccessKey": "eGBBZ2ZVzNWCFW5kvmKr",
      "SecretKey": "tnczwQAiKkNpEHIGwXwztZtFZCaT1i2JziBUZXY3",
      "BucketPrefix": "sta-"
    },
    "QMS": {
      "BaseApiUrl": "https://gwdcstaging01.gateway.com.tr:4445"
    },
    "Biometrics": {
      "BaseApiUrl": "http://***********:7095"
    },
    "Cargo": {
      "BaseApiUrl": "http://***********:6006"
    },
    "PrintData": {
      "BaseApiUrl": "https://sta-printdatalbpri.gateway.com.tr"
    }
  },
  "elasticsearch": {
    "logindex": "slog-portal",
    "index": "slog-portal",
    "username": "elastic",
    "password": "htJsew49I8jPiL6How7U",
    "url": "http://***********:9200"
  },
  "Logging": {
    "LogLevel": {
      "Default": "Information",
      "Microsoft": "Warning",
      "Microsoft.Hosting.Lifetime": "Information"
    }
  },
  "CacheSettings": {
    "ServiceCallInterval": 1, // minute(s)
    "CacheExpiryTime": 1, // day(s)
    "CacheItems": [
      {
        "CacheKey": "CountryCache",
        "CacheRefreshInterval": 1 // minute(s)
      },
      {
        "CacheKey": "RoleActionCache",
        "CacheRefreshInterval": 1 // minute(s)
      },
      {
        "CacheKey": "BranchCache",
        "CacheRefreshInterval": 1 // minute(s)
      },
      {
        "CacheKey": "ApplicationStatusOrderCache",
        "CacheRefreshInterval": 1 // minute(s)
      },
      {
        "CacheKey": "ForeignCityCache",
        "CacheRefreshInterval": 1 // minute(s)
      },
      {
        "CacheKey": "BranchApplicationStatusCache",
        "CacheRefreshInterval": 1 // minute(s)
      },
      {
        "CacheKey": "ActiveCountriesCache",
        "CacheRefreshInterval": 720 // minute(s)
      },
      {
        "CacheKey": "DepartmentsCache",
        "CacheRefreshInterval": 240 // minute(s)
      },
      {
        "CacheKey": "VisaCategoryCache",
        "CacheRefreshInterval": 30 // minute(s)
      }
    ]
  },
  "Redis": {
    "Url": "***********",
    "Port": "6379",
    "ConnectTimeout": 10000,
    "DefaultDatabase": 0
  },
  "RabbitMq": {
    "Host": "visauatrabbitmq.gateway.com.tr",
    "User": "staging_test",
    "Password": "stagin_test*4mqp",
    "Port": 5672,
    "Exchange": "log.exchange",
    "LoggerQueue": "LoggerQueue",
    "EmailQueue": "EmailQueue",
    "SMSQueue": "SMSQueue"
  },
  "Session": {
    "Distributed": true,
    "IdleTimeout": 1,
    "Authentication": "ldap"
  }
}
