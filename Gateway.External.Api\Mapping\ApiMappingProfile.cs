﻿using AutoMapper;
using Gateway.External.Api.Models.Appointment;
using Gateway.External.Api.Models.AppointmentDemand;
using Gateway.External.Api.Models.Company;
using Gateway.External.Api.Models.Customer;
using Gateway.External.Api.Models.Document;
using Gateway.External.Api.Models.Lookup;
using Gateway.External.Api.Models.Payment;
using Gateway.External.Api.Models.PhotoBooth;
using Gateway.External.Api.Models.Report;
using Gateway.External.Api.Models.Slot;
using Gateway.External.Api.Models.SlotDemand;
using Gateway.External.Application.Appointment.Dto;
using Gateway.External.Application.AppointmentDemand.Dto.Requests;
using Gateway.External.Application.Company.Dto.Requests;
using Gateway.External.Application.Customer.Dto;
using Gateway.External.Application.Document.Dto;
using Gateway.External.Application.Lookup.Dto;
using Gateway.External.Application.Payment.Dto.Requests;
using Gateway.External.Application.PhotoBooth.Dto;
using Gateway.External.Application.Report.Dto.Requests;
using Gateway.External.Application.SenCard.Dto;
using Gateway.External.Application.SenCard.Dto.External;
using Gateway.External.Application.Slot.Dto;
using Gateway.External.Application.SlotDemand.Dto.Request;
using Buyer = Gateway.External.Api.Models.Payment.Buyer;
using Card = Gateway.External.Api.Models.Payment.Card;
using Item = Gateway.External.Api.Models.Payment.Item;

namespace Gateway.External.Api.Mapping
{
    public class ApiMappingProfile : Profile
    {
        public ApiMappingProfile()
        {
            CreateMap<CreateAppointmentRequestModel, CreateAppointmentRequest>().ReverseMap();
            CreateMap<UpdateAppointmentRequestModel, UpdateAppointmentRequest>().ReverseMap();
            CreateMap<Models.Appointment.AppointmentApplicant, Application.Appointment.Dto.AppointmentApplicant>().ReverseMap();
            CreateMap<DeleteAppointmentRequestModel, DeleteAppointmentRequest>().ReverseMap();
            CreateMap<DeleteAppointmentApplicantRequestModel, DeleteAppointmentApplicantRequest>().ReverseMap();
            CreateMap<GetPaginatedAppointmentsRequestModel, GetPaginatedAppointmentsRequest>().ReverseMap();
            CreateMap<GetPaginatedAppointmentCollectionsRequestModel, GetPaginatedAppointmentCollectionsRequest>().ReverseMap();
            CreateMap<GetSlotsRequestModel, GetSlotsRequest>().ReverseMap();
            CreateMap<GetLookupRequestModel, GetLookupRequest>().ReverseMap();

            CreateMap<UpdateAppointmentSlotTodayRequestModel, UpdateAppointmentSlotTodayRequest>().ReverseMap();

            CreateMap<ValidateApplicantInformationRequestModel, ValidateApplicantInformationRequest>().ReverseMap();
            CreateMap<ValidateApplicantInformationRequestModel.ApplicantInformationValidationDto, ValidateApplicantInformationRequest.ApplicantInformationValidationDto>().ReverseMap();

            CreateMap<ValidateAppointmentRequestModel, ValidateAppointmentRequest>().ReverseMap();
            CreateMap<ValidateAppointmentRequestModel.ApplicantValidationDto, ValidateAppointmentRequest.ApplicantValidationDto>().ReverseMap();

            CreateMap<AddDocumentRequestModel, AddDocumentRequest>().ReverseMap();

            CreateMap<AddCustomerRequestModel, AddCustomerRequest>().ReverseMap();
            CreateMap<GetPaginatedCustomersRequestModel, GetPaginatedCustomersRequest>().ReverseMap();
            CreateMap<Models.Appointment.AppointmentFilters, Application.Appointment.Dto.AppointmentFilters>().ReverseMap();
			CreateMap<PhotoBoothModel, UpdateApplicationPhotoBoothStatusRequest>().ReverseMap();

			#region B2B 

			CreateMap<AddCompanyRequestModel, AddCompanyRequest>().ReverseMap();
            CreateMap<AddCompanyUserRequestModel, AddCompanyUserRequest>().ReverseMap();
            CreateMap<AddCompanyUserMessageRequestModel, AddCompanyUserMessageRequest>().ReverseMap();
            CreateMap<UpdateCompanyUserRequestModel, UpdateCompanyUserRequest>().ReverseMap();
            CreateMap<ChangePasswordRequestModel, ChangePasswordRequest>().ReverseMap();
            CreateMap<LoginRequestModel, LoginRequest>().ReverseMap();
            CreateMap<ForgotPasswordRequestModel, ForgotPasswordRequest>().ReverseMap();
            CreateMap<ResetPasswordRequestModel, ResetPasswordRequest>().ReverseMap();
            CreateMap<AddCompanyProfileApprovalRequestModel, AddCompanyProfileApprovalRequest>().ReverseMap();
            CreateMap<GetPaginatedCompanyUsersRequestModel, GetPaginatedCompanyUsersRequest>().ReverseMap();
            CreateMap<ChangeUserProfilePictureRequestModel, ChangeUserProfilePictureRequest>().ReverseMap();
            CreateMap<ConfirmEmailRequestModel, ConfirmEmailRequest>().ReverseMap();
			CreateMap<GetReportRequestModel, GetReportRequest>().ReverseMap();
			CreateMap<AddSlotDemandRequestModel, AddSlotDemandRequest>().ReverseMap();
			CreateMap<GetPaginatedSlotDemandsRequestModel, GetPaginatedSlotDemandsRequest>().ReverseMap();
			CreateMap<AddAppointmentDemandRequestModel, AddAppointmentDemandRequest>().ReverseMap();
			CreateMap<UpdateAppointmentDemandRequestModel, UpdateAppointmentDemandRequest>().ReverseMap();
			CreateMap<Models.AppointmentDemand.AppointmentApplicantDemand, Gateway.External.Application.AppointmentDemand.Dto.Requests.AppointmentApplicantDemand>().ReverseMap();
			CreateMap<GetPaginatedAppointmentDemandsRequestModel, GetPaginatedAppointmentDemandsRequest>().ReverseMap();
			CreateMap<CompleteCompanyPaymentOrderRequestModel, CompleteCompanyPaymentOrderRequest>().ReverseMap();
			CreateMap<GetCompanySlotsRequestModel, GetCompanySlotsRequest>().ReverseMap();
			CreateMap<GetPaginatedCompanyApplicationsRequestModel, GetPaginatedCompanyApplicationsRequest>().ReverseMap();

			#endregion

			CreateMap<Payment3dRequestModel, Payment3dRequest>().ReverseMap();
			CreateMap<BinInfoRequestModel, BinInfoRequest>().ReverseMap();
			CreateMap<Card, Gateway.External.Application.Payment.Dto.Requests.Card>().ReverseMap();
			CreateMap<Buyer, Gateway.External.Application.Payment.Dto.Requests.Buyer>().ReverseMap();
			CreateMap<Item, Gateway.External.Application.Payment.Dto.Requests.Item>().ReverseMap();


			CreateMap<SenCardOrganizationServiceResponse, SenCardOrganizationDto>().ReverseMap();
		}
    }
}
