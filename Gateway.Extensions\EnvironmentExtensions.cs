﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Net.Http.Headers;
using System.Text;
using System.Threading.Tasks;

namespace Gateway.Extensions
{
    public static class EnvironmentExtensions
    {
        public static string AddEnvironmentSuffix(this string value)
        {
            var environmentName = (Environment.GetEnvironmentVariable("ASPNETCORE_ENVIRONMENT")
                ?? Environment.GetEnvironmentVariable("DOTNET_ENVIRONMENT")).ToLower();

            if (environmentName == "production" || environmentName == "production-k8s")
            {
                return value;
            }

            return value + "." + environmentName;
        }
    }
}
