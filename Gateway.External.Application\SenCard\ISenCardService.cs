﻿using Gateway.External.Application.SenCard.Dto.Request;
using Gateway.External.Application.SenCard.Dto.Response;
using System.Threading.Tasks;

namespace Gateway.External.Application.SenCard
{
    public interface ISenCardService
    {
        Task<GetSenCardProvinceResult> GetSenCardProvince(SenCardProvinceRequest request);
        Task<GetSenCardInstitutionResult> GetSenCardInstitution(SenCardProvinceRequest request);
        Task<GetSenCardDistrictResult> GetSenCardDistrict(SenCardDistrictRequest request);
        Task<GetSenCardOrganizationResult> GetSenCardOrganization(SenCardOrganizationRequest request);
    }
}
