﻿namespace Gateway.External.Application.Country.Dto
{
    public class GetCountriesRequest:BaseServiceRequest
    {
        public bool To { get; set; }
        public bool From { get; set; }
        public bool Residence { get; set; }
    }

    public class GetCountryRequest : BaseServiceRequest
    {
        public int ResourceId { get; set; }
    }

    public class GetCountryVisaInformationRequest : BaseServiceRequest
    {
        public int ResourceId { get; set; }
    }

    public class GetCityByCountryRequest : BaseServiceRequest
    {
	    public int ResourceId { get; set; }
    }

    public class GetChecklistByCountryRequest : BaseServiceRequest
    {
        public int ResourceId { get; set; }
    }

    public class GetVasTypeMessageByCountryRequest : BaseServiceRequest
    {
	    public int CountryId { get; set; }
	    public int VasTypeId { get; set; }
    }
}
