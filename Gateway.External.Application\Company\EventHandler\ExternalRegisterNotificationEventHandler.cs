﻿using Gateway.EventBus.Publishers;
using Gateway.External.Application.Notification;
using Gateway.External.Application.Notification.Events;
using System;
using System.Collections.Generic;
using System.Threading.Tasks;
using Gateway.External.Application.Notification.Requests;
using Gateway.External.Resources;
using System.Linq;
using Gateway.External.Application.Extensions;
using Microsoft.Extensions.Configuration;
using Gateway.Extensions;

namespace Gateway.External.Application.Company.EventHandler
{
	public class ExternalRegisterNotificationEventHandler : INotificationEventHandler<ExternalRegisterSendNotificationEvent>
	{
		private readonly IMessagePublisher _messagePublisher;
        private readonly string _queueName;

        public ExternalRegisterNotificationEventHandler(IMessagePublisher messagePublisher, IConfiguration configuration)
		{
			_messagePublisher = messagePublisher;
            _queueName = configuration["RabbitMq:EmailQueue"].AddEnvironmentSuffix();
        }

        public async Task Handle(ExternalRegisterSendNotificationEvent args)
		{
			await _messagePublisher.PublishAsync(_queueName, new SendEmailRequest
			{
				TransactionId = args.UserId,
				To = args.Email,
				Content = DefineTextWithFooter(ServiceResources.EXTERNAL_REGISTER_NOTIFICATION.Replace("[PASSWORD]", args.Password), args.LanguageId),
				From = "<EMAIL>",
				Subject = "Gateway Register",
				MailType = 0,
				SendDate = DateTime.Now,
				ProviderId = 1 //sendGrid
			}, false, 0);
		}

		private static string DefineTextWithFooter(string message, int footerLanguageId)
		{
			return footerLanguageId switch
			{
				1 => message + " " + nameof(ServiceResources.MAIL_FOOTER).ToSiteResourcesValue(1),//TR
				2 => message + " " + nameof(ServiceResources.MAIL_FOOTER).ToSiteResourcesValue(),//EN
				_ => message
			};
		}
	}
}
