﻿{
  "iisSettings": {
    "windowsAuthentication": false,
    "anonymousAuthentication": true,
    "iisExpress": {
      "applicationUrl": "http://localhost:58169",
      "sslPort": 44371
    }
  },
    "profiles": {
        "Portal.Gateway.UI-Local": {
            "commandName": "Project",
            "launchBrowser": true,
            "environmentVariables": {
                "ASPNETCORE_ENVIRONMENT": "Local",
                "COMPlus_ThreadPool_ForceMinWorkerThreads": "100"
            },
            "applicationUrl": "http://localhost:5600"
        },
        "Portal.Gateway.UI-Development-K8S": {
            "commandName": "Project",
            "launchBrowser": true,
            "environmentVariables": {
                "ASPNETCORE_ENVIRONMENT": "Development-K8S",
                "COMPlus_ThreadPool_ForceMinWorkerThreads": "100"
            },
            "applicationUrl": "http://localhost:5600"
        },
        "Portal.Gateway.UI-Staging": {
            "commandName": "Project",
            "launchBrowser": true,
            "environmentVariables": {
                "ASPNETCORE_ENVIRONMENT": "Staging",
                "COMPlus_ThreadPool_ForceMinWorkerThreads": "100"
            },
            "applicationUrl": "http://localhost:5600"
        },
        "Portal.Gateway.UI-Production": {
            "commandName": "Project",
            "launchBrowser": true,
            "environmentVariables": {
                "ASPNETCORE_ENVIRONMENT": "Production",
                "COMPlus_ThreadPool_ForceMinWorkerThreads": "100"
            },
            "applicationUrl": "http://localhost:5600"
        }
    }
}
