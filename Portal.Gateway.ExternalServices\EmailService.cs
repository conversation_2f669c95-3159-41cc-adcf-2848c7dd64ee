﻿using Gateway.EventBus.Publishers;
using Microsoft.Extensions.Options;
using Portal.Gateway.Common.Utility.Extensions;
using Portal.Gateway.Contracts.Extensions;
using Portal.Gateway.Contracts.Settings.AppSettings;
using Portal.Gateway.ExternalServices.Common;
using Portal.Gateway.ExternalServices.Contracts;
using Portal.Gateway.ExternalServices.Models;
using Portal.Gateway.Resources;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Threading.Tasks;
using Portal.Gateway.Contracts.Entities.Dto.Notification;
using Microsoft.Extensions.Configuration;
using Gateway.Extensions;

namespace Portal.Gateway.ExternalServices
{
    public class EmailService : BaseExternalService, IEmailService
    {
        private readonly IntegrationSettings _integrationSettings;
        private readonly IMessagePublisher _messagePublisher;
        private readonly string _queueName;

        public EmailService(
            IOptions<IntegrationSettings> integrationSettings,
            IMessagePublisher messagePublisher,
            IConfiguration configuration
        )
        {
            _integrationSettings = integrationSettings.Value;
            _messagePublisher = messagePublisher;
            _queueName = configuration["RabbitMq:EmailQueue"].AddEnvironmentSuffix();
        }

        #region Public Methods

        public async Task SendEmail(List<SendNotificationServiceRequest.Notification> request)
        {
            try
            {
                await request.ForEachAsync(async p =>
                {
                    var mailTo = p.NotificationType != (int)NotificationType.Esim ? p.ApplicantList.DistinctBy(r => r.Contact).Select(r => new SendNotificationServiceRequest.Applicant
                    {
                        Contact = r.Contact,
                        ProviderId = r.ProviderId,
                        Sender = r.Sender,
                        IndividualAttachment = r.IndividualAttachment
                    }).ToList() : p.ApplicantList.Select(r => new SendNotificationServiceRequest.Applicant
                    {
                        Contact = r.Contact,
                        ProviderId = r.ProviderId,
                        Sender = r.Sender,
                        IndividualAttachment = r.IndividualAttachment
                    }).ToList();

                    for (var i = 0; i < mailTo.Count; i++)
                    {
                        var message = new SendEmailRequest
                        {
                            TransactionId = p.TransactionId,
                            To = mailTo[i].Contact,
                            Content = DefineTextWithFooter(p.Text, p.FooterLanguageId),
                            From = _integrationSettings.Email.SendGrid.Sender,
                            Subject = p.Subject,
                            Attachments = mailTo[i].IndividualAttachment != null && mailTo[i].IndividualAttachment.Any()
                                ? mailTo[i].IndividualAttachment.Select(q => new Attachment
                                {
                                    Content = Convert.ToBase64String(q),
                                    FileName = $"{NotificationType.Esim.GetDescription()}.png"
                                }).ToList()
                                : p.Attaches?.ToList(),
                            MailType = p.NotificationType,
                            SendDate = DateTime.Now,
                            ProviderId = mailTo[i].ProviderId
                        };

                        await _messagePublisher.PublishAsync(_queueName, message,false,0);
                    }
                });
            }
            catch (Exception ex)
            {
                //ignore
            }
        }

        #endregion

        #region Private Methods

        private static string DefineTextWithFooter(string message, int? footerLanguageId)
        {
            return footerLanguageId switch
            {
                1 => message + " " + nameof(SiteResources.MailFooter).ToSiteResourcesValue(1),
                2 => message + " " + nameof(SiteResources.MailFooter).ToSiteResourcesValue(2),
                _ => message
            };
        }

        #endregion

    }
}
