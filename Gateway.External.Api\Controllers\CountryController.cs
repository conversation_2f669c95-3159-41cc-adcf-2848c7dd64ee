﻿using AutoMapper;
using Gateway.Extensions;
using Gateway.External.Api.Factories.ResponseFactory;
using Gateway.External.Api.Models;
using Gateway.External.Api.Models.Country;
using Gateway.External.Api.Models.Lookup;
using Gateway.External.Application.Country;
using Gateway.External.Application.Country.Dto;
using Gateway.External.Application.Lookup.Dto;
using Gateway.External.Core.Context;
using Gateway.External.Resources;
using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Mvc;
using Swashbuckle.AspNetCore.Annotations;
using System.Threading.Tasks;

namespace Gateway.External.Api.Controllers
{
	[Authorize]
    [Route("api")]
    [ApiController]
    public class CountryController : Controller
    {
        private readonly ICountryService _countryService;
        private readonly IContext _context;
        private readonly IMapper _mapper;

        #region ctor

        public CountryController(IMapper mapper,ICountryService countryService, IContext context)
        {
            _countryService = countryService;
            _context = context;
            _mapper = mapper;
        }

        #endregion

        #region Public Methods

        /// <summary>
        /// Gets Countries
        /// </summary>
        [SwaggerOperation(Summary = "Get Countries with given parameters (from,to,residence) or empty", 
            Description = "Get Countries with given parameters (from,to,residence) or empty")]
        [HttpGet]
        [Route("countries")]
        public async Task<IActionResult> GetCountries([FromQuery] bool to, [FromQuery] bool from, [FromQuery] bool residence)
        {
            var request = new GetCountriesRequest
            {
                Context = _context,
                To = to,
                From = from,
                Residence = residence
            };

            var result = await _countryService.GetCountries(request);

            return CountryResponseFactory.CountriesResponse(result);
        }


        /// <summary>
        /// Gets a country with given id
        /// </summary>
        [SwaggerOperation(Summary = "Gets a country with given id", 
            Description = "Gets a country with given id")]
        [HttpGet]
        [Route("countries/{resourceId?}")]
        public async Task<IActionResult> GetCountry(int resourceId)
        {
            if (!resourceId.IsNumericAndGreaterThenZero())
                return BadRequest(ApiResponseFactory.BuildBaseApiResponse(ServiceResources.INPUT_ERROR,
                    Resource.GetKey(ServiceResources.INVALID_REQUEST),
                    ServiceResources.INVALID_REQUEST));

            var request = new GetCountryRequest
            {
                Context = _context,
                ResourceId = resourceId
            };

            var result = await _countryService.GetCountry(request);

            return CountryResponseFactory.CountryResponse(result);
        }

        /// <summary>
        /// Get a list of branches with related of given country id
        /// </summary>
        /// <param name="countryId"></param>  
        [SwaggerOperation(Summary = "Get a list of branches with related of given country id",
	        Description = "Get a list of branches with related of given country id")]
        [HttpGet]
        [Route("countries/{countryId?}/branches")]
        public async Task<IActionResult> GetCountryBranches(int countryId)
        {
	        var serviceRequest = new BranchesByCountryRequest
	        {
		        Context = _context,
		        CountryId = countryId
	        };
	        serviceRequest.Context = _context;

	        var result = await _countryService.GetBranchesByCountry(serviceRequest);

	        return CountryResponseFactory.BranchesByCountryResponse(result);
        }

		/// <summary>
		///  Retrieves a list of lookup values by the given parameter by country
		/// </summary>
		/// <param name="request"></param>
		/// <param name="resourceId"></param>      
		[SwaggerOperation(Summary = "Retrieves a list of lookup values by country",
	        Description = "Retrieves a list of lookup values by country")]
        [HttpPost]
        [Route("countries/{resourceId?}/lookup")]
        public async Task<IActionResult> GetCountryLookups(GetLookupRequestModel request, int resourceId)
        {
	        if (request == null)
		        return CountryResponseFactory.GetCountryLookupsResponse(new GetLookupResult()
		        {
			        Status = GetLookupStatus.BadRequest,
			        Message = ServiceResources.INVALID_REQUEST
		        });

	        var serviceRequest = _mapper.Map<GetLookupRequest>(request);
	        serviceRequest.CountryId = resourceId;
	        serviceRequest.Context = _context;

	        var result = await _countryService.GetLookupValueByCountry(serviceRequest);

	        return CountryResponseFactory.GetCountryLookupsResponse(result);
        }

		/// <summary>
		/// Gets a country with given id
		/// </summary>
		[SwaggerOperation(Summary = "Gets a country with given id",
            Description = "Gets a country with given id")]
        [HttpGet]
        [Route("countries/{resourceId?}/visainformation")]
        public async Task<IActionResult> GetCountryVisaInformation(int resourceId)
        {
            if (!resourceId.IsNumericAndGreaterThenZero())
                return BadRequest(ApiResponseFactory.BuildBaseApiResponse(ServiceResources.INPUT_ERROR,
                    Resource.GetKey(ServiceResources.INVALID_REQUEST),
                    ServiceResources.INVALID_REQUEST));

            var request = new GetCountryVisaInformationRequest()
            {
                Context = _context,
                ResourceId = resourceId
            };

            var result = await _countryService.GetCountryVisaInformation(request);

            return CountryResponseFactory.GetCountryVisaInformationResponse(result);
        }

		/// <summary>
		/// Get foreign cities with given country id
		/// </summary>
		[SwaggerOperation(Summary = "Get cities with given country id",
	        Description = "Get cities with given country id")]
        [HttpGet]
        [Route("countries/{resourceId?}/cities")]
        public async Task<IActionResult> GetCityByCountry(int resourceId)
        {
	        var request = new GetCityByCountryRequest()
	        {
		        Context = _context,
		        ResourceId = resourceId
	        };

	        var result = await _countryService.GetCityByCountry(request);

	        return BaseResponseFactory.CreateResponse(result, result.Cities);
		}

		/// <summary>
		/// Get Vas Type Message By Country
		/// </summary>
		[SwaggerOperation(Summary = "Get Vas Type Message By Country",
			Description = "Get Vas Type Message By Country")]
		[HttpGet]
		[Route("countries/{countryId?}/vastype/{vasTypeId?}/message")]
		[Authorize]
		public async Task<IActionResult> GetVasTypeMessageByCountry(int countryId, int vasTypeId)
		{
			var serviceRequest = new GetVasTypeMessageByCountryRequest()
			{
				Context = _context,
                CountryId = countryId,
                VasTypeId = vasTypeId
			};

			var result = await _countryService.GetVasTypeMessageByCountry(serviceRequest);

			return BaseResponseFactory.CreateResponse(result, result.Messages);
		}

        /// <summary>
        /// Gets Countries
        /// </summary>
        [SwaggerOperation(Summary = "Get Checklist data with given country id",
            Description = "Get Checklist data with given country id")]
        [HttpGet]
        [Route("countries/{countryId?}/checklist")]
        public async Task<IActionResult> GetChecklistByCountry(int countryId)
        {
            var request = new GetChecklistByCountryRequest
            {
                Context = _context,
                ResourceId = countryId
            };

            var result = await _countryService.GetChecklistByCountry(request);

            return BaseResponseFactory.CreateResponse(result, result.Checklists);
        }

        #endregion
    }
}
