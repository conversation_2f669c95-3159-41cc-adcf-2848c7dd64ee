﻿using Dapper;
using Gateway.Notification.Application.Context;
using Gateway.Notification.Application.Entities;
using Microsoft.EntityFrameworkCore;
using Portal.Gateway.Infrastructure.Dapper;
using System;
using System.Collections.Generic;
using System.Data;
using System.Linq;
using System.Threading.Tasks;

namespace Gateway.Notification.Application.Repository
{
    public class MailQueueRepository : IMailQueueRepository
    {
        private readonly NotificationWorkerDbContext _dbContext;

        public MailQueueRepository(NotificationWorkerDbContext dbContext)
        {
            _dbContext = dbContext;
        }

        public async Task<int> SaveMailQueue(MailQueue mailQueue)
        {
            mailQueue.CreatedAt = DateTime.Now;
            mailQueue.IsDeleted = false;

            await _dbContext.MailQueue.AddAsync(mailQueue);
            await _dbContext.SaveChangesAsync();

            return mailQueue.Id;
        }

        public void UpdateMailQueue(MailQueue mailQueue)
        {
            mailQueue.UpdatedAt = DateTime.Now;
            
            _dbContext.Update(mailQueue);
            _dbContext.SaveChanges();
        }

        public async Task<List<MailQueue>> GetFailedEmails()
        {
            var existingMailQueue = await _dbContext.MailQueue
                .Where(p => p.IsSendSuccessful == false && p.RetryCount < 5 &&
                            p.SendDate >= DateTime.UtcNow.Date && p.SendDate < DateTime.Now.Date.AddDays(1))
                .OrderBy(q => q.Id).Take(10)
                .ToListAsync();

            if (existingMailQueue.Count == 0) return new List<MailQueue>();

            var queueList = existingMailQueue.Select(item => new MailQueue
            {
                Id = item.Id,
                ExternalTransactionId = item.ExternalTransactionId,
                ToMailAddress = item.ToMailAddress,
                BccMailAddress = item.BccMailAddress,
                CcMailAddress = item.CcMailAddress,
                FromMailAddress = item.FromMailAddress,
                DisplayName = item.DisplayName,
                MailContent = item.MailContent,
                MailType = item.MailType,
                IsProcessing = item.IsProcessing,
                Subject = item.Subject,
                SendDate = item.SendDate,
                IsSendSuccessful = item.IsSendSuccessful,
                ProviderId = item.ProviderId,
                ProviderTransactionId = item.ProviderTransactionId,
                RetryCount = item.RetryCount,
                FailedStatus = item.FailedStatus
            })
                .ToList();

            return queueList;
        }
    }
}
