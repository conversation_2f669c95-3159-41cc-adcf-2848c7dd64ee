﻿using Microsoft.EntityFrameworkCore.Metadata.Builders;
using Microsoft.EntityFrameworkCore;
using Portal.Gateway.Entity.Entities.Portal;

namespace Portal.Gateway.Entity.EntityConfigurations.Portal
{
    public class BranchApplicationCountryExtraFeeEntityConfiguration : IEntityTypeConfiguration<BranchApplicationCountryExtraFee>
    {
        public void Configure(EntityTypeBuilder<BranchApplicationCountryExtraFee> builder)
        {
            builder.ToTable("BranchApplicationCountryExtraFee");

            builder.HasIndex(e => e.BranchApplicationCountryId, "IX_BranchApplicationCountryExtraFee_BranchApplicationCountryId");

            builder.HasIndex(e => e.ExtraFeeId, "IX_BranchApplicationCountryExtraFee_ExtraFeeId");

            #region StaticAuditableEntityConfig

            builder.HasKey(e => e.Id);
            builder.Property(e => e.Id).IsRequired().ValueGeneratedOnAdd();
            builder.Property(e => e.IsActive).IsRequired();
            builder.Property(e => e.IsDeleted).IsRequired();
            builder.Property(e => e.CreatedBy);
            builder.Property(e => e.CreatedAt).HasColumnType("timestamp with time zone");
            builder.Property(e => e.UpdatedBy);
            builder.Property(e => e.UpdatedAt).HasColumnType("timestamp with time zone");
            builder.Property(e => e.DeletedBy);
            builder.Property(e => e.DeletedAt).HasColumnType("timestamp with time zone");

            #endregion

            builder.Property(e => e.ShowInICR).HasColumnName("ShowInICR");
            builder.Property(e => e.ShowInSummary)
                .IsRequired()
                .HasDefaultValueSql("true");

            builder.Property(e => e.BranchApplicationCountryId).IsRequired();
            builder.Property(e => e.ExtraFeeId).IsRequired();
            builder.Property(e => e.IsAutoChecked).IsRequired();
            builder.Property(e => e.Price).IsRequired();
            builder.Property(e => e.Tax).IsRequired();
            builder.Property(e => e.CurrencyId).IsRequired();
            builder.Property(e => e.TaxRatio).IsRequired();
            builder.Property(e => e.BasePrice).IsRequired();
            builder.Property(e => e.ServiceTax).IsRequired();
            builder.Property(e => e.IsShowInReport).IsRequired();
            builder.Property(e => e.IsGroupInIcr).IsRequired();
            builder.Property(e => e.IsShowInRejectionList).IsRequired();
            builder.Property(e => e.IsShowInAllApplicationsReport).IsRequired();
            builder.Property(e => e.IsShowInApplicationAfterRejection).IsRequired().HasDefaultValueSql("false");

            builder.HasOne(d => d.BranchApplicationCountry)
                    .WithMany(p => p.BranchApplicationCountryExtraFees)
                    .HasForeignKey(d => d.BranchApplicationCountryId)
                    .OnDelete(DeleteBehavior.ClientSetNull)
                    .HasConstraintName("FK_BranchApplicationCountryExtraFee_BranchApplicationCountry_B~");

            builder.HasOne(d => d.ExtraFee)
                .WithMany(p => p.BranchApplicationCountryExtraFees)
                .HasForeignKey(d => d.ExtraFeeId)
                .OnDelete(DeleteBehavior.ClientSetNull);
        }
    }
}
