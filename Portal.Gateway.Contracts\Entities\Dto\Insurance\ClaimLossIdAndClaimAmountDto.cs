﻿using System;

namespace Portal.Gateway.Contracts.Entities.Dto.Insurance
{
    public class ClaimLossIdAndClaimAmountDto
	{
        public int Id { get; set; }
        public int ApplicationId { get; set; }
        public string ClaimAmount { get; set; }
        public DateTimeOffset CreatedAt { get; set;}
        public DateTimeOffset LossDate { get; set;}
        public DateTime? NextAttemptDateTime { get; set; }
        public int? RetryCount { get; set; }
    }
}
