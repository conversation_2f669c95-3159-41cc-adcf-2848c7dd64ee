﻿"use strict";
let branchId = 0;
let rejectedErrorMessage = "";
var isApplicationAvailable = false;
var selectDaySlotSum = 0;
var slotQuota = 0;
var firstAvaibleSlotTime = null;
var openUpdatePagination = false;
var dateTimeMinValue = '0001-01-01T00:00:00+00:00';
var isSlotChangedOnUpdate = false;
var ApplicationWizard = function () {

    var _wizardEl;
    var _wizardObj;
    var _validations = [];
    var _clearValidations = function () {
        _validations = [];
    }
    console.log($("#EncryptedPreApplicationId").val());
    if ($("#EncryptedPreApplicationId").val() !== "") {
        $.ajax({
            type: "GET",
            url: "/Parameter/GetBranchByBranchApplicationCountryId?date=" + "&branchApplicationCountryId=" + $("#BranchApplicationCountryId").val(),
            success: function (data) {
                if (data === null) {
                    KTUtil.scrollTop();
                }
                else {
                    branchId = data.Id;
                    $("#CurrentCallingCode").val(data.CallingCode);
                    $(".CallingCode").text("+" + $("#CurrentCallingCode").val())
                }
            },
        });
    }

    var _initWizard = function () {
        _wizardObj = new KTWizard(_wizardEl, {
            startStep: 1,
            clickableSteps: true
        });

        _wizardObj.on('change', function (wizard) {

            if (wizard.getStep() > wizard.getNewStep()) {
                return;
            }
            if (wizard.getStep() === 1) {
                let validationResult1 = true;
                let errorMessage = "";

                if (wizard.newStep === 4) {
                    displayApplicationSummary();

                    jQuery.each($('.ActiveApplicant'), function (i, val) {
                        var emailPhoneValidation = checkEmailPhone(i);
                        if (!emailPhoneValidation) {
                            errorMessage = jsResources.Applicant + " #" + Number(i + 1) + " - " + jsResources.EmailOrPhoneNumberMissing + "</br>";
                            validationResult1 = false;
                        }
                    });
                }

                if (checkApplicationsByPassportNumber()) {
                      validationResult1 = false;
                      errorMessage = rejectedErrorMessage;
                }
                if (!validationResult1 && wizard.getNewStep() === wizard.totalSteps) {
                    bootbox.dialog({
                        message: '<p style="color:#FFFFFF;"><b>' + errorMessage + '</b></p>',
                        size: 'extra-large',
                        onEscape: true,
                        backdrop: true
                    });
                    $('.modal-content').css("background-color", "#FFA800");
                    KTUtil.scrollTop();
                }
                else { 
                    if (wizard.getNewStep() === wizard.totalSteps)
                        wizard.goTo(wizard.totalSteps);
                    else if (wizard.getNewStep() === wizard.totalSteps - 1) {
                        wizard.goTo(wizard.totalSteps - 1);
                    }
                    else if (firstAvaibleSlotTime != null) {
                        var applicantCount = Number($("#NumberOfPerson").val());
                        if (firstAvaibleSlotTime == dateTimeMinValue && applicantCount > selectDaySlotSum) {
                            bootbox.dialog({
                                message: '<p style="color:#FFFFFF;"><b>' + jsResources.SlotNotFound + '</b></p>',
                                size: 'extra-large',
                                onEscape: true,
                                backdrop: true
                            });
                            $('.modal-content').css("background-color", "#FFA800");
                            wizard.goTo(wizard.getNewStep());
                            KTUtil.scrollTop();
                        }
                        else if (firstAvaibleSlotTime != dateTimeMinValue) {
                            bootbox.dialog({
                                message: '<p style="color:#FFFFFF;"><b>' + jsResources.DaySlotNotFound + ". " + jsResources.NearestAvailableSlotTime + ": " + moment(firstAvaibleSlotTime).format("DD/MM/YYYY") + '</b></p>',
                                size: 'extra-large',
                                onEscape: true,
                                backdrop: true
                            });
                            $('.modal-content').css("background-color", "#FFA800");
                            wizard.goTo(wizard.getNewStep());
                            KTUtil.scrollTop();
                            var newSlotTime = moment(firstAvaibleSlotTime).format("MM/DD/YYYY");
                            var datepicker = $("#Date").data("kendoDatePicker");
                            console.log(datepicker.max());
                            console.log(newSlotTime);
                            if (datepicker.max() < moment(firstAvaibleSlotTime))
                                datepicker.setOptions({
                                    max: new Date(newSlotTime)
                                });
                        }
                        else {
                            wizard.goTo(wizard.getNewStep());
                        }
                    }
                    else {
                        if (wizard.getNewStep() === wizard.totalSteps) {
                            displayApplicationSummary();
                        }
                        var validator = _validations[wizard.getStep() - 1];

                        if (validator) {
                            if (validator.validate()) {
                                wizard.goTo(wizard.getNewStep());
                                KTUtil.scrollTop();
                            } else {
                                bootbox.dialog({
                                    message: '<p style="color:#FFFFFF;"><b>' + jsResources.FillRequiredFields + '</b></p>',
                                    size: 'extra-large',
                                    onEscape: true,
                                    backdrop: true
                                });
                                $('.modal-content').css("background-color", "#FFA800"); 
                                KTUtil.scrollTop();
                            }
                        }
                        }
                }
            }
            else if (wizard.getStep() === 2) {

                let validationResult2 = true;
                let errorMessage = "";

                if (wizard.newStep === 4) {
                    displayApplicationSummary();
                    jQuery.each($('.ActiveApplicant'), function (i, val) {
                        var emailPhoneValidation = checkEmailPhone(i);
                        if (!emailPhoneValidation) {
                            errorMessage = jsResources.Applicant + " #" + Number(i + 1) + " - " + jsResources.EmailOrPhoneNumberMissing + "</br>";
                            validationResult2 = false;
                        }
                    });
                }

                if (checkApplicationsByPassportNumber()) {
                    validationResult2 = false;
                    errorMessage = rejectedErrorMessage;
                }

                if (!validationResult2 && wizard.getNewStep() === wizard.totalSteps) {
                    bootbox.dialog({
                        message: '<p style="color:#FFFFFF;"><b>' + errorMessage + '</b></p>',
                        size: 'extra-large',
                        onEscape: true,
                        backdrop: true
                    });
                    $('.modal-content').css("background-color", "#FFA800");
                    KTUtil.scrollTop();
                }
                else {  
                    var isExistingPreApplication = $("#IsExistingPreApplication").val();

                    if (isExistingPreApplication === "true" || isExistingPreApplication === "True" || isExistingPreApplication === true) {
                        if (!isSlotChangedOnUpdate) {
                            return;
                        }
                    }

                    var applicantCount = Number($("#NumberOfPerson").val());
                    if (applicantCount <= selectDaySlotSum) {
                        if (applicantCount > slotQuota) {
                            bootbox.dialog({
                                message: '<p style="color:#FFFFFF;"><b>' + jsResources.RemaningSlotQuota + '</b></p>',
                                size: 'extra-large',
                                onEscape: true,
                                backdrop: true
                            });
                            $('.modal-content').css("background-color", "#FFA800");
                            wizard.goTo(wizard.getNewStep());
                            KTUtil.scrollTop();
                        }
                        else {
                            wizard.goTo(wizard.getNewStep());
                            KTUtil.scrollTop();
                        }
                    }
                    else if (firstAvaibleSlotTime == dateTimeMinValue) {
                        bootbox.dialog({
                            message: '<p style="color:#FFFFFF;"><b>' + jsResources.SlotNotFound + '</b></p>',
                            size: 'extra-large',
                            onEscape: true,
                            backdrop: true
                        });
                        KTUtil.scrollTop();
                    }
                    else {
                        bootbox.dialog({
                            message: '<p style="color:#FFFFFF;"><b>' + jsResources.DaySlotNotFound + ". " + jsResources.NearestAvailableSlotTime + ": " + moment(firstAvaibleSlotTime).format("DD/MM/YYYY") + '</b></p>',
                            size: 'extra-large',
                            onEscape: true,
                            backdrop: true
                        });
                        $('.modal-content').css("background-color", "#FFA800");
                        KTUtil.scrollTop();
                        var newSlotTime = moment(firstAvaibleSlotTime).format("MM/DD/YYYY");
                        var datepicker = $("#Date").data("kendoDatePicker");
                        console.log(datepicker.max());
                        console.log(newSlotTime);
                        if (datepicker.max() < moment(firstAvaibleSlotTime))
                            datepicker.setOptions({
                                max: new Date(newSlotTime)
                            });
                    }
                }
            }
            else if (wizard.getStep() === 3) {
                if (wizard.getNewStep() === wizard.totalSteps) {
                    displayApplicationSummary();
                }
                var validationResult = true;
                var validationOfPassportExpirationDate = false;

                var errorMessage = "";

                jQuery.each($('.ActiveApplicant'), function (i, val) {
                    var validator = _validations[wizard.getStep() - 1 + i];

                    if (validator) {
                        if (!validator.validate()) {
                            errorMessage = errorMessage + jsResources.Applicant + " #" + Number(i + 1) + " - " + jsResources.FillRequiredFields + "</br>";
                            validationResult = false;
                        }
                    }

                    var birthDateValidation = checkBirthDate(i);
                    if (!birthDateValidation) {
                        errorMessage = errorMessage + jsResources.Applicant + " #" + Number(i + 1) + " - " + jsResources.BirthDateValidation + "</br>";
                        validationResult = false;
                    }
                    else {
                        var applicationRejectionByPersonalInformation = checkApplicationsByPersonalInformation(i)
                        if (applicationRejectionByPersonalInformation) {
                            validationResult = false;
                            errorMessage = rejectedErrorMessage;
                        }
                    }

                    var passportValidation = checkPassport(i);
                    if (!passportValidation) {
                        errorMessage = errorMessage + jsResources.Applicant + " #" + Number(i + 1) + " - " + jsResources.PassportValidityPeriodLessThan180Days + "!</br>";
                        validationResult = false;
                        validationOfPassportExpirationDate = true;
                    }

                    var emailPhoneValidation = checkEmailPhone(i);
                    if (!emailPhoneValidation) {
                        errorMessage = errorMessage + jsResources.Applicant + " #" + Number(i + 1) + " - " + jsResources.EmailOrPhoneNumberMissing + "</br>";
                        validationResult = false;
                    }
                });

                if (checkApplicationsByPassportNumber()) {
                    validationResult = false;
                    errorMessage = rejectedErrorMessage;
                }

                if ($("#EncryptedPreApplicationId").val() === "" && $("#VasTypeId").val() !== '5') { // ekleme ise
                    if (notExistPassportNumber()) {
                        validationResult = false;
                        errorMessage = errorMessage + jsResources.Exception_ExistingRecord;
                    }
                }

                if (validationResult) {
                    wizard.goTo(wizard.getNewStep());
                    KTUtil.scrollTop();
                }
                else {
                    const color = validationOfPassportExpirationDate ? "#F64E60" : "#FFA800";
                    bootbox.dialog({
                        message: '<p style="color:#FFFFFF;"><b>' + errorMessage + '</b></p>',
                        size: 'extra-large',
                        onEscape: true,
                        backdrop: true
                    });
                    $('.modal-content').css("background-color", color); 
                    KTUtil.scrollTop();
                }
            }
            else {
                if (wizard.getNewStep() === wizard.totalSteps) {
                    displayApplicationSummary();
                }
                var validator = _validations[wizard.getStep() - 1];

                if (validator) {
                    if (validator.validate()) {
                        wizard.goTo(wizard.getNewStep());
                        KTUtil.scrollTop();
                    } else {
                        bootbox.dialog({
                            message: '<p style="color:#FFFFFF;"><b>' + jsResources.FillRequiredFields + '</b></p>',
                            size: 'extra-large',
                            onEscape: true,
                            backdrop: true
                        });
                        $('.modal-content').css("background-color", "#FFA800"); 
                        KTUtil.scrollTop();
                    }
                }
            }
            return false;
        });

        _wizardObj.on('changed', function () {
            KTUtil.scrollTop();
        });

        _wizardObj.on('submit', function (wizard) {
            var validator = _validations[wizard.getStep() - 1];
            if (validator) {
                if (validator.validate()) {
                    var isExistingPreApplication = $("#IsExistingPreApplication").val();
                    if (isExistingPreApplication === "true" || isExistingPreApplication === "True" || isExistingPreApplication === true) {

                        $.ajax({
                            type: "POST",
                            url: "/Appointment/PreApplication/UpdatePreApplication",
                            data: $("#formAddUpdatePreApplication").serialize(),
                            success: function (data) {
                                showNotification(data.Type, data.Message);
                                if (data.Type === "success") {
                                    $('#submitButtonPreApplication').prop('disabled', true);
                                    setTimeout(function () {
                                        window.location.href = "/Appointment/PreApplication/List"
                                    }, 2500);
                                }
                            },
                            error: function (data) {
                                showNotification('danger', jsResources.ErrorOccurred);
                            }
                        });
                    }
                    else {
                        $.ajax({
                            type: "POST",
                            url: "/Appointment/PreApplication/AddPreApplication",
                            data: $("#formAddUpdatePreApplication").serialize(),
                            success: function (data) {
                                showNotification(data.Type, data.Message);
                                if (data.Type === "success") {
                                    $('#submitButtonPreApplication').prop('disabled', true);
                                    setTimeout(function () {
                                        window.location.href = "/Appointment/PreApplication/List"
                                    }, 2500);
                                }
                            },
                            error: function (data) {
                                showNotification('danger', jsResources.ErrorOccurred);
                            }
                        });
                    }

                } else {
                    bootbox.dialog({
                        message: '<p style="color:#FFFFFF;"><b>' + jsResources.FillRequiredFields + '</b></p>',
                        size: 'extra-large',
                        onEscape: true,
                        backdrop: true
                    });
                    $('.modal-content').css("background-color", "#FFA800");
                    KTUtil.scrollTop();
                }
            }
        });
    }

    var _initValidation = function () {
        _validations.push($('#divStep1Description').kendoValidator().data("kendoValidator"));
        _validations.push($('#divStep2SlotInformation').kendoValidator().data("kendoValidator"));

        jQuery.each($('.ActiveApplicant'), function (i, val) {
            _validations.push(
                $("#ApplicantForm-Validation-" + val.dataset.order).kendoValidator({
                    rules: {
                        customRule1: function (input) {
                            if (input[0].attributes.nameValid !== undefined) {
                                if (input[0].attributes.nameValid.value == "Name" || input[0].attributes.nameValid.value == "Surname" || input[0].attributes.nameValid.value == "Email" || input[0].attributes.nameValid.value == "PhoneNumber" || input[0].attributes.nameValid.value == "PassportNumber")
                                    return $.trim(input.val()) !== "";
                                else return true;
                            }
                            else return true;
                        }
                    },
                    messages: {
                        customRule1: jsResources.RequiredField,
                    }
                }).data("kendoValidator"));
        });

        _validations.push($('#divStep4Finalize').kendoValidator().data("kendoValidator"));
    }

    return {
        init: function () {
            _wizardEl = KTUtil.getById('wizardAddApplication');

            _clearValidations();
            _initWizard();
            _initValidation();
        }
    };
}();

function EnableApplicantForm(index) {

    $("#ApplicantForm-" + index).show();
    $("#Applicants_" + index + "__ApplicationEnabled").val("True");

    var applicantCount = Number($("#NumberOfPerson").val());
    $("#NumberOfPerson").val(applicantCount + 1)

    $("#Applicants_" + index + "__ApplicationEnabled").addClass("ActiveApplicant");
    ApplicationWizard.init();
}


function DisableApplicantForm(index) {
    bootbox.confirm(jsResources.AreYouSureToDeleteThisRecord, function (result) {
        if (result) {
            $("#ApplicantForm-" + index).hide();
            $("#Applicants_" + index + "__ApplicationEnabled").val("False");

            var applicantCount = Number($("#NumberOfPerson").val());
            $("#NumberOfPerson").val(applicantCount - 1)
            $("#Applicants_" + index + "__Name").val('');
            $("#Applicants_" + index + "__Surname").val('');
            $("#Applicants_" + index + "__GenderId").data("kendoDropDownList").select(null);
            $("#Applicants_" + index + "__Email").val('');
            $("#Applicants_" + index + "__PhoneNumber").val('');
            $("#Applicants_" + index + "__PassportNumber").val('');
            $("#Applicants_" + index + "__ApplicationEnabled").val("False");
            $("#Applicants_" + index + "__EncryptedPreApplicationApplicantId").val('');
            $("#Applicants_" + index + "__BirthDate").val("");
            $("#Applicants_" + index + "__PassportExpireDate").val("");
            $("#Applicants_" + index + "__NationalityId").data("kendoDropDownList").select(null);


            $("#Applicants_" + index + "__ApplicationEnabled").removeClass("ActiveApplicant");
            $("#collapse-" + index).collapse('hide');

            ApplicationWizard.init();
        }
    });
}

//step #1
function onChangeCountry() {

    DestroySlot();
    var widget = $("#BranchApplicationCountryId").getKendoDropDownList();
    var dataSource = widget.dataSource;

    if (dataSource.data().length > 0) {
        let data = dataSource.data();
        for (let i = data.length - 1; i >= 0; i--) {
            widget.dataSource.remove(data[i]);
        }
    }
    dataSource.sync();

    $.ajax({
        type: "GET",
        url: "/Parameter/GetAuthorizedBranchApplicationCountrySelectListByCountryId?countryId=" + $("#CountryId").val(),
        success: function (data) {
            jQuery.each(data, function (i, val) {
                dataSource.add({
                    Value: val.Value,
                    Text: val.Text
                });
            });

            dataSource.one("sync", function () {
                widget.select(dataSource.view().length - 1);
            });

            dataSource.sync();
        },
        error: function (data) {
            showNotification('danger', jsResources.ErrorOccurred);
        }
    });
}

function onApplicantChange() {

    DestroySlot();

    if ($('.ActiveApplicant').length !== Number($("#NumberOfPerson").val())) {

        for (var i = 0; i < Number($("#NumberOfPerson").val()); i++) {
            $("#ApplicantForm-" + i).show();
            $("#Applicants_" + i + "__ApplicationEnabled").val("True");
            $("#Applicants_" + i + "__ApplicationEnabled").addClass("ActiveApplicant");
            $(".CallingCode").text("+" + $("#CurrentCallingCode").val());
        }

        for (var i = Number($("#NumberOfPerson").val()); i < 15; i++) {
            $("#ApplicantForm-" + i).hide();
            $("#Applicants_" + i + "__ApplicationEnabled").val("False");
            $("#Applicants_" + i + "__ApplicationEnabled").removeClass("ActiveApplicant");
        }
    }

    ApplicationWizard.init();
}

$("#ApplicantTypeId").on('change', function () {
    setTimeout(function () {
        onApplicantChange();
    }, 750);
});

$("#BranchApplicationCountryId").change(function () {

    DestroySlot();

    onBranchApplicationCountryChange();

    $.ajax({
        type: "GET",
        url: "/Parameter/GetBranchByBranchApplicationCountryId?date=" + "&branchApplicationCountryId=" + $("#BranchApplicationCountryId").val(),
        success: function (data) {
            if (data === null) {
                KTUtil.scrollTop();
            }
            else {
                branchId = data.Id;

                if (data.MaxAppointmentDay != null) {
                    var today = new Date();
                    var maxDate = today.setDate(today.getDate() + data.MaxAppointmentDay);
                    var datepicker = $("#Date").data("kendoDatePicker");
                    datepicker.setOptions({
                        max: new Date(maxDate)
                    });
                } else {
                    var datepicker = $("#Date").data("kendoDatePicker");
                    datepicker.setOptions({
                        max: new Date(2099, 11, 31)
                    });
                }
                $("#CurrentCallingCode").val(data.CallingCode);
                $(".CallingCode").text("+" + $("#CurrentCallingCode").val())
            }
        },
        error: function (data) {
            showNotification('danger', jsResources.ErrorOccurred);
        }
    });
});

$("#IsAgency").change(function () {
    DestroySlot();
});

//step #2
function onChangeSlotTypeId() {
    DestroySlot();
}

$("#getSlot").click(function () {

    if ($("#SlotTypeId").val() === "2" && !$("#IsAgency").is(":checked")) {
        bootbox.dialog({
            message: '<p style="color:#FFFFFF;"><b>' + jsResources.AgencySlotMatchError + '</b></p>',
            size: 'extra-large',
            onEscape: true,
            backdrop: true
        });
        $('.modal-content').css("background-color", "#FFA800");
        return false;
    }
    if ($("#SlotTypeId").val() !== "2" && $("#IsAgency").is(":checked")) {
        bootbox.dialog({
            message: '<p style="color:#FFFFFF;"><b>' + jsResources.AgencySlotMatchError + '</b></p>',
            size: 'extra-large',
            onEscape: true,
            backdrop: true
        });
        $('.modal-content').css("background-color", "#FFA800");
        return false;
    }
    var isExistingPreApplication = $("#IsExistingPreApplication").val();
    if (isExistingPreApplication === "false" || isExistingPreApplication === "False" || isExistingPreApplication === false) {
        $("#SelectedSlotId").val("");
        $("#CurrentSlotTime").val("");
    }

    var parentItem = $("#SlotQuota");

    parentItem.empty();

    var date = $("#Date").data("kendoDatePicker");

    var applicantCount = 1;

    if ($("#ApplicantTypeId").val() == 2 || $("#ApplicantTypeId").val() == 3) {
        applicantCount = Number($("#NumberOfPerson").val());
    }
    $.ajax({
        type: "GET",
        url: "/Parameter/GetSlots?date=" + date.value().toLocaleString("en-US") + "&branchApplicationCountryId=" + $("#BranchApplicationCountryId").val() + "&slotTypeId=" + $("#SlotTypeId").val() + "&agencyId=" + $("#AgencyId").val() + "&applicantCount=" + applicantCount + "&slotId=" + $("#CurrentSlotId").val(),
        success: function (data) {
            if (data === null) {
                bootbox.dialog({
                    message: '<p style="color:#FFFFFF;"><b>' + jsResources.SlotNotFound + '</b></p>',
                    size: 'extra-large',
                    onEscape: true,
                    backdrop: true
                });
                $('.modal-content').css("background-color", "#FFA800");
                KTUtil.scrollTop();
            }
            else {
                var dynamicHtml = '<table class="table table-bordered text-center slot-table">';
                selectDaySlotSum = 0;
                dynamicHtml += '<tbody>';

                dynamicHtml += '<tr>';
                dynamicHtml += '<td class="h6 text-left">#</td>';
                dynamicHtml += '<td class="h6 text-left">' + jsResources.Start + '</td>';
                dynamicHtml += '<td class="h6 text-left">' + jsResources.Quota + '</td>';
                dynamicHtml += '</tr>';

                jQuery.each(data, function (i, val) {
                    if (val.Quota >= 0) {
                        var slotTime = val.SlotTimeText;
                        dynamicHtml += '<tr>';
                        if (val.Id == $("#CurrentSlotId").val() && val.Quota >= 0) {
                            dynamicHtml += '<td><input type="radio" checked="true" data-slotTime="' + slotTime + '" value="' + val.Id + '" quota="' + val.Quota + '" name="SlotId" onclick="SelectSlot(this)"/></td>';
                            $("#CurrentSlotTime").val(slotTime);
                            $("#SelectedSlotId").val(val.Id);
                            dynamicHtml += '<td>' + slotTime + '</td>';
                            dynamicHtml += '<td >' + val.Quota + '</td>';
                            firstAvaibleSlotTime = val.FirstAvaibleDateTime;
                        }
                        if (val.Quota > 0 && val.Id != $("#CurrentSlotId").val()) {
                            dynamicHtml += '<td><input type="radio" data-slotTime="' + slotTime + '" value="' + val.Id + '" quota="' + val.Quota + '" name="SlotId" onclick="SelectSlot(this)"/></td>';
                            dynamicHtml += '<td>' + slotTime + '</td>';
                            dynamicHtml += '<td >' + val.Quota + '</td>';
                        }
                        dynamicHtml += '</tr>';
                        if (selectDaySlotSum < 14) { selectDaySlotSum += val.Quota; }
                    }
                });

                dynamicHtml += '</tbody>';
                dynamicHtml += '</table>';
                if (selectDaySlotSum < applicantCount && (firstAvaibleSlotTime == dateTimeMinValue || $("#CurrentSlotId").val() == "")) {
                    if ($("#CurrentSlotId").val() == "") {
                        firstAvaibleSlotTime = data.SlotTime;
                        bootbox.dialog({
                            message: '<p style="color:#FFFFFF;"><b>' + jsResources.SlotNotFound + ". " + jsResources.NearestAvailableSlotTime + ": " + moment(firstAvaibleSlotTime).format("DD/MM/YYYY") + '</b></p>',
                            size: 'extra-large',
                            onEscape: true,
                            backdrop: true
                        });
                        $('.modal-content').css("background-color", "#FFA800");
                        KTUtil.scrollTop();
                    }
                    if (openUpdatePagination) {
                        bootbox.dialog({
                            message: '<p style="color:#FFFFFF;"><b>' + jsResources.SlotNotFound + '</b></p>',
                            size: 'extra-large',
                            onEscape: true,
                            backdrop: true
                        });
                        $('.modal-content').css("background-color", "#FFA800");
                        KTUtil.scrollTop();
                    }
                    if (firstAvaibleSlotTime == dateTimeMinValue) {
                        openUpdatePagination = true;
                    }
                    else {
                        var newSlotTime = moment(firstAvaibleSlotTime).format("MM/DD/YYYY");
                        var datepicker = $("#Date").data("kendoDatePicker");
                        console.log(datepicker.max());
                        console.log(newSlotTime);
                        if (datepicker.max() < moment(firstAvaibleSlotTime))
                            datepicker.setOptions({
                                max: new Date(newSlotTime)
                            });
                    }
                }
                parentItem.append(dynamicHtml);

            }
        },
        error: function (data) {
            showNotification('danger', jsResources.ErrorOccurred);
        }
    });

    if (document.getElementById("IsExistingPreApplication").value.toString() === "True") {
        document.querySelector('input[value="' + $("#CurrentSlotId").val() + '"]').checked = true;
    }
});

$("#Date").change(function () {
    bootbox.dialog({
        message: '<p style="color:#FFFFFF;"><b>' + jsResources.SlotSelectionDestroyed + '</b></p>',
        size: 'extra-large',
        onEscape: true,
        backdrop: true
    });
    $('.modal-content').css("background-color", "#FFA800");
    $("#SelectedSlotId").val("");
    $('#CurrentSlotId').val(null);
    $("#CurrentSlotTime").val("");
    firstAvaibleSlotTime = null;
});


function SelectSlot(element) {
    var oldSlotId = $("#SelectedSlotId").val();
    $("#SelectedSlotId").val(element.value);
    $("#CurrentSlotTime").val(element.getAttribute("data-slotTime"));
    slotQuota = element.getAttribute("quota");
    var isExistingPreApplication = $("#IsExistingPreApplication").val();
    if (isExistingPreApplication === "true" || isExistingPreApplication === "True" || isExistingPreApplication === true) {
        isSlotChangedOnUpdate = oldSlotId !== element.vaalue;
    }

}

function DestroySlot() {
    var isSlotValid = $('.slot-table').length;
    if (isSlotValid) {
        $('.slot-table').remove();
        bootbox.dialog({
            message: '<p style="color:#FFFFFF;"><b>' + jsResources.SlotSelectionDestroyed + '</b></p>',
            size: 'extra-large',
            onEscape: true,
            backdrop: true
        });
        $('.modal-content').css("background-color", "#FFA800");
        $("#SelectedSlotId").val("");
    }
    $('#CurrentSlotId').val(null);
    firstAvaibleSlotTime = null;
}

//step #3

function RelationControlForStart() {
    if ($("#EncryptedPreApplicationId").val() !== "") {
        let isWifeorHusbandSelected = false;
        let relation = '';
        let filterValue = "2"; // child

        jQuery.each($('.ActiveApplicant'), function (i, val) {
            relation = $("#Applicants_" + i + "__RelationShipId").val();
            if (relation === '1') {
                isWifeorHusbandSelected = true;
            }
        });

        if (isWifeorHusbandSelected) {
            jQuery.each($('.ActiveApplicant'), function (r, val) {
                if (r > 0 && $("#Applicants_" + r + "__RelationShipId").val() !== '1') {
                    let newFilter = {
                        logic: "contains",
                        filters: [
                            { field: "Value", operator: "contains", value: filterValue, FilterName: "UniqueFilterForRelation" }
                        ]
                    };

                    $("#Applicants_" + r + "__RelationShipId").getKendoDropDownList().dataSource.filter(newFilter);

                }
            });
        }
    }
}

function PreApplicationRelationControl() {
    let isWifeorHusbandSelected = false;
    let relation = '';
    let filterValue = "2"; // child
    let removeIndex = -1;

    jQuery.each($('.ActiveApplicant'), function (i, val) {
        relation = $("#Applicants_" + i + "__RelationShipId").val();
        if (relation === '1') {
            isWifeorHusbandSelected = true;
        }
    });

    jQuery.each($('.ActiveApplicant'), function (r, val) {
        if (r > 0) {
            if (isWifeorHusbandSelected) {
                let newFilter = {
                    logic: "contains",
                    filters: [
                        { field: "Value", operator: "contains", value: filterValue, FilterName: "UniqueFilterForRelation"}
                    ]
                };

                $("#Applicants_" + r + "__RelationShipId").getKendoDropDownList().dataSource.filter(newFilter);
            }
            else {
                if ($("#Applicants_" + r + "__RelationShipId").getKendoDropDownList().dataSource.filter() !== undefined) {

                    let filters = $("#Applicants_" + r + "__RelationShipId").getKendoDropDownList().dataSource.filter().filters;
                    for (let x = 0; x < filters.length; x++) {
                        let temp = filters[x];
                        if (temp.field == "Value") {
                            removeIndex = x;
                        }
                    }
                    if (removeIndex != -1)
                        filters.splice(0, 1);

                    $("#Applicants_" + r + "__RelationShipId").getKendoDropDownList().dataSource.filter(filters);
                }
            }
        }
    });
}

function SearchCustomerCard(index) {
    $("#CustomerCardSelectListBody-" + index).css('visibility', 'visible');

    var widget = $("#CustomerCardSelectList-" + index).getKendoDropDownList();
    var dataSource = widget.dataSource;

    var data = dataSource.data();

    for (var i = data.length - 1; i >= 0; i--) {
        widget.dataSource.remove(data[i]);
    }

    dataSource.sync();

    $.ajax({
        type: "GET",
        url: "/Appointment/PreApplication/SearchCustomerCard?passportNumber=" + $("#PassportNumber-" + index).val(),
        success: function (data) {
            if (data.length > 0) {

                jQuery.each(data, function (i, val) {
                    dataSource.add({
                        Value: val.Value,
                        Text: val.Text
                    });
                });

                dataSource.one("sync", function () {
                    widget.select(dataSource.view().length - 1);
                });

                dataSource.sync();
            }
        },
        error: function (data) {
            showNotification('danger', jsResources.ErrorOccurred);
        }
    });
}

function onChangeCustomerCard(index) {

    $.ajax({
        type: "GET",
        url: "/Appointment/PreApplication/GetCustomerCard?encryptedCustomerCardId=" + $("#CustomerCardSelectList-" + index).data("kendoDropDownList").value(),
        success: function (data) {
            if (data.IsSuccess === true) {

                if (data.Data.BirthDate === null) {
                    $("#Applicants_" + index + "__BirthDate").val("");
                }
                else {
                    var birthDate = new Date(data.Data.BirthDate);
                    $("#Applicants_" + index + "__BirthDate").val(("0" + birthDate.getDate()).slice(-2) + "/" + ("0" + (birthDate.getMonth() + 1)).slice(-2) + "/" + birthDate.getFullYear());
                }

                if (data.Data.PassportExpireDate === null) {
                    $("#Applicants_" + index + "__PassportExpireDate").val("");
                }
                else {
                    var passportExpireDate = new Date(data.Data.PassportExpireDate);
                    $("#Applicants_" + index + "__PassportExpireDate").val(("0" + passportExpireDate.getDate()).slice(-2) + "/" + ("0" + (passportExpireDate.getMonth() + 1)).slice(-2) + "/" + passportExpireDate.getFullYear());
                }


                $("#Applicants_" + index + "__Name").val(data.Data.Name);
                $("#Applicants_" + index + "__Surname").val(data.Data.Surname);
                $("#Applicants_" + index + "__GenderId").data("kendoDropDownList").select(data.Data.GenderId);
                $("#Applicants_" + index + "__Email").val(data.Data.Email);
                $("#Applicants_" + index + "__PhoneNumber").val(data.Data.PhoneNumber);
                $("#Applicants_" + index + "__NationalityId").data("kendoDropDownList").value(data.Data.NationalityId);
                $("#Applicants_" + index + "__PassportNumber").val(data.Data.PassportNumber);
                if ($("#Applicants_" + index + "__PassportNumber").val() !== "") {
                    $("#Applicants_" + index + "__SaveApplicantAsCustomer").prop('checked', true);
                }
                else {
                    $("#Applicants_" + index + "__SaveApplicantAsCustomer").prop('checked', false);
                }
            } else {
                bootbox.dialog({
                    message: '<p style="color:#FFFFFF;"><b>' + data.Message + '</b></p>',
                    size: 'extra-large',
                    onEscape: true,
                    backdrop: true
                });
                $('.modal-content').css("background-color", "#F64E60");
            }
        },
        error: function (data) {
            showNotification('danger', jsResources.ErrorOccurred);
        }
    });
}

jQuery(document).ready(function () {

    var today = new Date();
    var tomorrow = new Date(today);
    tomorrow.setDate(today.getDate() + 1);
    const datesAreOnSameDay = (first, second) =>
        first.getFullYear() === second.getFullYear() &&
        first.getMonth() === second.getMonth() &&
        first.getDate() === second.getDate();
    if (document.getElementById("IsExistingPreApplication").value.toString() === "False") {
        var datepicker = $("#Date").data("kendoDatePicker");
        datepicker.min(tomorrow);
        onChangeCountry();
        for (var i = 0; i < 15; i++) {
            $("#Applicants_" + i + "__BirthDate").val("");
            $("#Applicants_" + i + "__PassportExpireDate").val("");
        }
    }
    else {
        var datepicker = $("#Date").data("kendoDatePicker");
        datepicker.min(tomorrow);
        var selectedDay = $("#Date").data("kendoDatePicker").value();
        if (datesAreOnSameDay(selectedDay, today)) {
            $("#Date").data("kendoDatePicker").value(tomorrow);
        }
        $("#getSlot").click();
        for (var i = Number($("#NumberOfPerson").val()); i < 15; i++) {
            $("#Applicants_" + i + "__BirthDate").val("");
            $("#Applicants_" + i + "__PassportExpireDate").val("");
        }

        fireVisaCategoryValidation();
    }

    for (var i = 0; i < Number($("#NumberOfPerson").val()); i++) {
        $("#ApplicantForm-" + i).show();
        $("#Applicants_" + i + "__ApplicationEnabled").val("True");
        $("#Applicants_" + i + "__ApplicationEnabled").addClass("ActiveApplicant");
    }

    ApplicationWizard.init();
});

function fireVisaCategoryValidation() {
    //sayfa başlangıcında çalışması için VisaCategoryId jquery validate manipüle edildi
    var visaCategoryIdField = $('#VisaCategoryId');

    var visaCategoryIdValidationSpan = document.querySelector('[data-valmsg-for="VisaCategoryId"]');

    if (visaCategoryIdValidationSpan) {
        if (visaCategoryIdField.val() === '0') {
            visaCategoryIdValidationSpan.classList.remove('field-validation-valid');
            visaCategoryIdValidationSpan.classList.add('field-validation-error');
            visaCategoryIdValidationSpan.textContent = visaCategoryIdField.data('val-required');
            visaCategoryIdValidationSpan.style.color = "red";
        }
        else {
            visaCategoryIdValidationSpan.classList.remove('field-validation-error');
            visaCategoryIdValidationSpan.classList.add('field-validation-valid');
            visaCategoryIdValidationSpan.textContent = '';
        }
    }
}

function checkApplicationsByPassportNumber() {
    let passports = '';
    let arr = [];
    let isApplicationRejected = false;
    let slotDate = $("#Date").val();
 
    jQuery.each($('.ActiveApplicant'), function (i, val) {
        if ($("#Applicants_" + i + "__PassportNumber").val() !== '') {
            passports = ',' + $("#Applicants_" + i + "__PassportNumber").val();
            arr.push(passports);
        }
    });

    if (arr.length > 0) {
        $.ajax({
            type: "GET",
            url: "/Appointment/PreApplication/IsPreApplicationAvailableByPassportNumber?passportNumber=" + arr + "&branchId=" + branchId + "&slotDate=" + slotDate,
            async: false,
            success: function (data) {
                if ((data.Result === false)) {
                    rejectedErrorMessage = data.ErrorMessage;
                    isApplicationRejected = true;
                }
            },
            dataType: "json"
        });
    }
    return isApplicationRejected;
}
function checkApplicationsByPersonalInformation(index) {
    var isApplicationRejected = false;
    var name = $("#Applicants_" + index + "__Name").val();
    var surname = $("#Applicants_" + index + "__Surname").val();
    var birthDate = $("#Applicants_" + index + "__BirthDate").val();
    var nationalityId = $("#Applicants_" + index + "__NationalityId").val();
    var AppointmentDate = $("#Date").val();

    $.ajax({
        type: "GET",
        url: "/Appointment/PreApplication/IsPreApplicationAvailableByPersonalInformation",
        async: false,
        data: {
            branchId: branchId,
            name: name,
            surname: surname,
            birthDate: birthDate,
            nationalityId: nationalityId,
            AppointmentDate: AppointmentDate,
        },
        success: function (data) {
            if (data.Result === false) {
                rejectedErrorMessage = data.ErrorMessage;
                isApplicationRejected = true;
            }
            else {
                //
            }
        },
    });
    return isApplicationRejected;
}

function notExistPassportNumber() {

    let passports = '';
    let arr = [];
    var isExistPassport = false;

    jQuery.each($('.ActiveApplicant'), function (i, val) {
        passports = passports + ',' + $("#Applicants_" + i + "__PassportNumber").val();
        arr.push(passports);
    });

    $.ajax({
        type: "GET",
        url: "/Appointment/PreApplication/GetPreApplicationNotExistPassportNumber?passportNumber=" + arr,
        async: false,
        success: function (data) {
            if (data === false) {
                isExistPassport = true;
            }
        },
        dataType: "json"
    });
    return isExistPassport;
}

$("#Applicants_0__Email").on('change', function () {
    jQuery.each($('.ActiveApplicant'), function (i, val) {
        if (i !== 0) {
            $("#Applicants_" + i + "__Email").val($("#Applicants_0__Email").val())
        }
    });
});

$("#Applicants_0__PhoneNumber").on('change', function () {
    jQuery.each($('.ActiveApplicant'), function (i, val) {
        if (i !== 0) {
            $("#Applicants_" + i + "__PhoneNumber").val($("#Applicants_0__PhoneNumber").val());
        }
    });
});

//step #4
function displayApplicationSummary() {

    var branchApplicationCountryId = $("#BranchApplicationCountryId").val();

    //$.post('/Appointment/PreApplication/PartialApplicationFiles', { branchApplicationCountryId: branchApplicationCountryId },
    //    function (data) {
    //        $('#divRequiredFiles').html(data);
    //    }, 'html');

    var branchName = document.querySelectorAll("[aria-owns='BranchApplicationCountryId_listbox']")[0].innerText;
    var agencyName = document.querySelectorAll("[aria-owns='AgencyId_listbox']")[0].innerText;

    var parentItem = $("#divPreApplicationSummary");

    parentItem.empty();

    var dynamicHtml = '<table class="table table-bordered text-center">';
    dynamicHtml += '<tbody>';

    dynamicHtml += '<tr>';
    dynamicHtml += '<td colspan="2" class="h6 text-left">' + jsResources.PreApplicationDetails + '</td>';
    dynamicHtml += '</tr>';

    dynamicHtml += '<tr>';
    dynamicHtml += '<td class="font-weight-bolder">' + jsResources.VisaCategory + ':</td>';
    dynamicHtml += '<td>' + getItemTextValue($("#VisaCategoryId").val(), "GetVisaCategoryType") + '</td>';
    dynamicHtml += '</tr>';

    dynamicHtml += '<tr>';
    dynamicHtml += '<td class="font-weight-bolder">' + jsResources.ApplicantType + ':</td>';
    dynamicHtml += '<td>' + getItemTextValue($("#ApplicantTypeId").val(), "GetApplicantType") + '</td>';
    dynamicHtml += '</tr>';

    if (agencyName !== jsResources.Select && $("#SlotTypeId").val() !== "1") {
        dynamicHtml += '<tr>';
        dynamicHtml += '<td class="font-weight-bolder">' + jsResources.Agency + ':</td>';
        dynamicHtml += '<td>' + agencyName + '</td>';
        dynamicHtml += '</tr>';
    }

    dynamicHtml += '<tr>';
    dynamicHtml += '<td class="font-weight-bolder">' + jsResources.ApplicationType + ':</td>';
    dynamicHtml += '<td>' + getItemTextValue($("#ApplicationTypeId").val(), "GetApplicationType") + '</td>';
    dynamicHtml += '</tr>';

    dynamicHtml += '<tr>';
    dynamicHtml += '<td class="font-weight-bolder">' + jsResources.Country + ':</td>';
    dynamicHtml += '<td>' + getItemTextValue($("#CountryId").val(), "GetNationality") + '</td>';
    dynamicHtml += '</tr>';

    dynamicHtml += '<tr>';
    dynamicHtml += '<td class="font-weight-bolder">' + jsResources.Branch + ':</td>';
    dynamicHtml += '<td>' + branchName + '</td>';
    dynamicHtml += '</tr>';

    dynamicHtml += '<tr>';
    dynamicHtml += '<td class="font-weight-bolder">' + jsResources.ApplicationDate + ':</td>';
    dynamicHtml += '<td>' + $("#Date").val() + ' ' + $("#CurrentSlotTime").val() + '</td>';
    dynamicHtml += '</tr>';

    dynamicHtml += '<tr>';
    dynamicHtml += '<td class="font-weight-bolder">' + jsResources.VasType + ':</td>';
    dynamicHtml += '<td>' + getItemTextValue($("#VasTypeId").val(), "GetVasType") + '</td>';
    dynamicHtml += '</tr>';

    jQuery.each($('.ActiveApplicant'), function (i, val) {

        var order = val.dataset.order;

        dynamicHtml += '<tr>';
        dynamicHtml += '<td colspan="2" class="h6 text-left">' + jsResources.Application + ' #' + (i + 1) + '</td>';
        dynamicHtml += '</tr>';

        dynamicHtml += '<tr>';
        dynamicHtml += '<td class="font-weight-bolder">' + jsResources.Name + ':</td>';
        dynamicHtml += '<td>' + $("#Applicants_" + order + "__Name").val() + '</td>';
        dynamicHtml += '</tr>';

        dynamicHtml += '<tr>';
        dynamicHtml += '<td class="font-weight-bolder">' + jsResources.Surname + ':</td>';
        dynamicHtml += '<td>' + $("#Applicants_" + order + "__Surname").val() + '</td>';
        dynamicHtml += '</tr>';

        dynamicHtml += '<tr>';
        dynamicHtml += '<td class="font-weight-bolder">' + jsResources.BirthDate + ':</td>';
        dynamicHtml += '<td>' + $("#Applicants_" + order + "__BirthDate").val() + '</td>';
        dynamicHtml += '</tr>';

        dynamicHtml += '<tr>';
        dynamicHtml += '<td class="font-weight-bolder">' + jsResources.Gender + ':</td>';
        dynamicHtml += '<td>' + getItemTextValue($("#Applicants_" + order + "__GenderId").val(), "GetGender") + '</td>';
        dynamicHtml += '</tr>';

        dynamicHtml += '<tr>';
        dynamicHtml += '<td class="font-weight-bolder">' + jsResources.Email + ':</td>';
        dynamicHtml += '<td>' + $("#Applicants_" + order + "__Email").val() + '</td>';
        dynamicHtml += '</tr>';

        var phoneNumberCountryCallingCode = '';
        phoneNumberCountryCallingCode = '+' + $("#CurrentCallingCode").val();
        dynamicHtml += '<tr>';
        dynamicHtml += '<td class="font-weight-bolder">' + jsResources.PhoneNumber + ':</td>';
        dynamicHtml += '<td>' + phoneNumberCountryCallingCode + $("#Applicants_" + order + "__PhoneNumber").val() + '</td>';
        dynamicHtml += '</tr>';

        dynamicHtml += '<tr>';
        dynamicHtml += '<td class="font-weight-bolder">' + jsResources.PassportNumber + ':</td>';
        dynamicHtml += '<td>' + $("#Applicants_" + order + "__PassportNumber").val() + '</td>';
        dynamicHtml += '</tr>';

        dynamicHtml += '<tr>';
        dynamicHtml += '<td class="font-weight-bolder">' + jsResources.PassportExpireDate + ':</td>';
        dynamicHtml += '<td>' + $("#Applicants_" + order + "__PassportExpireDate").val() + '</td>';
        dynamicHtml += '</tr>';

        dynamicHtml += '<tr>';
        dynamicHtml += '<td class="font-weight-bolder">' + jsResources.SaveApplicantAsCustomer + ':</td>';
        var saveCustomer = $("#Applicants_" + order + "__SaveApplicantAsCustomer").is(":checked") ? jsResources.Yes : jsResources.No;
        dynamicHtml += '<td>' + saveCustomer + '</td>';
        dynamicHtml += '</tr>';

        if (order > 0) {
            if ($("#Applicants_" + order + "__RelationShipId").val() !== "" && $("#Applicants_" + order + "__RelationShipId").val() !== undefined) {
                dynamicHtml += '<tr>';
                dynamicHtml += '<td class="font-weight-bolder">' + jsResources.ApplicantRelationship + ':</td>';
                dynamicHtml += '<td>' + getItemTextValue($("#Applicants_" + order + "__RelationShipId").val(), "GetRelationShip") + '</td>';
                dynamicHtml += '</tr>';
            }  
        }
        else {
            dynamicHtml += '<tr>';
            dynamicHtml += '<td class="font-weight-bolder">' + jsResources.ApplicantRelationship + ':</td>';
            dynamicHtml += '<td>' + jsResources.HimselfHerself + '</td>';
            dynamicHtml += '</tr>';
        }
    });

    dynamicHtml += '</tbody>';
    dynamicHtml += '</table>';

    parentItem.append(dynamicHtml);

}

function checkBirthDate(index) {
    if ($("#ApplicantForm-" + index).is(":visible")) {
        var birthDate = kendo.parseDate($("#Applicants_" + index + "__BirthDate").val(), jsResources.DatePickerFormatJs);
        if (birthDate == null) return true;
        else {
            var todayDate = new Date(new Date().toDateString());
            var diff = new Date(todayDate - birthDate);
            var days = diff / 1000 / 60 / 60 / 24;
            if (days < 0) return false;
            return true;
        }
    }
    else
        return true;
}

function checkPassport(index) {
    if ($("#ApplicantForm-" + index).is(":visible")) {
        var passportExpireDate = kendo.parseDate($("#Applicants_" + index + "__PassportExpireDate").val(), jsResources.DatePickerFormatJs);
        if (passportExpireDate == null) return true;
        else {
            var todayDate = new Date(new Date().toDateString());
            var diff = new Date(passportExpireDate - todayDate);
            var days = diff / 1000 / 60 / 60 / 24;
            if (days < 180)
                return false;
            return true;
        }
    }
    else
        return true;
}

function checkEmailPhone(index) {
    var phoneNumber = $("#Applicants_" + index + "__PhoneNumber").val();
    var email = $("#Applicants_" + index + "__Email").val();
    if (phoneNumber == null || phoneNumber === '') return false;
    else if (email == null || email === '') return false;
    else return true;
}

function onBranchApplicationCountryChange() {
    let widget = $("#SlotTypeId").getKendoDropDownList();
    let dataSource = widget.dataSource;

    if (dataSource.data().length > 0) {
        let data = dataSource.data();
        for (let i = data.length - 1; i >= 0; i--) {
            widget.dataSource.remove(data[i]);
        }
    }
    dataSource.sync();

    $.ajax({
        type: "GET",
        url: "/Parameter/GetSlotTypeByBranchApplicationCountry?branchApplicationCountryId=" + $("#BranchApplicationCountryId").val(),
        success: function (data) {
            if (data.length > 0) {
                jQuery.each(data, function (i, val) {
                    dataSource.add({
                        Value: val.Value,
                        Text: val.Text
                    });
                });

                dataSource.one("sync", function () {
                    widget.select(dataSource.view().length - 1);
                });

                if (dataSource.data().length >= 1) {
                    widget.select(0);
                }
            }

            dataSource.sync();
        },
        error: function (data) {
            showNotification('danger', jsResources.ErrorOccurred);
        }
    });
}

function getVisaCategoryRequestData() {
    return {
        BranchApplicationCountryId: $("#BranchApplicationCountryId").val(),
        TakeFromSession: false,
        IsReturnTypeBranchBased: false
    };
}