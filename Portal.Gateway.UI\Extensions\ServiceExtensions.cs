﻿using Gateway.Redis;
using Microsoft.AspNetCore.DataProtection;
using Microsoft.AspNetCore.Hosting;
using Microsoft.Extensions.Configuration;
using Microsoft.Extensions.DependencyInjection;
using Portal.Gateway.UI.Constants;
using StackExchange.Redis;
using System;

namespace Portal.Gateway.UI.Extensions
{
    public static class ServiceExtensions
    {
        public static IServiceCollection AddSessionManagement(this IServiceCollection services)
        {
            var config = services.BuildServiceProvider();

            using (var serviceScope = config.CreateScope())
            {
                var svc = serviceScope.ServiceProvider;
                var configuration = svc.GetRequiredService<IConfiguration>();

                if (configuration.GetValue<bool>(SessionConfigurationNames.Distributed))
                {
                    var hosting = svc.GetRequiredService<IWebHostEnvironment>();
                    var url = string.Format("{0}:{1}", configuration[RedisConfigurationNames.Url], configuration[RedisConfigurationNames.Port]);
                    var redisConfigurationOptions = ConfigurationOptions.Parse(url);

                    redisConfigurationOptions.ConnectTimeout = configuration.GetValue<int>(RedisConfigurationNames.ConnectTimeout);
                    redisConfigurationOptions.DefaultDatabase = configuration.GetValue<int>(RedisConfigurationNames.DefaultDatabase);
                    redisConfigurationOptions.ClientName = $"{hosting.ApplicationName}-{hosting.EnvironmentName}";
                    redisConfigurationOptions.AbortOnConnectFail = false;

                    services.AddStackExchangeRedisCache(redisCacheConfig =>
                    {
                        redisCacheConfig.ConfigurationOptions = redisConfigurationOptions;
                    });

                    ConnectionMultiplexer.SetFeatureFlag("preventthreadtheft", true);
                    var redisConnection = ConnectionMultiplexer.Connect(redisConfigurationOptions);

                    services.AddDataProtection()
                        .SetApplicationName(hosting.ApplicationName)
                        .PersistKeysToStackExchangeRedis(redisConnection, $"{hosting.ApplicationName}-Keys-{hosting.EnvironmentName}");
                }

                services.AddSession(options => {
                    options.IdleTimeout = TimeSpan.FromHours(configuration.GetValue<int>(SessionConfigurationNames.IdleTimeout));
                });
            }

            return services;
        }
    }
}
