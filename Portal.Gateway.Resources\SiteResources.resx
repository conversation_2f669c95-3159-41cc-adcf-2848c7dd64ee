﻿<?xml version="1.0" encoding="utf-8"?>
<root>
  <!-- 
    Microsoft ResX Schema 
    
    Version 2.0
    
    The primary goals of this format is to allow a simple XML format 
    that is mostly human readable. The generation and parsing of the 
    various data types are done through the TypeConverter classes 
    associated with the data types.
    
    Example:
    
    ... ado.net/XML headers & schema ...
    <resheader name="resmimetype">text/microsoft-resx</resheader>
    <resheader name="version">2.0</resheader>
    <resheader name="reader">System.Resources.ResXResourceReader, System.Windows.Forms, ...</resheader>
    <resheader name="writer">System.Resources.ResXResourceWriter, System.Windows.Forms, ...</resheader>
    <data name="Name1"><value>this is my long string</value><comment>this is a comment</comment></data>
    <data name="Color1" type="System.Drawing.Color, System.Drawing">Blue</data>
    <data name="Bitmap1" mimetype="application/x-microsoft.net.object.binary.base64">
        <value>[base64 mime encoded serialized .NET Framework object]</value>
    </data>
    <data name="Icon1" type="System.Drawing.Icon, System.Drawing" mimetype="application/x-microsoft.net.object.bytearray.base64">
        <value>[base64 mime encoded string representing a byte array form of the .NET Framework object]</value>
        <comment>This is a comment</comment>
    </data>
                
    There are any number of "resheader" rows that contain simple 
    name/value pairs.
    
    Each data row contains a name, and value. The row also contains a 
    type or mimetype. Type corresponds to a .NET class that support 
    text/value conversion through the TypeConverter architecture. 
    Classes that don't support this are serialized and stored with the 
    mimetype set.
    
    The mimetype is used for serialized objects, and tells the 
    ResXResourceReader how to depersist the object. This is currently not 
    extensible. For a given mimetype the value must be set accordingly:
    
    Note - application/x-microsoft.net.object.binary.base64 is the format 
    that the ResXResourceWriter will generate, however the reader can 
    read any of the formats listed below.
    
    mimetype: application/x-microsoft.net.object.binary.base64
    value   : The object must be serialized with 
            : System.Runtime.Serialization.Formatters.Binary.BinaryFormatter
            : and then encoded with base64 encoding.
    
    mimetype: application/x-microsoft.net.object.soap.base64
    value   : The object must be serialized with 
            : System.Runtime.Serialization.Formatters.Soap.SoapFormatter
            : and then encoded with base64 encoding.

    mimetype: application/x-microsoft.net.object.bytearray.base64
    value   : The object must be serialized into a byte array 
            : using a System.ComponentModel.TypeConverter
            : and then encoded with base64 encoding.
    -->
  <xsd:schema id="root" xmlns="" xmlns:xsd="http://www.w3.org/2001/XMLSchema" xmlns:msdata="urn:schemas-microsoft-com:xml-msdata">
    <xsd:import namespace="http://www.w3.org/XML/1998/namespace" />
    <xsd:element name="root" msdata:IsDataSet="true">
      <xsd:complexType>
        <xsd:choice maxOccurs="unbounded">
          <xsd:element name="metadata">
            <xsd:complexType>
              <xsd:sequence>
                <xsd:element name="value" type="xsd:string" minOccurs="0" />
              </xsd:sequence>
              <xsd:attribute name="name" use="required" type="xsd:string" />
              <xsd:attribute name="type" type="xsd:string" />
              <xsd:attribute name="mimetype" type="xsd:string" />
              <xsd:attribute ref="xml:space" />
            </xsd:complexType>
          </xsd:element>
          <xsd:element name="assembly">
            <xsd:complexType>
              <xsd:attribute name="alias" type="xsd:string" />
              <xsd:attribute name="name" type="xsd:string" />
            </xsd:complexType>
          </xsd:element>
          <xsd:element name="data">
            <xsd:complexType>
              <xsd:sequence>
                <xsd:element name="value" type="xsd:string" minOccurs="0" msdata:Ordinal="1" />
                <xsd:element name="comment" type="xsd:string" minOccurs="0" msdata:Ordinal="2" />
              </xsd:sequence>
              <xsd:attribute name="name" type="xsd:string" use="required" msdata:Ordinal="1" />
              <xsd:attribute name="type" type="xsd:string" msdata:Ordinal="3" />
              <xsd:attribute name="mimetype" type="xsd:string" msdata:Ordinal="4" />
              <xsd:attribute ref="xml:space" />
            </xsd:complexType>
          </xsd:element>
          <xsd:element name="resheader">
            <xsd:complexType>
              <xsd:sequence>
                <xsd:element name="value" type="xsd:string" minOccurs="0" msdata:Ordinal="1" />
              </xsd:sequence>
              <xsd:attribute name="name" type="xsd:string" use="required" />
            </xsd:complexType>
          </xsd:element>
        </xsd:choice>
      </xsd:complexType>
    </xsd:element>
  </xsd:schema>
  <resheader name="resmimetype">
    <value>text/microsoft-resx</value>
  </resheader>
  <resheader name="version">
    <value>2.0</value>
  </resheader>
  <resheader name="reader">
    <value>System.Resources.ResXResourceReader, System.Windows.Forms, Version=*******, Culture=neutral, PublicKeyToken=b77a5c561934e089</value>
  </resheader>
  <resheader name="writer">
    <value>System.Resources.ResXResourceWriter, System.Windows.Forms, Version=*******, Culture=neutral, PublicKeyToken=b77a5c561934e089</value>
  </resheader>
  <data name="ActivatedSuccessfully" xml:space="preserve">
    <value>Aktive etme işlemi başarılı</value>
  </data>
  <data name="Active" xml:space="preserve">
    <value>Aktif</value>
  </data>
  <data name="ActiveTooltip" xml:space="preserve">
    <value>Aktif seçimi olmayan firmaların yetkileri devre dışı kalacaktır</value>
  </data>
  <data name="Add" xml:space="preserve">
    <value>Ekle</value>
  </data>
  <data name="AddBranch" xml:space="preserve">
    <value>Şube ekle</value>
  </data>
  <data name="AddCompany" xml:space="preserve">
    <value>Yeni firma ekle</value>
  </data>
  <data name="AddedSuccessfully" xml:space="preserve">
    <value>Ekleme işlemi başarılı</value>
  </data>
  <data name="AddRole" xml:space="preserve">
    <value>Role ekle</value>
  </data>
  <data name="AddExtraFee" xml:space="preserve">
    <value>Extra ücret ekle</value>
  </data>
  <data name="AdminLogin" xml:space="preserve">
    <value>Yönetici girişi</value>
  </data>
  <data name="AdminNotFound" xml:space="preserve">
    <value>Yönetici bulunamadı</value>
  </data>
  <data name="AreYouSureToDeleteThisRecord" xml:space="preserve">
    <value>Bu kaydı silmek istediğinize emin misiniz?</value>
  </data>
  <data name="Back" xml:space="preserve">
    <value>Geri</value>
  </data>
  <data name="BranchAddress" xml:space="preserve">
    <value>Şube adresi</value>
  </data>
  <data name="BranchCityName" xml:space="preserve">
    <value>Şube şehir adı</value>
  </data>
  <data name="BranchCountryName" xml:space="preserve">
    <value>Şube ülke adı</value>
  </data>
  <data name="BranchDetails" xml:space="preserve">
    <value>Şube detayları</value>
  </data>
  <data name="BranchEmail" xml:space="preserve">
    <value>Şube epostası</value>
  </data>
  <data name="BranchList" xml:space="preserve">
    <value>Şube listesi</value>
  </data>
  <data name="BranchMission" xml:space="preserve">
    <value>Şube görevi</value>
  </data>
  <data name="BranchName" xml:space="preserve">
    <value>Şube adı</value>
  </data>
  <data name="BranchTelephone" xml:space="preserve">
    <value>Şube telefonu</value>
  </data>
  <data name="Clear" xml:space="preserve">
    <value>Temizle</value>
  </data>
  <data name="Close" xml:space="preserve">
    <value>Kapat</value>
  </data>
  <data name="CompanyDetails" xml:space="preserve">
    <value>Firma bilgileri</value>
  </data>
  <data name="CompanyList" xml:space="preserve">
    <value>Firma listesi</value>
  </data>
  <data name="CompanyModuleMatchExp" xml:space="preserve">
    <value>Aktive etmek istenen modüllerin yanındaki işaret kutuları seçilerek açılan alanlarda lisans sayısı ve lisans geçerlilik sürelerini tanımlanarak modül eklenebilir</value>
  </data>
  <data name="CompanyName" xml:space="preserve">
    <value>Firma adı</value>
  </data>
  <data name="CorporateId" xml:space="preserve">
    <value>Kurumsal kimlik</value>
  </data>
  <data name="Country" xml:space="preserve">
    <value>Ülke</value>
  </data>
  <data name="DatePickerFormatJs" xml:space="preserve">
    <value>dd/MM/yyyy</value>
  </data>
  <data name="DatePickerFormatView" xml:space="preserve">
    <value>dd/MM/yyyy</value>
  </data>
  <data name="TimePickerFormatView" xml:space="preserve">
    <value>HH:mm</value>
  </data>
  <data name="DbConnection" xml:space="preserve">
    <value>Veritabanı bağlantısı</value>
  </data>
  <data name="Delete" xml:space="preserve">
    <value>Sil</value>
  </data>
  <data name="DeletedSuccessfully" xml:space="preserve">
    <value>Silme işlemi başarılı</value>
  </data>
  <data name="Details" xml:space="preserve">
    <value>Detaylar</value>
  </data>
  <data name="DueDate" xml:space="preserve">
    <value>Bitiş tarihi</value>
  </data>
  <data name="Email" xml:space="preserve">
    <value>Eposta</value>
  </data>
  <data name="EnterYourDetailsToLogin" xml:space="preserve">
    <value>Hesabınıza giriş yapmak için bilgilerinizi girin</value>
  </data>
  <data name="ErrorOccurred" xml:space="preserve">
    <value>Hata oluştu</value>
  </data>
  <data name="ExtraFeeDetails" xml:space="preserve">
    <value>Ekstra ücret detayları</value>
  </data>
  <data name="ExtraFeeName" xml:space="preserve">
    <value>Ekstra ücret adı</value>
  </data>
  <data name="ExtraFeeType" xml:space="preserve">
    <value>Ekstra ücret türü</value>
  </data>
  <data name="ExtraFeeTypes" xml:space="preserve">
    <value>Ekstra ücret türleri</value>
  </data>
  <data name="Filter" xml:space="preserve">
    <value>Filtrele</value>
  </data>
  <data name="Hello" xml:space="preserve">
    <value>Merhaba</value>
  </data>
  <data name="IndicatesRequiredFields" xml:space="preserve">
    <value>Zorunlu alanları göstermektedir</value>
  </data>
  <data name="Languages" xml:space="preserve">
    <value>Diller</value>
  </data>
  <data name="LicenseCount" xml:space="preserve">
    <value>Lisans sayısı</value>
  </data>
  <data name="Login" xml:space="preserve">
    <value>Giriş</value>
  </data>
  <data name="Logout" xml:space="preserve">
    <value>Çıkış</value>
  </data>
  <data name="ModuleManagement" xml:space="preserve">
    <value>Modül yönetimi</value>
  </data>
  <data name="ModuleOperations" xml:space="preserve">
    <value>Modül işlemleri</value>
  </data>
  <data name="MultiCase" xml:space="preserve">
    <value>Çoklu durum</value>
  </data>
  <data name="MustBeGreateThanZero" xml:space="preserve">
    <value>0'dan büyük bir değer girilmeli</value>
  </data>
  <data name="OperationIsSuccessful" xml:space="preserve">
    <value>İşlem başarılı</value>
  </data>
  <data name="Operations" xml:space="preserve">
    <value>İşlemler</value>
  </data>
  <data name="PageNotFound" xml:space="preserve">
    <value>Sayfa bulunamadı</value>
  </data>
  <data name="PassivatedSuccessfully" xml:space="preserve">
    <value>Pasife çekme işlemi başarılı</value>
  </data>
  <data name="Passive" xml:space="preserve">
    <value>Pasif</value>
  </data>
  <data name="Password" xml:space="preserve">
    <value>Şifre</value>
  </data>
  <data name="RequiredField" xml:space="preserve">
    <value>Zorunlu alan</value>
  </data>
  <data name="RoleDescription" xml:space="preserve">
    <value>Rol açıklama</value>
  </data>
  <data name="RoleDetails" xml:space="preserve">
    <value>Rol detayları</value>
  </data>
  <data name="RoleList" xml:space="preserve">
    <value>Rol listesi</value>
  </data>
  <data name="RoleName" xml:space="preserve">
    <value>Rol adı</value>
  </data>
  <data name="Save" xml:space="preserve">
    <value>Kaydet</value>
  </data>
  <data name="Select" xml:space="preserve">
    <value>Seç</value>
  </data>
  <data name="SelectToAddModule" xml:space="preserve">
    <value>Modülünü eklemek için seçim işaretleyiniz</value>
  </data>
  <data name="ServerError" xml:space="preserve">
    <value>Sunucu hatası</value>
  </data>
  <data name="Show" xml:space="preserve">
    <value>Göster</value>
  </data>
  <data name="SomethingWentWrong" xml:space="preserve">
    <value>Bir şeyler yanlış gitti</value>
  </data>
  <data name="Status" xml:space="preserve">
    <value>Durum</value>
  </data>
  <data name="Update" xml:space="preserve">
    <value>Güncelle</value>
  </data>
  <data name="UpdateBranch" xml:space="preserve">
    <value>Şube Güncelle</value>
  </data>
  <data name="UpdateCompany" xml:space="preserve">
    <value>Firma güncelleme</value>
  </data>
  <data name="UpdateCompanyModuleMatchExp_1" xml:space="preserve">
    <value>Yeni bir modül tanımlamak için işaret kutusu seçilerek bilgiler girilebilir. Ekle butonu ile yeni modül tanımı tamamlanacaktır</value>
  </data>
  <data name="UpdateCompanyModuleMatchExp_2" xml:space="preserve">
    <value>Pasif durumda olan modüllerin yanında güncelle ve sil butonları bulunur, bir modül tümüyle silinmedikten sonra her zaman veritabanında pasif işlem olarak saklanır.  İşaret kutusu seçilen pasif modüller güncellenerek aktive edilebilir</value>
  </data>
  <data name="UpdateCompanyModuleMatchExp_3" xml:space="preserve">
    <value>Aktif bir modül işaret kutusu seçimi kaldırılıp güncellenerek pasif duruma döndürülebilir, öylece ilgili kullanıcıların yetkisi askıya alınmış olur</value>
  </data>
  <data name="UpdateCompanyModuleMatchExp_4" xml:space="preserve">
    <value>Aktif / pasif modüllerde bulunan sil butonları ile modül kaydı ve bağlantıları tümüyle silinebilir</value>
  </data>
  <data name="UpdatedSuccessfully" xml:space="preserve">
    <value>Güncelleme işlemi başarılı</value>
  </data>
  <data name="UpdateRole" xml:space="preserve">
    <value>Rol güncelle</value>
  </data>
  <data name="UpdateExtrafee" xml:space="preserve">
    <value>Ekstra ücret güncelle</value>
  </data>
  <data name="UserLogin" xml:space="preserve">
    <value>Kullanıcı girişi</value>
  </data>
  <data name="Username" xml:space="preserve">
    <value>Kullanıcı adı</value>
  </data>
  <data name="UserNotFound" xml:space="preserve">
    <value>Kullanıcı bulunamadı</value>
  </data>
  <data name="UserProfile" xml:space="preserve">
    <value>Kullanıcı profili</value>
  </data>
  <data name="ActiveModuleCount" xml:space="preserve">
    <value>Aktif modül sayısı</value>
  </data>
  <data name="AddNewModule" xml:space="preserve">
    <value>Yeni modül ekle</value>
  </data>
  <data name="UpdateExistingModules" xml:space="preserve">
    <value>Mevcut modülleri güncelle</value>
  </data>
  <data name="AddCompanyModuleExp" xml:space="preserve">
    <value>Bu form üzerinden firmaya daha önce tanımlanmamış olan yeni modüller eklenebilmektedir. Daha önce tanımlanmış, durumu aktif ya da pasif olan modüller üzerinde işlem yapmak için 'Mevcut Modülleri Güncelle' ile devam etmek gerekmektedir</value>
  </data>
  <data name="UpdateCompanyModeuleExp" xml:space="preserve">
    <value>Bu form üzerinden sadece mevcut olan aktif ve/veya pasif modüller güncellenebilir. Daha önce firmaya tanımlanmamış bir modül eklemek için 'Yeni Modül Ekle' ile devam etmek gerekmektedir </value>
  </data>
  <data name="NoModuleFoundToAdd" xml:space="preserve">
    <value>Eklenecek bir modül bulunamadı, bu firma için tüm modül tanımlamaları gerçekleştirilmiştir. İşlemlere Mevcut Modülleri Güncelle butonu üzerinden devam edilebilir</value>
  </data>
  <data name="NoModuleFoundToUpdate" xml:space="preserve">
    <value>Güncellemeye açık bir modül bulunamadı. Yeni Modül Ekle butonu üzerinden modül tanımlaması yapılabilir</value>
  </data>
  <data name="ModuleName" xml:space="preserve">
    <value>Modül adı</value>
  </data>
  <data name="Company" xml:space="preserve">
    <value>Firma</value>
  </data>
  <data name="Dashboard" xml:space="preserve">
    <value>Pano</value>
  </data>
  <data name="No" xml:space="preserve">
    <value>Hayır</value>
  </data>
  <data name="Yes" xml:space="preserve">
    <value>Evet</value>
  </data>
  <data name="AddApplicationStatus" xml:space="preserve">
    <value>Başvuru statüsü ekle</value>
  </data>
  <data name="ApplicationStatusDescription" xml:space="preserve">
    <value>Başvuru statü açıklaması</value>
  </data>
  <data name="ApplicationStatusDetails" xml:space="preserve">
    <value>Başvuru statü detayları</value>
  </data>
  <data name="ApplicationStatusName" xml:space="preserve">
    <value>Başvuru statü adı</value>
  </data>
  <data name="ApplicationStatusTypes" xml:space="preserve">
    <value>Başvuru statü türleri</value>
  </data>
  <data name="SendOfEmail" xml:space="preserve">
    <value>Email gönderimi</value>
  </data>
  <data name="SendOfSms" xml:space="preserve">
    <value>Sms gönderimi</value>
  </data>
  <data name="UpdateApplicationStatus" xml:space="preserve">
    <value>Başvuru statüsü güncelle</value>
  </data>
  <data name="ActionDescription" xml:space="preserve">
    <value>Sayfa açıklama</value>
  </data>
  <data name="ActionDetails" xml:space="preserve">
    <value>Sayfa detayları</value>
  </data>
  <data name="ActionList" xml:space="preserve">
    <value>Sayfa listesi</value>
  </data>
  <data name="ActionName" xml:space="preserve">
    <value>Sayfa başlık</value>
  </data>
  <data name="AddAction" xml:space="preserve">
    <value>Sayfa ekle</value>
  </data>
  <data name="UpdateAction" xml:space="preserve">
    <value>Sayfa güncelle</value>
  </data>
  <data name="AreaName" xml:space="preserve">
    <value>Alan adı</value>
  </data>
  <data name="ControllerName" xml:space="preserve">
    <value>Controller ad</value>
  </data>
  <data name="IsPublicAddress" xml:space="preserve">
    <value>Açık adres</value>
  </data>
  <data name="IsShownOnMenu" xml:space="preserve">
    <value>Menüde görünsün</value>
  </data>
  <data name="MethodName" xml:space="preserve">
    <value>Metot ad</value>
  </data>
  <data name="MenuOrder" xml:space="preserve">
    <value>Menü sırası</value>
  </data>
  <data name="AddSubAction" xml:space="preserve">
    <value>Alt sayfa ekle</value>
  </data>
  <data name="Address" xml:space="preserve">
    <value>Adres</value>
  </data>
  <data name="CountryName" xml:space="preserve">
    <value>Ülke adı</value>
  </data>
  <data name="AddBranchApplicationCountry" xml:space="preserve">
    <value>Şube başvuru ülkesi ekle</value>
  </data>
  <data name="Branch" xml:space="preserve">
    <value>Şube</value>
  </data>
  <data name="BranchApplicationCountryDetails" xml:space="preserve">
    <value>Şube başvuru ülkesi detayları</value>
  </data>
  <data name="BranchApplicationCountryList" xml:space="preserve">
    <value>Şube başvuru ülke listesi</value>
  </data>
  <data name="UpdateBranchApplicationCountry" xml:space="preserve">
    <value>Şube bavşuru ülkesi güncelle</value>
  </data>
  <data name="AddUser" xml:space="preserve">
    <value>Kullanıcı ekle</value>
  </data>
  <data name="Name" xml:space="preserve">
    <value>Ad</value>
  </data>
  <data name="PhoneNumber" xml:space="preserve">
    <value>Telefon numarası</value>
  </data>
  <data name="Surname" xml:space="preserve">
    <value>Soyad</value>
  </data>
  <data name="UpdatePassword" xml:space="preserve">
    <value>Şifreyi güncelle</value>
  </data>
  <data name="UpdateUser" xml:space="preserve">
    <value>Kullanıcı güncelle</value>
  </data>
  <data name="UserDetails" xml:space="preserve">
    <value>Kullanıcı detayları</value>
  </data>
  <data name="UserList" xml:space="preserve">
    <value>Kullanıcı listesi</value>
  </data>
  <data name="PageAuthority" xml:space="preserve">
    <value>Sayfa yetkisi</value>
  </data>
  <data name="AddUserModule" xml:space="preserve">
    <value>Kullanıcı modülü ekle</value>
  </data>
  <data name="UserModuleList" xml:space="preserve">
    <value>Kullanıcı modül listesi</value>
  </data>
  <data name="UserModuleDetails" xml:space="preserve">
    <value>Kullanıcı modül detayları</value>
  </data>
  <data name="Module" xml:space="preserve">
    <value>Modül</value>
  </data>
  <data name="UserRoles" xml:space="preserve">
    <value>Kullanıcı rolleri</value>
  </data>
  <data name="AddUserBranch" xml:space="preserve">
    <value>Kullanıcı şubesi ekle</value>
  </data>
  <data name="UserBranchList" xml:space="preserve">
    <value>Kullanıcı şube listesi</value>
  </data>
  <data name="UserBranchDetails" xml:space="preserve">
    <value>Kullanıcı şube detayları</value>
  </data>
  <data name="UpdateBranchApplicationCountryVisaCategory" xml:space="preserve">
    <value>Şube başvuru ülkesi vize kategorileri güncelle</value>
  </data>
  <data name="UpdateVisaCategories" xml:space="preserve">
    <value>Vize kategorilerini güncelle</value>
  </data>
  <data name="VisaCategory" xml:space="preserve">
    <value>Vize kategorisi</value>
  </data>
  <data name="SelectBranch" xml:space="preserve">
    <value>Şube seç</value>
  </data>
  <data name="BranchNotFound" xml:space="preserve">
    <value>Şube bulunamadı</value>
  </data>
  <data name="PleaseContactYourAdministrator" xml:space="preserve">
    <value>Lütfen yöneticinize başvurun</value>
  </data>
  <data name="AddBranchApplicationCountryExtraFee" xml:space="preserve">
    <value>Şube başvuru ülkesi ekstra ücreti ekle</value>
  </data>
  <data name="BranchApplicationCountryExtraFeeList" xml:space="preserve">
    <value>Şube başvuru ülkesi ekstra ücret listesi</value>
  </data>
  <data name="BranchApplicationCountryExtraFees" xml:space="preserve">
    <value>Şube başvuru ülkesi ekstra ücretleri</value>
  </data>
  <data name="Currency" xml:space="preserve">
    <value>Para birimi</value>
  </data>
  <data name="IsAutoChecked" xml:space="preserve">
    <value>Otomatik seçili</value>
  </data>
  <data name="Price" xml:space="preserve">
    <value>Ücret</value>
  </data>
  <data name="Tax" xml:space="preserve">
    <value>Vergi</value>
  </data>
  <data name="UpdateBranchApplicationCountryExtraFee" xml:space="preserve">
    <value>Şube başvuru ülkesi ekstra ücreti güncelle</value>
  </data>
  <data name="ApplicationStatusOrder" xml:space="preserve">
    <value>Başvuru statü sırası</value>
  </data>
  <data name="OrderStatus" xml:space="preserve">
    <value>Durum sırası</value>
  </data>
  <data name="SelectModule" xml:space="preserve">
    <value>Modül seç</value>
  </data>
  <data name="SelectModuleBranch" xml:space="preserve">
    <value>Modül / şube seç</value>
  </data>
  <data name="ModuleNotFound" xml:space="preserve">
    <value>Modül bulunamadı</value>
  </data>
  <data name="ReturnUrl" xml:space="preserve">
    <value>Yönlendirme adresi</value>
  </data>
  <data name="ApplicationEmailStatus" xml:space="preserve">
    <value>Başvuru durumu email bilgileri</value>
  </data>
  <data name="ApplicationSmsStatus" xml:space="preserve">
    <value>Başvuru durumu sms bilgileri</value>
  </data>
  <data name="EmailContent" xml:space="preserve">
    <value>Email içeriği</value>
  </data>
  <data name="Provider" xml:space="preserve">
    <value>Tedarikçi</value>
  </data>
  <data name="SmsContent" xml:space="preserve">
    <value>Sms içeriği</value>
  </data>
  <data name="UpdateApplicationEmailStatus" xml:space="preserve">
    <value>Başvuru durumu email bilgilerini güncelle</value>
  </data>
  <data name="UpdateApplicationSmsStatus" xml:space="preserve">
    <value>Başvuru durumu sms bilgilerini güncelle</value>
  </data>
  <data name="UpdateApplicationFormElements" xml:space="preserve">
    <value>Başvuru form öğelerini güncelle</value>
  </data>
  <data name="FormElementName" xml:space="preserve">
    <value>Form öğe adı</value>
  </data>
  <data name="Required" xml:space="preserve">
    <value>Zorunlu</value>
  </data>
  <data name="SelectAll" xml:space="preserve">
    <value>Tümünü seç</value>
  </data>
  <data name="CompanyModuleManagement" xml:space="preserve">
    <value>Modül yönetimi</value>
  </data>
  <data name="ApplicationForm" xml:space="preserve">
    <value>Başvuru formu</value>
  </data>
  <data name="SelectCountry" xml:space="preserve">
    <value>Ülke seçiniz</value>
  </data>
  <data name="ApplicationStep1Description" xml:space="preserve">
    <value>Açıklama</value>
  </data>
  <data name="ApplicationStep2BasicInformation" xml:space="preserve">
    <value>Temel bilgiler</value>
  </data>
  <data name="ApplicationStep3DocumentInformation" xml:space="preserve">
    <value>Belge bilgileri</value>
  </data>
  <data name="ApplicationStep4ExtraPackages" xml:space="preserve">
    <value>Ekstra paketler</value>
  </data>
  <data name="ApplicationStep5Finalize" xml:space="preserve">
    <value>Sonuçlandır</value>
  </data>
  <data name="ApplicantType" xml:space="preserve">
    <value>Başvuran türü</value>
  </data>
  <data name="ApplicationType" xml:space="preserve">
    <value>Başvuru türü</value>
  </data>
  <data name="PassportExpireDate" xml:space="preserve">
    <value>Pasaport geçerlilik tarihi</value>
  </data>
  <data name="PassportNumber" xml:space="preserve">
    <value>Pasaport numarası</value>
  </data>
  <data name="FillRequiredFields" xml:space="preserve">
    <value>Zorunlu Alanları Doldurun</value>
  </data>
  <data name="NextStep" xml:space="preserve">
    <value>Sonraki adım</value>
  </data>
  <data name="PreviousStep" xml:space="preserve">
    <value>Önceki adım</value>
  </data>
  <data name="AddNewApplication" xml:space="preserve">
    <value>Yeni başvuru ekle</value>
  </data>
  <data name="BirthDate" xml:space="preserve">
    <value>Doğum tarihi</value>
  </data>
  <data name="FatherName" xml:space="preserve">
    <value>Baba adı</value>
  </data>
  <data name="Gender" xml:space="preserve">
    <value>Cinsiyet</value>
  </data>
  <data name="IdentificationNumber" xml:space="preserve">
    <value>Kimlik numarası</value>
  </data>
  <data name="MaidenName" xml:space="preserve">
    <value>Bekarlık soyadı</value>
  </data>
  <data name="MotherName" xml:space="preserve">
    <value>Anne adı</value>
  </data>
  <data name="Nationality" xml:space="preserve">
    <value>Uyruk</value>
  </data>
  <data name="RegistrationPlaceCity" xml:space="preserve">
    <value>Kayıt yeri (şehir)</value>
  </data>
  <data name="RegistrationPlaceCountry" xml:space="preserve">
    <value>Kayıt yeri (ülke)</value>
  </data>
  <data name="Title" xml:space="preserve">
    <value>Ünvan</value>
  </data>
  <data name="InvalidFormat" xml:space="preserve">
    <value>Geçersiz format</value>
  </data>
  <data name="Color" xml:space="preserve">
    <value>Renk</value>
  </data>
  <data name="Item" xml:space="preserve">
    <value>Adet</value>
  </data>
  <data name="ClickForNewApplication" xml:space="preserve">
    <value>Yeni başvuru için tıklayın</value>
  </data>
  <data name="ApplicationPassportStatus" xml:space="preserve">
    <value>Pasaport durumu</value>
  </data>
  <data name="MaritalStatus" xml:space="preserve">
    <value>Medeni durumu</value>
  </data>
  <data name="MinimumItem" xml:space="preserve">
    <value>Asgari adet</value>
  </data>
  <data name="YouWillBeRedirectedTo" xml:space="preserve">
    <value>Yönlendirileceksiniz</value>
  </data>
  <data name="ApplicationList" xml:space="preserve">
    <value>Başvuru listesi</value>
  </data>
  <data name="ApplicationFormElements" xml:space="preserve">
    <value>Başvuru formu öğeleri</value>
  </data>
  <data name="InformationNotes" xml:space="preserve">
    <value>Bilgilendirme notları</value>
  </data>
  <data name="ApplicationTime" xml:space="preserve">
    <value>Başvuru zamanı</value>
  </data>
  <data name="FilteringOptions" xml:space="preserve">
    <value>Filtreleme seçenekleri</value>
  </data>
  <data name="ApplicationDate" xml:space="preserve">
    <value>Başvuru tarihi</value>
  </data>
  <data name="ApplicationNumber" xml:space="preserve">
    <value>Başvuru numarası</value>
  </data>
  <data name="Contact" xml:space="preserve">
    <value>İletişim</value>
  </data>
  <data name="Category" xml:space="preserve">
    <value>Kategori</value>
  </data>
  <data name="AtMostTenBarcodesScan" xml:space="preserve">
    <value>En fazla 10 barkod taratılabilir</value>
  </data>
  <data name="BarcodeAlreadyScanned" xml:space="preserve">
    <value>Barkod zaten taratılmıştır</value>
  </data>
  <data name="PleaseScanBarcode" xml:space="preserve">
    <value>Lütfen barkodu taratınız</value>
  </data>
  <data name="RemoveAllBarcodes" xml:space="preserve">
    <value>Tüm barkodları kaldır</value>
  </data>
  <data name="AccomodationDetail" xml:space="preserve">
    <value>Konaklama bilgisi</value>
  </data>
  <data name="ApplicationTogether" xml:space="preserve">
    <value>Birlikte başvuru mu?</value>
  </data>
  <data name="BankBalance" xml:space="preserve">
    <value>Banka bakiyesi</value>
  </data>
  <data name="CityToVisit" xml:space="preserve">
    <value>Ziyaret edilecek şehir</value>
  </data>
  <data name="EntryDate" xml:space="preserve">
    <value>Giriş tarihi</value>
  </data>
  <data name="ExitDate" xml:space="preserve">
    <value>Çıkış tarihi</value>
  </data>
  <data name="HasDeed" xml:space="preserve">
    <value>Tapusu var mı?</value>
  </data>
  <data name="HasEntryBan" xml:space="preserve">
    <value>Giriş yasağı var mı?</value>
  </data>
  <data name="HasRelativeAbroad" xml:space="preserve">
    <value>Yurtdışında akrabası var mı?</value>
  </data>
  <data name="Job" xml:space="preserve">
    <value>Meslek</value>
  </data>
  <data name="MonthlySalary" xml:space="preserve">
    <value>Aylık maaş</value>
  </data>
  <data name="PersonTravelWith" xml:space="preserve">
    <value>Birlikte seyahat ettiği kişi</value>
  </data>
  <data name="PersonTravelWithHasVisa" xml:space="preserve">
    <value>Birlikte seyahat ettiği kişinin vizesi var mı?</value>
  </data>
  <data name="ReimbursementType" xml:space="preserve">
    <value>Masraf karşılama türü</value>
  </data>
  <data name="RelativeLocation" xml:space="preserve">
    <value>Akrabanın bulunduğu yer</value>
  </data>
  <data name="TotalDay" xml:space="preserve">
    <value>Toplam gün</value>
  </data>
  <data name="TotalYearInCompany" xml:space="preserve">
    <value>Kaç yıldır şirkette çalışıyor?</value>
  </data>
  <data name="TotalYearInCountry" xml:space="preserve">
    <value>Kaç yıldır ülkede yaşıyor?</value>
  </data>
  <data name="VisaEntryType" xml:space="preserve">
    <value>Vize giriş türü</value>
  </data>
  <data name="PreviouslyObtainedVisas" xml:space="preserve">
    <value>Daha önceden alınmış vizeler</value>
  </data>
  <data name="ApplicationIsCompleted" xml:space="preserve">
    <value>Başvuru tamamlandı</value>
  </data>
  <data name="SelectNextAction" xml:space="preserve">
    <value>Sonraki işlemi seçiniz</value>
  </data>
  <data name="ApplicationDetails" xml:space="preserve">
    <value>Başvuru detayları</value>
  </data>
  <data name="ExportToSAP" xml:space="preserve">
    <value>SAP'ye aktar</value>
  </data>
  <data name="PrintApplicationPage" xml:space="preserve">
    <value>Başvuru sayfası yazdır</value>
  </data>
  <data name="PrintBarcode" xml:space="preserve">
    <value>Barkod yazdır</value>
  </data>
  <data name="PrintICR" xml:space="preserve">
    <value>ICR yazdır</value>
  </data>
  <data name="PrintInsurancePolicy" xml:space="preserve">
    <value>Sigorta poliçe yazdır</value>
  </data>
  <data name="ApplicationSummary" xml:space="preserve">
    <value>Başvuru özeti</value>
  </data>
  <data name="ApplicationDocumentSummary" xml:space="preserve">
    <value>Başvuru belge özeti</value>
  </data>
  <data name="ApplicationExtraFeeSummary" xml:space="preserve">
    <value>Başvuru ekstra ücret özeti</value>
  </data>
  <data name="ApplicationNotFound" xml:space="preserve">
    <value>Başvuru bulunamadı</value>
  </data>
  <data name="UpdateAll" xml:space="preserve">
    <value>Tümünü güncelle</value>
  </data>
  <data name="CreateInsurance" xml:space="preserve">
    <value>Sigorta Oluştur</value>
  </data>
  <data name="Description" xml:space="preserve">
    <value>Açıklama</value>
  </data>
  <data name="Value" xml:space="preserve">
    <value>Değer</value>
  </data>
  <data name="PleaseWait" xml:space="preserve">
    <value>Lütfen bekleyin</value>
  </data>
  <data name="CorporateName" xml:space="preserve">
    <value>Firma Adı</value>
  </data>
  <data name="InvoiceNumber" xml:space="preserve">
    <value>Vergi numarası</value>
  </data>
  <data name="Arabic" xml:space="preserve">
    <value>Arapça</value>
  </data>
  <data name="English" xml:space="preserve">
    <value>İngilizce</value>
  </data>
  <data name="NotSufficientPrivileges" xml:space="preserve">
    <value>Bu işlem için yetkiniz bulunmamaktadır</value>
  </data>
  <data name="ChooseNewApplicationStatus" xml:space="preserve">
    <value>Yeni başvuru durumu seçiniz</value>
  </data>
  <data name="DatedApplications" xml:space="preserve">
    <value>Tarihli başvurular</value>
  </data>
  <data name="ForUpdatingAllApplicationStatus" xml:space="preserve">
    <value>Tüm başvuruların durumunu güncellemek için</value>
  </data>
  <data name="PleaseClick" xml:space="preserve">
    <value>Lütfen tıklayınız</value>
  </data>
  <data name="Record" xml:space="preserve">
    <value>Kayıt</value>
  </data>
  <data name="WillbeUpdated" xml:space="preserve">
    <value>Güncellenecektir</value>
  </data>
  <data name="ContactInformation" xml:space="preserve">
    <value>İletişim bilgileri</value>
  </data>
  <data name="CannotApplyWithThisPassportNumber" xml:space="preserve">
    <value>Bu pasaport numarası ile başvuru yapamazsınız</value>
  </data>
  <data name="ExistingInitialApplicationStatus" xml:space="preserve">
    <value>İlk başvuru durumu zaten mevcut</value>
  </data>
  <data name="ApplicationStatus" xml:space="preserve">
    <value>Başvuru durumu</value>
  </data>
  <data name="Search" xml:space="preserve">
    <value>Ara</value>
  </data>
  <data name="History" xml:space="preserve">
    <value>Geçmiş</value>
  </data>
  <data name="Notes" xml:space="preserve">
    <value>Notlar</value>
  </data>
  <data name="Application" xml:space="preserve">
    <value>Başvuru</value>
  </data>
  <data name="BranchDailyStatisctics" xml:space="preserve">
    <value>Şube günlük istatistikleri</value>
  </data>
  <data name="BranchDistribution" xml:space="preserve">
    <value>Şube dağılımı</value>
  </data>
  <data name="DailyStatistics" xml:space="preserve">
    <value>Günlük veriler</value>
  </data>
  <data name="WeeklyApplicationStatistics" xml:space="preserve">
    <value>Haftalık başvuru dağılımı</value>
  </data>
  <data name="AddNewFamilyApplication" xml:space="preserve">
    <value>Yeni aile başvurusu ekle</value>
  </data>
  <data name="AddNewGroupApplication" xml:space="preserve">
    <value>Yeni grup başvurusu ekle</value>
  </data>
  <data name="FamilyApplicationFormWarning" xml:space="preserve">
    <value>Aile başvuru formu dolduruyorsunuz</value>
  </data>
  <data name="GroupApplicationFormWarning" xml:space="preserve">
    <value>Grup başvuru formu dolduruyorsunuz</value>
  </data>
  <data name="RelationalApplicationNumber" xml:space="preserve">
    <value>İlişkili başvuru numarası</value>
  </data>
  <data name="Main" xml:space="preserve">
    <value>Ana</value>
  </data>
  <data name="StatusHistory" xml:space="preserve">
    <value>Durum geçmişi</value>
  </data>
  <data name="UpdateBy" xml:space="preserve">
    <value>Güncelleyen</value>
  </data>
  <data name="AddPhotoBooth" xml:space="preserve">
    <value>Fotokabin kaydı oluştur</value>
  </data>
  <data name="PhotoBoothOperations" xml:space="preserve">
    <value>Fotokabin işlemleri</value>
  </data>
  <data name="ReferenceType" xml:space="preserve">
    <value>Kayıt türü</value>
  </data>
  <data name="WithoutReference" xml:space="preserve">
    <value>Başvurusuz</value>
  </data>
  <data name="WithReference" xml:space="preserve">
    <value>Başvurulu</value>
  </data>
  <data name="CreateBarcode" xml:space="preserve">
    <value>Barkod oluştur</value>
  </data>
  <data name="NonReferencedPhotoBoothOperations" xml:space="preserve">
    <value>Başvurusuz fotokabin işlemleri</value>
  </data>
  <data name="ReferencedPhotoBoothOperations" xml:space="preserve">
    <value>Başvurulu fotokabin işlemleri</value>
  </data>
  <data name="CannotProcess" xml:space="preserve">
    <value>İşlem yapılamıyor</value>
  </data>
  <data name="AreYouSureCreateSapOrder" xml:space="preserve">
    <value>Başvuru SAP aktarılacaktır, emin misiniz?</value>
  </data>
  <data name="BranchSapId" xml:space="preserve">
    <value>Şube sap numarası</value>
  </data>
  <data name="ExtraFeeSapId" xml:space="preserve">
    <value>Extra ücret sap numarası</value>
  </data>
  <data name="Cancel" xml:space="preserve">
    <value>İptal et</value>
  </data>
  <data name="PartialRefund" xml:space="preserve">
    <value>Kısmi iade</value>
  </data>
  <data name="ApplicationCancellationRequestSaved" xml:space="preserve">
    <value>Başvuru iptali talebi kaydedildi</value>
  </data>
  <data name="ApplicationCancellationReason" xml:space="preserve">
    <value>Başvuru iptal/kısmi iade nedeni</value>
  </data>
  <data name="ApplicationCancellationDetails" xml:space="preserve">
    <value>Başvuru iptal/kısmi iade detayları</value>
  </data>
  <data name="ApplicationCancellationType" xml:space="preserve">
    <value>Başvuru iptal türü</value>
  </data>
  <data name="ApplicationCancellationRequestList" xml:space="preserve">
    <value>Başvuru iptal/kısmi iade talebi listesi</value>
  </data>
  <data name="ApplicationCancellationStatus" xml:space="preserve">
    <value>Başvuru iptal/kısmi iade durumu</value>
  </data>
  <data name="CreatedAt" xml:space="preserve">
    <value>Oluşturulma zamanı</value>
  </data>
  <data name="CreatedBy" xml:space="preserve">
    <value>Oluşturan kişi</value>
  </data>
  <data name="Approve" xml:space="preserve">
    <value>Onayla</value>
  </data>
  <data name="Reject" xml:space="preserve">
    <value>Reddet</value>
  </data>
  <data name="AreYouSureToDoThisAction" xml:space="preserve">
    <value>Bu işlemi yapmak istediğinize emin misiniz?</value>
  </data>
  <data name="Piece" xml:space="preserve">
    <value>Adet</value>
  </data>
  <data name="AverageApplicationProcessTime" xml:space="preserve">
    <value>Ortalama başvuru alma süresi</value>
  </data>
  <data name="DateRange" xml:space="preserve">
    <value>Tarih aralığı</value>
  </data>
  <data name="Day" xml:space="preserve">
    <value>Gün</value>
  </data>
  <data name="PreviousMonthVasSales" xml:space="preserve">
    <value>Son ay yapılan VAS satışları</value>
  </data>
  <data name="PreviousQuarterApplicationNumbers" xml:space="preserve">
    <value>Son çeyrek yapılan başvurular</value>
  </data>
  <data name="PreviousWeekApplicationNumbers" xml:space="preserve">
    <value>Son hafta yapılan başvuru sayıları</value>
  </data>
  <data name="Total" xml:space="preserve">
    <value>Toplam</value>
  </data>
  <data name="DailyPassportStatusValues" xml:space="preserve">
    <value>Günlük pasaport dağıtım verileri</value>
  </data>
  <data name="DeclinedApplication" xml:space="preserve">
    <value>Reddedilen başvuru</value>
  </data>
  <data name="DeclinedRatio" xml:space="preserve">
    <value>Ret oranı</value>
  </data>
  <data name="FromBeginning" xml:space="preserve">
    <value>Başlangıçtan itibaren</value>
  </data>
  <data name="Insurance" xml:space="preserve">
    <value>Sigorta</value>
  </data>
  <data name="LastUpdate" xml:space="preserve">
    <value>Son güncelleme</value>
  </data>
  <data name="NumberOfActiveUsers" xml:space="preserve">
    <value>Aktif kullanıcı sayısı</value>
  </data>
  <data name="PendingApproval" xml:space="preserve">
    <value>Onay bekleyen</value>
  </data>
  <data name="Person" xml:space="preserve">
    <value>Kişi</value>
  </data>
  <data name="Realized" xml:space="preserve">
    <value>Gerçekleşen</value>
  </data>
  <data name="AllBranches" xml:space="preserve">
    <value>Tüm şubeler</value>
  </data>
  <data name="NumberOfCompletedApplications" xml:space="preserve">
    <value>Tamamlanan başvuru sayısı</value>
  </data>
  <data name="NumberOfRegisteredUser" xml:space="preserve">
    <value>Kayıtlı kullanıcı sayısı</value>
  </data>
  <data name="TotalApplicationsBasedOnCountry" xml:space="preserve">
    <value>Ülkelere göre toplam başvuru sayısı</value>
  </data>
  <data name="BranchesDailyApplicationStats" xml:space="preserve">
    <value>Şubelerin günlük başvuru sayısı</value>
  </data>
  <data name="QuarterApplicationsOfCountries" xml:space="preserve">
    <value>Son çeyrek yapılan başvuru sayısı</value>
  </data>
  <data name="QuarterApplicationCategoriesOfCountries" xml:space="preserve">
    <value>Son çeyrek yapılan başvuru kategorileri</value>
  </data>
  <data name="QuarterVasOfCountries" xml:space="preserve">
    <value>Son çeyrek yapılan VAS satışları</value>
  </data>
  <data name="AllCountries" xml:space="preserve">
    <value>Tüm ülkeler</value>
  </data>
  <data name="QuarterApplicationStatusOfCountries" xml:space="preserve">
    <value>Son çeyrek yapılan başvuru statüleri</value>
  </data>
  <data name="DeclinedAndDeliverd" xml:space="preserve">
    <value>Vize ret iadesi yapıldı</value>
  </data>
  <data name="ApplicationCancellationWarning" xml:space="preserve">
    <value>Bu işlem yöneticinizin onayına sunulacaktır</value>
  </data>
  <data name="EntryBannedApplications" xml:space="preserve">
    <value>Giriş yasaklı başvurular</value>
  </data>
  <data name="Facebook" xml:space="preserve">
    <value>Facebook</value>
  </data>
  <data name="Instagram" xml:space="preserve">
    <value>Instagram</value>
  </data>
  <data name="Linkedin" xml:space="preserve">
    <value>Linkedin</value>
  </data>
  <data name="NumberOfBiometricData" xml:space="preserve">
    <value>Biyometrik veri sayısı</value>
  </data>
  <data name="SocialMedia" xml:space="preserve">
    <value>Sosyal medya</value>
  </data>
  <data name="ActiveInsurance" xml:space="preserve">
    <value>Devam eden sigorta sayısı</value>
  </data>
  <data name="TotalInsurance" xml:space="preserve">
    <value>Toplam yapılan sigorta sayısı</value>
  </data>
  <data name="FreeApplicationsOfCountries" xml:space="preserve">
    <value>Ülkelerde alınan ücretsiz başvurular</value>
  </data>
  <data name="Reporting" xml:space="preserve">
    <value>Raporlama</value>
  </data>
  <data name="ReportType" xml:space="preserve">
    <value>Rapor türü</value>
  </data>
  <data name="EndDate" xml:space="preserve">
    <value>Bitiş tarihi</value>
  </data>
  <data name="StartDate" xml:space="preserve">
    <value>Başlangıç tarihi</value>
  </data>
  <data name="Create" xml:space="preserve">
    <value>Oluştur</value>
  </data>
  <data name="ReportDate" xml:space="preserve">
    <value>Rapor tarihi</value>
  </data>
  <data name="StaffName" xml:space="preserve">
    <value>Personel adı</value>
  </data>
  <data name="StaffSurname" xml:space="preserve">
    <value>Personel soyadı</value>
  </data>
  <data name="TotalApplicationCount" xml:space="preserve">
    <value>Toplam başvuru sayısı</value>
  </data>
  <data name="OrderNumber" xml:space="preserve">
    <value>Sıra numarası</value>
  </data>
  <data name="StaffNameSurname" xml:space="preserve">
    <value>Personel ad soyad</value>
  </data>
  <data name="BadRequest" xml:space="preserve">
    <value>Geçersiz istek</value>
  </data>
  <data name="DataNotFound" xml:space="preserve">
    <value>Veri bulunamadı</value>
  </data>
  <data name="ErrorOccurredInApplicationStatusCheck" xml:space="preserve">
    <value>Başvuru durumu kontrolünde hata oluştu</value>
  </data>
  <data name="PartiallyReplaceable" xml:space="preserve">
    <value>Kısmi güncellenebilir</value>
  </data>
  <data name="Static" xml:space="preserve">
    <value>Statik</value>
  </data>
  <data name="Updateable" xml:space="preserve">
    <value>Değiştirilebilir</value>
  </data>
  <data name="AverageBiometricAcquisition" xml:space="preserve">
    <value>Ortalama biyometrik veri alım süresi</value>
  </data>
  <data name="AverageWaitingTime" xml:space="preserve">
    <value>Ortalama başvuru bekleme süresi</value>
  </data>
  <data name="GeneralPicture" xml:space="preserve">
    <value>Genel tablo</value>
  </data>
  <data name="Hour" xml:space="preserve">
    <value>Saat</value>
  </data>
  <data name="IstizanResultTime" xml:space="preserve">
    <value>Ortalama istizan sonuçlanma süresi</value>
  </data>
  <data name="NormalApplicationAverageResultTime" xml:space="preserve">
    <value>Ortalama normal başvuru sonuçlanma süresi</value>
  </data>
  <data name="Portal" xml:space="preserve">
    <value>Portal</value>
  </data>
  <data name="Statistics" xml:space="preserve">
    <value>İstatistik</value>
  </data>
  <data name="DailyAgeRangeStats" xml:space="preserve">
    <value>Günlük başvuru yaş aralıkları</value>
  </data>
  <data name="DailyGenderRatio" xml:space="preserve">
    <value>Günlük başvuran cinsiyet oranı</value>
  </data>
  <data name="Female" xml:space="preserve">
    <value>Kadın</value>
  </data>
  <data name="Male" xml:space="preserve">
    <value>Erkek</value>
  </data>
  <data name="MonthlyApplicationSummaryStats" xml:space="preserve">
    <value>Aylık başvuru durum istatistikleri</value>
  </data>
  <data name="PreviousDayApplicationChangeStats" xml:space="preserve">
    <value>Önceki gün başvuru değişiklikleri</value>
  </data>
  <data name="PreviousDayApplicationTypes" xml:space="preserve">
    <value>Önceki gün başvuru tipleri</value>
  </data>
  <data name="PreviousDayDetailedApplicationStatus" xml:space="preserve">
    <value>Önceki gün alınan başvuru sayıları</value>
  </data>
  <data name="SatisfactionSurvey" xml:space="preserve">
    <value>Memnuniyet anketi</value>
  </data>
  <data name="AllAppointmentsCreatedThroughPortal" xml:space="preserve">
    <value>Portal üzerinden oluşturulan tüm randevular</value>
  </data>
  <data name="AllBranchesActivePolicyStats" xml:space="preserve">
    <value>Tüm şubeler aktif sigorta verileri</value>
  </data>
  <data name="AllBranchesMonthlyInsuranceStats" xml:space="preserve">
    <value>Tüm şubeler aylık yapılan sigorta bilgileri</value>
  </data>
  <data name="AllBranchesQuarterInsuranceStats" xml:space="preserve">
    <value>Son çeyrek tüm şubeler yapılan toplam sigorta bilgileri</value>
  </data>
  <data name="Appointment" xml:space="preserve">
    <value>Randevu</value>
  </data>
  <data name="DailyDetailedInsuranceStats" xml:space="preserve">
    <value>Tüm şubeler günlük yapılan sigorta detayları</value>
  </data>
  <data name="DailyInsuranceAgeRangeStats" xml:space="preserve">
    <value>Günlük sigorta yaptıran başvuru sahibi yaş aralıkları</value>
  </data>
  <data name="InstitutionTypeStats" xml:space="preserve">
    <value>Kayıtlı kurum tipi istatistikleri</value>
  </data>
  <data name="PortalMembershipApplicationCount" xml:space="preserve">
    <value>Portal toplam üyelik başvuruları</value>
  </data>
  <data name="PortalRequestedMembershipCount" xml:space="preserve">
    <value>Portal talep edilen randevu sayısı</value>
  </data>
  <data name="QuarterDetailedInsuranceStats" xml:space="preserve">
    <value>Son çeyrek tüm şubeler yapılan sigorta detayları</value>
  </data>
  <data name="QuarterInsuranceAgeRangeStats" xml:space="preserve">
    <value>Son çeyrek sigorta yaptıran başvuru sahibi yaş aralıkları</value>
  </data>
  <data name="RatingsOfInstitutions" xml:space="preserve">
    <value>Kurumların ratingleri</value>
  </data>
  <data name="QuarterVasSales" xml:space="preserve">
    <value>Son çeyrek yapılan VAS satışları</value>
  </data>
  <data name="AddSlot" xml:space="preserve">
    <value>Slot ekle</value>
  </data>
  <data name="EnterSlotDateRange" xml:space="preserve">
    <value>Slot tarih aralığı giriniz</value>
  </data>
  <data name="EnterSlotQuota" xml:space="preserve">
    <value>Slot ve kota bilgilerini giriniz</value>
  </data>
  <data name="NumberOfSlot" xml:space="preserve">
    <value>Slot Sayısı</value>
  </data>
  <data name="Quota" xml:space="preserve">
    <value>Kota</value>
  </data>
  <data name="SelectBranchApplicationCountry" xml:space="preserve">
    <value>Branch başvuru ülke seçiniz</value>
  </data>
  <data name="SelectSlotWeekDays" xml:space="preserve">
    <value>Slot geçerli olduğu haftanın günlerini seçiniz</value>
  </data>
  <data name="SlotList" xml:space="preserve">
    <value>Slot listesi</value>
  </data>
  <data name="SlotOpenClosed" xml:space="preserve">
    <value>Slot açık/kapalı</value>
  </data>
  <data name="ManageShortcuts" xml:space="preserve">
    <value>Kısayolları yönet</value>
  </data>
  <data name="Remove" xml:space="preserve">
    <value>Kaldır</value>
  </data>
  <data name="ShortcutUpperLimitWarning" xml:space="preserve">
    <value>En fazla 5 kısayol seçilebilir</value>
  </data>
  <data name="GatewayMotto" xml:space="preserve">
    <value>Dünya'dan Türkiye'ye; Türkiye'den Dünya'ya açılan kapınız</value>
  </data>
  <data name="AvailableShortcuts" xml:space="preserve">
    <value>Uygun kısayollar</value>
  </data>
  <data name="PageReloadAfterSuccess" xml:space="preserve">
    <value>İşlem başarılı, menü yeniden yüklenecek</value>
  </data>
  <data name="SelectedShortcuts" xml:space="preserve">
    <value>Seçilen kısayollar</value>
  </data>
  <data name="BranchManagement" xml:space="preserve">
    <value>Şube yönetimi</value>
  </data>
  <data name="GoToModules" xml:space="preserve">
    <value>Modül seçim sayfasına git</value>
  </data>
  <data name="NeedToSelectBranchNotification" xml:space="preserve">
    <value>Modül ve şube seçimi yapılmalı</value>
  </data>
  <data name="SlotDate" xml:space="preserve">
    <value>Slot tarih</value>
  </data>
  <data name="UpdateSlot" xml:space="preserve">
    <value>Slot güncelle</value>
  </data>
  <data name="UserNameSurname" xml:space="preserve">
    <value>Kullanıcı adı soyadı</value>
  </data>
  <data name="BranchApplicationCountry" xml:space="preserve">
    <value>Şube başvuru ülkesi</value>
  </data>
  <data name="HasBankAccount" xml:space="preserve">
    <value>Banka hesabı var mı?</value>
  </data>
  <data name="ByAgeRange" xml:space="preserve">
    <value>Yaş aralığına göre</value>
  </data>
  <data name="ByApplicationKind" xml:space="preserve">
    <value>Başvuru türüne göre</value>
  </data>
  <data name="ByApplicationType" xml:space="preserve">
    <value>Başvuru tipine göre</value>
  </data>
  <data name="ByGender" xml:space="preserve">
    <value>Cinsiyete göre</value>
  </data>
  <data name="ByNationality" xml:space="preserve">
    <value>Uyruğa göre</value>
  </data>
  <data name="ByVisaCategory" xml:space="preserve">
    <value>Vize kategorisine göre</value>
  </data>
  <data name="FreeApplication" xml:space="preserve">
    <value>Ücretsiz başvuru</value>
  </data>
  <data name="TotalApplication" xml:space="preserve">
    <value>Toplam başvuru</value>
  </data>
  <data name="CancellationReason" xml:space="preserve">
    <value>İade nedeni</value>
  </data>
  <data name="CompanyCurrency" xml:space="preserve">
    <value>Kuruluş birim</value>
  </data>
  <data name="CompanyPrice" xml:space="preserve">
    <value>Kuruluş fiyat</value>
  </data>
  <data name="InsuranceEndDate" xml:space="preserve">
    <value>Sigorta bitiş tarihi</value>
  </data>
  <data name="InsuranceStartDate" xml:space="preserve">
    <value>Sigorta başlangıç tarihi</value>
  </data>
  <data name="InsuredNameSurname" xml:space="preserve">
    <value>Sigortalı adı soyadı</value>
  </data>
  <data name="PolicyNumber" xml:space="preserve">
    <value>Poliçe numarası</value>
  </data>
  <data name="ProviderCurrency" xml:space="preserve">
    <value>Üretim kaynağı birim</value>
  </data>
  <data name="ProviderLongName" xml:space="preserve">
    <value>Üretim kaynağı</value>
  </data>
  <data name="ProviderPrice" xml:space="preserve">
    <value>Üretim kaynağı fiyat</value>
  </data>
  <data name="ReferenceNumber" xml:space="preserve">
    <value>Referans numarası</value>
  </data>
  <data name="RefundDate" xml:space="preserve">
    <value>İade yapılan tarih</value>
  </data>
  <data name="RefundedFeeName" xml:space="preserve">
    <value>İade Edilen Ücret  Adı</value>
  </data>
  <data name="RefundedFeeAmount" xml:space="preserve">
    <value>İade Edilen Ücret Tutar</value>
  </data>
  <data name="PassportValidityPeriodLessThan180Days" xml:space="preserve">
    <value>Pasaport geçerlilik süresi 180 günden az olamaz</value>
  </data>
  <data name="CargoCount" xml:space="preserve">
    <value>Kargo sayısı</value>
  </data>
  <data name="CargoType" xml:space="preserve">
    <value>Kargo türü</value>
  </data>
  <data name="ProcessedBy" xml:space="preserve">
    <value>İşlem yapan</value>
  </data>
  <data name="TotalPrice" xml:space="preserve">
    <value>Toplam fiyat</value>
  </data>
  <data name="UnitPrice" xml:space="preserve">
    <value>Birim fiyat</value>
  </data>
  <data name="SelectedCategory" xml:space="preserve">
    <value>Seçilen kategori</value>
  </data>
  <data name="CancellationDate" xml:space="preserve">
    <value>İptal tarihi</value>
  </data>
  <data name="CancellationStatus" xml:space="preserve">
    <value>İptal durumu</value>
  </data>
  <data name="PolicyRemainingDays" xml:space="preserve">
    <value>Poliçe kalan süre</value>
  </data>
  <data name="ReasonWithoutInsurance" xml:space="preserve">
    <value>Sigortasız ise sebebi</value>
  </data>
  <data name="ReimbursementSponsorDetail" xml:space="preserve">
    <value>Masraf karşılama sponsor detayı</value>
  </data>
  <data name="DeleteDateTime" xml:space="preserve">
    <value>Silinme tarihi - saati</value>
  </data>
  <data name="Explanation" xml:space="preserve">
    <value>Açıklama</value>
  </data>
  <data name="LastStatus" xml:space="preserve">
    <value>Son durum</value>
  </data>
  <data name="RefundedPrice" xml:space="preserve">
    <value>İade edilen tutar</value>
  </data>
  <data name="ServiceFee" xml:space="preserve">
    <value>Servis ücreti</value>
  </data>
  <data name="VisaFee" xml:space="preserve">
    <value>Vize ücreti</value>
  </data>
  <data name="PcrStatus" xml:space="preserve">
    <value>PCR durumu</value>
  </data>
  <data name="PcrStatusUpdate" xml:space="preserve">
    <value>Pcr durum güncelleme tarihi</value>
  </data>
  <data name="BarcodeNumber" xml:space="preserve">
    <value>Barkod numarası</value>
  </data>
  <data name="Pending" xml:space="preserve">
    <value>Beklemede</value>
  </data>
  <data name="PaymentType" xml:space="preserve">
    <value>Ödeme türü</value>
  </data>
  <data name="PolicyDays" xml:space="preserve">
    <value>Poliçe süresi</value>
  </data>
  <data name="PolicyStatus" xml:space="preserve">
    <value>Poliçe durumu</value>
  </data>
  <data name="Quantity" xml:space="preserve">
    <value>Adet</value>
  </data>
  <data name="ConsularRequest" xml:space="preserve">
    <value>Konsolosluk isteği</value>
  </data>
  <data name="NumberOfCancelations" xml:space="preserve">
    <value>İptal sayısı</value>
  </data>
  <data name="TotalPriceOfCancelations" xml:space="preserve">
    <value>İade edilen toplam tutar</value>
  </data>
  <data name="BranchDeliveryDate" xml:space="preserve">
    <value>Ofiste teslim alınan tarih</value>
  </data>
  <data name="DeliveredPassports" xml:space="preserve">
    <value>Teslim edilen pasaportlar</value>
  </data>
  <data name="DeliveryDate" xml:space="preserve">
    <value>Teslim tarihi</value>
  </data>
  <data name="DeliveryType" xml:space="preserve">
    <value>Teslim seçeneği</value>
  </data>
  <data name="PassportsWaitingForDelivery" xml:space="preserve">
    <value>Teslim edilmeyi bekleyen pasaportlar</value>
  </data>
  <data name="StandByDuration" xml:space="preserve">
    <value>Bekleme süresi</value>
  </data>
  <data name="NameSurname" xml:space="preserve">
    <value>Ad soyad</value>
  </data>
  <data name="UserSapId" xml:space="preserve">
    <value>Kullanıcı sap numarası</value>
  </data>
  <data name="UpdateDate" xml:space="preserve">
    <value>Güncelleme tarihi</value>
  </data>
  <data name="UploadedRejectionDocuments" xml:space="preserve">
    <value>Yüklenen vize ret evrakları</value>
  </data>
  <data name="ApplicationId" xml:space="preserve">
    <value>Başvuru numarası</value>
  </data>
  <data name="UpdateApplication" xml:space="preserve">
    <value>Başvuru güncelle</value>
  </data>
  <data name="ApplicationCountry" xml:space="preserve">
    <value>Başvuru yapılan ülke</value>
  </data>
  <data name="CanceledQuantity" xml:space="preserve">
    <value>İptal sayısı</value>
  </data>
  <data name="CancelledTotalFee" xml:space="preserve">
    <value>Toplam iptal tutarı</value>
  </data>
  <data name="Product" xml:space="preserve">
    <value>Ürün</value>
  </data>
  <data name="TotalFee" xml:space="preserve">
    <value>Toplam tutar</value>
  </data>
  <data name="Fee" xml:space="preserve">
    <value>Tutar</value>
  </data>
  <data name="Net" xml:space="preserve">
    <value>Net</value>
  </data>
  <data name="TotalCancellation" xml:space="preserve">
    <value>Toplam iptal</value>
  </data>
  <data name="TotalPolicy" xml:space="preserve">
    <value>Toplam poliçe</value>
  </data>
  <data name="UtcTimeZone" xml:space="preserve">
    <value>Utc zaman dilimi</value>
  </data>
  <data name="Taxed" xml:space="preserve">
    <value>Vergili</value>
  </data>
  <data name="FeeInformation" xml:space="preserve">
    <value>Ücret bilgisi</value>
  </data>
  <data name="NotDefined" xml:space="preserve">
    <value>Tanımsız</value>
  </data>
  <data name="VisaNo" xml:space="preserve">
    <value>Vize numarası</value>
  </data>
  <data name="AddCustomerCard" xml:space="preserve">
    <value>Kişi kartı ekle</value>
  </data>
  <data name="CustomerCardDetails" xml:space="preserve">
    <value>Kişi kartı detayları</value>
  </data>
  <data name="CustomerCardList" xml:space="preserve">
    <value>Kişi kartı listesi</value>
  </data>
  <data name="UpdateCustomerCard" xml:space="preserve">
    <value>Kişi kartı güncelle</value>
  </data>
  <data name="ApprovalForEntryBanned" xml:space="preserve">
    <value>Giriş yasaklı başvuru</value>
  </data>
  <data name="QuarterApplicationCategoryOfBranches" xml:space="preserve">
    <value>Son çeyrek şube bazlı yapılan başvuru kategorileri</value>
  </data>
  <data name="QuarterApplicationsOfBranches" xml:space="preserve">
    <value>Son çeyrek yapılan şube bazlı başvurular</value>
  </data>
  <data name="AgeRange" xml:space="preserve">
    <value>Yaş aralığı</value>
  </data>
  <data name="AllBranchesMonthlyTotalInsurances" xml:space="preserve">
    <value>Tüm şubeler aylık toplam sigorta sayıları</value>
  </data>
  <data name="DailyApplicationProcessTpes" xml:space="preserve">
    <value>Günlük başvuru türleri</value>
  </data>
  <data name="DailyApplicationTypes" xml:space="preserve">
    <value>Günlük başvuru tipleri</value>
  </data>
  <data name="PropertyName" xml:space="preserve">
    <value>Özellik adı</value>
  </data>
  <data name="ValueUpdateHistory" xml:space="preserve">
    <value>Değer güncelleme geçmişi</value>
  </data>
  <data name="CurrentValue" xml:space="preserve">
    <value>Mevcut değer</value>
  </data>
  <data name="PreviousValue" xml:space="preserve">
    <value>Önceki değer</value>
  </data>
  <data name="UpdatedAt" xml:space="preserve">
    <value>Güncelleme zamanı</value>
  </data>
  <data name="UpdatedBy" xml:space="preserve">
    <value>Güncelleyen kişi</value>
  </data>
  <data name="FreeApplications" xml:space="preserve">
    <value>Ücretsiz başvurular</value>
  </data>
  <data name="QuarterAllBranches" xml:space="preserve">
    <value>Son çeyrek şube detayları</value>
  </data>
  <data name="PreviousDayApplicationStats" xml:space="preserve">
    <value>Önceki gün alınan başvuru sayıları</value>
  </data>
  <data name="Free" xml:space="preserve">
    <value>Ücretsiz</value>
  </data>
  <data name="Cancelled" xml:space="preserve">
    <value>İptal</value>
  </data>
  <data name="PersonIsNotAllowedToApply" xml:space="preserve">
    <value>Bu kişinin başvuru yapmasına izin verilmiyor</value>
  </data>
  <data name="PreviouslyRejected" xml:space="preserve">
    <value>Önceden ret almış</value>
  </data>
  <data name="MenuPage" xml:space="preserve">
    <value>Menü sayfası</value>
  </data>
  <data name="ApplicationKind" xml:space="preserve">
    <value>Başvuru türü</value>
  </data>
  <data name="Rejection" xml:space="preserve">
    <value>Ret</value>
  </data>
  <data name="ValidInsuranceDetailsOfApplicant" xml:space="preserve">
    <value>Başvuran kişiye ait geçerli sigorta detayları</value>
  </data>
  <data name="IsLastItem" xml:space="preserve">
    <value>Son öğe mi</value>
  </data>
  <data name="LastApplicationStatusItemNotFound" xml:space="preserve">
    <value>Son başvuru durumu öğesi bulunamadı</value>
  </data>
  <data name="AddNewPreApplication" xml:space="preserve">
    <value>Ön başvuru ekle</value>
  </data>
  <data name="PreApplicationList" xml:space="preserve">
    <value>Ön başvuru listesi</value>
  </data>
  <data name="PreApplicationStep1Description" xml:space="preserve">
    <value>Açıklama</value>
  </data>
  <data name="PreApplicationStep2SlotInformation" xml:space="preserve">
    <value>Slot bilgileri</value>
  </data>
  <data name="PreApplicationStep3BasicInformation" xml:space="preserve">
    <value>Temel bilgiler</value>
  </data>
  <data name="PreApplicationStep4Finalize" xml:space="preserve">
    <value>Sonuçlandır</value>
  </data>
  <data name="UpdatePreApplication" xml:space="preserve">
    <value>Ön başvuru güncelle</value>
  </data>
  <data name="Applicant" xml:space="preserve">
    <value>Başvuran</value>
  </data>
  <data name="End" xml:space="preserve">
    <value>Bitiş</value>
  </data>
  <data name="NumberOfApplicants" xml:space="preserve">
    <value>Başvuran sayısı</value>
  </data>
  <data name="SaveApplicantAsCustomer" xml:space="preserve">
    <value>Başvuran için müşteri kartı oluştur</value>
  </data>
  <data name="SearchSlot" xml:space="preserve">
    <value>Slot ara</value>
  </data>
  <data name="SlotNotFound" xml:space="preserve">
    <value>Slot bulunamadı</value>
  </data>
  <data name="SlotSelectionDestroyed" xml:space="preserve">
    <value>Slot seçimi kaldırıldı, yeni bir slot seçiniz</value>
  </data>
  <data name="Start" xml:space="preserve">
    <value>Başlangıç</value>
  </data>
  <data name="SearchCustomerCard" xml:space="preserve">
    <value>Kişi kartı ara</value>
  </data>
  <data name="CheckRejectedStatus" xml:space="preserve">
    <value>Reddedildi durumunu kontrol et</value>
  </data>
  <data name="CheckRejectedStatusPeriod" xml:space="preserve">
    <value>Reddedildi durumu kontrol süresi</value>
  </data>
  <data name="Month" xml:space="preserve">
    <value>Ay</value>
  </data>
  <data name="ExistingRejectedApplicationStatus" xml:space="preserve">
    <value>Reddedilen başvuru durumu mevcut</value>
  </data>
  <data name="AppointmentDetails" xml:space="preserve">
    <value>Randevu detayları</value>
  </data>
  <data name="AppointmentTime" xml:space="preserve">
    <value>Randevu zamanı</value>
  </data>
  <data name="DeleteAppointment" xml:space="preserve">
    <value>Randevuyu sil</value>
  </data>
  <data name="UpdateAppointment" xml:space="preserve">
    <value>Randevuyu güncelle</value>
  </data>
  <data name="DeleteFamilyPreApplication" xml:space="preserve">
    <value>Aile başvurusunu sil</value>
  </data>
  <data name="DeleteGroupPreApplication" xml:space="preserve">
    <value>Grup başvurusunu sil</value>
  </data>
  <data name="DeleteOnlyPreApplication" xml:space="preserve">
    <value>Sadece bu başvuruyu sil</value>
  </data>
  <data name="PreApplicationDetails" xml:space="preserve">
    <value>Ön başvuru detayları</value>
  </data>
  <data name="AddDepartment" xml:space="preserve">
    <value>Departman ekle</value>
  </data>
  <data name="DepartmentDetails" xml:space="preserve">
    <value>Departman detayları</value>
  </data>
  <data name="DepartmentList" xml:space="preserve">
    <value>Departman listesi</value>
  </data>
  <data name="DepartmentName" xml:space="preserve">
    <value>Departman adı</value>
  </data>
  <data name="UpdateDepartment" xml:space="preserve">
    <value>Bilgileri güncelle</value>
  </data>
  <data name="Exception_NoRecordsFound" xml:space="preserve">
    <value>Kayıt bulunamadı</value>
  </data>
  <data name="Exception_BadRequest" xml:space="preserve">
    <value>Geçersiz istek</value>
  </data>
  <data name="Exception_ExistingRecord" xml:space="preserve">
    <value>Mevcut kayıt</value>
  </data>
  <data name="Exception_ValidationFailed" xml:space="preserve">
    <value>Doğrulama başarısız</value>
  </data>
  <data name="User" xml:space="preserve">
    <value>Kullanıcı</value>
  </data>
  <data name="AddBranchDepartmentFlow" xml:space="preserve">
    <value>Şube departman akışı ekle</value>
  </data>
  <data name="BranchDepartmentFlowDetails" xml:space="preserve">
    <value>Şube departman akışı detayları</value>
  </data>
  <data name="BranchDepartmentList" xml:space="preserve">
    <value>Şube departman listesi</value>
  </data>
  <data name="ProcessOrder" xml:space="preserve">
    <value>İşlem sırası</value>
  </data>
  <data name="UpdateBranchDepartmentFlow" xml:space="preserve">
    <value>Şube departman akışı güncelle</value>
  </data>
  <data name="BranchDepartmentFlow" xml:space="preserve">
    <value>Şube departman akışı</value>
  </data>
  <data name="AtLeastTwoDepartment" xml:space="preserve">
    <value>En az 2 departman seçilmelidir</value>
  </data>
  <data name="AvailableDepartments" xml:space="preserve">
    <value>Uygun departmanlar</value>
  </data>
  <data name="SelectedDepartmants" xml:space="preserve">
    <value>Seçilen departmanlar</value>
  </data>
  <data name="MenuManagement" xml:space="preserve">
    <value>Menü yönetimi</value>
  </data>
  <data name="ManageMenuInfo1" xml:space="preserve">
    <value>Menü en fazla 3 dizin ile oluşturulabilmektedir: Ana Başlık - Alt Başlık - Sayfa Linki</value>
  </data>
  <data name="ManageMenuInfo2" xml:space="preserve">
    <value>Altında yeni dizin olmayan sayfalar link olarak düzenlenmektedir: Örn: Ana Başlığa bağlı bir Alt başlık yoksa Ana Başlık sayfa linkine yönlendirir, aksi durumda alt kırılımı açar</value>
  </data>
  <data name="ManageMenuInfo3" xml:space="preserve">
    <value>(*) ibaresine sahip bileşenler link olarak kullanılamaz, alt dizine sahip Ana Başlık ya da Alt Başlık olabilirler</value>
  </data>
  <data name="ManageMenuInfo4" xml:space="preserve">
    <value>Yapılan sıralama önemlidir, menü dizini burada belirlenen sıraya göre yerleştirilecektir</value>
  </data>
  <data name="ManageMenuInfo5" xml:space="preserve">
    <value>Yapılan düzenlemelerin ekranlara yansıması için cache bilgisinin temizlenmesi gerekmektedir. Bu işlem periyodik gün içerisinde gerçekleştirilmektedir</value>
  </data>
  <data name="DateTimePickerFormatView" xml:space="preserve">
    <value>dd/MM/yyyy HH:mm</value>
  </data>
  <data name="AllSelectedApplicationsNeedToHaveSameStatus" xml:space="preserve">
    <value>Seçili tüm başvurular aynı statüde olmalıdır</value>
  </data>
  <data name="ForUpdatingSelectedApplicationStatus" xml:space="preserve">
    <value>Seçili başvuruların durumunu güncellemek için</value>
  </data>
  <data name="StatusedSelectedApplications" xml:space="preserve">
    <value>Durumuna sahip seçili başvurular</value>
  </data>
  <data name="CultureTr" xml:space="preserve">
    <value>tr-TR</value>
  </data>
  <data name="SelectStatuses" xml:space="preserve">
    <value>Statü(ler) seç</value>
  </data>
  <data name="Admin" xml:space="preserve">
    <value>Yönetici</value>
  </data>
  <data name="ApplicationCancellation" xml:space="preserve">
    <value>Başvuru iptali</value>
  </data>
  <data name="Barcode" xml:space="preserve">
    <value>Barkod</value>
  </data>
  <data name="BranchApplicationStatus" xml:space="preserve">
    <value>Şube başvuru durumu</value>
  </data>
  <data name="Exception_ApplicationStatusIsDifferentFromInitialStatus" xml:space="preserve">
    <value>Başvuru durumu ilk başvuru durumundan farklı olduğundan başvuru güncellenemez</value>
  </data>
  <data name="Exception_BarcodeAlreadyScanned" xml:space="preserve">
    <value>Barkod zaten taratılmış</value>
  </data>
  <data name="Exception_DataIsStaticCannotBeDeleted" xml:space="preserve">
    <value>Veri statik, silinemez</value>
  </data>
  <data name="Exception_DataOutOfUse" xml:space="preserve">
    <value>Veri kullanım dışı</value>
  </data>
  <data name="Exception_ErrorOccurredUpdatingApplicationSapStatus" xml:space="preserve">
    <value>Başvurular SAP'ye gönderildi ancak başvuru SAP durumu güncellenirken hata oluştu</value>
  </data>
  <data name="Exception_ExistingApprovedApplicationCancellation" xml:space="preserve">
    <value>İlgili başvurunun iptal talebi zaten sonuçlandırılmış</value>
  </data>
  <data name="Exception_ExistingPendingCancellationRequest" xml:space="preserve">
    <value>Uygulama için bekleyen bir iptal/iade talebi zaten mevcut</value>
  </data>
  <data name="Exception_NoAvailableSlotQuota" xml:space="preserve">
    <value>Gün içerisinde uygun mevcut yer kotası yok</value>
  </data>
  <data name="ExtraFee" xml:space="preserve">
    <value>Ekstra ücret</value>
  </data>
  <data name="InitialApplicationStatus" xml:space="preserve">
    <value>İlk uygulama durumu</value>
  </data>
  <data name="MainApplication" xml:space="preserve">
    <value>Ana başvuru</value>
  </data>
  <data name="PhotoBooth" xml:space="preserve">
    <value>Fotokabin</value>
  </data>
  <data name="PreApplication" xml:space="preserve">
    <value>Ön başvuru</value>
  </data>
  <data name="PreApplicationApplicant" xml:space="preserve">
    <value>Ön başvuru sahibi</value>
  </data>
  <data name="RelationalApplication" xml:space="preserve">
    <value>İlişkili başvuru</value>
  </data>
  <data name="Slot" xml:space="preserve">
    <value>Slot</value>
  </data>
  <data name="Role" xml:space="preserve">
    <value>Rol</value>
  </data>
  <data name="Action" xml:space="preserve">
    <value>Sayfa</value>
  </data>
  <data name="CompanyModule" xml:space="preserve">
    <value>Firma modülü</value>
  </data>
  <data name="BranchApplicationCountryExtraFee" xml:space="preserve">
    <value>Şube başvuru ülkesi ekstra ücreti</value>
  </data>
  <data name="BranchDepartment" xml:space="preserve">
    <value>Şube departmanı</value>
  </data>
  <data name="CustomerCard" xml:space="preserve">
    <value>Kişi kartı</value>
  </data>
  <data name="Date" xml:space="preserve">
    <value>Tarih</value>
  </data>
  <data name="Department" xml:space="preserve">
    <value>Departman</value>
  </data>
  <data name="Exception_ExceedsLicenseCountLimit" xml:space="preserve">
    <value>Lisans sayısının sınırını aşıyor</value>
  </data>
  <data name="Exception_ExistingRecordWithSameOrder" xml:space="preserve">
    <value>Aynı sıralama ile kayıt mevcut</value>
  </data>
  <data name="Exception_NoAvailableSlotFound" xml:space="preserve">
    <value>Uygun yer bulunamadı</value>
  </data>
  <data name="UserBranch" xml:space="preserve">
    <value>Kullanıcı şubesi</value>
  </data>
  <data name="UserModule" xml:space="preserve">
    <value>Kullanıcı modülü</value>
  </data>
  <data name="Exception_NoWaitingApplicationExtraFeeForSAP" xml:space="preserve">
    <value>SAP'ye gönderilmek üzere bekleyen başvuru ekstra ücreti yok</value>
  </data>
  <data name="IcrRelation" xml:space="preserve">
    <value>Icr bağlantısı</value>
  </data>
  <data name="IcrManagement" xml:space="preserve">
    <value>Icr yönetimi</value>
  </data>
  <data name="BranchIcrDescription_1" xml:space="preserve">
    <value>ICR yapısı ile uyumlu olması açısından editör alanının 'Tablo' kullanılarak oluşturulması önerilmektedir</value>
  </data>
  <data name="BranchIcrDescription_2" xml:space="preserve">
    <value>Editör alanında uygulanan tasarım direkt olarak ICR'da gösterileceği için tablo sütun genişlikleri vb. gibi unsurlar önem arz etmektedir</value>
  </data>
  <data name="BranchIcrDescription_3" xml:space="preserve">
    <value>ICR'da gözükmesi istenmeyen boşluk-satır yükseklikleri vb. gibi içeriklerin girilmemesi önemlidir</value>
  </data>
  <data name="SinceBorn" xml:space="preserve">
    <value>Doğduğundan beri</value>
  </data>
  <data name="OnlyOneExtraFeeCanBeSelected" xml:space="preserve">
    <value>Bu kategoride yalnızca bir ekstra ücret seçilebilir</value>
  </data>
  <data name="DisplayPolicy" xml:space="preserve">
    <value>Poliçe görüntüle</value>
  </data>
  <data name="PolicyLanguage" xml:space="preserve">
    <value>Poliçe dili</value>
  </data>
  <data name="SelectLanguage" xml:space="preserve">
    <value>Dil seçiniz</value>
  </data>
  <data name="BirthDateValidation" xml:space="preserve">
    <value>Doğum tarihi geçmiş zamanlı olmalıdır</value>
  </data>
  <data name="NotificationMail_SentCenterDueToMissingDocuments" xml:space="preserve">
    <value>[DATE] tarihinde alınan [APPNUMBER] referans numaralı başvuru eksik /ek evrak nedeniyle vize başvuru merkezine gönderilmiştir.</value>
  </data>
  <data name="SelectBranches" xml:space="preserve">
    <value>Şube(ler) seç</value>
  </data>
  <data name="NotificationMail_CancelRequested" xml:space="preserve">
    <value>[DATE] tarihinde alınan [APPNUMBER] referans numaralı başvuru için [REQUESTEDBY] adlı personel tarafından iptal  talebi oluşturulmuştur , onayınızı beklemektedir.</value>
  </data>
  <data name="NotificationMail_RefundRequested" xml:space="preserve">
    <value>[DATE] tarihinde alınan [APPNUMBER]referans numaralı başvuru için [REQUESTEDBY] adlı personel tarafından kısmi iade talebi oluşturulmuştur , onayınızı beklemektedir.</value>
  </data>
  <data name="AtMostFiftyBarcodesScan" xml:space="preserve">
    <value>En fazla 50 barkod taratılabilir</value>
  </data>
  <data name="ApplicationNotAppliedFromBranch" xml:space="preserve">
    <value>Başvuru seçilen şubeye ait değildir</value>
  </data>
  <data name="AreYouSureCreateSapOrderForFamily" xml:space="preserve">
    <value>Aile başvurularından SAP'ye şu ana kadar yapılmış olan başvurular gidecektir. Tüm aile bireylerinin başvurularının tamamlandığını onaylıyor musunuz?</value>
  </data>
  <data name="ApplicationFileType" xml:space="preserve">
    <value>Dosya tipi</value>
  </data>
  <data name="FileName" xml:space="preserve">
    <value>Dosya adı</value>
  </data>
  <data name="AddApplicationFile" xml:space="preserve">
    <value>Başvuru dosyası ekle</value>
  </data>
  <data name="ApplicationFiles" xml:space="preserve">
    <value>Başvuru dosyaları</value>
  </data>
  <data name="ValidFileExtensions" xml:space="preserve">
    <value>Geçerli uzantılar</value>
  </data>
  <data name="ValidFileSize" xml:space="preserve">
    <value>Geçerli dosya boyutu</value>
  </data>
  <data name="ApplicationFile" xml:space="preserve">
    <value>Başvuru dosyası</value>
  </data>
  <data name="Download" xml:space="preserve">
    <value>İndir</value>
  </data>
  <data name="EnterApplicationReferenceNumber" xml:space="preserve">
    <value>Başında 0 olmadan başvuru numarası girin</value>
  </data>
  <data name="EnterPreApplicationReferenceNumber" xml:space="preserve">
    <value>Başında 0 olmadan randevu numarası girin</value>
  </data>
  <data name="PassportDelivery" xml:space="preserve">
    <value>Pasaport teslim</value>
  </data>
  <data name="QueueMatic" xml:space="preserve">
    <value>Sıramatik</value>
  </data>
  <data name="ReBiometry" xml:space="preserve">
    <value>Re-Biyometri</value>
  </data>
  <data name="WalkIn" xml:space="preserve">
    <value>Randevusuz</value>
  </data>
  <data name="AppointmentNumber" xml:space="preserve">
    <value>Randevu numarası</value>
  </data>
  <data name="GenerateQueueNumber" xml:space="preserve">
    <value>Sıra numarası oluştur</value>
  </data>
  <data name="AtLeastOneDepartment" xml:space="preserve">
    <value>En az 1 departman seçilmelidir</value>
  </data>
  <data name="Vip" xml:space="preserve">
    <value>VIP</value>
  </data>
  <data name="CreateWalkInAppointement" xml:space="preserve">
    <value>Randevusuz giriş kaydı oluştur</value>
  </data>
  <data name="CounterNumber" xml:space="preserve">
    <value>Kontuar numarası</value>
  </data>
  <data name="DepartmentCounterSelection" xml:space="preserve">
    <value>Departman - kontuar seçimi</value>
  </data>
  <data name="ProcessType" xml:space="preserve">
    <value>İşlem türü</value>
  </data>
  <data name="QueueList" xml:space="preserve">
    <value>Kuyruk listesi</value>
  </data>
  <data name="WithGroupId" xml:space="preserve">
    <value>Grup numarası ile</value>
  </data>
  <data name="WithIndividualId" xml:space="preserve">
    <value>Bireysel numara ile</value>
  </data>
  <data name="StartProcess" xml:space="preserve">
    <value>İşlem başlat</value>
  </data>
  <data name="InProgress" xml:space="preserve">
    <value>İşlemde</value>
  </data>
  <data name="DeviceNotReady" xml:space="preserve">
    <value>Cihaz hazır değil</value>
  </data>
  <data name="DeviceReady" xml:space="preserve">
    <value>Cihaz hazır</value>
  </data>
  <data name="PassportReader" xml:space="preserve">
    <value>Pasaport okuyucu</value>
  </data>
  <data name="ScanPassportAndUpdateInformation" xml:space="preserve">
    <value>Pasaportu tara ve bilgileri güncelle</value>
  </data>
  <data name="UpdateStatus" xml:space="preserve">
    <value>Statü güncelle</value>
  </data>
  <data name="ResumeProcess" xml:space="preserve">
    <value>Devam ettir</value>
  </data>
  <data name="Other" xml:space="preserve">
    <value>Diğer</value>
  </data>
  <data name="CargoIsNotAvailable" xml:space="preserve">
    <value>Başvuru için kargo hizmeti tanımlanmamıştır</value>
  </data>
  <data name="PrintCargoReceipt" xml:space="preserve">
    <value>Kargo çıktısı yazdır</value>
  </data>
  <data name="WalkInAppointment" xml:space="preserve">
    <value>Randevusuz başvuru formu</value>
  </data>
  <data name="SelectBranchToContinue" xml:space="preserve">
    <value>Devam etmek için şube seçimi yapın</value>
  </data>
  <data name="ByAppointment" xml:space="preserve">
    <value>Randevulu</value>
  </data>
  <data name="Minute" xml:space="preserve">
    <value>Dk</value>
  </data>
  <data name="StandBy" xml:space="preserve">
    <value>Bekleme süresi</value>
  </data>
  <data name="Resume" xml:space="preserve">
    <value>İşleme devam ediliyor</value>
  </data>
  <data name="QueueNumber" xml:space="preserve">
    <value>Sıra numarası</value>
  </data>
  <data name="ShowCompleted" xml:space="preserve">
    <value>Tamamlananları göster</value>
  </data>
  <data name="Order" xml:space="preserve">
    <value>Sıra</value>
  </data>
  <data name="Family" xml:space="preserve">
    <value>Aile</value>
  </data>
  <data name="Group" xml:space="preserve">
    <value>Grup</value>
  </data>
  <data name="BoundAppointment" xml:space="preserve">
    <value>Bağlı randevu</value>
  </data>
  <data name="ApplicantCount" xml:space="preserve">
    <value>Kişi sayısı</value>
  </data>
  <data name="IndividualApplication" xml:space="preserve">
    <value>Bireysel başvuru</value>
  </data>
  <data name="SubApplication" xml:space="preserve">
    <value>Bağlı başvuru</value>
  </data>
  <data name="CreateApplication" xml:space="preserve">
    <value>Başvuru oluştur</value>
  </data>
  <data name="WaitingForMainApplication" xml:space="preserve">
    <value>Ana başvurunun gerçekleştirilmesi bekleniyor</value>
  </data>
  <data name="DisplayPassportInformation" xml:space="preserve">
    <value>Pasaport bilgilerini göster</value>
  </data>
  <data name="ShortcutPassportInformation" xml:space="preserve">
    <value>Kısayol: CTRL + V tuşları ile pasaport bilgilerini görüntüleyebilirsiniz</value>
  </data>
  <data name="PassportDataIsMissing" xml:space="preserve">
    <value>Pasaport verisi eksik</value>
  </data>
  <data name="DeletedData" xml:space="preserve">
    <value>Silinen kayıt</value>
  </data>
  <data name="ShowDeleted" xml:space="preserve">
    <value>Silinenleri göster</value>
  </data>
  <data name="NotificationMail_NewPreApplication" xml:space="preserve">
    <value>Randevu: [BRANCH] Şube, Tarih: [DATE], Saat: [TIME]</value>
  </data>
  <data name="NotificationMail_UpdatePreApplication" xml:space="preserve">
    <value>Randevu: [BRANCH] Şube, Tarih: [DATE], Saat: [TIME]</value>
  </data>
  <data name="Postpone" xml:space="preserve">
    <value>Ertele</value>
  </data>
  <data name="AreYouSureToPostponeThisRecord" xml:space="preserve">
    <value>Bu başvuruyu ertelemek istediğinize emin misiniz?</value>
  </data>
  <data name="PostponeAppointment" xml:space="preserve">
    <value>Randevuyu ertele</value>
  </data>
  <data name="NewSlotSelection" xml:space="preserve">
    <value>Yeni slot seçimi</value>
  </data>
  <data name="AppointmentNumberIs" xml:space="preserve">
    <value>Başvuru numarası</value>
  </data>
  <data name="BranchApplicationCountryFile" xml:space="preserve">
    <value>Şube başvuru ülkesi dosyası</value>
  </data>
  <data name="BranchApplicationCountryFileType" xml:space="preserve">
    <value>Şube başvuru ülkesi dosya tipi</value>
  </data>
  <data name="AddNewFile" xml:space="preserve">
    <value>Yeni dosya ekle</value>
  </data>
  <data name="FileManagement" xml:space="preserve">
    <value>Dosya yönetimi</value>
  </data>
  <data name="FileType" xml:space="preserve">
    <value>Dosya tipi</value>
  </data>
  <data name="ApplicationCancelledOrDeleted" xml:space="preserve">
    <value>Başvuru silinmiş ya da iptal durumundadır</value>
  </data>
  <data name="IsApplicationStatusHidden" xml:space="preserve">
    <value>Başvuru durumu gizlensin</value>
  </data>
  <data name="IsApplicationUpdateAllowed" xml:space="preserve">
    <value>Başvuru güncellenebilir</value>
  </data>
  <data name="IsNewApplicationWithSamePassportNumberBlocked" xml:space="preserve">
    <value>Aynı pasaport numarası ile yeni başvuruyu engelle</value>
  </data>
  <data name="BranchApplicationStatusItemNotFound" xml:space="preserve">
    <value>Şube başvuru durumu öğesi bulunamadı</value>
  </data>
  <data name="Ratio" xml:space="preserve">
    <value>Oran</value>
  </data>
  <data name="SalesFee" xml:space="preserve">
    <value>Satış ücreti</value>
  </data>
  <data name="CustomerCardNote" xml:space="preserve">
    <value>Müşteri kartı notu</value>
  </data>
  <data name="CustomerCardNotes" xml:space="preserve">
    <value>Müşteri kartı notları</value>
  </data>
  <data name="AddCustomerCardNote" xml:space="preserve">
    <value>Müşteri kartına not ekle</value>
  </data>
  <data name="Note" xml:space="preserve">
    <value>Not</value>
  </data>
  <data name="AppointmentManagement" xml:space="preserve">
    <value>Randevu yönetimi</value>
  </data>
  <data name="Age" xml:space="preserve">
    <value>Yaş</value>
  </data>
  <data name="AppointmentDate" xml:space="preserve">
    <value>Randevu günü</value>
  </data>
  <data name="AppointmentHour" xml:space="preserve">
    <value>Randevu saati</value>
  </data>
  <data name="AppointmentReport" xml:space="preserve">
    <value>Randevu raporu</value>
  </data>
  <data name="CancelledReport" xml:space="preserve">
    <value>İptal raporu</value>
  </data>
  <data name="GenerateReport" xml:space="preserve">
    <value>Rapor oluştur</value>
  </data>
  <data name="AgencyType" xml:space="preserve">
    <value>Acente tipi</value>
  </data>
  <data name="AgencyTypeList" xml:space="preserve">
    <value>Acente tipi listesi</value>
  </data>
  <data name="AddAgencyType" xml:space="preserve">
    <value>Acente tipi ekle</value>
  </data>
  <data name="AgencyTypeDetails" xml:space="preserve">
    <value>Acente tipi detayı</value>
  </data>
  <data name="AgencyTypeName" xml:space="preserve">
    <value>Acente tipi adı</value>
  </data>
  <data name="UpdateAgencyType" xml:space="preserve">
    <value>Acente tipi güncelleme</value>
  </data>
  <data name="AgencyTypeDescription" xml:space="preserve">
    <value>Acente tipi açıklaması</value>
  </data>
  <data name="AgencyTypeFile" xml:space="preserve">
    <value>Acente tipi dosyası</value>
  </data>
  <data name="AddAgency" xml:space="preserve">
    <value>Acente ekle</value>
  </data>
  <data name="Agency" xml:space="preserve">
    <value>Acente</value>
  </data>
  <data name="AgencyAddress" xml:space="preserve">
    <value>Acente adresi</value>
  </data>
  <data name="AgencyAuthorizedPersonName" xml:space="preserve">
    <value>Acente Yetkili Kişi Adı</value>
  </data>
  <data name="AgencyAuthorizedPersonSurname" xml:space="preserve">
    <value>Acente Yetkili Kişi Soyadı</value>
  </data>
  <data name="AgencyCategory" xml:space="preserve">
    <value>Acente kategorisi</value>
  </data>
  <data name="AgencyCityName" xml:space="preserve">
    <value>Acentenin şehir adı</value>
  </data>
  <data name="AgencyCountryName" xml:space="preserve">
    <value>Acentenin ülke adı</value>
  </data>
  <data name="AgencyDescription" xml:space="preserve">
    <value>Acente açıklaması</value>
  </data>
  <data name="AgencyDetails" xml:space="preserve">
    <value>Acente detayı</value>
  </data>
  <data name="AgencyEmail" xml:space="preserve">
    <value>Acente e-postası</value>
  </data>
  <data name="AgencyList" xml:space="preserve">
    <value>Acente listesi</value>
  </data>
  <data name="AgencyName" xml:space="preserve">
    <value>Acente adı</value>
  </data>
  <data name="AgencyTelephone" xml:space="preserve">
    <value>Acente telefon numarası</value>
  </data>
  <data name="AgencyTypeFileCount" xml:space="preserve">
    <value>Dosya tipi sayısı</value>
  </data>
  <data name="UpdateAgency" xml:space="preserve">
    <value>Acente güncelleme</value>
  </data>
  <data name="AgencyTypeFileList" xml:space="preserve">
    <value>Acente Tipi Dosyaları Listesi</value>
  </data>
  <data name="NotificationMail_LinkForNewPassword" xml:space="preserve">
    <value>Yeni şifre belirlemek için lütfen tıklayınız. [LINK]</value>
  </data>
  <data name="SlotType" xml:space="preserve">
    <value>Slot tipi</value>
  </data>
  <data name="AgencyUser" xml:space="preserve">
    <value>Acente kullanıcısı</value>
  </data>
  <data name="UserAccountIsNotActive" xml:space="preserve">
    <value>Kullancı hesabı aktif değil</value>
  </data>
  <data name="ManageAgencyTypeFile" xml:space="preserve">
    <value>Dosya tiplerini yönet</value>
  </data>
  <data name="AuthorizedUserAlreadyExists" xml:space="preserve">
    <value>Şirket yetkilisi rolü başka kullanıcı üzerinde etkin durumda</value>
  </data>
  <data name="AddAgencyUser" xml:space="preserve">
    <value>Acente kullanıcısı ekle</value>
  </data>
  <data name="AgencyUserList" xml:space="preserve">
    <value>Acente kullanıcı listesi</value>
  </data>
  <data name="Authorized" xml:space="preserve">
    <value>Yetkili</value>
  </data>
  <data name="Position" xml:space="preserve">
    <value>Pozisyon</value>
  </data>
  <data name="UpdateAgencyUser" xml:space="preserve">
    <value>Acente kullanıcısı güncelle</value>
  </data>
  <data name="UserAgencyDetails" xml:space="preserve">
    <value>Acente kullanıcı bilgileri</value>
  </data>
  <data name="Exception_ApplicationUpdateIsNotAllowedForThisApplicationStatus" xml:space="preserve">
    <value>Bu başvuru durumu için başvuru güncellemeye izin verilmiyor</value>
  </data>
  <data name="Exception_NewApplicationWithSamePassportNumberIsNotAllowedForThisApplicationStatus" xml:space="preserve">
    <value>Bu başvuru durumu için aynı pasaport numarası ile yeni başvuruya izin verilmiyor</value>
  </data>
  <data name="VasType" xml:space="preserve">
    <value>Vas tipi</value>
  </data>
  <data name="NotificationMail_InsurancePolicy" xml:space="preserve">
    <value>Sigorta poliçeniz ektedir.</value>
  </data>
  <data name="SendEmail" xml:space="preserve">
    <value>Email gönder</value>
  </data>
  <data name="Priority" xml:space="preserve">
    <value>Öncelikli</value>
  </data>
  <data name="AgencyFile" xml:space="preserve">
    <value>Acente dosyası</value>
  </data>
  <data name="AgencyFileList" xml:space="preserve">
    <value>Acente dosyaları listesi</value>
  </data>
  <data name="UploadStatus" xml:space="preserve">
    <value>Yükleme Durumu</value>
  </data>
  <data name="ConfirmUpdateAllStatus" xml:space="preserve">
    <value>Toplu güncelleme yapmak için tümünü güncelle seçeneği seçili olmalıdır. Durumu değişen başvuruları güncellenecektedir, devam etmek istiyor musunuz?</value>
  </data>
  <data name="AllowUpdate" xml:space="preserve">
    <value>Güncellemeye izin ver</value>
  </data>
  <data name="BranchApplicationCountryExtraFeeCost" xml:space="preserve">
    <value>Şube başvuru ülkesi ekstra ücret masrafı</value>
  </data>
  <data name="AddUpdateBranchApplicationCountryExtraFeeCost" xml:space="preserve">
    <value>Masraf Ekle/Güncelle</value>
  </data>
  <data name="TaxRate" xml:space="preserve">
    <value>Vergi oranı</value>
  </data>
  <data name="BranchApplicationCountryExtraFeeCommission" xml:space="preserve">
    <value>Şube başvuru ülkesi ekstra ücret komisyonu</value>
  </data>
  <data name="CommissionType" xml:space="preserve">
    <value>Komisyon tipi</value>
  </data>
  <data name="AddUpdateBranchApplicationCountryExtraFeeCommission" xml:space="preserve">
    <value>Komisyon Ekle/Güncelle</value>
  </data>
  <data name="IsVisaUsed" xml:space="preserve">
    <value>Vize kullanıldı</value>
  </data>
  <data name="VisaFromDate" xml:space="preserve">
    <value>Vize başlangıç tarihi</value>
  </data>
  <data name="VisaUntilDate" xml:space="preserve">
    <value>Vize bitiş tarihi</value>
  </data>
  <data name="AgencySlotMatchError" xml:space="preserve">
    <value>Başvuran ve slot tipleri birbiri ile eşleşmelidir</value>
  </data>
  <data name="PhotoBoothsWithoutReference" xml:space="preserve">
    <value>Başvuru dışı fotokabin</value>
  </data>
  <data name="ReferencedPhotoBooth" xml:space="preserve">
    <value>Başvurulu fotokabin</value>
  </data>
  <data name="AppointmentNotActivated" xml:space="preserve">
    <value>Randevu aktive edilmemiş</value>
  </data>
  <data name="FeeCurrencyForCompany" xml:space="preserve">
    <value>Firmaya ödenecek ücret para birimi</value>
  </data>
  <data name="FeeForCompany" xml:space="preserve">
    <value>Firmaya ödenecek ücret</value>
  </data>
  <data name="IsAgencyApplication" xml:space="preserve">
    <value>Acente başvurusu</value>
  </data>
  <data name="SftpConfiguration" xml:space="preserve">
    <value>Sftp yapılandırması</value>
  </data>
  <data name="Document" xml:space="preserve">
    <value>Belge</value>
  </data>
  <data name="ApplicationData" xml:space="preserve">
    <value>Başvuru veri</value>
  </data>
  <data name="ApplicationDataContact" xml:space="preserve">
    <value>Başvuru veri iletişim</value>
  </data>
  <data name="ApplicationDataDemographic" xml:space="preserve">
    <value>Başvuru veri demokrafik</value>
  </data>
  <data name="ApplicationDataTravel" xml:space="preserve">
    <value>Başvuru veri seyahat</value>
  </data>
  <data name="ApplicationDataTravelDetail" xml:space="preserve">
    <value>Başvuru veri seyahat detayı</value>
  </data>
  <data name="QueueMaticPlaylistId" xml:space="preserve">
    <value>Sıra yönetimi oynatma listesi Id'si</value>
  </data>
  <data name="TurkeyVisaApplicationCenter" xml:space="preserve">
    <value>Türkiye Vize Başvuru Merkezi</value>
  </data>
  <data name="Recall" xml:space="preserve">
    <value>Hatırlatma</value>
  </data>
  <data name="TravelDocumentType" xml:space="preserve">
    <value>Pasaport türü</value>
  </data>
  <data name="PurposeOfTrip" xml:space="preserve">
    <value>Seyahat amacı</value>
  </data>
  <data name="Information" xml:space="preserve">
    <value>Bilgi</value>
  </data>
  <data name="Demographic" xml:space="preserve">
    <value>Demografik</value>
  </data>
  <data name="Travel" xml:space="preserve">
    <value>Seyahat</value>
  </data>
  <data name="TravelDetail" xml:space="preserve">
    <value>Seyahat detayları</value>
  </data>
  <data name="DataInformationSaved" xml:space="preserve">
    <value>Veri bilgisi kaydedildi</value>
  </data>
  <data name="SaveAndContinueToTravel" xml:space="preserve">
    <value>Kaydet ve seyahat bilgilerine geç</value>
  </data>
  <data name="SaveAndContinueToTravelDetails" xml:space="preserve">
    <value>Kaydet ve seyahat detaylarına geç</value>
  </data>
  <data name="IssuingAuthority" xml:space="preserve">
    <value>Düzenleyen yetkili makam</value>
  </data>
  <data name="IssuingState" xml:space="preserve">
    <value>Düzenleyen ülke</value>
  </data>
  <data name="NotApplicable" xml:space="preserve">
    <value>Uygulanamaz</value>
  </data>
  <data name="PassportIssueDate" xml:space="preserve">
    <value>Pasaport düzenleme tarihi</value>
  </data>
  <data name="ValidUntil" xml:space="preserve">
    <value>Geçerlilik tarihi</value>
  </data>
  <data name="ArrivalDate" xml:space="preserve">
    <value>Varış zamanı</value>
  </data>
  <data name="DepartureDate" xml:space="preserve">
    <value>Kalkış zamanı</value>
  </data>
  <data name="DurationStayInDays" xml:space="preserve">
    <value>Kalış gün sayısı</value>
  </data>
  <data name="MeansOfTransport" xml:space="preserve">
    <value>Ulaşım aracı</value>
  </data>
  <data name="SaveAndContinueToDemographic" xml:space="preserve">
    <value>Kaydet ve demokrafik bilgilere devam et</value>
  </data>
  <data name="City" xml:space="preserve">
    <value>Şehir</value>
  </data>
  <data name="PostalCode" xml:space="preserve">
    <value>Posta kodu</value>
  </data>
  <data name="QuestionTextExpensesCoverBy" xml:space="preserve">
    <value>Türkiye'de kaldığınız süre boyunca seyahat masraflarınızı ve/veya yaşam masraflarınızı kim karşılayacak?</value>
  </data>
  <data name="QuestionTextIsHealthInsuranceExisting" xml:space="preserve">
    <value>Türkiye ziyaretiniz boyunca geçerli seyahat ve/veya sağlık sigortanız var mı?</value>
  </data>
  <data name="SaveAndContinueToContact" xml:space="preserve">
    <value>Kaydet ve iletişim bilgilerine geç</value>
  </data>
  <data name="BirthCountry" xml:space="preserve">
    <value>Doğduğu ülke</value>
  </data>
  <data name="BirthPlace" xml:space="preserve">
    <value>Doğum yeri</value>
  </data>
  <data name="Occupation" xml:space="preserve">
    <value>Meslek</value>
  </data>
  <data name="OtherSurname" xml:space="preserve">
    <value>Diğer ad</value>
  </data>
  <data name="SaveAndSendToQualityCheck" xml:space="preserve">
    <value>Kaydet ve kalite kontrole gönder</value>
  </data>
  <data name="TitleCurrentEmployer" xml:space="preserve">
    <value>Mevcut işveren / eğitim kurumu bilgileri</value>
  </data>
  <data name="Announcement" xml:space="preserve">
    <value>Duyuru</value>
  </data>
  <data name="AnnouncementList" xml:space="preserve">
    <value>Duyuru listesi</value>
  </data>
  <data name="AddAnnouncement" xml:space="preserve">
    <value>Duyuru ekle</value>
  </data>
  <data name="AnnouncementDetails" xml:space="preserve">
    <value>Duyuru detayları</value>
  </data>
  <data name="UpdateAnnouncement" xml:space="preserve">
    <value>Duyuru güncelle</value>
  </data>
  <data name="Subject" xml:space="preserve">
    <value>Konu</value>
  </data>
  <data name="Message" xml:space="preserve">
    <value>Mesaj</value>
  </data>
  <data name="ReadStatus" xml:space="preserve">
    <value>Okunma durumu</value>
  </data>
  <data name="MarkAsRead" xml:space="preserve">
    <value>Okundu olarak işaretle</value>
  </data>
  <data name="AnnouncementHistory" xml:space="preserve">
    <value>Duyuru geçmişi</value>
  </data>
  <data name="Announcements" xml:space="preserve">
    <value>Duyurular</value>
  </data>
  <data name="AppliedDocument" xml:space="preserve">
    <value>Uygulanan belge</value>
  </data>
  <data name="CrimeDate" xml:space="preserve">
    <value>Suç tarihi</value>
  </data>
  <data name="DeportationDate" xml:space="preserve">
    <value>Sınır dışı tarihi</value>
  </data>
  <data name="DeportationInstitution" xml:space="preserve">
    <value>Sınır dışı eden kurum</value>
  </data>
  <data name="DeportationReason" xml:space="preserve">
    <value>Sınır dışı edilme nedeni</value>
  </data>
  <data name="DetailOfCrime" xml:space="preserve">
    <value>Suç ayrıntısı</value>
  </data>
  <data name="PointOfEntry" xml:space="preserve">
    <value>Giriş noktası</value>
  </data>
  <data name="ReasonOfOverStay" xml:space="preserve">
    <value>Fazla kalma nedeni</value>
  </data>
  <data name="RefusingAuthority" xml:space="preserve">
    <value>Rededen kurum</value>
  </data>
  <data name="RejectedDate" xml:space="preserve">
    <value>Reddedilme tarihi</value>
  </data>
  <data name="RevocationDate" xml:space="preserve">
    <value>İptal tarihi</value>
  </data>
  <data name="RevocationReason" xml:space="preserve">
    <value>İptal nedeni</value>
  </data>
  <data name="RevokingAuthority" xml:space="preserve">
    <value>İptal eden kurum</value>
  </data>
  <data name="TypeOfCrime" xml:space="preserve">
    <value>Suç türü</value>
  </data>
  <data name="EnterUpdateData" xml:space="preserve">
    <value>Data gir / güncelle</value>
  </data>
  <data name="AddApplicationNote" xml:space="preserve">
    <value>Başvuru notu ekle</value>
  </data>
  <data name="ApplicationNotes" xml:space="preserve">
    <value>Başvuru notları</value>
  </data>
  <data name="ApplicationNote" xml:space="preserve">
    <value>Başvuru notu</value>
  </data>
  <data name="ApplicationNotesExist" xml:space="preserve">
    <value>Başvuru notları var</value>
  </data>
  <data name="AddDamageRejectionFile" xml:space="preserve">
    <value>Hasar / ret dosyası ekle</value>
  </data>
  <data name="LastProcessedBy" xml:space="preserve">
    <value>Son işlem yapan</value>
  </data>
  <data name="QualityCheck" xml:space="preserve">
    <value>Başvuru kontrol</value>
  </data>
  <data name="QualityCheckDetails" xml:space="preserve">
    <value>Başvuru kontrol detayları</value>
  </data>
  <data name="QualityCheckList" xml:space="preserve">
    <value>Başvuru kontrol listesi</value>
  </data>
  <data name="Check" xml:space="preserve">
    <value>Kontrol et</value>
  </data>
  <data name="DataNumber" xml:space="preserve">
    <value>Data numarası</value>
  </data>
  <data name="ReadonlyNotAllowToMakeChanges" xml:space="preserve">
    <value>İzleme modunda olduğunuz için değişiklik yapamazsınız</value>
  </data>
  <data name="ConfirmQualityCheck" xml:space="preserve">
    <value>Lütfen kalite kontrol onaylayınız</value>
  </data>
  <data name="AllowPassiveDeletedApplications" xml:space="preserve">
    <value>Pasif / silinmiş başvurulara izin ver</value>
  </data>
  <data name="EasyUse" xml:space="preserve">
    <value>Kolay kullanım</value>
  </data>
  <data name="Deleted" xml:space="preserve">
    <value>Silinmiş</value>
  </data>
  <data name="InsuranceNumber" xml:space="preserve">
    <value>Sigorta numarası</value>
  </data>
  <data name="ResetApplicationStatus" xml:space="preserve">
    <value>Başvuru durumunu sıfırla</value>
  </data>
  <data name="ChangesNotAllowedDueToQCStage" xml:space="preserve">
    <value>QC işlemleri beklendiğinde değişiklik yapılamıyor</value>
  </data>
  <data name="DataCompletedSendToQC" xml:space="preserve">
    <value>Data girişleri tamamlandı, QC için onaylıyorum?</value>
  </data>
  <data name="SendToQC" xml:space="preserve">
    <value>QC'ye Gönder</value>
  </data>
  <data name="AreYouSureToRejectThisRecord" xml:space="preserve">
    <value>Bu datayı reddetmek istediğinize emin misiniz?</value>
  </data>
  <data name="NotificationMail_AddAgencyUser" xml:space="preserve">
    <value>Kullanıcı kaydınız oluşturulmuştur, geçici şifreniz [PASSWORD]</value>
  </data>
  <data name="AreYouSureToActivateThisRecord" xml:space="preserve">
    <value>Bu kaydı aktive etmek istediğinize emin misiniz?</value>
  </data>
  <data name="ActivateFamilyPreApplication" xml:space="preserve">
    <value>Aile başvurusunu aktive et</value>
  </data>
  <data name="ActivateGroupPreApplication" xml:space="preserve">
    <value>Grup başvurusunu aktive et</value>
  </data>
  <data name="ActivatePreApplication" xml:space="preserve">
    <value>Başvuruyu aktive et</value>
  </data>
  <data name="BackToInfoDesk" xml:space="preserve">
    <value>Info desk ekranına dön</value>
  </data>
  <data name="MaxAppointmentDay" xml:space="preserve">
    <value>Max randevu günü</value>
  </data>
  <data name="DeleteSlot" xml:space="preserve">
    <value>Slot sil</value>
  </data>
  <data name="Customer" xml:space="preserve">
    <value>Müşteri</value>
  </data>
  <data name="CustomerList" xml:space="preserve">
    <value>Müşteri listesi</value>
  </data>
  <data name="PreviousApplications" xml:space="preserve">
    <value>Önceki başvurular</value>
  </data>
  <data name="PreviousPreApplications" xml:space="preserve">
    <value>Önceki randevular</value>
  </data>
  <data name="TotalSalesCount" xml:space="preserve">
    <value>Toplam satış sayısı</value>
  </data>
  <data name="ApplicationSearchWithPassportNumber" xml:space="preserve">
    <value>Pasaport numarası ile başvuru sorgulama</value>
  </data>
  <data name="EnterPassportNumber" xml:space="preserve">
    <value>Pasaport numarası girin</value>
  </data>
  <data name="Readers" xml:space="preserve">
    <value>Okuyanlar</value>
  </data>
  <data name="B2BUser" xml:space="preserve">
    <value>B2B kullanıcısı</value>
  </data>
  <data name="B2CUser" xml:space="preserve">
    <value>B2C kullanıcısı</value>
  </data>
  <data name="ConfirmUpdateApplicationNotToSendToSap" xml:space="preserve">
    <value>Başvuru henüz SAP'e gönderilmemiştir, güncellemeye devam etmek istiyor musunuz?</value>
  </data>
  <data name="UploadedDocuments" xml:space="preserve">
    <value>Yüklenen dosyalar</value>
  </data>
  <data name="ShowInICR" xml:space="preserve">
    <value>ICR gösterimi</value>
  </data>
  <data name="TakenNotesWillBeSeenByAgency" xml:space="preserve">
    <value>Alınan notlar acente tarafından görülecektir</value>
  </data>
  <data name="AuthorizedPersonEmail" xml:space="preserve">
    <value>Yetkili kişi email adres</value>
  </data>
  <data name="AuthorizedPersonPhoneNumber" xml:space="preserve">
    <value>Yetkili kişi telefon numarsı</value>
  </data>
  <data name="AuthorizedPersonPosition" xml:space="preserve">
    <value>Yetkili kişi görevi</value>
  </data>
  <data name="NearestAvailableSlotTime" xml:space="preserve">
    <value>En yakın uygun slot zamanı</value>
  </data>
  <data name="NotificationMail_AddAgencyUserFile" xml:space="preserve">
    <value>Başvurunuz alınmıştır. Başvurunuzu tamamlamak için portal üzerinden dosyalarınızı yükleyiniz. [FILE]</value>
  </data>
  <data name="ShowPast" xml:space="preserve">
    <value>Geçmişi göster</value>
  </data>
  <data name="AllItemsTaxed" xml:space="preserve">
    <value>Tüm kalemler vergili</value>
  </data>
  <data name="InsuranceExists" xml:space="preserve">
    <value>Sigorta var</value>
  </data>
  <data name="IsSpouseTurkishCitizen" xml:space="preserve">
    <value>Eş Türk vatandaşı mı?</value>
  </data>
  <data name="SpouseNationalNumber" xml:space="preserve">
    <value>Eş kimlik numarası</value>
  </data>
  <data name="BankPos" xml:space="preserve">
    <value>Banka pos</value>
  </data>
  <data name="AddBankPos" xml:space="preserve">
    <value>Bank pos ekle</value>
  </data>
  <data name="BankPosList" xml:space="preserve">
    <value>Bank pos listesi</value>
  </data>
  <data name="Bank" xml:space="preserve">
    <value>Banka</value>
  </data>
  <data name="BankPosDetails" xml:space="preserve">
    <value>Bank pos detayları</value>
  </data>
  <data name="MerchantCode" xml:space="preserve">
    <value>Mağaza numarası</value>
  </data>
  <data name="TerminalCode" xml:space="preserve">
    <value>Terminal numarası</value>
  </data>
  <data name="UpdateBankPos" xml:space="preserve">
    <value>Bank pos güncelle</value>
  </data>
  <data name="EncryptionKey" xml:space="preserve">
    <value>Şifreleme anahtarı</value>
  </data>
  <data name="Payment3dConfirmUrl" xml:space="preserve">
    <value>Ödeme onay url (3d)</value>
  </data>
  <data name="Payment3dUrl" xml:space="preserve">
    <value>Ödeme url (3d)</value>
  </data>
  <data name="PaymentUrl" xml:space="preserve">
    <value>Ödeme url</value>
  </data>
  <data name="StoreKey" xml:space="preserve">
    <value>Mağaza anahtarı</value>
  </data>
  <data name="Is3dEnabled" xml:space="preserve">
    <value>3d ödeme aktif</value>
  </data>
  <data name="Channel" xml:space="preserve">
    <value>Kanal</value>
  </data>
  <data name="BankPosInstallmentList" xml:space="preserve">
    <value>Banka pos taksit listesi</value>
  </data>
  <data name="GetPosInstallment" xml:space="preserve">
    <value>Banka taksitleri getir</value>
  </data>
  <data name="CommissionRate" xml:space="preserve">
    <value>Komisyon oranı</value>
  </data>
  <data name="InstallmentCount" xml:space="preserve">
    <value>Taksit sayısı</value>
  </data>
  <data name="RejectionSummaryPage" xml:space="preserve">
    <value>Ret özel sayfası</value>
  </data>
  <data name="RejectedEndDate" xml:space="preserve">
    <value>İade bitiş tarihi</value>
  </data>
  <data name="RejectedStartDate" xml:space="preserve">
    <value>İade başlangıç tarihi</value>
  </data>
  <data name="Data" xml:space="preserve">
    <value>Data</value>
  </data>
  <data name="Policy" xml:space="preserve">
    <value>Poliçe</value>
  </data>
  <data name="AppointmentOutOfDate" xml:space="preserve">
    <value>Randevu tarihi geçmiş</value>
  </data>
  <data name="EarlyAppointmentProcess" xml:space="preserve">
    <value>Randevu tarihi henüz gelmemiştir. Beklenen randevu tarihi: </value>
  </data>
  <data name="DoYouWantToUpdatePreApplicationDetails" xml:space="preserve">
    <value>Randevu detaylarını güncellemek ister misiniz?</value>
  </data>
  <data name="AppointmentStatus" xml:space="preserve">
    <value>Randevu durumu</value>
  </data>
  <data name="BiometryProcessTimeLength" xml:space="preserve">
    <value>Biyometri işlem süresi</value>
  </data>
  <data name="BiometryWaitingTimeLength" xml:space="preserve">
    <value>Biyometri bekleme süresi</value>
  </data>
  <data name="CashierProcessTimeLength" xml:space="preserve">
    <value>Kasa işlem süresi</value>
  </data>
  <data name="CashierWaitingTimeLength" xml:space="preserve">
    <value>Kasa bekleme süresi</value>
  </data>
  <data name="OnHoldStatus" xml:space="preserve">
    <value>Beklemeye alınma durumu</value>
  </data>
  <data name="ProcessTimeLength" xml:space="preserve">
    <value>İşlem süresi</value>
  </data>
  <data name="SubmissionProcessTimeLength" xml:space="preserve">
    <value>Başvuru işlem süresi</value>
  </data>
  <data name="SubmissionWaitingTimeLength" xml:space="preserve">
    <value>Başvuru bekleme süresi</value>
  </data>
  <data name="TimeLengthOfComplete" xml:space="preserve">
    <value>Tüm prosedür tamamlanma süresi</value>
  </data>
  <data name="TokenCreatedBy" xml:space="preserve">
    <value>Token veren personel</value>
  </data>
  <data name="WaitingTimeLength" xml:space="preserve">
    <value>Bekleme süresi</value>
  </data>
  <data name="Inventory" xml:space="preserve">
    <value>Envanter</value>
  </data>
  <data name="AddInventory" xml:space="preserve">
    <value>Envanter ekle</value>
  </data>
  <data name="Brand" xml:space="preserve">
    <value>Marka</value>
  </data>
  <data name="Definition" xml:space="preserve">
    <value>Tanım</value>
  </data>
  <data name="DefinitionuStatus" xml:space="preserve">
    <value>Tanım durumu</value>
  </data>
  <data name="InventoryDetails" xml:space="preserve">
    <value>Envanter detayları</value>
  </data>
  <data name="InventoryList" xml:space="preserve">
    <value>Envanter listesi</value>
  </data>
  <data name="InventoryType" xml:space="preserve">
    <value>Envanter tipi</value>
  </data>
  <data name="InventoryUpdate" xml:space="preserve">
    <value>Envanter tarihi</value>
  </data>
  <data name="Model" xml:space="preserve">
    <value>Model</value>
  </data>
  <data name="SDKAddress" xml:space="preserve">
    <value>SDK Adresi</value>
  </data>
  <data name="SDKVersion" xml:space="preserve">
    <value>SDK versiyonu</value>
  </data>
  <data name="SerialNumber" xml:space="preserve">
    <value>Seri numarası</value>
  </data>
  <data name="UpdateInventory" xml:space="preserve">
    <value>Envanter güncelle</value>
  </data>
  <data name="HealthInstitution" xml:space="preserve">
    <value>Sağlık kurumu</value>
  </data>
  <data name="DeleteToken" xml:space="preserve">
    <value>Token sil</value>
  </data>
  <data name="PremiumLounge" xml:space="preserve">
    <value>Premium lounge</value>
  </data>
  <data name="CallNext" xml:space="preserve">
    <value>Sıradaki kaydı çağır</value>
  </data>
  <data name="InOrder" xml:space="preserve">
    <value>Sırada</value>
  </data>
  <data name="WaitingForCall" xml:space="preserve">
    <value>Çağrı bekleniyor</value>
  </data>
  <data name="ClientDevice" xml:space="preserve">
    <value>İstemci cihaz</value>
  </data>
  <data name="AddClientDevice" xml:space="preserve">
    <value>İstemci cihaz ekle</value>
  </data>
  <data name="ClientDeviceDetails" xml:space="preserve">
    <value>İstemci cihaz detayları</value>
  </data>
  <data name="ClientDeviceList" xml:space="preserve">
    <value>İstemci cihaz listesi</value>
  </data>
  <data name="UpdateClientDevice" xml:space="preserve">
    <value>İstemci cihaz güncelle</value>
  </data>
  <data name="ClientDeviceInventory" xml:space="preserve">
    <value>Cihaz envanter</value>
  </data>
  <data name="Exception_AlreadyInProgress" xml:space="preserve">
    <value>Bu başvuru zaten işlemde</value>
  </data>
  <data name="ShowInSummary" xml:space="preserve">
    <value>Özet gösterimi</value>
  </data>
  <data name="PhotoDate" xml:space="preserve">
    <value>Fotoğrafın çekildiği tarih</value>
  </data>
  <data name="AdressBarcode" xml:space="preserve">
    <value>Adresli Barkod</value>
  </data>
  <data name="ScanCycleReport" xml:space="preserve">
    <value>Scan Cycle Rapor</value>
  </data>
  <data name="ChangeSlotSelection" xml:space="preserve">
    <value>Slot Seçiminizi Değiştirin, Aynı Gün İçinde Slot Seçemezsiniz.</value>
  </data>
  <data name="RemaningSlotQuota" xml:space="preserve">
    <value>Başvuru kişi sayısı seçilen slot'un kota miktarından fazla olduğu için seçilen gün içerisinde uygun slotlara randevu oluşturulacaktır</value>
  </data>
  <data name="DaySlotNotFound" xml:space="preserve">
    <value>Seçilen gün içerisinde uygun slot bulunamadı</value>
  </data>
  <data name="PriorApplicationStatus" xml:space="preserve">
    <value>Önceki Başvuru Durumu</value>
  </data>
  <data name="AddPriorApplicationStatus" xml:space="preserve">
    <value>Önceki Başvuru durumunu ekle</value>
  </data>
  <data name="ExistingBranchPriorStatus" xml:space="preserve">
    <value>Bu şube için önceki başvuru durumu mevcut</value>
  </data>
  <data name="UpdatePriorApplicationStatus" xml:space="preserve">
    <value>Önceki başvuru durumunu güncelle</value>
  </data>
  <data name="ExceptionPriorApplicationStatus" xml:space="preserve">
    <value>Başvuru durumu güncelleme sırası yanlış</value>
  </data>
  <data name="AllowDeniedPassport" xml:space="preserve">
    <value>Reddedilen Pasaporta İzin Ver</value>
  </data>
  <data name="Reason" xml:space="preserve">
    <value>Sebep</value>
  </data>
  <data name="ThisPassportIsNotBlocked" xml:space="preserve">
    <value>Bu pasaport bloke değil</value>
  </data>
  <data name="BySamePassportNumber" xml:space="preserve">
    <value>Aynı pasaport numarasına göre</value>
  </data>
  <data name="BySamePhoneNumber" xml:space="preserve">
    <value>Aynı telefon numarasına göre</value>
  </data>
  <data name="ProcedureControlReport" xml:space="preserve">
    <value>Prosedür Kontrol Raporu</value>
  </data>
  <data name="FamilyApplicationReport" xml:space="preserve">
    <value>Aile Raporu</value>
  </data>
  <data name="LastUpdatedAt" xml:space="preserve">
    <value>Son Güncelleme Zamanı</value>
  </data>
  <data name="ShowUpdated" xml:space="preserve">
    <value>Güncellenenleri göster</value>
  </data>
  <data name="UpdatedInformation" xml:space="preserve">
    <value>Güncellenen Bilgiler</value>
  </data>
  <data name="UpdatedReport" xml:space="preserve">
    <value>Güncelleme Raporu</value>
  </data>
  <data name="PrintEntryInformationFormForTurkey" xml:space="preserve">
    <value>Türkiye Giriş Bilgilendirme Formu Yazdır</value>
  </data>
  <data name="French" xml:space="preserve">
    <value>Fransızca</value>
  </data>
  <data name="RemainingBlockTime" xml:space="preserve">
    <value>Kalan Blok Süresi</value>
  </data>
  <data name="DocumentEditing" xml:space="preserve">
    <value>Evrak Düzenleme</value>
  </data>
  <data name="RefundedFeeDate" xml:space="preserve">
    <value>İade Tarihi</value>
  </data>
  <data name="CorporateNameArabic" xml:space="preserve">
    <value>Firma Adı (Arapça)</value>
  </data>
  <data name="InvoiceNumberArabic" xml:space="preserve">
    <value>Vergi numarası (arapça)</value>
  </data>
  <data name="BranchAddressArabic" xml:space="preserve">
    <value>Şube adresi (arapça)</value>
  </data>
  <data name="BranchMissionArabic" xml:space="preserve">
    <value>Şube görevi (arapça)</value>
  </data>
  <data name="IcrNoteArabic" xml:space="preserve">
    <value>ICR not (Arapça)</value>
  </data>
  <data name="IcrNoteEnglish" xml:space="preserve">
    <value>ICR not (İngilizce)</value>
  </data>
  <data name="ApplicantRelationship" xml:space="preserve">
    <value>Başvuru sahibi ilişkisi</value>
  </data>
  <data name="MoreThanOneHusbandOrWifeCannotBeEntered" xml:space="preserve">
    <value>Birden fazla eş girilemez.</value>
  </data>
  <data name="VisaDecision" xml:space="preserve">
    <value>Vize Kararı</value>
  </data>
  <data name="FileCategory" xml:space="preserve">
    <value>Dosya kategorisi</value>
  </data>
  <data name="NumberOfEntry" xml:space="preserve">
    <value>Giriş sayısı</value>
  </data>
  <data name="VisaDuration" xml:space="preserve">
    <value>Vize süresi</value>
  </data>
  <data name="AddApplicationVisaDecision" xml:space="preserve">
    <value>Başvuru vize kararı ekle</value>
  </data>
  <data name="RecieveTimeAtVac" xml:space="preserve">
    <value>VAC'tan teslim alınma tarihi</value>
  </data>
  <data name="AppointmentCreationDate" xml:space="preserve">
    <value>Randevu oluşturma tarihi</value>
  </data>
  <data name="BasePrice" xml:space="preserve">
    <value>Temel fiyat</value>
  </data>
  <data name="ServiceTax" xml:space="preserve">
    <value>Hizmet vergisi</value>
  </data>
  <data name="AddOtherApplicationFiles" xml:space="preserve">
    <value>Diğer evraklar ekle</value>
  </data>
  <data name="PreviousJobOfApplicant" xml:space="preserve">
    <value>Başvuran kişinin daha önceki mesleği</value>
  </data>
  <data name="DateAndTime" xml:space="preserve">
    <value>Tarih ve Saat</value>
  </data>
  <data name="Exception_SapInvocePartialCancel" xml:space="preserve">
    <value>SAP de tahsilatı yapılmamış başvuru için kısmı iade yapılamaz</value>
  </data>
  <data name="IncorrectApplicationStatus" xml:space="preserve">
    <value>Yanlış başvuru durumlarını düzeltme</value>
  </data>
  <data name="EntryDateCannotBeLessThanTheApplicationTime" xml:space="preserve">
    <value>Giriş tarihi başvuru tarihinden küçük olamaz</value>
  </data>
  <data name="UpdatedCancelledQuantity" xml:space="preserve">
    <value>Güncellenmiş İptal Edilen Miktar</value>
  </data>
  <data name="UpdatedQuantity" xml:space="preserve">
    <value>Güncellenmiş Miktar</value>
  </data>
  <data name="UpdateApplicationTime" xml:space="preserve">
    <value>Başvuru Tarihini Güncelle</value>
  </data>
  <data name="InsuranceNotExist" xml:space="preserve">
    <value>Sigorta yok</value>
  </data>
  <data name="Russian" xml:space="preserve">
    <value>Rusça</value>
  </data>
  <data name="Turkmen" xml:space="preserve">
    <value>Türkmence</value>
  </data>
  <data name="IcrNoteRussian" xml:space="preserve">
    <value>Icr not (Rusça)</value>
  </data>
  <data name="IcrNoteTurkmen" xml:space="preserve">
    <value>Icr not (Türkmence)</value>
  </data>
  <data name="LocalAuthorityStatus" xml:space="preserve">
    <value>Lokal Yetki Durumu</value>
  </data>
  <data name="LocalAuthorityStatusIsRequired" xml:space="preserve">
    <value>Lokal Yetki Durumunu Seçiniz</value>
  </data>
  <data name="FirstVisaId" xml:space="preserve">
    <value>İlk Visa ID</value>
  </data>
  <data name="SecondVisaId" xml:space="preserve">
    <value>Güncellenen Visa ID</value>
  </data>
  <data name="UpdatedInfo" xml:space="preserve">
    <value>Güncellenen Bilgi</value>
  </data>
  <data name="UpdatedLast" xml:space="preserve">
    <value>Guncelleme Sonrası</value>
  </data>
  <data name="AutomaticAccepted" xml:space="preserve">
    <value>Otomatik Kabul</value>
  </data>
  <data name="AddToFamilyMembers" xml:space="preserve">
    <value>Aile üyelerine ekleyin</value>
  </data>
  <data name="CheckUnrealDocumentStatusPeriod" xml:space="preserve">
    <value>Sahte evrak durumu kontrol süresi</value>
  </data>
  <data name="ExistingUnrealDocumentApplicationStatus" xml:space="preserve">
    <value>Sahte evraklı başvuru durumu mevcut</value>
  </data>
  <data name="PreviouslyHasUnrealDocument" xml:space="preserve">
    <value>Önceden sahte evraklı başvuruya sahip</value>
  </data>
  <data name="CheckUnrealDocumentStatus" xml:space="preserve">
    <value>Sahte evrak durumunu kontrol et</value>
  </data>
  <data name="RelatedProcessVisaId" xml:space="preserve">
    <value>İlgili işlem için VISA ID</value>
  </data>
  <data name="Info" xml:space="preserve">
    <value>Bilgi</value>
  </data>
  <data name="Turkish" xml:space="preserve">
    <value>Türkçe</value>
  </data>
  <data name="ExplicitConsentText" xml:space="preserve">
    <value>Açık Rıza Metni</value>
  </data>
  <data name="OutscanToEmbassy" xml:space="preserve">
    <value>Elçiliğe gönderildi</value>
  </data>
  <data name="VisaFeeInformation" xml:space="preserve">
    <value>Vize Bedeli</value>
  </data>
  <data name="TurkmenistanConsulateReportTitle" xml:space="preserve">
    <value>TARİHLİ HARCA TABİ VİZE TESLİMİ</value>
  </data>
  <data name="AddSurvey" xml:space="preserve">
    <value>Anket Ekle</value>
  </data>
  <data name="Print" xml:space="preserve">
    <value>Yazdır</value>
  </data>
  <data name="Survey" xml:space="preserve">
    <value>Anket</value>
  </data>
  <data name="UsingCalendarNotification" xml:space="preserve">
    <value>Lütfen takvimden bir tarih seçiniz.</value>
  </data>
  <data name="VisaOfficerObservation" xml:space="preserve">
    <value>Vize Görevlisinin Gözlemi</value>
  </data>
  <data name="Refund" xml:space="preserve">
    <value>İade</value>
  </data>
  <data name="NetTotal" xml:space="preserve">
    <value>Net Toplam</value>
  </data>
  <data name="HimselfHerself" xml:space="preserve">
    <value>Kendisi</value>
  </data>
  <data name="ApplicationsPreviousNote" xml:space="preserve">
    <value>Önceki başvuruya ait not</value>
  </data>
  <data name="PreviousApplicationUnrealNll" xml:space="preserve">
    <value>Önceki Başvuru NLL(Sahte Evrak)</value>
  </data>
  <data name="SecondRefundIsDoneInsureCheck" xml:space="preserve">
    <value>Devam eden sigorta olduğundan dolayı ikinci iade işlemi yapılamamaktadır</value>
  </data>
  <data name="ShowPreviousApplication" xml:space="preserve">
    <value>Geçmiş başvuruyu göster</value>
  </data>
  <data name="CheckRejectionWithCountryEntryBannedStatus" xml:space="preserve">
    <value>YGY-R başvuru durumu kontrolü</value>
  </data>
  <data name="CheckRejectionWithCountryEntryBannedStatusPeriod" xml:space="preserve">
    <value>YGY-R kontrol süresi</value>
  </data>
  <data name="ExistingRejectionWithCountryEntryBannedStatus" xml:space="preserve">
    <value>Yurda giriş yasaklı red (YGY-R) başvuru durumu mevcut</value>
  </data>
  <data name="PreviouslyHasRejectionWithCountryEntryBannedStatus" xml:space="preserve">
    <value>Önceden yurda giriş yasaklı red (YGY-R) almış başvuru</value>
  </data>
  <data name="IsShowInReport" xml:space="preserve">
    <value>Rapor Gösterimi</value>
  </data>
  <data name="LineType" xml:space="preserve">
    <value>Hat Türü</value>
  </data>
  <data name="AppointmentNumberIsRequired" xml:space="preserve">
    <value>Başvuru Numarası Girilmelidir</value>
  </data>
  <data name="LineTypeShouldBeSelected" xml:space="preserve">
    <value>Hat türü seçilmelidir</value>
  </data>
  <data name="LineNumber" xml:space="preserve">
    <value>Hat numarası</value>
  </data>
  <data name="Assign" xml:space="preserve">
    <value>Devret</value>
  </data>
  <data name="HoldOn" xml:space="preserve">
    <value>Beklet</value>
  </data>
  <data name="Individual" xml:space="preserve">
    <value>Bireysel</value>
  </data>
  <data name="NotFound" xml:space="preserve">
    <value>Bulunamadı</value>
  </data>
  <data name="ApplicantList" xml:space="preserve">
    <value>Başvuran listesi</value>
  </data>
  <data name="Assignee" xml:space="preserve">
    <value>Devralan kişi</value>
  </data>
  <data name="Assigner" xml:space="preserve">
    <value>Devreden kişi</value>
  </data>
  <data name="ChooseLineAndOrCounter" xml:space="preserve">
    <value>Hat ve/veya kontuar seçiniz</value>
  </data>
  <data name="Warning" xml:space="preserve">
    <value>Uyarı</value>
  </data>
  <data name="SelectTheLineType" xml:space="preserve">
    <value>Hat türünü seçiniz</value>
  </data>
  <data name="RecordNotFound" xml:space="preserve">
    <value>Kayıt bulunamadı</value>
  </data>
  <data name="ExistingRecord" xml:space="preserve">
    <value>Mevcut kayıt</value>
  </data>
  <data name="PleaseSelectAtLeastOneApplication" xml:space="preserve">
    <value>Lütfen en az bir başvuru seçiniz</value>
  </data>
  <data name="TokenStatusUpdatedAsCancelled" xml:space="preserve">
    <value>Bilet statüsü iptal edildi olarak güncellendi</value>
  </data>
  <data name="TokenStatusUpdatedAsNotFound" xml:space="preserve">
    <value>Bilet statüsü bulunamadı olarak güncellendi</value>
  </data>
  <data name="TokenStatusUpdatedAsPostponed" xml:space="preserve">
    <value>Bilet statüsü ertelendi olarak güncellendi</value>
  </data>
  <data name="TokenStatusUpdatedAsAssigned" xml:space="preserve">
    <value>Bilet statüsü atandı olarak güncellendi</value>
  </data>
  <data name="TokenStatusUpdatedAsCompleted" xml:space="preserve">
    <value>Bilet statüsü tamamlandı olarak güncellendi</value>
  </data>
  <data name="TokenStatusUpdatedAsHoldOn" xml:space="preserve">
    <value>Bilet statüsü beklemeye alındı olarak güncellendi</value>
  </data>
  <data name="LineNotFound" xml:space="preserve">
    <value>Hat bulunamadı</value>
  </data>
  <data name="TokenNotFound" xml:space="preserve">
    <value>Token bulunamadı</value>
  </data>
  <data name="AppointmentNotFound" xml:space="preserve">
    <value>Randevu bulunamadı</value>
  </data>
  <data name="WrongBranchNotification" xml:space="preserve">
    <value>Başvuru numarası bu şubeye ait değildir</value>
  </data>
  <data name="MoreThanOnePersonShouldBeSelectedForFamilyOrGroupAppointments" xml:space="preserve">
    <value>Aile veya grup randevuları için birden fazla kişi seçilmelidir.</value>
  </data>
  <data name="RESOURCE_ALREADY_REGISTERED" xml:space="preserve">
    <value>Önceden kaydedilmiş</value>
  </data>
  <data name="TicketHaBeenTakenForSelectedPeople" xml:space="preserve">
    <value>Bilet seçilen kişi/kişiler için alınmıştır.</value>
  </data>
  <data name="Error" xml:space="preserve">
    <value>Hata</value>
  </data>
  <data name="AreYouSureToAssignThisRecord" xml:space="preserve">
    <value>Bu başvuruyu başka bir kişiye atamak istediğinizden emin misiniz?</value>
  </data>
  <data name="AreYouSureToCancelThisRecord" xml:space="preserve">
    <value>Bu başvuruyu iptal edildi olarak atamak istediğinizden emin misiniz?</value>
  </data>
  <data name="AreYouSureToHoldOnThisRecord" xml:space="preserve">
    <value>Bu başvuruyu beklemeye almak istediğinizden emin misiniz?</value>
  </data>
  <data name="AreYouSureToNotFoundThisRecord" xml:space="preserve">
    <value>Bu başvuruyu bulunamadı olarak atamak istediğinizden emin misiniz?</value>
  </data>
  <data name="EnterNumericAppointmentNumber" xml:space="preserve">
    <value>Lütfen geçerli bir randevu numarası giriniz</value>
  </data>
  <data name="WhiteSpaceAppointmentNumber" xml:space="preserve">
    <value>Lütfen bir randevu numarası giriniz</value>
  </data>
  <data name="ApplicantWithHasTicketNotFound" xml:space="preserve">
    <value>Bilete sahip başvuran bulunamadı</value>
  </data>
  <data name="ApplicantNotFound" xml:space="preserve">
    <value>Başvuran bulunamadı</value>
  </data>
  <data name="SearchOperationNotValidForThisLineDepartment" xml:space="preserve">
    <value>Arama işlemi bu hat departmanı için yetkilendirilmedi</value>
  </data>
  <data name="NumberOfApplications" xml:space="preserve">
    <value>Başvuru sayısı</value>
  </data>
  <data name="PleaseWaitForYourQueueNumberToBeCalled" xml:space="preserve">
    <value>Lütfen sıra numaranızın çağrılmasını bekleyiniz. Teşekkür ederiz.</value>
  </data>
  <data name="BlockNumber" xml:space="preserve">
    <value>Blok numarası</value>
  </data>
  <data name="CounterName" xml:space="preserve">
    <value>Kontuar adı</value>
  </data>
  <data name="FloorNumber" xml:space="preserve">
    <value>Kat numarası</value>
  </data>
  <data name="LineDepartment" xml:space="preserve">
    <value>Hat departmanı</value>
  </data>
  <data name="AddCounter" xml:space="preserve">
    <value>Kontuar ekle</value>
  </data>
  <data name="CounterDetails" xml:space="preserve">
    <value>Kontuar detayı</value>
  </data>
  <data name="CounterList" xml:space="preserve">
    <value>Kontuar listesi</value>
  </data>
  <data name="UpdateCounter" xml:space="preserve">
    <value>Kontuar güncelle</value>
  </data>
  <data name="AddToTheExistingFamilyOrGroupApplication" xml:space="preserve">
    <value>Mevcut aile veya grup başvurusuna ekleyin</value>
  </data>
  <data name="AppointmentIsCompleted" xml:space="preserve">
    <value>Randevu Tamamlandı</value>
  </data>
  <data name="TypeYourMessage" xml:space="preserve">
    <value>Mesajınızı yazın</value>
  </data>
  <data name="UpdateOrder" xml:space="preserve">
    <value>Sıra güncelle</value>
  </data>
  <data name="SelectedLine" xml:space="preserve">
    <value>Seçilen hat</value>
  </data>
  <data name="TokenRecalled" xml:space="preserve">
    <value>Token yeniden çağrıldı</value>
  </data>
  <data name="AreYouSureToRecallThisRecord" xml:space="preserve">
    <value>Bu kaydı tekrar çağırmak istiyormusunuz</value>
  </data>
  <data name="CallNextFromHoldOn" xml:space="preserve">
    <value>Sıradaki bekleyen kaydı çağır</value>
  </data>
  <data name="Completed" xml:space="preserve">
    <value>Tamamlandı</value>
  </data>
  <data name="CannotBeLeftBlank" xml:space="preserve">
    <value>Bu alan boş bırakılamaz</value>
  </data>
  <data name="EnterValidQmsNote" xml:space="preserve">
    <value>Lütfen geçerli bir not giriniz</value>
  </data>
  <data name="CreateToken" xml:space="preserve">
    <value>Token Oluştur</value>
  </data>
  <data name="ScanPassportAndAddNewApplicant" xml:space="preserve">
    <value>Pasaportu tarat ve yeni başvuru ekle</value>
  </data>
  <data name="LineDepartmentFlow" xml:space="preserve">
    <value>Line Departman Akışı</value>
  </data>
  <data name="AddLineDepartmentFlow" xml:space="preserve">
    <value>Line Department Akışı Ekle</value>
  </data>
  <data name="LineList" xml:space="preserve">
    <value>Line Listesi</value>
  </data>
  <data name="LineDepartmentFlowDetails" xml:space="preserve">
    <value>Line Departman Akışı Detayları</value>
  </data>
  <data name="UpdateLineDepartmentFlow" xml:space="preserve">
    <value>Line Departman Akışını Güncelle</value>
  </data>
  <data name="Interval" xml:space="preserve">
    <value>Aralık</value>
  </data>
  <data name="IsDepartmentCreateApplication" xml:space="preserve">
    <value>Departman başvuru oluştursun</value>
  </data>
  <data name="ProcessSameCounter" xml:space="preserve">
    <value>Toplu işlem yapılabilsin</value>
  </data>
  <data name="RESOURCE_CREATED" xml:space="preserve">
    <value>Kayıt oluşturuldu</value>
  </data>
  <data name="ADepartmentShouldNotBeSelectedMoreThanOne" xml:space="preserve">
    <value>Bir departman birden fazla seçilmemelidir</value>
  </data>
  <data name="AtLeastOneDepartmentIsSelected" xml:space="preserve">
    <value>En az bir departman seçilmelidir</value>
  </data>
  <data name="TheIntervalShouldNotBeSelectedZero" xml:space="preserve">
    <value>Aralık sıfır seçilmemelidir</value>
  </data>
  <data name="PleaseEnterLineName" xml:space="preserve">
    <value>Lütfen line ismini giriniz</value>
  </data>
  <data name="Line" xml:space="preserve">
    <value>Hat</value>
  </data>
  <data name="SUCCESS" xml:space="preserve">
    <value>SUCCESS</value>
  </data>
  <data name="RESOURCE_UPDATED" xml:space="preserve">
    <value>Kayıt güncellendi</value>
  </data>
  <data name="PleaseSelectTheDepartmentYouWantToAddInOrder" xml:space="preserve">
    <value>Lütfen eklemek istediğiniz departmanı sırayla seçiniz</value>
  </data>
  <data name="Year_Month_Day" xml:space="preserve">
    <value>Yıl/Ay/Gün</value>
  </data>
  <data name="Second" xml:space="preserve">
    <value>sn</value>
  </data>
  <data name="VisaIsUsedYear" xml:space="preserve">
    <value>Vize Verildiği Yıl</value>
  </data>
  <data name="SelectedApplicantPostponed" xml:space="preserve">
    <value>Seçilen kişi ertelendi</value>
  </data>
  <data name="AreYouSureToChangeStatusOfThisApplicant" xml:space="preserve">
    <value>Seçili başvuranın statüsünü değiştirmek istiyormusunuz ?</value>
  </data>
  <data name="TokenStatusUpdatedForSelectedApplicant" xml:space="preserve">
    <value>Seçili başvuranın statüsü güncellendi</value>
  </data>
  <data name="IndividualAction" xml:space="preserve">
    <value>Bireysel aksiyon</value>
  </data>
  <data name="IndividualActionsAppliedToAllApplicants" xml:space="preserve">
    <value>Bütün başvuranlar için bireysel action uygulandı</value>
  </data>
  <data name="SearchByPassportNumber" xml:space="preserve">
    <value>Pasaport numarasına göre arama</value>
  </data>
  <data name="All" xml:space="preserve">
    <value>Tümü</value>
  </data>
  <data name="Scan" xml:space="preserve">
    <value>Tara</value>
  </data>
  <data name="DescriptionIsRequired" xml:space="preserve">
    <value>Açıklama gerekli</value>
  </data>
  <data name="AreYouSureToRemoveAddedApplicant" xml:space="preserve">
    <value>Eklenmiş olan randevuyu silmek istediğinizden emin misiniz ?</value>
  </data>
  <data name="PrintAll" xml:space="preserve">
    <value>Tümünü Yazdır</value>
  </data>
  <data name="FinalStatusReport" xml:space="preserve">
    <value>Son Statü Raporu</value>
  </data>
  <data name="RePrintTicket" xml:space="preserve">
    <value>Yeniden Bilet Basımı</value>
  </data>
  <data name="QMS" xml:space="preserve">
    <value>QMS</value>
  </data>
  <data name="ATokenHasBeenCreatedForAllPeopleofThisAppointmentNumber" xml:space="preserve">
    <value>Bu randevu numarasındaki tüm kişiler için bir jeton oluşturuldu</value>
  </data>
  <data name="ActiveForEmail" xml:space="preserve">
    <value>Aktif mail gönderimi</value>
  </data>
  <data name="ActiveForSms" xml:space="preserve">
    <value>Akfif sms gönderimi</value>
  </data>
  <data name="NotificationMail_Postpone" xml:space="preserve">
    <value>[DATE] tarihi ve [TIME] saat icin [BRANCH] subesine randevunuz ertelenmiştir. Randevu numaraniz [APPNUMBER]</value>
  </data>
  <data name="MailFooter" xml:space="preserve">
    <value>Otomatik maildir. Lütfen bu maili cevaplamayınız.</value>
  </data>
  <data name="AddApplicationOfficialNote" xml:space="preserve">
    <value>Yetkili Notu Ekle</value>
  </data>
  <data name="ApplicationOfficialNote" xml:space="preserve">
    <value>Yetkili Notu</value>
  </data>
  <data name="ApplicationOfficialNotes" xml:space="preserve">
    <value>Yetkili Notları</value>
  </data>
  <data name="OfficialNotes" xml:space="preserve">
    <value>Yetkili Notları</value>
  </data>
  <data name="EmaaHasarExcelUpdate" xml:space="preserve">
    <value>Emaa Hasar Servis Excel Aktarım</value>
  </data>
  <data name="OpenToEmaaLossClaimEnty" xml:space="preserve">
    <value>Hasar dosyası aç</value>
  </data>
  <data name="ApplicationDetailNotes" xml:space="preserve">
    <value>Başvuru Detay Notları</value>
  </data>
  <data name="NetAmount" xml:space="preserve">
    <value>Net Tutar</value>
  </data>
  <data name="NetQuantity" xml:space="preserve">
    <value>Net Adet</value>
  </data>
  <data name="IsGroupInIcr" xml:space="preserve">
    <value>Icr da Grupla</value>
  </data>
  <data name="AddedToken" xml:space="preserve">
    <value>Eklenen token</value>
  </data>
  <data name="RecallTimeLength" xml:space="preserve">
    <value>Tekrar çağırılma süresi</value>
  </data>
  <data name="EntireProcedureCompleteTimeLength" xml:space="preserve">
    <value>Tüm prosedür tamamlanma süresi</value>
  </data>
  <data name="DepartmentNotDefined" xml:space="preserve">
    <value>Bu şube için departman tanımlı değil</value>
  </data>
  <data name="TokenNumber" xml:space="preserve">
    <value>Bilet numarası</value>
  </data>
  <data name="ActionDate" xml:space="preserve">
    <value>Aksiyon tarihi</value>
  </data>
  <data name="PostponedDate" xml:space="preserve">
    <value>Ertelendiği tarih</value>
  </data>
  <data name="AssignRatio" xml:space="preserve">
    <value>Atanma oranı</value>
  </data>
  <data name="AverageServeTimeLength" xml:space="preserve">
    <value>Ortalama hizmet süresi</value>
  </data>
  <data name="CancelledRatio" xml:space="preserve">
    <value>İptal edildi oranı</value>
  </data>
  <data name="CompletedRatio" xml:space="preserve">
    <value>Tamamlanma oranı</value>
  </data>
  <data name="HoldOnRatio" xml:space="preserve">
    <value>Beklemeye alınma oranı</value>
  </data>
  <data name="NotFoundRatio" xml:space="preserve">
    <value>Bulunamadı oranı</value>
  </data>
  <data name="PostponeRatio" xml:space="preserve">
    <value>Ertelenme oranı</value>
  </data>
  <data name="TokenCalledCount" xml:space="preserve">
    <value>Kaç token çağrıldı</value>
  </data>
  <data name="TotalServeTime" xml:space="preserve">
    <value>Toplam hizmet süresi</value>
  </data>
  <data name="Cabin" xml:space="preserve">
    <value>Kabin</value>
  </data>
  <data name="CabinName" xml:space="preserve">
    <value>Kabin adı</value>
  </data>
  <data name="IsBiometricCabin" xml:space="preserve">
    <value>Biyometrik kabin</value>
  </data>
  <data name="Office" xml:space="preserve">
    <value>Ofis</value>
  </data>
  <data name="OfficeName" xml:space="preserve">
    <value>Ofis adı</value>
  </data>
  <data name="AddCabin" xml:space="preserve">
    <value>Kabin ekle</value>
  </data>
  <data name="AddOffice" xml:space="preserve">
    <value>Ofis ekle</value>
  </data>
  <data name="CabinDetails" xml:space="preserve">
    <value>Kabin detay</value>
  </data>
  <data name="CabinList" xml:space="preserve">
    <value>Kabin Listesi</value>
  </data>
  <data name="DeleteOffice" xml:space="preserve">
    <value>Ofis sil</value>
  </data>
  <data name="OfficeCode" xml:space="preserve">
    <value>Ofis Kodu</value>
  </data>
  <data name="OfficeDetails" xml:space="preserve">
    <value>Ofis Detay</value>
  </data>
  <data name="OfficeList" xml:space="preserve">
    <value>Ofis Listesi</value>
  </data>
  <data name="UpdateCabin" xml:space="preserve">
    <value>Kabin güncelle</value>
  </data>
  <data name="UpdateOffice" xml:space="preserve">
    <value>Ofis güncelle</value>
  </data>
  <data name="AddClientConfiguration" xml:space="preserve">
    <value>İstemci konfigürasyon ekle</value>
  </data>
  <data name="AddInventoryDefinition" xml:space="preserve">
    <value>Envanter tanımı ekle</value>
  </data>
  <data name="ClientConfigurationDetails" xml:space="preserve">
    <value>İstemci konfigürasyon detay</value>
  </data>
  <data name="ClientConfigurationList" xml:space="preserve">
    <value>İstemci konfigürasyon listesi</value>
  </data>
  <data name="HostName" xml:space="preserve">
    <value>Makine adı</value>
  </data>
  <data name="InventoryDefinition" xml:space="preserve">
    <value>Envanter tanımı</value>
  </data>
  <data name="InventoryDefinitionDetails" xml:space="preserve">
    <value>Envanter tanımı detay</value>
  </data>
  <data name="InventoryDefinitionList" xml:space="preserve">
    <value>Envanter tanım listesi</value>
  </data>
  <data name="UpdateClientConfiguration" xml:space="preserve">
    <value>İstemci konfigürasyon güncelle</value>
  </data>
  <data name="UpdateInventoryDefinition" xml:space="preserve">
    <value>Envanter tanımı güncelle</value>
  </data>
  <data name="AssignedInventories" xml:space="preserve">
    <value>Atanmış envanterler</value>
  </data>
  <data name="AvailableInventories" xml:space="preserve">
    <value>Mevcut envanterler</value>
  </data>
  <data name="UnAssign" xml:space="preserve">
    <value>Atamayı Kaldır</value>
  </data>
  <data name="ClaimNo" xml:space="preserve">
    <value>Hasar Dosya Numarası</value>
  </data>
  <data name="RejectionRefundDoneLastDescription" xml:space="preserve">
    <value>tarihinde vize ret iadesi yapılmıştır. Lütfen başvuru sahibini bilgilendiriniz.</value>
  </data>
  <data name="RejectionRefundDonePreviousDescription" xml:space="preserve">
    <value>numaralı bir önceki başvuru için</value>
  </data>
  <data name="RejectionRefundDonePermission" xml:space="preserve">
    <value>Vize Ret İadesi Kontrol Sayısı</value>
  </data>
  <data name="AddBlackList" xml:space="preserve">
    <value>Black List Ekle</value>
  </data>
  <data name="AddWhiteList" xml:space="preserve">
    <value>Add White List</value>
  </data>
  <data name="BlackList" xml:space="preserve">
    <value>Black List</value>
  </data>
  <data name="WhiteList" xml:space="preserve">
    <value>White List</value>
  </data>
  <data name="IHBUploadedNotAvailableForThisApplication" xml:space="preserve">
    <value>İlgili başvuru için IHB Bekleniyor statüsü kullanılamaz</value>
  </data>
  <data name="IHBDocumentNumber" xml:space="preserve">
    <value>İyi hal belgesi döküman numarası</value>
  </data>
  <data name="NotSuitable" xml:space="preserve">
    <value>Uygun değildir</value>
  </data>
  <data name="Suitable" xml:space="preserve">
    <value>Uygun</value>
  </data>
  <data name="IHBNumber" xml:space="preserve">
    <value>IHB numarası</value>
  </data>
  <data name="EvaluationDate" xml:space="preserve">
    <value>Değerlendirme tarihi</value>
  </data>
  <data name="EvaluationTime" xml:space="preserve">
    <value>Değerlendirme zamanı</value>
  </data>
  <data name="FileOperations" xml:space="preserve">
    <value>Dosya operasyonu</value>
  </data>
  <data name="FilterAllList" xml:space="preserve">
    <value>Tümünü listele</value>
  </data>
  <data name="FilterWaiting" xml:space="preserve">
    <value>Bekleyenler</value>
  </data>
  <data name="IHBStatus" xml:space="preserve">
    <value>IHB statüsü</value>
  </data>
  <data name="WalkinVasTypeControl" xml:space="preserve">
    <value>Randevusuz vas tipi kontrolü</value>
  </data>
  <data name="ContentType" xml:space="preserve">
    <value>Content Type</value>
  </data>
  <data name="Screen" xml:space="preserve">
    <value>Ekran</value>
  </data>
  <data name="ScreenType" xml:space="preserve">
    <value>Ekran Türü</value>
  </data>
  <data name="Exception_NoWaitingForIhbApplicationFound" xml:space="preserve">
    <value>IHB bekleniyor statüsüne sahip başvuran bulunamadı</value>
  </data>
  <data name="ErrorNotFoundPolicyClaimLoss" xml:space="preserve">
    <value>Sigortası olmayan başvurunun hasar dosyası açılamaz</value>
  </data>
  <data name="IsShowInRejectionList" xml:space="preserve">
    <value>Ret Listesinde Göster</value>
  </data>
  <data name="GeneratedTokenStatusChart" xml:space="preserve">
    <value>Token statüleri tablosu</value>
  </data>
  <data name="NumberOfTokensByApplicantType" xml:space="preserve">
    <value>Başvuran türüne göre token sayısı</value>
  </data>
  <data name="NumberOfVisitorsDaily" xml:space="preserve">
    <value>Günlük ziyaretçi sayısı</value>
  </data>
  <data name="TotalApplicantsUsedQmsByBranches" xml:space="preserve">
    <value>Şubelere göre toplam ziyaretçi sayısı</value>
  </data>
  <data name="VisitorsByHourOfDay" xml:space="preserve">
    <value>Saatlik ziyaretçi sayısı</value>
  </data>
  <data name="VisitorsByWeekday" xml:space="preserve">
    <value>Haftalık ziyaretçi sayısı</value>
  </data>
  <data name="HasPersonVisitedTurkeyBefore" xml:space="preserve">
    <value>Daha önce Türkiye ziyareti var mı ?</value>
  </data>
  <data name="Approved" xml:space="preserve">
    <value>Onaylandı</value>
  </data>
  <data name="HasVisitedTurkeyAndApprovedPercentage" xml:space="preserve">
    <value>Daha önce Türkiye ziyareti olup onaylanan</value>
  </data>
  <data name="HasVisitedTurkeyAndNonApprovedPercentage" xml:space="preserve">
    <value>Daha önce Türkiye seyahati olup onaylanmayan</value>
  </data>
  <data name="NegativeIHBPercentage" xml:space="preserve">
    <value>Olumsuz İHB yüzdesi</value>
  </data>
  <data name="NotApproved" xml:space="preserve">
    <value>Onaylanmadı</value>
  </data>
  <data name="PositiveIHBPercentage" xml:space="preserve">
    <value>Olumlu İHB yüzdesi</value>
  </data>
  <data name="TurkmenistanApplicationStatistics" xml:space="preserve">
    <value>Türkmenistan başvuru istatistikleri</value>
  </data>
  <data name="LastHeartbeatTime" xml:space="preserve">
    <value>Son Çalışma Zamanı</value>
  </data>
  <data name="CourierInCity" xml:space="preserve">
    <value>Şehir İçi Kargo</value>
  </data>
  <data name="CourierOutOfCity" xml:space="preserve">
    <value>Şehir Dışı Kargo</value>
  </data>
  <data name="DoubleTransitForForeigners" xml:space="preserve">
    <value>Yabancı Uyruklu Transit (Çift Giriş)</value>
  </data>
  <data name="DoubleTransitVisaFee" xml:space="preserve">
    <value>Çift Giriş Transit Vize Ücreti</value>
  </data>
  <data name="FormFilling" xml:space="preserve">
    <value>Form Doldurma</value>
  </data>
  <data name="GatewayServiceFee" xml:space="preserve">
    <value>Gateway Servis Ücreti</value>
  </data>
  <data name="GratisGWServiceFee" xml:space="preserve">
    <value>Bedelsiz GW Servis Ücreti</value>
  </data>
  <data name="GratisServiceFeeRequestedByEmbassy" xml:space="preserve">
    <value>Konsolosluk Talepli Bedelsiz Servis Ücreti</value>
  </data>
  <data name="MultipleVisaFee" xml:space="preserve">
    <value>Çok Giriş Vize Ücreti</value>
  </data>
  <data name="MultipleVisaFeeForForeigners" xml:space="preserve">
    <value>Yabancı Uyruklu Çok Giriş Vize Ücreti</value>
  </data>
  <data name="Photocopy" xml:space="preserve">
    <value>Fotokopi</value>
  </data>
  <data name="Photograph" xml:space="preserve">
    <value>Fotoğraf</value>
  </data>
  <data name="PrintOut" xml:space="preserve">
    <value>Çıktı</value>
  </data>
  <data name="SingleEntryVisaFee" xml:space="preserve">
    <value>Tek Giriş Vize Ücreti</value>
  </data>
  <data name="SingleTransitForForeigners" xml:space="preserve">
    <value>Yabancı Uyruklu Transit (Tek Giriş)</value>
  </data>
  <data name="SingleTransitVisaFee" xml:space="preserve">
    <value>Tek Giriş Transit Vize Ücreti</value>
  </data>
  <data name="SingleVisaFeeForForeigners" xml:space="preserve">
    <value>Yabancı Uyruklu Tek Giriş Vize Ücreti</value>
  </data>
  <data name="Staff" xml:space="preserve">
    <value>Personel</value>
  </data>
  <data name="VisaAssistService" xml:space="preserve">
    <value>Vize Danışmanlık Hizmeti</value>
  </data>
  <data name="Year" xml:space="preserve">
    <value>Yıl</value>
  </data>
  <data name="TokenReport" xml:space="preserve">
    <value>Token raporu</value>
  </data>
  <data name="LastActionCreatedAt" xml:space="preserve">
    <value>Son aksiyon tarihi</value>
  </data>
  <data name="LastActionCreatedBy" xml:space="preserve">
    <value>Son aksiyon oluşturan</value>
  </data>
  <data name="TokenCreatedAt" xml:space="preserve">
    <value>Bilet tarihi</value>
  </data>
  <data name="Called" xml:space="preserve">
    <value>Çağrıldı</value>
  </data>
  <data name="TicketCreated" xml:space="preserve">
    <value>Bileti basıldı</value>
  </data>
  <data name="TokenHistory" xml:space="preserve">
    <value>Token geçmişi</value>
  </data>
  <data name="FilterVip" xml:space="preserve">
    <value>Vip filtrele</value>
  </data>
  <data name="WaitingCount" xml:space="preserve">
    <value>Bekleyen sayısı</value>
  </data>
  <data name="ActionReport" xml:space="preserve">
    <value>Aksiyon Raporu</value>
  </data>
  <data name="DepartmentReport" xml:space="preserve">
    <value>Departman Raporu</value>
  </data>
  <data name="PersonalReport" xml:space="preserve">
    <value>Personel Raporu</value>
  </data>
  <data name="QmsReport" xml:space="preserve">
    <value>Qms Raporu</value>
  </data>
  <data name="Version" xml:space="preserve">
    <value>Version</value>
  </data>
  <data name="WhitelistProcessAuthorization" xml:space="preserve">
    <value>Whitelist işlem yetkisi</value>
  </data>
  <data name="ApplicationCreatedBy" xml:space="preserve">
    <value>Başvuru yaratan kişi</value>
  </data>
  <data name="IsWhiteListApplicant" xml:space="preserve">
    <value>whitelist başvurusu mu ?</value>
  </data>
  <data name="UnscannedPassport" xml:space="preserve">
    <value>Taranmamış pasaport mevcut</value>
  </data>
  <data name="RepetitiveRecording" xml:space="preserve">
    <value>Tekrar eden kayıt</value>
  </data>
  <data name="OtherReason" xml:space="preserve">
    <value>Diğer Neden</value>
  </data>
  <data name="ErrorOpenedBeforeClaimLoss" xml:space="preserve">
    <value>Bu başvurunun hasar dosyası daha önce işleme alınmıştır</value>
  </data>
  <data name="AreYouSureToApproveThis" xml:space="preserve">
    <value>başvuru güncellenecektir. Onaylıyor musunuz?</value>
  </data>
  <data name="StatusCorrectedByIncorrectApplication" xml:space="preserve">
    <value>Yanlış Başvuru ile Düzeltilen Durum</value>
  </data>
  <data name="AdditionalServiceType" xml:space="preserve">
    <value>Ek Hizmet Türü</value>
  </data>
  <data name="Exception_RefunedApplicationMust" xml:space="preserve">
    <value>Tahsilatı yapılmış başvuru için bu işlemi kısmı iadeden yapmalısınız</value>
  </data>
  <data name="YSS_Check" xml:space="preserve">
    <value>YSS Kontrol</value>
  </data>
  <data name="TypeOfInsurancePolicy" xml:space="preserve">
    <value>Sigorta Poliçesi Türü</value>
  </data>
  <data name="Insured" xml:space="preserve">
    <value>Sigortalı</value>
  </data>
  <data name="OnHold" xml:space="preserve">
    <value>Beklemede</value>
  </data>
  <data name="EnryExitDateWarning" xml:space="preserve">
    <value>Yanlış tarih Formatı. Lütfen Tarihleri tekrar kontrol ediniz.</value>
  </data>
  <data name="ExitDateWarning" xml:space="preserve">
    <value>Çıkış tarihi giriş tarihinizden çok uzaktadır. Lütfen Tarihleri tekrar kontrol ediniz.</value>
  </data>
  <data name="TotalApplicantsTaken" xml:space="preserve">
    <value>Toplam alınan başvuru</value>
  </data>
  <data name="ActionsApplied" xml:space="preserve">
    <value>Uygulanan Aksiyonlar</value>
  </data>
  <data name="TotalHourlyServiceTime" xml:space="preserve">
    <value>Toplam saatlik hizmet süresi</value>
  </data>
  <data name="AtLeastOneExtraFeeIsSelected" xml:space="preserve">
    <value>En az 1 ücret seçilmelidir</value>
  </data>
  <data name="ApplicationTakenFromPortal" xml:space="preserve">
    <value>Portal'dan alınan başvurular</value>
  </data>
  <data name="ApplicationTakenFromQms" xml:space="preserve">
    <value>QMS' den alınmış başvurular</value>
  </data>
  <data name="ServiceTimeMin" xml:space="preserve">
    <value>Hizmet süresi (dk)</value>
  </data>
  <data name="PleaseCheckTheExtraFees" xml:space="preserve">
    <value>Lütfen ekstra ücretleri kontrol edin</value>
  </data>
  <data name="PolicyDate" xml:space="preserve">
    <value>Poliçe Tarihi</value>
  </data>
  <data name="AreYouSureYouWantToUpdateThisInsurance" xml:space="preserve">
    <value>Bu sigorta kaydını güncellemek istediğinizden emin misiniz?</value>
  </data>
  <data name="ApproveSms" xml:space="preserve">
    <value>Onay Sms'i</value>
  </data>
  <data name="RejectionSms" xml:space="preserve">
    <value>Ret Sms'i</value>
  </data>
  <data name="AvailableForResend" xml:space="preserve">
    <value>Tekrar gönderim</value>
  </data>
  <data name="NotSent" xml:space="preserve">
    <value>Gönderilemedi</value>
  </data>
  <data name="Resend" xml:space="preserve">
    <value>Tekrar Gönder</value>
  </data>
  <data name="SendDate" xml:space="preserve">
    <value>Gönderim tarihi</value>
  </data>
  <data name="Sent" xml:space="preserve">
    <value>Gönderildi</value>
  </data>
  <data name="SendStatus" xml:space="preserve">
    <value>Gönderim statüsü</value>
  </data>
  <data name="SMS" xml:space="preserve">
    <value>SMS</value>
  </data>
  <data name="SmsHistory" xml:space="preserve">
    <value>Sms geçmişi</value>
  </data>
  <data name="SmsType" xml:space="preserve">
    <value>Sms türü</value>
  </data>
  <data name="NotYetDelivered" xml:space="preserve">
    <value>Henüz iletilmedi</value>
  </data>
  <data name="Tokens" xml:space="preserve">
    <value>Tokens</value>
  </data>
  <data name="CreateInsuranceFirstWarning" xml:space="preserve">
    <value>Lütfen Önce Sigorta Oluşturun</value>
  </data>
  <data name="IsResended" xml:space="preserve">
    <value>Tekrar gönderim mi?</value>
  </data>
  <data name="CCError" xml:space="preserve">
    <value>ÇM Hatası</value>
  </data>
  <data name="SaveAndSendAsMail" xml:space="preserve">
    <value>Kaydet ve mail olarak gönder</value>
  </data>
  <data name="SaveAndSendAsSMS" xml:space="preserve">
    <value>Kaydet ve SMS olarak gönder</value>
  </data>
  <data name="PreApplicationUpdatedBy" xml:space="preserve">
    <value>Randevuyu Güncelleyen Kişi</value>
  </data>
  <data name="MyCreations" xml:space="preserve">
    <value>Benim Oluşturduklarım</value>
  </data>
  <data name="PreApplicationCreatedBy" xml:space="preserve">
    <value>Randevuyu Oluşturan Kişi</value>
  </data>
  <data name="WorkPermitSms" xml:space="preserve">
    <value>Çalışma İzni Sms'i</value>
  </data>
  <data name="IsAuthorizedForPostponeButton" xml:space="preserve">
    <value>Postpone Butonunu Görmeye Yetkili Mi ?</value>
  </data>
  <data name="IsAuthorizedForQmsButton" xml:space="preserve">
    <value>Qms Butonunu Görmeye Yetkili Mi ?</value>
  </data>
  <data name="NotCompleted" xml:space="preserve">
    <value>Tamamlanmadı</value>
  </data>
  <data name="InterviewDone" xml:space="preserve">
    <value>Mülakat Tamamlandı</value>
  </data>
  <data name="InterviewRequired" xml:space="preserve">
    <value>Mülakat Gerekli</value>
  </data>
  <data name="Exeption_EmailFormat" xml:space="preserve">
    <value>Eposta formatı hatalı. Lütfen düzeltiniz!</value>
  </data>
  <data name="FamilyReunificationApplicant" xml:space="preserve">
    <value>Aile Birleşimine Başvuran</value>
  </data>
  <data name="NoApprovalRequired" xml:space="preserve">
    <value>Onay Gerekmiyor</value>
  </data>
  <data name="NonTurkmenistanCitizen" xml:space="preserve">
    <value>Türkmenistan Vatandaşı Olmayan</value>
  </data>
  <data name="NotRequiredForApproval" xml:space="preserve">
    <value>Onaya Tabii Olmayanlar</value>
  </data>
  <data name="WaitingApproval" xml:space="preserve">
    <value>Onay Bekleniyor</value>
  </data>
  <data name="PreApplicationExcelUpload" xml:space="preserve">
    <value>Randevu Excel Yükleme Alanı</value>
  </data>
  <data name="AddPreApplicationsToSystem" xml:space="preserve">
    <value>Randevuları sisteme ekle</value>
  </data>
  <data name="InvalidReason" xml:space="preserve">
    <value>Geçersiz sebebi</value>
  </data>
  <data name="RecordsInserted" xml:space="preserve">
    <value>Kayıt girildi</value>
  </data>
  <data name="Valid" xml:space="preserve">
    <value>Geçerli</value>
  </data>
  <data name="HasSamePassportNumberInExcel" xml:space="preserve">
    <value>Aynı pasaporta sahip kayıt var</value>
  </data>
  <data name="BiometricData" xml:space="preserve">
    <value>Biyometrik Veri</value>
  </data>
  <data name="DocumentExemption" xml:space="preserve">
    <value>Evrak Muafiyeti</value>
  </data>
  <data name="MissionNotes" xml:space="preserve">
    <value>Misyon Notları</value>
  </data>
  <data name="RelevantInstitutionPerson" xml:space="preserve">
    <value>İlgili Kurum veya Kişi</value>
  </data>
  <data name="ConfirmationCode" xml:space="preserve">
    <value>Onay Kodu</value>
  </data>
  <data name="ApprovalStatus" xml:space="preserve">
    <value>Onay durumu</value>
  </data>
  <data name="SendConfirmationCode" xml:space="preserve">
    <value>Onay kodu gönder</value>
  </data>
  <data name="ApproveStatus" xml:space="preserve">
    <value>Onay durumu</value>
  </data>
  <data name="PleaseSelectConfirmationType" xml:space="preserve">
    <value>Önce onay türü seçiniz</value>
  </data>
  <data name="VoiceAnnouncement" xml:space="preserve">
    <value>Sesli Anons</value>
  </data>
  <data name="ViewWhiteListNotes" xml:space="preserve">
    <value>White list notlarını görüntüle</value>
  </data>
  <data name="WhiteListNotes" xml:space="preserve">
    <value>White list notları</value>
  </data>
  <data name="ConfirmationCodeSms" xml:space="preserve">
    <value>Onay kodu SMS</value>
  </data>
  <data name="InsuranceType" xml:space="preserve">
    <value>Sigorta Türü</value>
  </data>
  <data name="UpdateInsurance" xml:space="preserve">
    <value>Sigorta Güncelle</value>
  </data>
  <data name="UpdateInsuranceCheck" xml:space="preserve">
    <value>Bu işleminizde kişinin ilk oluşturulan sigorta poliçesi iptal edilip yeni sigorta poliçesi oluşturulacaktır. Devam etmek istiyor musunuz?</value>
  </data>
  <data name="SelectedAppointmentInAnotherBranch" xml:space="preserve">
    <value>Seçili randevu başka bir şubeye ait</value>
  </data>
  <data name="FirstInsurancePolicyDuration" xml:space="preserve">
    <value>İlk Sigorta Poliçe Süresi</value>
  </data>
  <data name="FirstInsurancePolicyNumber" xml:space="preserve">
    <value>İlk Sigorta Poliçe Numarası</value>
  </data>
  <data name="FirstInsurancePolicyPrice" xml:space="preserve">
    <value>İlk Sigorta Poliçe Bedeli</value>
  </data>
  <data name="RefundAmount" xml:space="preserve">
    <value>İade Tutarı</value>
  </data>
  <data name="UpdateInsurancePolicyDuration" xml:space="preserve">
    <value>Güncellenen Sigorta Poliçe Süresi</value>
  </data>
  <data name="UpdateInsurancePolicyNumber" xml:space="preserve">
    <value>Güncellenen Sigorta Poliçe Numarası</value>
  </data>
  <data name="UpdateInsurancePolicyPrice" xml:space="preserve">
    <value>Güncellenen Sigorta Poliçe Bedeli</value>
  </data>
  <data name="WhitelistReport" xml:space="preserve">
    <value>Whitelist Raporu</value>
  </data>
  <data name="IndiaEarlyApplicationAuthorization" xml:space="preserve">
    <value>India 1 Year Filtration</value>
  </data>
  <data name="NotificationContent" xml:space="preserve">
    <value>Bildirim içeriği</value>
  </data>
  <data name="SmsConfirmationCode" xml:space="preserve">
    <value>SMS onay</value>
  </data>
  <data name="PleaseConsultYourManager" xml:space="preserve">
    <value>Lütfen token oluşturmadan önce lütfen yöneticinize danışın!
 Girilen pasaport numarası süresi dolmuş Whitelist kayıtlarındadır.</value>
  </data>
  <data name="FHI" xml:space="preserve">
    <value>YSS</value>
  </data>
  <data name="TI" xml:space="preserve">
    <value>TS</value>
  </data>
  <data name="MailConfirmationCode" xml:space="preserve">
    <value>Mail onay</value>
  </data>
  <data name="CreatedToday" xml:space="preserve">
    <value>Bugün Oluşturulanlar</value>
  </data>
  <data name="ApplicationReceiver" xml:space="preserve">
    <value>Başvuruyu Alan</value>
  </data>
  <data name="PolicyPeriodAccordingToPassportExpireDate" xml:space="preserve">
    <value>Pasaport bitiş tarihine göre 3 ya da 6 aylık sigorta seçilmelidir</value>
  </data>
  <data name="MultipleEntryVisaAccordingToPassportExpireDate" xml:space="preserve">
    <value>Pasaport Geçerlilik Tarihi 1 Yıldan Daha Az Olduğu İçin Çok Giriş Vize'ye Başvuru Yapılamaz</value>
  </data>
  <data name="CargoBarcode" xml:space="preserve">
    <value>Kargo Barkodu Yazdır</value>
  </data>
  <data name="ChassisNumber" xml:space="preserve">
    <value>Şaşi No</value>
  </data>
  <data name="ModelYear" xml:space="preserve">
    <value>Model Yılı</value>
  </data>
  <data name="PlateNo" xml:space="preserve">
    <value>Plaka No</value>
  </data>
  <data name="VehicleType" xml:space="preserve">
    <value>Araç Tipi</value>
  </data>
  <data name="UPS" xml:space="preserve">
    <value>UPS</value>
  </data>
  <data name="ActivateCargoIntegration" xml:space="preserve">
    <value>Kargo Entegrasyonunu Etkinleştirin</value>
  </data>
  <data name="IsCargoIntegrationActive" xml:space="preserve">
    <value>Kargo Entegrasyonu Aktif Mi</value>
  </data>
  <data name="IcrNoteFrench" xml:space="preserve">
    <value>ICR not (Fransızca)</value>
  </data>
  <data name="CargoStatus" xml:space="preserve">
    <value>Kargo Durumu</value>
  </data>
  <data name="BarcodeCreatedBy" xml:space="preserve">
    <value>Barkodu yaratan kişi</value>
  </data>
  <data name="BarcodeCreatedDate" xml:space="preserve">
    <value>Barkod yaratılma tarihi</value>
  </data>
  <data name="NotificationMail_NewPreApplication_Mail" xml:space="preserve">
    <value>[DATE] tarihi ve [TIME] saat icin [BRANCH] subesine randevunuz olusturulmustur. Randevu numaraniz [APPNUMBER]</value>
  </data>
  <data name="AddressAreaInformation" xml:space="preserve">
    <value>Adres alan bilgisi</value>
  </data>
  <data name="UseCargoIntegration" xml:space="preserve">
    <value>Kargo bilgisi girilecek mi ?</value>
  </data>
  <data name="IsApplicationCreated" xml:space="preserve">
    <value>Başvuru yaratıldı mı ?</value>
  </data>
  <data name="CameraId" xml:space="preserve">
    <value>Kamera Id</value>
  </data>
  <data name="InvalidGuid" xml:space="preserve">
    <value>Geçersiz guid</value>
  </data>
  <data name="IpCameras" xml:space="preserve">
    <value>Ip Kameralar</value>
  </data>
  <data name="AddInventoryIpCamera" xml:space="preserve">
    <value>Ip Kamera Ekle</value>
  </data>
  <data name="UpdateInventoryIpCamera" xml:space="preserve">
    <value>Ip Kamera Güncelle</value>
  </data>
  <data name="ThisCameraIdAllreadyExists" xml:space="preserve">
    <value>Bu kamera id zaten var</value>
  </data>
  <data name="TrackingNumber" xml:space="preserve">
    <value>Takip No</value>
  </data>
  <data name="AddDetails" xml:space="preserve">
    <value>Detay Ekle</value>
  </data>
  <data name="AddPrinter" xml:space="preserve">
    <value>Yazıcı Ekle</value>
  </data>
  <data name="AddPrinterAgent" xml:space="preserve">
    <value>Yazıcı Yöneticisi Ekle</value>
  </data>
  <data name="PrinterAgent" xml:space="preserve">
    <value>Yazıcı Yöneticisi</value>
  </data>
  <data name="PrinterAgentDetail" xml:space="preserve">
    <value>Yazıcı Yöneticisi Detayı</value>
  </data>
  <data name="PrinterType" xml:space="preserve">
    <value>Yazıcı Tipi</value>
  </data>
  <data name="RegistrationStatus" xml:space="preserve">
    <value>Kayıt Durumu</value>
  </data>
  <data name="Printer" xml:space="preserve">
    <value>Yazıcı</value>
  </data>
  <data name="CargoTrackingNumber" xml:space="preserve">
    <value>Kargo takip numarası</value>
  </data>
  <data name="CargoReprintNotification" xml:space="preserve">
    <value>Güncelleme mevcuttur, kargo adres barkodunu yeniden yazdırmayı unutmayınız</value>
  </data>
  <data name="PassportNo" xml:space="preserve">
    <value>Pasaport No</value>
  </data>
  <data name="PhoneNo" xml:space="preserve">
    <value>Telefon No</value>
  </data>
  <data name="TrackingNo" xml:space="preserve">
    <value>Takip No</value>
  </data>
  <data name="ActivatePrintAllIntegration" xml:space="preserve">
    <value>Print-All Entegrasyonunu Aktifleştirin</value>
  </data>
  <data name="IsPrintAllIntegrationActive" xml:space="preserve">
    <value>Print-All Entegrasyonu Aktif Mi</value>
  </data>
  <data name="NotCompletedReason" xml:space="preserve">
    <value>Tamamlanmadı nedeni</value>
  </data>
  <data name="MustBeSelectedForProcess" xml:space="preserve">
    <value>Güncelleme işlemi için seçeneklerden biri seçilmelidir</value>
  </data>
  <data name="IsPassportScanRequired" xml:space="preserve">
    <value>Pasaport okutmak zorunlu mu (QMS)</value>
  </data>
  <data name="QmsCompanyType" xml:space="preserve">
    <value>QMS firma türü</value>
  </data>
  <data name="SmsExtraFeeCheck" xml:space="preserve">
    <value>Sms eksta ücret kontrolü</value>
  </data>
  <data name="InventoryStatusPanel" xml:space="preserve">
    <value>Envanter Durum Paneli</value>
  </data>
  <data name="EVisa" xml:space="preserve">
    <value>E-Vize</value>
  </data>
  <data name="NumberOfDaysInStatus" xml:space="preserve">
    <value>Statüde Bulunma Gün Sayısı</value>
  </data>
  <data name="EmailProvider" xml:space="preserve">
    <value>Email sağlayıcı</value>
  </data>
  <data name="SmsProvider" xml:space="preserve">
    <value>Sms sağlayıcısı</value>
  </data>
  <data name="SavePrint" xml:space="preserve">
    <value>Kaydet ve Yazdır</value>
  </data>
  <data name="InventoryStatusLogs" xml:space="preserve">
    <value>Envanter durum logları</value>
  </data>
  <data name="NameOfSecondContactPerson" xml:space="preserve">
    <value>2. Kontak Kişi Adı</value>
  </data>
  <data name="CanNotReduceApplicantCount" xml:space="preserve">
    <value>Tüm başvurular girilmiştir, başvuran sayısını azaltma işlemi için iptal et aksiyonu alınmalıdır.</value>
  </data>
  <data name="GovernorateInformation" xml:space="preserve">
    <value>Valilik bilgisi</value>
  </data>
  <data name="VersionTime" xml:space="preserve">
    <value>Versiyon Zamanı</value>
  </data>
  <data name="IsSendByPrefix" xml:space="preserve">
    <value>Operatör bazlı otomatik gönderim</value>
  </data>
  <data name="SmsSender" xml:space="preserve">
    <value>Sms Göndericisi</value>
  </data>
  <data name="ResidenceNumber" xml:space="preserve">
    <value>İkamet Numarası</value>
  </data>
  <data name="AddLineDepartmentConnection" xml:space="preserve">
    <value>Hat departmanı bağlantısı ekle</value>
  </data>
  <data name="ButtonName" xml:space="preserve">
    <value>Buton adı</value>
  </data>
  <data name="ConnectionList" xml:space="preserve">
    <value>Bağlantı listesi</value>
  </data>
  <data name="LineDepartmentConnectionDetails" xml:space="preserve">
    <value>Hat departmanı bağlantısı detayı</value>
  </data>
  <data name="LineDepartmentConnectionList" xml:space="preserve">
    <value>Hat departmanı bağlantı listesi</value>
  </data>
  <data name="ShowWaitingButton" xml:space="preserve">
    <value>Bekleyen sayısını göster</value>
  </data>
  <data name="UpdateLineDepartmentConnection" xml:space="preserve">
    <value>Hat departmanı bağlantısı güncelle</value>
  </data>
  <data name="CopyCount" xml:space="preserve">
    <value> Kopya Sayısı</value>
  </data>
  <data name="ErrorOpenedBeforeInsurance" xml:space="preserve">
    <value>Poliçe numarası olmayan bir başvurunun vize reddi Sap'ye aktarılamaz</value>
  </data>
  <data name="ErrorOpenedBeforeSap" xml:space="preserve">
    <value>Sap kaydı olmayan başvurunun vize reddi Sap'ye aktarılmaz</value>
  </data>
  <data name="DigitalSignature" xml:space="preserve">
    <value>Digital İmza</value>
  </data>
  <data name="SendToTablet" xml:space="preserve">
    <value>Tablete Gönder</value>
  </data>
  <data name="Exception_ConvertingToHtml" xml:space="preserve">
    <value>Html'e dönüştürme hatası</value>
  </data>
  <data name="AppointmentDateChangeWarning" xml:space="preserve">
    <value>Tarih değişikliği yaptınız. “Slot Ara” butonuna tıklamanız gerekmektedir.</value>
  </data>
  <data name="SecondRefundIsDoneOneYearLaterCheck1" xml:space="preserve">
    <value>Vize Ret İadesi Hakkı</value>
  </data>
  <data name="SecondRefundIsDoneOneYearLaterCheck2" xml:space="preserve">
    <value>tarihinde</value>
  </data>
  <data name="SecondRefundIsDoneOneYearLaterCheck3" xml:space="preserve">
    <value>si'nde kullanılmıştır</value>
  </data>
  <data name="VisaRejectionRefundApplicationNumberWarning" xml:space="preserve">
    <value>Başvuru Numarası ile benzerdir ve Vize Ret İadesi yapılmıştır.</value>
  </data>
  <data name="RejectFilesNotUploadedWarning" xml:space="preserve">
    <value>Ret Dosyaları yüklenmemiştir, Dosyaları yükledikten sonra statüyü değiştirebilirsiniz.</value>
  </data>
  <data name="ActivatePrinterIntegration" xml:space="preserve">
    <value>Yazıcı Entegrasyonunu Etkinleştirin</value>
  </data>
  <data name="Tablet" xml:space="preserve">
    <value>Tablet</value>
  </data>
  <data name="PleaseSelectTablet" xml:space="preserve">
    <value>Lütfen bir tablet seçiniz</value>
  </data>
  <data name="RejectionRefundDoneCanNotBeSelectedForUD" xml:space="preserve">
    <value>NLL (Sahte Evrak) statüsünü seçtiniz. Vize Ret İadesi Yapıldı statüsünü seçemezsiniz!</value>
  </data>
  <data name="RejectionRefundDoneCanNotBeSelectedForYGYR" xml:space="preserve">
    <value>YGY/R statüsünü seçtiniz. Vize Ret İadesi Yapıldı statüsünü seçemezsiniz!</value>
  </data>
  <data name="PrintTsInsurancePolicy" xml:space="preserve">
    <value>Ts Poliçe Yazdır</value>
  </data>
  <data name="TsCreatePolicy" xml:space="preserve">
    <value>Ts Oluştur</value>
  </data>
  <data name="BrandModel" xml:space="preserve">
    <value>Marka Model</value>
  </data>
  <data name="WrongLanguageSelection" xml:space="preserve">
    <value>Yanlış dil seçimi</value>
  </data>
  <data name="CurrentPassword" xml:space="preserve">
    <value>Mevcut Şifre</value>
  </data>
  <data name="NewPassword" xml:space="preserve">
    <value>Yeni Şifre</value>
  </data>
  <data name="RetypeYourNewPassword" xml:space="preserve">
    <value>Yeni Şifreni Tekrar Yaz</value>
  </data>
  <data name="UpdatePasswordWarning" xml:space="preserve">
    <value>Şifrenizin 1 aylık süresi dolmuştur. Lütfen şifrenizi yeniden oluşturunuz.</value>
  </data>
  <data name="UserPasswordUpdate" xml:space="preserve">
    <value>Kullanıcı Şifre Güncelleme</value>
  </data>
  <data name="UserPasswordRequirements" xml:space="preserve">
    <value>Şifren en az 8 karakter olmalı, büyük ve küçük harf, rakam ve özel karakter içermelidir!</value>
  </data>
  <data name="NewPasswordNotMatch" xml:space="preserve">
    <value>Yeni şifre eşleşmiyor. Yeni şifreyi tekrar giriniz.</value>
  </data>
  <data name="PasswordCannotSame" xml:space="preserve">
    <value>Yeni şifreniz son üç şifreniz ile aynı olamaz.</value>
  </data>
  <data name="PasswordIncorrectWarning" xml:space="preserve">
    <value>Mevcut şifre hatalı. Mevcut şifrenizi doğru girdiğinizden emin olup bir daha deneyiniz.</value>
  </data>
  <data name="PleaseSelectAtLeastOne" xml:space="preserve">
    <value>Lütfen en az bir tane seçiniz</value>
  </data>
  <data name="AccountLockedWarning" xml:space="preserve">
    <value>Hesabınız 3 hatalı giriş denemesinden sonra 15 dakika süreyle kilitlenmiştir. Lütfen daha sonra tekrar deneyiniz.</value>
  </data>
  <data name="DeviceId" xml:space="preserve">
    <value>Device Id</value>
  </data>
  <data name="TabletList" xml:space="preserve">
    <value>Tablet Listesi</value>
  </data>
  <data name="UpdateTablet" xml:space="preserve">
    <value>Tableti Güncelle</value>
  </data>
  <data name="PROPERTY_REQUIRED" xml:space="preserve">
    <value>Property Required</value>
  </data>
  <data name="ThereIsNoAppropriateDocumentation" xml:space="preserve">
    <value>Uygun doküman bulunmamaktadır</value>
  </data>
  <data name="DeviceIsOffline" xml:space="preserve">
    <value>Cihaz çevrimdışı</value>
  </data>
  <data name="IsApplicationCreatedOnThisToken" xml:space="preserve">
    <value>Başvuru bu bilette mi yaratıldı</value>
  </data>
  <data name="Student" xml:space="preserve">
    <value>Öğrenci</value>
  </data>
  <data name="Tomer" xml:space="preserve">
    <value>Tömer</value>
  </data>
  <data name="SendToEmbassyStatusDate" xml:space="preserve">
    <value>Scan Cycle Elçilikte Teslim Alındı Durumu Tarihi</value>
  </data>
  <data name="CheckRejectedDocuments" xml:space="preserve">
    <value>Vize Ret Evraklarını Kontrol Et</value>
  </data>
  <data name="UpdateWorkPermitPolicy" xml:space="preserve">
    <value>Çalışma İzni Sigorta Güncelle</value>
  </data>
  <data name="AreYouSureUpdateWorkPermitPolicy" xml:space="preserve">
    <value>Bu işleminizde kişinin ilk oluşturulan sigorta poliçesi iptal edilip yeni sigorta poliçesi oluşturulacaktır. Devam etmek istiyor musunuz?</value>
  </data>
  <data name="RejectionDocumentNotUploaded" xml:space="preserve">
    <value>Ret Dokümanı Yüklenmemiş</value>
  </data>
  <data name="RejectionDocumentsIncompletelyUploaded" xml:space="preserve">
    <value>Ret Dokümanları Eksik Yüklenmiş</value>
  </data>
  <data name="LegalGuardian" xml:space="preserve">
    <value>Yasal Vasi</value>
  </data>
  <data name="PleaseEnterLegalGuardianName" xml:space="preserve">
    <value>Lütfen yasal ebeveyn adını giriniz</value>
  </data>
  <data name="PleaseSelectTheApplicantParentType" xml:space="preserve">
    <value>Lütfen başvuru sahibi veli türünü seçiniz</value>
  </data>
  <data name="LegalGuardianSignature" xml:space="preserve">
    <value>Yasal Vasi İmzası</value>
  </data>
  <data name="ApplicationIsUnder18" xml:space="preserve">
    <value>Başvuru 18 yaşın altında, imza yetkilisini seçin</value>
  </data>
  <data name="ProcessAt" xml:space="preserve">
    <value>İşlem tarihi</value>
  </data>
  <data name="ViewNotCompletedReasons" xml:space="preserve">
    <value>Tamamlanmadı nedeni görüntüle</value>
  </data>
  <data name="ApplicationVisaRejection" xml:space="preserve">
    <value>Vize Ret Listesi</value>
  </data>
  <data name="ExcelExport" xml:space="preserve">
    <value>EXCEL'E AKTAR</value>
  </data>
  <data name="CourierSalesStatus" xml:space="preserve">
    <value>Kurye Satış Durumu</value>
  </data>
  <data name="NumberOfApplicationsToCourierSales" xml:space="preserve">
    <value>Kurye satış durumuna göre başvuru sayısı</value>
  </data>
  <data name="TotalApplicationsForCourierSales" xml:space="preserve">
    <value>Kurye satışı yapılan toplam başvuru</value>
  </data>
  <data name="TotalApplicationsWithoutCourierSales" xml:space="preserve">
    <value>Kurye satışı yapılmayan toplam başvuru</value>
  </data>
  <data name="Approvement" xml:space="preserve">
    <value>Doğrulama</value>
  </data>
  <data name="ApprovementCodeManagement" xml:space="preserve">
    <value>Doğrulama kodu yönetimi</value>
  </data>
  <data name="ApprovementCodeMessage" xml:space="preserve">
    <value>İadenin yapılabilmesi için doğrulama kodu girmelisiniz</value>
  </data>
  <data name="EnterApprovementCode" xml:space="preserve">
    <value>Doğrulama kodu giriniz</value>
  </data>
  <data name="RefundStatus" xml:space="preserve">
    <value>İade statüsü</value>
  </data>
  <data name="Supervisor" xml:space="preserve">
    <value>Supervisor</value>
  </data>
  <data name="Send" xml:space="preserve">
    <value>Gönder</value>
  </data>
  <data name="Verify" xml:space="preserve">
    <value>Doğrula</value>
  </data>
  <data name="Exception_AlreadyVerified" xml:space="preserve">
    <value>Bu veri daha önceden doğrulanmış</value>
  </data>
  <data name="RejectionApprovalHistory" xml:space="preserve">
    <value>Ret doğrulama geçmişi</value>
  </data>
  <data name="CodeNotVerified" xml:space="preserve">
    <value>Kod doğrulanamadı</value>
  </data>
  <data name="CodeVerified" xml:space="preserve">
    <value>Kod doğrulandı</value>
  </data>
  <data name="Exception_UserNotDefinedToThisBranchAsSupervisor" xml:space="preserve">
    <value>Kullanıcı bu şubeye supervisor olarak atanmamış</value>
  </data>
  <data name="SupervisorVerification" xml:space="preserve">
    <value>Supervisor kontrolü</value>
  </data>
  <data name="SupervisorVerificationMessage" xml:space="preserve">
    <value>Doğrulama yapılabilmesi için supervisor bilgilerini giriniz</value>
  </data>
  <data name="CantSendNotificationMoreThanTwoTimes" xml:space="preserve">
    <value>2 kereden fazla ret doğrulama bildirimi gönderilemez. Lütfen supervisor seçeneğini seçiniz</value>
  </data>
  <data name="BranchAuthorities" xml:space="preserve">
    <value>Şube yetkileri</value>
  </data>
  <data name="BranchAuthoritiesList" xml:space="preserve">
    <value>Şube yetkileri listesi</value>
  </data>
  <data name="BranchManager" xml:space="preserve">
    <value>Şube müdürü</value>
  </data>
  <data name="CountryManager" xml:space="preserve">
    <value>Ülke müdürü</value>
  </data>
  <data name="OperationManager" xml:space="preserve">
    <value>Operasyon müdürü</value>
  </data>
  <data name="ApprovementCompleteDate" xml:space="preserve">
    <value>Onay alım zamanı</value>
  </data>
  <data name="CodeResendCount" xml:space="preserve">
    <value>Kod tekrar gönderim sayısı</value>
  </data>
  <data name="CodeSendDate" xml:space="preserve">
    <value>Kod gönderim zamanı</value>
  </data>
  <data name="RejectionApprovalSms" xml:space="preserve">
    <value>Ret doğrulama sms</value>
  </data>
  <data name="VerificationCompleted" xml:space="preserve">
    <value>Doğrulama yapıldı</value>
  </data>
  <data name="VerificationFailed" xml:space="preserve">
    <value>Doğrulama başarısız</value>
  </data>
  <data name="ApproveEmail" xml:space="preserve">
    <value>Onay maili</value>
  </data>
  <data name="EmailHistory" xml:space="preserve">
    <value>Mail geçmişi</value>
  </data>
  <data name="EmailType" xml:space="preserve">
    <value>Mail türü</value>
  </data>
  <data name="RejectionApprovalEmail" xml:space="preserve">
    <value>Ret doğrulama maili</value>
  </data>
  <data name="RejectionEmail" xml:space="preserve">
    <value>Ret maili</value>
  </data>
  <data name="ConfirmationCodeEmail" xml:space="preserve">
    <value>Onay kodu maili</value>
  </data>
  <data name="WorkPermitEmail" xml:space="preserve">
    <value>Çalışma izni maili</value>
  </data>
  <data name="PhotoBoothList" xml:space="preserve">
    <value>Fotokabin Listesi</value>
  </data>
  <data name="BadShotPhotoBooth" xml:space="preserve">
    <value>Hatalı Çekim</value>
  </data>
  <data name="DeletedPhotoBooth" xml:space="preserve">
    <value>Silinmiş Fotokabinler</value>
  </data>
  <data name="ExpiredPhotoBooth" xml:space="preserve">
    <value>Süresi Geçen Fotokabinler</value>
  </data>
  <data name="ThreeUpdatedBarcodeNumber" xml:space="preserve">
    <value>3. Güncellenen Barkod Numarası</value>
  </data>
  <data name="TwoUpdatedBarcodeNumber" xml:space="preserve">
    <value>2. Güncellenen Barkod Numarası</value>
  </data>
  <data name="CheckUsedPhotoBoothDeletedApplicationSure" xml:space="preserve">
    <value>Kullanılmış fotokabin barkodu var silmek istediğinize emin misiniz?</value>
  </data>
  <data name="SmsCargoFeeCheck" xml:space="preserve">
    <value>Sms kargo ücreti kontrolü</value>
  </data>
  <data name="BranchNames" xml:space="preserve">
    <value>Şube Adları</value>
  </data>
  <data name="UsedPhotoBooth" xml:space="preserve">
    <value>Kullanılan Fotokabinler</value>
  </data>
  <data name="OnlyOneExtraFeePhotoCanBeSelected" xml:space="preserve">
    <value>Sadece 1 tane fotoğraf ücreti seçebilirsiniz!</value>
  </data>
  <data name="ServiceError" xml:space="preserve">
    <value>Servis Hatası</value>
  </data>
  <data name="EnterNotCompletedReason" xml:space="preserve">
    <value>Tamamlanamadı nedenini giriniz</value>
  </data>
  <data name="QmsAllBranchesWhiteListReport" xml:space="preserve">
    <value>Tüm Şubeler Whitelist Raporu</value>
  </data>
  <data name="QmsNoShowReport" xml:space="preserve">
    <value>Randevuya Gelmeyenler Raporu</value>
  </data>
  <data name="QmsVIPReport" xml:space="preserve">
    <value>VIP Raporu</value>
  </data>
  <data name="SearchAppointment" xml:space="preserve">
    <value>Randevu ara</value>
  </data>
  <data name="PreApplicationHistory" xml:space="preserve">
    <value>Randevu geçmişi</value>
  </data>
  <data name="EnterNationalityPlease" xml:space="preserve">
    <value>Lütfen uyruk bilgisi giriniz.</value>
  </data>
  <data name="EnterPassportNumberPlease" xml:space="preserve">
    <value>Lütfen pasaport numarası giriniz</value>
  </data>
  <data name="ApplicationPreApplicationConnection" xml:space="preserve">
    <value>Randevu-başvuru bağlantısı</value>
  </data>
  <data name="TokenNumberProcess" xml:space="preserve">
    <value>Token Numarası İşlemi</value>
  </data>
  <data name="EndwithCannotBeSmallerThanStartwith" xml:space="preserve">
    <value>Endwith startwith'den daha küçük olamaz</value>
  </data>
  <data name="NoShow" xml:space="preserve">
    <value>Randevuya Gelmeyen</value>
  </data>
  <data name="ThereIsAnAppointmentForThisPersonAtLaterDate" xml:space="preserve">
    <value>Bu kişi için ileri bir tarihte randevu bulunmaktadır</value>
  </data>
  <data name="Code" xml:space="preserve">
    <value>Kod</value>
  </data>
  <data name="IsVerified" xml:space="preserve">
    <value>Doğrulandı</value>
  </data>
  <data name="VerfyMessageTemplate" xml:space="preserve">
    <value>İşleme devam edebilmeniz için gereken doğrulama kodu:</value>
  </data>
  <data name="VerificationHistoryPageLabel" xml:space="preserve">
    <value>SMS E-Mail veya Supervisor ile iletişim bilgisi doğrulama geçmişi</value>
  </data>
  <data name="VerificationTime" xml:space="preserve">
    <value>Doğrulama Zamanı</value>
  </data>
  <data name="VerificationType" xml:space="preserve">
    <value>Doğrulama Türü</value>
  </data>
  <data name="VerifiedContactInformationTabLabel" xml:space="preserve">
    <value>Doğrulanmış iletşim bilgileri</value>
  </data>
  <data name="VeriyfModalTitle" xml:space="preserve">
    <value>Doğrulama Kodu Yönetimi</value>
  </data>
  <data name="ContactInformationVerificationMailSubject" xml:space="preserve">
    <value>İletişim Bilgisi Doğrulaması</value>
  </data>
  <data name="QmsNotCompletedReport" xml:space="preserve">
    <value>Tamamlanmadı raporu</value>
  </data>
  <data name="DisableContactInformationVerification" xml:space="preserve">
    <value>Başvuru Doğrulamasını Pasifleştir</value>
  </data>
  <data name="BiometricsProcessingTime" xml:space="preserve">
    <value>Biyometri İşlem Süresi</value>
  </data>
  <data name="BiometricsStaff" xml:space="preserve">
    <value>Biyometri Personeli</value>
  </data>
  <data name="BiometricsWaitingTime" xml:space="preserve">
    <value>Biyometri Bekleme Süresi</value>
  </data>
  <data name="QmsProcessTimeReport" xml:space="preserve">
    <value>İşlem Süresi Raporu</value>
  </data>
  <data name="SubmissionProcessingTime" xml:space="preserve">
    <value>Submission İşlem Süresi</value>
  </data>
  <data name="SubmissionStaff" xml:space="preserve">
    <value>Submission Personeli</value>
  </data>
  <data name="SubmissionWaitingTime" xml:space="preserve">
    <value>Submission Bekleme Süresi</value>
  </data>
  <data name="TotalTime" xml:space="preserve">
    <value>Toplam Süre</value>
  </data>
  <data name="LineChanging" xml:space="preserve">
    <value>Line değiştirme</value>
  </data>
  <data name="TheLineTypeOfTheCurrentAppointmentWillBeChangedToVip" xml:space="preserve">
    <value>Mevcut randevunun line tipi VIP olarak değiştirilecektir</value>
  </data>
  <data name="VasTypeMustBeVip" xml:space="preserve">
    <value>Vas tipi Vip olmalıdır</value>
  </data>
  <data name="FutureAppointment" xml:space="preserve">
    <value>Gelecek tarihteki randevu </value>
  </data>
  <data name="ThereIsFutureAppointmentForThisPassportNumber" xml:space="preserve">
    <value>Bu pasaport numarası için ileri tarihte bir randevu bulunmaktadır. VIP line üzerinden kayıt oluşturuluyor</value>
  </data>
  <data name="EmailOrPhoneNumberMissing" xml:space="preserve">
    <value>Mail veya telefon numarası eksik</value>
  </data>
  <data name="ScanCycleRejectionApprovalControl" xml:space="preserve">
    <value>Ret doğrulama vize ret kontrolü</value>
  </data>
  <data name="RejectionApprovalWarning" xml:space="preserve">
    <value>Ret doğrulama işlemi tamamlanmadı. İade öncesi lütfen ret işlemini tamamlayın</value>
  </data>
  <data name="PhotoBoothIntegrationActive" xml:space="preserve">
    <value>Fotoğraf ICR Entegrasyonunu Aktifleştirin</value>
  </data>
  <data name="ContactInformationValidationFailed" xml:space="preserve">
    <value>Doğrulama kodu hatalı yada süresi geçmiş</value>
  </data>
  <data name="SendNotificationCountMessage" xml:space="preserve">
    <value>Doğrulama bildirimi en fazla 2 kez gönderilebilir. Lütfen Supervisor yetkisi ile devam ediniz.</value>
  </data>
  <data name="NotAuthorizedBranchManager" xml:space="preserve">
    <value>Şube yöneticisi bu şubede yetkili değildir.</value>
  </data>
  <data name="AppointmentBranch" xml:space="preserve">
    <value>Randevu Şubesi</value>
  </data>
  <data name="ApplicationUpdateStatusCheckActive" xml:space="preserve">
    <value>Başvuru güncelleme statü kontrolü (ret onay)</value>
  </data>
  <data name="IsRestrictChangeContactInformation" xml:space="preserve">
    <value>Başvuru iletişim bilgileri değişimi kısıtla</value>
  </data>
  <data name="MissionRejectionWarning" xml:space="preserve">
    <value>Misyon kararı gereği, bu başvuru sahibi başvuru yapamaz!</value>
  </data>
  <data name="DatePickerFirstMonthFormatView" xml:space="preserve">
    <value>MM/dd/yyyy</value>
  </data>
  <data name="IsEsimBeSent" xml:space="preserve">
    <value>E-Sim Gönderilsin</value>
  </data>
  <data name="QmsTatReport" xml:space="preserve">
    <value>TAT Report</value>
  </data>
  <data name="TatService" xml:space="preserve">
    <value>Tat Service</value>
  </data>
  <data name="TokenProcessedBy" xml:space="preserve">
    <value>Token Processed By</value>
  </data>
  <data name="ShowInAllApplicationsReport" xml:space="preserve">
    <value>Başvuru Rapor Gösterimi</value>
  </data>
  <data name="ResidingCountry" xml:space="preserve">
    <value>Başvuru Yapılan Ülke Adı</value>
  </data>
  <data name="ExtraFeeSalesCannotBeMade" xml:space="preserve">
    <value>extra fee satışı şu anda yapılamamaktadır!</value>
  </data>
  <data name="ExtraFeeCannotBeCancelled" xml:space="preserve">
    <value>Ekstra ücretİ iptal edilemez</value>
  </data>
  <data name="PrintEsimQr" xml:space="preserve">
    <value>E-Sim Qr Kodu Yazdır</value>
  </data>
  <data name="DoubleTransitForForeignersDZD" xml:space="preserve">
    <value>Yabancı Uyruklu Transit (Çift Giriş) DZD</value>
  </data>
  <data name="MultipleVisaFeeForForeignersDZD" xml:space="preserve">
    <value>Yabancı Uyruklu Çok Giriş Vize Ücreti DZD</value>
  </data>
  <data name="PhotoDZD" xml:space="preserve">
    <value>Fotoğraf (DZD)</value>
  </data>
  <data name="SingleEntryVisaFeeForForeignersDZD" xml:space="preserve">
    <value>Yabancı Uyruklu Tek Giriş Vize DZD</value>
  </data>
  <data name="SingleTransitForForeignersDZD" xml:space="preserve">
    <value>Yabancı Uyruklu Transit (Tek Giriş) DZD</value>
  </data>
  <data name="ScanQrCodeWithinTurkiye" xml:space="preserve">
    <value>Lütfen Türkiye sınırları içindeyken QR Code'u okutunuz.</value>
  </data>
  <data name="ApplicationSale" xml:space="preserve">
    <value>Başvuru Satışı</value>
  </data>
  <data name="FiveYearsExtraFee" xml:space="preserve">
    <value>5 Yıllık Ek Ücret</value>
  </data>
  <data name="FourYearsExtraFee" xml:space="preserve">
    <value>4 Yıllık Ek Ücret</value>
  </data>
  <data name="ThreeYearsExtraFee" xml:space="preserve">
    <value>3 Yıllık Ek Ücret</value>
  </data>
  <data name="TwoYearsExtraFee" xml:space="preserve">
    <value>2 Yıllık Ek Ücret</value>
  </data>
  <data name="Available" xml:space="preserve">
    <value>Hazır</value>
  </data>
  <data name="Reserved" xml:space="preserve">
    <value>Rezerve</value>
  </data>
  <data name="Used" xml:space="preserve">
    <value>Kullanılmış</value>
  </data>
  <data name="QmsDashboardFilterAccessedBranchOnly" xml:space="preserve">
    <value>Qms dashboard sadece yetkili olduğu şubeyi mi görsün</value>
  </data>
  <data name="QmsCompletedReport" xml:space="preserve">
    <value>Tamamlandı Raporu</value>
  </data>
  <data name="SendKsaIcrGenereteEInvoice" xml:space="preserve">
    <value>Vergi E-Fatura Oluştur</value>
  </data>
  <data name="PrintComplementaryServices" xml:space="preserve">
    <value>Tamamlayıcı Hizmetler Yazdır</value>
  </data>
  <data name="isAuthorizedForDeletePhotoBooth" xml:space="preserve">
    <value>Photobooth Silmeye Yetkili Mi?</value>
  </data>
  <data name="OnlyOneExtraFeeESIMCanBeSelected" xml:space="preserve">
    <value>Sadece 1 tane eSIM ücreti seçebilirsiniz!</value>
  </data>
  <data name="Photo" xml:space="preserve">
    <value>Fotoğraf</value>
  </data>
  <data name="ShowCityDropdown" xml:space="preserve">
    <value>Şehir bilgisini göster</value>
  </data>
  <data name="CostMode" xml:space="preserve">
    <value>Mod</value>
  </data>
  <data name="InCity" xml:space="preserve">
    <value>Şehir içi</value>
  </data>
  <data name="OutOfCity" xml:space="preserve">
    <value>Şehir dışı</value>
  </data>
  <data name="QmsAppointmentPassTime" xml:space="preserve">
    <value>Numaralı randevunun randevu saati geçmiştir</value>
  </data>
  <data name="RejectionRefundDoneCanNotBeSelected" xml:space="preserve">
    <value>Vize Ret İadesi Yapıldı statüsünü seçemezsiniz!</value>
  </data>
  <data name="VisaFeeAmount" xml:space="preserve">
    <value>Vize Ücreti</value>
  </data>
  <data name="IsSlotTypesConnected" xml:space="preserve">
    <value>Slotlar bağlantılı mı ? </value>
  </data>
  <data name="DeleteReason" xml:space="preserve">
    <value>Silinme Nedeni</value>
  </data>
  <data name="B2CAppointment" xml:space="preserve">
    <value>B2C Randevusu</value>
  </data>
  <data name="ReceivedAtEmbassy" xml:space="preserve">
    <value>Elçilikte teslim alındı</value>
  </data>
  <data name="SentToVACFromEmbassy" xml:space="preserve">
    <value>Elçilikten VAC’a gönderildi</value>
  </data>
  <data name="ConsularReporting" xml:space="preserve">
    <value>Konsolosluk Raporları</value>
  </data>
  <data name="ApplicationListUpdatedAt" xml:space="preserve">
    <value>Başvuru listesi güncelleme zamanı</value>
  </data>
  <data name="ApplicationListUpdatedBy" xml:space="preserve">
    <value>Başvuru listesi güncelleyen kişi</value>
  </data>
  <data name="ApplicationListUpdatedInformation" xml:space="preserve">
    <value>Başvuru listesi güncellenen bilgiler</value>
  </data>
  <data name="ApplicationNotReceived" xml:space="preserve">
    <value>Başvurusu oluşturulmadı</value>
  </data>
  <data name="ApplicationReceived" xml:space="preserve">
    <value>Başvurusu oluşturuldu</value>
  </data>
  <data name="TypeOfVisa" xml:space="preserve">
    <value>Vize türü</value>
  </data>
  <data name="LdapAccountLockedWarning" xml:space="preserve">
    <value>Hesabnınız kilitlenmiştir. Lütfen sistem yöneticiniz ile iletişime geçiniz.</value>
  </data>
  <data name="Answer" xml:space="preserve">
    <value>Cevap</value>
  </data>
  <data name="Question" xml:space="preserve">
    <value>Soru</value>
  </data>
  <data name="PleaseAnswerRequiredQuestions" xml:space="preserve">
    <value>Lütfen zorunlu olan soruları cevaplayınız</value>
  </data>
  <data name="UpdateSurvey" xml:space="preserve">
    <value>Anket güncelle</value>
  </data>
  <data name="AgentObservation" xml:space="preserve">
    <value>Başvuru Personeli Gözlemleri</value>
  </data>
  <data name="SurveySummary" xml:space="preserve">
    <value>Anket özeti</value>
  </data>
  <data name="ResidenceApplication" xml:space="preserve">
    <value>İkamet Başvuru</value>
  </data>
  <data name="ResidenceApplicationToBeMade" xml:space="preserve">
    <value>İkamet Başvurusu Yapılacak</value>
  </data>
  <data name="Number" xml:space="preserve">
    <value>Sayısı</value>
  </data>
  <data name="ExtraPackagesApplication" xml:space="preserve">
    <value>Ekstra Paketler Başvuru</value>
  </data>
  <data name="ExtraPackagesApplicationFee" xml:space="preserve">
    <value>Ekstra Paketler Başvuru Ücreti</value>
  </data>
  <data name="Normal" xml:space="preserve">
    <value>Normal</value>
  </data>
  <data name="TurquoiseGratis" xml:space="preserve">
    <value>Turkuaz Gratis</value>
  </data>
  <data name="Unspecified" xml:space="preserve">
    <value>Belirtilmemiş</value>
  </data>
  <data name="ApplicationExcelUpload" xml:space="preserve">
    <value>Başvuru excel yükleme alanı</value>
  </data>
  <data name="ShowInB2c" xml:space="preserve">
    <value>B2C'de göster</value>
  </data>
  <data name="VisaExemptionIraqUnder15" xml:space="preserve">
    <value>15 yaş altındaki Irak vatandaşları vizeden muaftır!</value>
  </data>
  <data name="VisaExemptionIraqOver50" xml:space="preserve">
    <value>50 yaş üstündeki Irak vatandaşları vizeden muaftır!</value>
  </data>
  <data name="VisaExemptionAlgerianUnder15" xml:space="preserve">
    <value>15 yaş altındaki Cezayir vatandaşları vizeden muaftır!</value>
  </data>
  <data name="VisaExemptionAlgerianOver65" xml:space="preserve">
    <value>65 yaş üstündeki Cezayir vatandaşları vizeden muaftır!</value>
  </data>
  <data name="VisaExemptionLibyanUnder16" xml:space="preserve">
    <value>16 yaş altındaki Libya vatandaşları vizeden muaftır!
</value>
  </data>
  <data name="VisaExemptionLibyanOver45" xml:space="preserve">
    <value>45 yaş üstündeki Libya vatandaşları vizeden muaftır!</value>
  </data>
  <data name="VisaExemptionEgyptianUnder20" xml:space="preserve">
    <value>20 yaş altındaki Mısır vatandaşları vizeden muaftır!</value>
  </data>
  <data name="VisaExemptionEgyptianOver45" xml:space="preserve">
    <value>45 yaş üstündeki Mısır vatandaşları vizeden muaftır!</value>
  </data>
  <data name="VisaExemption" xml:space="preserve">
    <value>Vize Muafiyet</value>
  </data>
  <data name="VisaExemptReportTitle" xml:space="preserve">
    <value>Yaş Aralığına Göre Vize Muafiyet Başvurusu</value>
  </data>
  <data name="Approval" xml:space="preserve">
    <value>Onay</value>
  </data>
  <data name="Istizan" xml:space="preserve">
    <value>İstizan</value>
  </data>
  <data name="IstizanApproval" xml:space="preserve">
    <value>İstizan Onay</value>
  </data>
  <data name="IstizanRejection" xml:space="preserve">
    <value>İstizan Ret</value>
  </data>
  <data name="NumberOfPassportsReceivedAtEmbassy" xml:space="preserve">
    <value>Elçilikte Teslim Alınan Pasaport Sayısı</value>
  </data>
  <data name="RejectedNotIncludingIR" xml:space="preserve">
    <value>Ret Edilen (IR dahil değil)</value>
  </data>
  <data name="ApplicationTogetherRequirementWarning" xml:space="preserve">
    <value>Birlikte başvuru sayısını giriniz</value>
  </data>
  <data name="Zero_Fifteen" xml:space="preserve">
    <value>0-15 yaş arası</value>
  </data>
  <data name="MoreThanFifty" xml:space="preserve">
    <value>50 ve üstü</value>
  </data>
  <data name="ByAgeRangeWithApplicationTogether" xml:space="preserve">
    <value>Birlikte Seyahat Yaş Aralığına Göre</value>
  </data>
  <data name="IsAuthorizedForTurkmenistanStatisticReport" xml:space="preserve">
    <value>Türkmenistan Raporunu Görmeye Yetkili Mi ?</value>
  </data>
  <data name="QmsVfsAppointmentsEntrySelectFilesButtonLabelName" xml:space="preserve">
    <value>Dosyaları Seçiniz</value>
  </data>
  <data name="QmsVfsAppointmentsEntryClearButtonLabelName" xml:space="preserve">
    <value>Temizle</value>
  </data>
  <data name="QmsVfsAppointmentsEntryUploadButtonLabelName" xml:space="preserve">
    <value>Yükle</value>
  </data>
  <data name="QmsVfsAppointmentsUploadSuccessMessage" xml:space="preserve">
    <value>Dosya(lar) başarıyla yüklendi.</value>
  </data>
  <data name="QmsVfsAppointmentsEntryDropFilesHereInfo" xml:space="preserve">
    <value>YÜklemek için dosyaları buraya bırakınız</value>
  </data>
  <data name="QmsVfsAppointmentsEntryStatusUploaded" xml:space="preserve">
    <value>Tamamlandı</value>
  </data>
  <data name="QmsDashboardResetButtonLabelName" xml:space="preserve">
    <value>Sıfırla</value>
  </data>
  <data name="QmsDashboardApplyFiltersButtonLabelName" xml:space="preserve">
    <value>Filtreleri Uygula</value>
  </data>
  <data name="WhitelistResenApplicationReport" xml:space="preserve">
    <value>Whitelist Resen Başvuru Raporu</value>
  </data>
  <data name="WhiteListResen" xml:space="preserve">
    <value>White list resen</value>
  </data>
  <data name="AddWhiteListResen" xml:space="preserve">
    <value>Whitelist Resen Başvurusu Ekle</value>
  </data>
  <data name="UpdateWhiteListResen" xml:space="preserve">
    <value>WhiteList Resen Başvurusu Güncelle</value>
  </data>
  <data name="PortalUserNotFound" xml:space="preserve">
    <value>Kullanıcı bulunamadı. Sistem yöneticisine başvurunuz</value>
  </data>
  <data name="PortalUserRoleNotFound" xml:space="preserve">
    <value>Kullanıcının yetkisi yoktur. Yazılım Destek Ekibine başvurunuz</value>
  </data>
  <data name="SendBasicGuidelineInAms" xml:space="preserve">
    <value>Temel kılavuzu gönder (AMS)</value>
  </data>
  <data name="BasicGuidelineType" xml:space="preserve">
    <value>Temel kılavuz türü</value>
  </data>
  <data name="QmsAllReport" xml:space="preserve">
    <value>Bütün raporlar (QMS)</value>
  </data>
  <data name="OneDay" xml:space="preserve">
    <value>1 gün</value>
  </data>
  <data name="OneWeek" xml:space="preserve">
    <value>1 hafta</value>
  </data>
  <data name="SelectFilterMessage" xml:space="preserve">
    <value>Tarih aralığı seçimi yapmalısınız</value>
  </data>
  <data name="ShowDigitalSignature" xml:space="preserve">
    <value>Dijital İmza Göster</value>
  </data>
  <data name="AreYouSureToAddSlotAgain" xml:space="preserve">
    <value>{Branch} {Date} tarihleri arası tekrar slot eklemek istediğinize emin misiniz?</value>
  </data>
  <data name="AreYouSureToDeleteSlot" xml:space="preserve">
    <value>{Branch} slotlarını silmek istediğinize emin misiniz</value>
  </data>
  <data name="Amount" xml:space="preserve">
    <value>Adet</value>
  </data>
  <data name="StatusAmount" xml:space="preserve">
    <value>Tutar</value>
  </data>
  <data name="ApplicationReportOfRejectedPassports" xml:space="preserve">
    <value>Reddedilen Pasaportların Başvuru Raporu</value>
  </data>
  <data name="Ikibinonalti" xml:space="preserve">
    <value>2016</value>
  </data>
  <data name="Ikibinondokuz" xml:space="preserve">
    <value>2019</value>
  </data>
  <data name="Ikibinonsekiz" xml:space="preserve">
    <value>2018</value>
  </data>
  <data name="Ikibinonyedi" xml:space="preserve">
    <value>2017</value>
  </data>
  <data name="Ikibinyirmi" xml:space="preserve">
    <value>2020</value>
  </data>
  <data name="Ikibinyirmibir" xml:space="preserve">
    <value>2021</value>
  </data>
  <data name="Ikibinyirmiiki" xml:space="preserve">
    <value>2022</value>
  </data>
  <data name="Ikibinyirmiuc" xml:space="preserve">
    <value>2023</value>
  </data>
  <data name="Mission" xml:space="preserve">
    <value>Görev</value>
  </data>
  <data name="DigitalSignatureDocuments" xml:space="preserve">
    <value>Dijital İmza Dosyaları</value>
  </data>
  <data name="FileTypeName" xml:space="preserve">
    <value>Dosya Türü Adı</value>
  </data>
  <data name="UsesDigitalSignature" xml:space="preserve">
    <value>Dijital İmza Kullanımı</value>
  </data>
  <data name="SelectedLanguages" xml:space="preserve">
    <value>Seçili Diller</value>
  </data>
  <data name="RelatedInsuranceForExempt" xml:space="preserve">
    <value>Muafiyetli Kişiler İçin İlişkili Sigorta</value>
  </data>
  <data name="AppointmentPeriod" xml:space="preserve">
    <value>Randevu Dönemi</value>
  </data>
  <data name="BranchShiftHoliday" xml:space="preserve">
    <value>Şube Vardiya/Tatil</value>
  </data>
  <data name="BranchShiftHolidayList" xml:space="preserve">
    <value>Şube Vardiya/Tatil Listesi</value>
  </data>
  <data name="EndOfLunch" xml:space="preserve">
    <value>Öğle Yemeği Sonu</value>
  </data>
  <data name="EndOfShift" xml:space="preserve">
    <value>Vardiya Sonu</value>
  </data>
  <data name="SelectBranchApplicationCountryDetail" xml:space="preserve">
    <value>Şube Başvuru Ülkesi Detayları</value>
  </data>
  <data name="SelectWeekWekeends" xml:space="preserve">
    <value>Şube Haftasonları</value>
  </data>
  <data name="StartOfLunch" xml:space="preserve">
    <value>Öğle Yemeği Başlangıcı</value>
  </data>
  <data name="StartOfShift" xml:space="preserve">
    <value>Vardiya Başlangıcı</value>
  </data>
  <data name="BranchShift" xml:space="preserve">
    <value>Şube Vardiyası</value>
  </data>
  <data name="BranchWeekends" xml:space="preserve">
    <value>Şube Haftasonları</value>
  </data>
  <data name="HolidayDay" xml:space="preserve">
    <value>Tatil Tarihi</value>
  </data>
  <data name="ShowPaymentMethods" xml:space="preserve">
    <value>Ödeme Seçeneklerini Göster</value>
  </data>
  <data name="Cash" xml:space="preserve">
    <value>Nakit</value>
  </data>
  <data name="PaymentMethodsWarning" xml:space="preserve">
    <value>Extra Paketlerde seçili olmayan Ödeme seçeneği bulunmaktadır.</value>
  </data>
  <data name="BranchHolidays" xml:space="preserve">
    <value>Şube Tatilleri</value>
  </data>
  <data name="HolidayDays" xml:space="preserve">
    <value>Tatil Tarihleri</value>
  </data>
  <data name="TotalCashCollections" xml:space="preserve">
    <value>Toplam Nakit Tahsilatlar</value>
  </data>
  <data name="TotalOnlineCollections" xml:space="preserve">
    <value>Toplam Online Tahsilatlar</value>
  </data>
  <data name="TotalPaymentOptions" xml:space="preserve">
    <value>Toplam Ödeme Seçenekleri</value>
  </data>
  <data name="TotalPosCollections" xml:space="preserve">
    <value>Toplam Pos Tahsilatlar</value>
  </data>
  <data name="AddVisaType" xml:space="preserve">
    <value>Vize Kategorisi Ekle</value>
  </data>
  <data name="UpdateVisaType" xml:space="preserve">
    <value>Vize Kategorisi Güncelle</value>
  </data>
  <data name="VisaTypeDetails" xml:space="preserve">
    <value>Vize Kategorisi Detayları</value>
  </data>
  <data name="VisaTypeName" xml:space="preserve">
    <value>Vize Kategorisi Adı</value>
  </data>
  <data name="VisaTypes" xml:space="preserve">
    <value>Vize Kategorileri</value>
  </data>
  <data name="VisaType" xml:space="preserve">
    <value>Vize Kategorisi</value>
  </data>
  <data name="AddUpdateBranchApplicationCountryCompanyExtraFee" xml:space="preserve">
    <value>Firma Ücreti Ekle/Güncelle</value>
  </data>
  <data name="CompanyExtraFeePrice" xml:space="preserve">
    <value>Firma Ücreti</value>
  </data>
  <data name="ProviderCompany" xml:space="preserve">
    <value>Sağlayıcı Firma</value>
  </data>
  <data name="MainExtraFeeCategory" xml:space="preserve">
    <value>Üst Ücret</value>
  </data>
  <data name="AddInquiry" xml:space="preserve">
    <value>Anket ekle</value>
  </data>
  <data name="Inquiry" xml:space="preserve">
    <value>Anket</value>
  </data>
  <data name="InquiryList" xml:space="preserve">
    <value>Anket listesi</value>
  </data>
  <data name="InquiryManagementPage" xml:space="preserve">
    <value>Anket yönetim sayfası</value>
  </data>
  <data name="InquiryName" xml:space="preserve">
    <value>Anket adı</value>
  </data>
  <data name="ShowInInquiryTab" xml:space="preserve">
    <value>Anket tabında göster</value>
  </data>
  <data name="MainExtraFeeType" xml:space="preserve">
    <value>Üst Ücret Türü</value>
  </data>
  <data name="RelatedMainFeeType" xml:space="preserve">
    <value>Bağlı Olacağı Üst Ücret</value>
  </data>
  <data name="SubExtraFee" xml:space="preserve">
    <value>Alt Ekstra Ücret</value>
  </data>
  <data name="MainFeeCategoryName" xml:space="preserve">
    <value>Üst Ücret Adı</value>
  </data>
  <data name="AddMainExtraFeeCategory" xml:space="preserve">
    <value>Üst Ücret Ekle</value>
  </data>
  <data name="UpdateMainExtraFeeCategory" xml:space="preserve">
    <value>Üst Ücret Güncelle</value>
  </data>
  <data name="MainExtraFeeCategoryList" xml:space="preserve">
    <value>Üst Ücret Türleri</value>
  </data>
  <data name="HasNoPermissionForOperation" xml:space="preserve">
    <value>Bu işlem için yetkiniz bulunmamaktadır</value>
  </data>
  <data name="RelatedInsuranceApplicationCancellationType" xml:space="preserve">
    <value>Başvuruda “İlgili Sigorta” seçeneğini işaretleyen muaflar bulunmaktadır. Aksiyonu seçiniz!</value>
  </data>
  <data name="QuestionType" xml:space="preserve">
    <value>Soru Türü</value>
  </data>
  <data name="UpdateInquiry" xml:space="preserve">
    <value>Anket Güncelle</value>
  </data>
  <data name="ColumnLength" xml:space="preserve">
    <value>Kolon Uzunluğu</value>
  </data>
  <data name="ChoiceType" xml:space="preserve">
    <value>Seçenek Türü</value>
  </data>
  <data name="Preview" xml:space="preserve">
    <value>Önizle</value>
  </data>
  <data name="AlreadyExistDataMessage" xml:space="preserve">
    <value>Bu soru/yanıt daha önceden kullanılmıştır.Dolayısıyla silinemez</value>
  </data>
  <data name="ProvidedWithHasRelatedInsurance" xml:space="preserve">
    <value>Muaf olan kişiler için sigorta satışı yapılacak mı?</value>
  </data>
  <data name="CreateUpdate" xml:space="preserve">
    <value>Oluştur/Güncelle</value>
  </data>
  <data name="AddNewQuestion" xml:space="preserve">
    <value>Yeni Soru Ekle</value>
  </data>
  <data name="AddNewQuestionChoice" xml:space="preserve">
    <value>Yeni Soru Seçeneği Ekle</value>
  </data>
  <data name="AddNewSelection" xml:space="preserve">
    <value>Yeni Seçenek Ekle</value>
  </data>
  <data name="AddNewAnswer" xml:space="preserve">
    <value>Yeni Cevap Ekle</value>
  </data>
  <data name="ComboboxChoices" xml:space="preserve">
    <value>Combobox Seçenekleri</value>
  </data>
  <data name="QuestionAnswerSelections" xml:space="preserve">
    <value>Soru Cevap Seçenekleri</value>
  </data>
  <data name="UpdateInquiryDefinition" xml:space="preserve">
    <value>Anket Tanımı Güncelle</value>
  </data>
  <data name="ShowInPmsPage" xml:space="preserve">
    <value>IK sayfasında göster</value>
  </data>
  <data name="RelatedService" xml:space="preserve">
    <value>İlişkili Hizmetler</value>
  </data>
  <data name="NonApplicationRelatedInsurance" xml:space="preserve">
    <value>Başvuru Dışı İlişkili Hizmetler</value>
  </data>
  <data name="Exception_StartOfShiftDate" xml:space="preserve">
    <value>Vardiya başlangıç saati diğer saatlerden daha geç bir saat seçilemez</value>
  </data>
  <data name="Exception_EndOfShiftDate" xml:space="preserve">
    <value>Vardiya bitiş saati diğer saatlerden daha erken bir saat seçilemez</value>
  </data>
  <data name="Exception_EndOfLunchDate" xml:space="preserve">
    <value>Öğle yemeği başlangıç ve bitiş tarihi uyumlu olmalıdır</value>
  </data>
  <data name="DataGroup" xml:space="preserve">
    <value>İşlem Grubu</value>
  </data>
  <data name="DataType" xml:space="preserve">
    <value>İşlem Tipi</value>
  </data>
  <data name="CancellationBy" xml:space="preserve">
    <value>İptal Eden Kişi</value>
  </data>
  <data name="ShowScoreCard" xml:space="preserve">
    <value>Skor Kart Görüntüle</value>
  </data>
  <data name="PMS" xml:space="preserve">
    <value>Performans Yönetim Sistemi</value>
  </data>
  <data name="Exception_CreateSlot" xml:space="preserve">
    <value>Slot oluşturulamadı</value>
  </data>
  <data name="ScoreCardReport" xml:space="preserve">
    <value>Skor Kart Rapor</value>
  </data>
  <data name="AddVideo" xml:space="preserve">
    <value>Video Ekle</value>
  </data>
  <data name="VideoScreenTimes" xml:space="preserve">
    <value>Video Gösterim Saatleri</value>
  </data>
  <data name="AnnouncementScreeningTimes" xml:space="preserve">
    <value>Duyuru Gösterim Saatleri</value>
  </data>
  <data name="VideoName" xml:space="preserve">
    <value>Video Adı</value>
  </data>
  <data name="EnterText" xml:space="preserve">
    <value>Metin giriniz</value>
  </data>
  <data name="Text" xml:space="preserve">
    <value>Metin</value>
  </data>
  <data name="UpdateVmsVideoTime" xml:space="preserve">
    <value>Video Gösterim Saatleri Güncelle</value>
  </data>
  <data name="UpdateVmsAnnouncementTime" xml:space="preserve">
    <value>Duyuru Gösterim Saatleri Güncelle</value>
  </data>
  <data name="AddUpdateVmsAnnouncement" xml:space="preserve">
    <value>Ekle/Güncelle Duyuru</value>
  </data>
  <data name="LdapInvalidCredentials" xml:space="preserve">
    <value>Hatalı kullanıcı adı / şifre</value>
  </data>
  <data name="SlotReport" xml:space="preserve">
    <value>Slot Raporu</value>
  </data>
  <data name="SlotReportAgentColumn" xml:space="preserve">
    <value>Acenta(VIP)</value>
  </data>
  <data name="SlotReportCallCenterColumn" xml:space="preserve">
    <value>Çağrı Merkezi(B2C) </value>
  </data>
  <data name="RemainingAppointment" xml:space="preserve">
    <value>Kalan Randevu</value>
  </data>
  <data name="SlotReportWalkinAppointment" xml:space="preserve">
    <value>Walkin Randevu</value>
  </data>
  <data name="ScanCycleStatus" xml:space="preserve">
    <value>Başvuru Durumu Statüsü</value>
  </data>
  <data name="IhbInformation" xml:space="preserve">
    <value>IHB Bilgisi</value>
  </data>
  <data name="LdapAccountExpired" xml:space="preserve">
    <value>Şifrenizin süresi dolmuştur. Lütfen sistem yöneticiniz ile iletişime geçiniz</value>
  </data>
  <data name="AddRelatedIndividual" xml:space="preserve">
    <value>İlgili Kişiler Ekle</value>
  </data>
  <data name="ReleatedInvidualInsurance" xml:space="preserve">
    <value>Muaf olan kişiler için sigorta satışı yapılacak mı?</value>
  </data>
  <data name="HasRelatedInsuranceWarning" xml:space="preserve">
    <value>Muaf kişiler için sigorta bilgileri seçilmiştir. Lütfen İlişkili sigorta seçimi yapınız!</value>
  </data>
  <data name="HasRelatedInsuranceWarningCount" xml:space="preserve">
    <value>İlişkili Sigorta tablosuna girilen kişi sayısına göre sigorta adetini girmelisiniz!</value>
  </data>
  <data name="SentLinkInviduals" xml:space="preserve">
    <value>Muaf bireylere ilişkin için onaylanmış poliçeleri gönder</value>
  </data>
  <data name="SendLink" xml:space="preserve">
    <value>Bağlantıyı gönder</value>
  </data>
  <data name="ReleatedInsuranceSmsContentFirst" xml:space="preserve">
    <value>Aşağıdaki bağlantıya tıklayabilirsiniz:</value>
  </data>
  <data name="InsuranceRefundCheckMessageScanSycle" xml:space="preserve">
    <value>[TYPE] [DATE] sonrasındaki başvurularda Vize Ret İadesi yapılmamaktadır.</value>
  </data>
  <data name="CreatePrinterInsuranceFirstWarning" xml:space="preserve">
    <value>Lütfen ICR aldığınız başvuruya sigorta oluşturunuz.</value>
  </data>
  <data name="PassportValidityPeriodForVisaTypeWarning" xml:space="preserve">
    <value>Seçilen vize türü için pasaport geçerlilik süresi en az 2 yıl olmalıdır. Lütfen geçerli bir pasaport bilgisi giriniz.</value>
  </data>
  <data name="PrintIndividualICR" xml:space="preserve">
    <value>Bireysel ICR Yazdır</value>
  </data>
  <data name="ADAccountName" xml:space="preserve">
    <value>AD Hesap Adı</value>
  </data>
  <data name="AreYouSureToProcessScanOperationWithDiffrentPassport" xml:space="preserve">
    <value>Randevu esnasında girilmiş olan pasaport numarası ile tarama yapılan pasaport numarası farklıdır, scan işlemine devam ederseniz,  güncellenecektir</value>
  </data>
  <data name="Continue" xml:space="preserve">
    <value>Devam et</value>
  </data>
  <data name="HasRelatedInsuranceIsRequired" xml:space="preserve">
    <value>Muaf kişiler için sigorta bilgilerini girmelisiniz!</value>
  </data>
  <data name="IsBiometricsDesktopUser" xml:space="preserve">
    <value>Biyometri Desktop Kullanıcısı</value>
  </data>
  <data name="ScanCycleRefundInformation" xml:space="preserve">
    <value>İade Bilgisi</value>
  </data>
  <data name="ScanCycleRefundInformationContent" xml:space="preserve">
    <value>Vize Türü Nedeni ile İade yapılamaz</value>
  </data>
  <data name="RejectionRefundStatusWarning" xml:space="preserve">
    <value>, Vize kategorisi nedeniyle 'Vize Ret İadesi Alındı' statüsüne getirilemez</value>
  </data>
  <data name="IsBlockedRefundIsDoneAllPolicies" xml:space="preserve">
    <value>Tüm poliçelerde vize ret ödeme iadesi yapılmasın</value>
  </data>
  <data name="PolicyDetail" xml:space="preserve">
    <value>Poliçe detayı</value>
  </data>
  <data name="UpdateInsuranceRefundSetting" xml:space="preserve">
    <value>Sigorta vize ret iade ayarı güncelle</value>
  </data>
  <data name="BranchInsuranceRefundSetting" xml:space="preserve">
    <value>Şube sigorta iade ayarı</value>
  </data>
  <data name="PleaseEnterDateValue" xml:space="preserve">
    <value>Lütfen tarih değeri giriniz</value>
  </data>
  <data name="NewMultipleVisaFee" xml:space="preserve">
    <value>Yeni Çok Giriş Vize Ücreti</value>
  </data>
  <data name="Successful" xml:space="preserve">
    <value>Başarılı</value>
  </data>
  <data name="PassportNumberChanged" xml:space="preserve">
    <value>Pasaport numarası değiştirilmiştir</value>
  </data>
  <data name="QmsScreenTitle" xml:space="preserve">
    <value>Qms Ekran Başlığı</value>
  </data>
  <data name="AddNote" xml:space="preserve">
    <value>Not ekle</value>
  </data>
  <data name="ClickToSeeNote" xml:space="preserve">
    <value>Notu görmek için tıklayınız</value>
  </data>
  <data name="NoteCreatedBy" xml:space="preserve">
    <value>Notu Oluşturan Kişi</value>
  </data>
  <data name="NoteUpdatedBy" xml:space="preserve">
    <value>Notu Güncelleyen Kişi</value>
  </data>
  <data name="BlackListNotes" xml:space="preserve">
    <value>Black List Notları</value>
  </data>
  <data name="NumberOfEntryFee" xml:space="preserve">
    <value>Vize Giriş Sayısı Ücreti</value>
  </data>
  <data name="WarrantyPeriodExpired" xml:space="preserve">
    <value>Teminat Süresi Doldu</value>
  </data>
  <data name="SlotStartDate" xml:space="preserve">
    <value>Slot Başlangıç Tarihi</value>
  </data>
  <data name="SlotEndDate" xml:space="preserve">
    <value>Slot Bitiş Tarihi</value>
  </data>
  <data name="PolicyPeriod" xml:space="preserve">
    <value>Poliçe periyodu</value>
  </data>
  <data name="UploadArea" xml:space="preserve">
    <value>Yükleme alanı</value>
  </data>
  <data name="UploadEsim" xml:space="preserve">
    <value>Esim yükle</value>
  </data>
  <data name="Exception_TypeNotFound" xml:space="preserve">
    <value>Tür bilgisi bulunamadı</value>
  </data>
  <data name="Exception_FeeNotFoundFromFlag" xml:space="preserve">
    <value>Seçili ücret bulunamadı</value>
  </data>
  <data name="Exception_FeeNotFound" xml:space="preserve">
    <value>Ücret bulunamadı</value>
  </data>
  <data name="Exception_FileNotFoundInCollection" xml:space="preserve">
    <value>Dosya Bulunamadı</value>
  </data>
  <data name="Exception_AlreadyExist" xml:space="preserve">
    <value>Kayıt daha önceden kaydedilmiş</value>
  </data>
  <data name="Exception_UploadError" xml:space="preserve">
    <value>Yüklerken bir sorun oluştu</value>
  </data>
  <data name="ForgotPassword" xml:space="preserve">
    <value>Şifremi Unuttum/Güncelle</value>
  </data>
  <data name="PasswordUpdateGuide" xml:space="preserve">
    <value>Şifre Güncelleme Kılavuzu</value>
  </data>
  <data name="ShowBranchInMobile" xml:space="preserve">
    <value>Şubeyi Mobilde Göster</value>
  </data>
  <data name="SMSSendingDate" xml:space="preserve">
    <value>SMS Gönderim Tarihi</value>
  </data>
  <data name="EmailSendingDate" xml:space="preserve">
    <value>Mail Gönderim Tarihi</value>
  </data>
  <data name="NonApplicationRelatedInsuranceNotAllowed" xml:space="preserve">
    <value>Başvuru Dışı İlişkili Sigorta türü ile başvuru oluşturamazsınız.</value>
  </data>
  <data name="Price2" xml:space="preserve">
    <value>Ücret 2</value>
  </data>
  <data name="MainApplicantPhoneNumber" xml:space="preserve">
    <value>Ana Başvuru Telefon Numarası</value>
  </data>
  <data name="RelatedInsurancePhoneNumber" xml:space="preserve">
    <value>İlişkili Sigorta Telefon Numarası</value>
  </data>
  <data name="ImportApplicationHistoryData" xml:space="preserve">
    <value>Verileri İçe Aktar</value>
  </data>
  <data name="CannotEditWithIhbFile" xml:space="preserve">
    <value>Bu satırda IHB belgesi bulunduğu için düzenleme yapılamıyor.</value>
  </data>
  <data name="IHBStatusNotFound" xml:space="preserve">
    <value>IHB statü bilgisi bulunamadı</value>
  </data>
  <data name="IHBDocumentNumberNotFound" xml:space="preserve">
    <value>IHB döküman numarası bilgisi bulunamadı</value>
  </data>
  <data name="IHBNumberNotFound" xml:space="preserve">
    <value>IHB numarası bilgisi bulunamadı</value>
  </data>
  <data name="AlreadyHasIhbOrder" xml:space="preserve">
    <value>Bu başvuru zaten devam eden bir IHB'nin oluşturulma isteğine sahip</value>
  </data>
  <data name="ImportApplicationDataSuccessfullMessage" xml:space="preserve">
    <value>Veriler başarılı bir şekilde içe aktarıldı</value>
  </data>
  <data name="TimePickerAmPmFormatView" xml:space="preserve">
    <value>HH:mm</value>
  </data>
  <data name="NeurotecLicenseNumber" xml:space="preserve">
    <value>Neurotec lisans numarası</value>
  </data>
  <data name="LicenseNumber" xml:space="preserve">
    <value>Lisans numarası</value>
  </data>
  <data name="LicenseType" xml:space="preserve">
    <value>Lisans tipi</value>
  </data>
  <data name="NeurotecLicenses" xml:space="preserve">
    <value>Neurotec Lisansları</value>
  </data>
  <data name="UpdateNeurotecLicense" xml:space="preserve">
    <value>Neurotec Lisans Güncelle</value>
  </data>
  <data name="NeurotecLicenseDetails" xml:space="preserve">
    <value>Neurotec Lisans Detay</value>
  </data>
  <data name="CompanyExtraFeePriceCurrency" xml:space="preserve">
    <value>Firma Ücreti Para Birimi</value>
  </data>
  <data name="Price2Currency" xml:space="preserve">
    <value>Ücret 2 Para Birimi</value>
  </data>
  <data name="ShowExtraFeeOnNewApplicationByRejectionStatus" xml:space="preserve">
    <value>Ret durumunda yeniden başvuruda göster</value>
  </data>
  <data name="FreeCreateThreeMonthInsurance" xml:space="preserve">
    <value>Ücretsiz 3 aylık sigorta</value>
  </data>
  <data name="ThreeMonthCancelledInsuranceMessage" xml:space="preserve">
    <value>Son başvuruda 3 aylık poliçe ve ret statüsü bulunduğu için (NLL ve IR) ücretsiz 3 aylık poliçe ile devam edilebilir</value>
  </data>
  <data name="RejectedFeesMadeVisible" xml:space="preserve">
    <value>Reddedilme Durumunda Yeniden Başvuru Ücretleri tekrar görüntülenebilir hale getirildi</value>
  </data>
  <data name="SixMonthCancelledInsuranceMessage" xml:space="preserve">
    <value>Son başvuruda 6 aylık poliçe ve ret statüsü bulunduğu için (NLL ve IR) ücretsiz 6 aylık poliçe ile devam edilebilir</value>
  </data>
  <data name="OneYearCancelledInsuranceMessage" xml:space="preserve">
    <value>Son başvuruda 1 yıllık poliçe ve ret statüsü bulunduğu için (NLL ve IR) ücretsiz 1 yıllık poliçe ile devam edilebilir</value>
  </data>
  <data name="FreeCreateThreeMonthInsuranceButtonLabel" xml:space="preserve">
    <value>Ücretsiz 3 aylık sigorta oluşturun</value>
  </data>
  <data name="FreeCreateSixMonthInsuranceButtonLabel" xml:space="preserve">
    <value>Ücretsiz 6 aylık sigorta oluşturun</value>
  </data>
  <data name="FreeCreateOneYearInsuranceButtonLabel" xml:space="preserve">
    <value>Ücretsiz 1 yıllık sigorta oluşturun</value>
  </data>
  <data name="NoEmailLabelName" xml:space="preserve">
    <value>Email Yoktur</value>
  </data>
  <data name="CancellationOfRejected" xml:space="preserve">
    <value>Retten İptal</value>
  </data>
  <data name="FirstInsuranceStartDate" xml:space="preserve">
    <value>İlk Sigorta Başlangıç Tarihi</value>
  </data>
  <data name="FirstInsuranceEndDate" xml:space="preserve">
    <value>İlk Sigorta Bitiş Tarihi</value>
  </data>
  <data name="FirstInsurancePolicyStatus" xml:space="preserve">
    <value>İlk Sigorta Poliçe Durumu</value>
  </data>
  <data name="FirstReferenceNumber" xml:space="preserve">
    <value>İlk Referans Numarası</value>
  </data>
  <data name="FirstApplicationDate" xml:space="preserve">
    <value>İlk Başvuru Tarihi</value>
  </data>
  <data name="RelatedInsuranceSmsContentFooter" xml:space="preserve">
    <value>Destek Hattı: +90 (850) 255 36 22</value>
  </data>
</root>