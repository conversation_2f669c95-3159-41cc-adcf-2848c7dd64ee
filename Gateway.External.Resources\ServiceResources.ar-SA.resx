﻿<?xml version="1.0" encoding="utf-8"?>
<root>
  <!-- 
    Microsoft ResX Schema 
    
    Version 2.0
    
    The primary goals of this format is to allow a simple XML format 
    that is mostly human readable. The generation and parsing of the 
    various data types are done through the TypeConverter classes 
    associated with the data types.
    
    Example:
    
    ... ado.net/XML headers & schema ...
    <resheader name="resmimetype">text/microsoft-resx</resheader>
    <resheader name="version">2.0</resheader>
    <resheader name="reader">System.Resources.ResXResourceReader, System.Windows.Forms, ...</resheader>
    <resheader name="writer">System.Resources.ResXResourceWriter, System.Windows.Forms, ...</resheader>
    <data name="Name1"><value>this is my long string</value><comment>this is a comment</comment></data>
    <data name="Color1" type="System.Drawing.Color, System.Drawing">Blue</data>
    <data name="Bitmap1" mimetype="application/x-microsoft.net.object.binary.base64">
        <value>[base64 mime encoded serialized .NET Framework object]</value>
    </data>
    <data name="Icon1" type="System.Drawing.Icon, System.Drawing" mimetype="application/x-microsoft.net.object.bytearray.base64">
        <value>[base64 mime encoded string representing a byte array form of the .NET Framework object]</value>
        <comment>This is a comment</comment>
    </data>
                
    There are any number of "resheader" rows that contain simple 
    name/value pairs.
    
    Each data row contains a name, and value. The row also contains a 
    type or mimetype. Type corresponds to a .NET class that support 
    text/value conversion through the TypeConverter architecture. 
    Classes that don't support this are serialized and stored with the 
    mimetype set.
    
    The mimetype is used for serialized objects, and tells the 
    ResXResourceReader how to depersist the object. This is currently not 
    extensible. For a given mimetype the value must be set accordingly:
    
    Note - application/x-microsoft.net.object.binary.base64 is the format 
    that the ResXResourceWriter will generate, however the reader can 
    read any of the formats listed below.
    
    mimetype: application/x-microsoft.net.object.binary.base64
    value   : The object must be serialized with 
            : System.Runtime.Serialization.Formatters.Binary.BinaryFormatter
            : and then encoded with base64 encoding.
    
    mimetype: application/x-microsoft.net.object.soap.base64
    value   : The object must be serialized with 
            : System.Runtime.Serialization.Formatters.Soap.SoapFormatter
            : and then encoded with base64 encoding.

    mimetype: application/x-microsoft.net.object.bytearray.base64
    value   : The object must be serialized into a byte array 
            : using a System.ComponentModel.TypeConverter
            : and then encoded with base64 encoding.
    -->
  <xsd:schema id="root" xmlns="" xmlns:xsd="http://www.w3.org/2001/XMLSchema" xmlns:msdata="urn:schemas-microsoft-com:xml-msdata">
    <xsd:import namespace="http://www.w3.org/XML/1998/namespace" />
    <xsd:element name="root" msdata:IsDataSet="true">
      <xsd:complexType>
        <xsd:choice maxOccurs="unbounded">
          <xsd:element name="metadata">
            <xsd:complexType>
              <xsd:sequence>
                <xsd:element name="value" type="xsd:string" minOccurs="0" />
              </xsd:sequence>
              <xsd:attribute name="name" use="required" type="xsd:string" />
              <xsd:attribute name="type" type="xsd:string" />
              <xsd:attribute name="mimetype" type="xsd:string" />
              <xsd:attribute ref="xml:space" />
            </xsd:complexType>
          </xsd:element>
          <xsd:element name="assembly">
            <xsd:complexType>
              <xsd:attribute name="alias" type="xsd:string" />
              <xsd:attribute name="name" type="xsd:string" />
            </xsd:complexType>
          </xsd:element>
          <xsd:element name="data">
            <xsd:complexType>
              <xsd:sequence>
                <xsd:element name="value" type="xsd:string" minOccurs="0" msdata:Ordinal="1" />
                <xsd:element name="comment" type="xsd:string" minOccurs="0" msdata:Ordinal="2" />
              </xsd:sequence>
              <xsd:attribute name="name" type="xsd:string" use="required" msdata:Ordinal="1" />
              <xsd:attribute name="type" type="xsd:string" msdata:Ordinal="3" />
              <xsd:attribute name="mimetype" type="xsd:string" msdata:Ordinal="4" />
              <xsd:attribute ref="xml:space" />
            </xsd:complexType>
          </xsd:element>
          <xsd:element name="resheader">
            <xsd:complexType>
              <xsd:sequence>
                <xsd:element name="value" type="xsd:string" minOccurs="0" msdata:Ordinal="1" />
              </xsd:sequence>
              <xsd:attribute name="name" type="xsd:string" use="required" />
            </xsd:complexType>
          </xsd:element>
        </xsd:choice>
      </xsd:complexType>
    </xsd:element>
  </xsd:schema>
  <resheader name="resmimetype">
    <value>text/microsoft-resx</value>
  </resheader>
  <resheader name="version">
    <value>2.0</value>
  </resheader>
  <resheader name="reader">
    <value>System.Resources.ResXResourceReader, System.Windows.Forms, Version=4.0.0.0, Culture=neutral, PublicKeyToken=b77a5c561934e089</value>
  </resheader>
  <resheader name="writer">
    <value>System.Resources.ResXResourceWriter, System.Windows.Forms, Version=4.0.0.0, Culture=neutral, PublicKeyToken=b77a5c561934e089</value>
  </resheader>
  <data name="BRANCH_DEPARTMENT_NOT_FOUND" xml:space="preserve">
    <value>لم يتم العثور على قسم الفرع</value>
  </data>
  <data name="APPLICANT_NOT_FOUND" xml:space="preserve">
    <value>لم يتم العثور على المتقدم</value>
  </data>
  <data name="APPOINTMENT_NOT_FOUND" xml:space="preserve">
    <value>لم يتم العثور على الموعد</value>
  </data>
  <data name="BRANCH_NOT_FOUND" xml:space="preserve">
    <value>لم يتم العثور على الفرع</value>
  </data>
  <data name="FAILED" xml:space="preserve">
    <value>FAILED</value>
  </data>
  <data name="FAMILY_APPLICANT_COUNT_ERROR" xml:space="preserve">
    <value>يمكن تواجد اكثر من متقدم واحد عند التقديم العائلي</value>
  </data>
  <data name="GROUP_APPLICANT_COUNT_ERROR" xml:space="preserve">
    <value>يمكن تواجد اكثر من متقدم واحد عند التقديم الجماعي</value>
  </data>
  <data name="INDIVIDUAL_APPLICANT_COUNT_ERROR" xml:space="preserve">
    <value>لا يمكن تواجد اكثر من متقدم واحد عند التقديم الفردي</value>
  </data>
  <data name="INPUT_ERROR" xml:space="preserve">
    <value>INPUT_ERROR</value>
  </data>
  <data name="INTERNAL_SERVER_ERROR" xml:space="preserve">
    <value>INTERNAL_SERVER_ERROR</value>
  </data>
  <data name="INVALID_INPUT_ERROR" xml:space="preserve">
    <value>خطأ دخول غير صالح</value>
  </data>
  <data name="INVALID_REQUEST" xml:space="preserve">
    <value>طلب غير صحيح</value>
  </data>
  <data name="PROPERTY_MAX_LENGTH_ERROR" xml:space="preserve">
    <value> {1} لا يمكن ان تكون خاصية {0} اكثر من رمز</value>
  </data>
  <data name="PROPERTY_REQUIRED" xml:space="preserve">
    <value>{0} إلزامي</value>
  </data>
  <data name="RESOURCE_ALREADY_REGISTERED" xml:space="preserve">
    <value>ان المصدر مسجل بالفعل</value>
  </data>
  <data name="RESOURCE_CREATED" xml:space="preserve">
    <value>تم انشاء المصدر</value>
  </data>
  <data name="RESOURCE_DELETED" xml:space="preserve">
    <value>تم حذف المصدر</value>
  </data>
  <data name="RESOURCE_FOUND" xml:space="preserve">
    <value>تم العثور على المصدر</value>
  </data>
  <data name="RESOURCE_NOT_FOUND" xml:space="preserve">
    <value>لم يتم العثور على المصدر</value>
  </data>
  <data name="RESOURCE_RETRIEVED" xml:space="preserve">
    <value>تم استلام المصدر</value>
  </data>
  <data name="RESOURCE_UPDATED" xml:space="preserve">
    <value>تم تحديث المصدر</value>
  </data>
  <data name="SUCCESS" xml:space="preserve">
    <value>SUCCESS</value>
  </data>
  <data name="SLOT_NOT_FOUND" xml:space="preserve">
    <value>لم يتم العثور على موعد شاغر</value>
  </data>
  <data name="NO_AVAILABLE_SLOTS_FOUND" xml:space="preserve">
    <value>لم يتم العثور على أي موعد مناسب</value>
  </data>
  <data name="FIRST_AVAILABLE_SLOT_FOUND" xml:space="preserve">
    <value>تم العثور على أول موعد مناسب</value>
  </data>
  <data name="COUNTRY_NOT_FOUND" xml:space="preserve">
    <value>لم يتم العثور على البلد</value>
  </data>
  <data name="INVALID_EMAIL_ADDRESS" xml:space="preserve">
    <value>البريد الالكتروني غير صحيح</value>
  </data>
  <data name="REPEATED_APPOINTMENT_FOUND" xml:space="preserve">
    <value>يوجد موعد مع جواز السفر</value>
  </data>
  <data name="SLOT_QUOTA_NOT_FOUND" xml:space="preserve">
    <value>لم يتم العثور على حصة المواعيد الشاغرة</value>
  </data>
  <data name="APPLICATION_STATUS_NOT_FOUND" xml:space="preserve">
    <value>لم يتم العثور على وضع التقديم</value>
  </data>
  <data name="APPLICATION_NOT_FOUND" xml:space="preserve">
    <value>لم يتم العثور على التقديم</value>
  </data>
  <data name="PROPERTY_FORMAT_ERROR" xml:space="preserve">
    <value>ان هيئة خاصية {0} غير نافذة</value>
  </data>
  <data name="APPLICANT_ONLY_HAS_ONE_WİFEORHUSBAND" xml:space="preserve">
    <value>ملاحظة: لا يمكن إضافة اكثر من زوجة واحدة الى ملف التقديم</value>
  </data>
  <data name="ONLY_ONE_RECORD_CANBE_SET_AS_WIFEORHUSBAND_RECORD_FOR_APPOINTMENT" xml:space="preserve">
    <value>يمكن تحديد بطاقة سجل واحدة او سجل زوج واحد فقط للموعد.</value>
  </data>
  <data name="METHOD_REQUIREMENT_ERROR" xml:space="preserve">
    <value>ان هذه الطريقة غير متوافقة مع هذه الحزمة من الاختيارات</value>
  </data>
  <data name="APPOINTMENT_NOT_CONVERTED_APPLICATION" xml:space="preserve">
    <value>لم يتم تحويل الموعد الى التطبيق</value>
  </data>
  <data name="APPOINTMENT_HAS_CONVERTED_APPLICATION_NOT_FOUND" xml:space="preserve">
    <value>الموعد الذي تم تحويله إلى التطبيق غير موجود</value>
  </data>
  <data name="BRANCH_CHANGE_NOT_ALLOWED" xml:space="preserve">
    <value>يمكن تغيير الفرع مرة واحدة فقط.</value>
  </data>
  <data name="NEW_APPLICANT_NOT_ALLOWED" xml:space="preserve">
    <value>مقدم الطلب الجديد غير مسموح به</value>
  </data>
  <data name="SLOT_NOT_IN_THIS_BRANCH" xml:space="preserve">
    <value>الفتحة لا تنتمي إلى هذا الفرع</value>
  </data>
  <data name="PASSPORT_VALIDITY_PERIOD_ERROR" xml:space="preserve">
    <value>لا يمكن أن تقل فترة صلاحية جواز السفر عن 180 يومًا</value>
  </data>
  <data name="BIRTHDATE_MUST_BE_PAST_TENSE" xml:space="preserve">
    <value>يجب أن يكون تاريخ الميلاد بصيغة الماضي</value>
  </data>
  <data name="APPOINTMENT_SLOT_LIMIT" xml:space="preserve">
    <value>يمكن إجراء تحديثات اليوم والتاريخ مرتين على الأكثر</value>
  </data>
  <data name="APPOINTMENT_UPDATE_NOT_ALLOWED" xml:space="preserve">
    <value>لا يمكن إجراء تحديث للموعد خلال آخر 48 ساعة</value>
  </data>
  <data name="DELETE_OPERATION_NOT_ALLOWED" xml:space="preserve">
    <value>لا يمكن حذف المواعيد التي يتبقى لها أقل من 24 ساعة</value>
  </data>
  <data name="REPEATED_MAIL_NOT_ALLOWED" xml:space="preserve">
    <value>يمكن إجراء موعدين كحد أقصى باستخدام نفس عنوان البريد الإلكتروني.</value>
  </data>
  <data name="REPEATED_PHONE_NUMBER_NOT_ALLOWED" xml:space="preserve">
    <value>يمكن إجراء 3 مواعيد كحد أقصى بنفس رقم الهاتف.</value>
  </data>
  <data name="VAST_TYPE_PRICE_MESSAGE" xml:space="preserve">
    <value>بالإضافة إلى رسوم الخدمة ، هناك رسوم خدمة إضافية بقيمة {price} لهذه الخدمة.</value>
  </data>
  <data name="VAS_TYPE_NOT_ACTIVE" xml:space="preserve">
    <value>VasType غير نشط</value>
  </data>
  <data name="VISA_INFORMATION_NOT_ACTIVE" xml:space="preserve">
    <value>معلومات التأشيرة غير نشطة</value>
  </data>
  <data name="USER_ALREADY_REGISTERED" xml:space="preserve">
    <value>المستخدم مسجل بالفعل</value>
  </data>
  <data name="PASSWORD_MISMATCH" xml:space="preserve">
    <value>عدم تطابق كلمة المرور</value>
  </data>
  <data name="ALREADY_HAS_UPDATE_REQUEST" xml:space="preserve">
    <value>لديه بالفعل طلب التحديث</value>
  </data>
  <data name="USER_NOT_FOUND" xml:space="preserve">
    <value>لم يتم العثور على المستخدم</value>
  </data>
  <data name="AUTHORIZED_USER_NOT_FOUND" xml:space="preserve">
    <value>لم يتم العثور على المستخدم المعتمد</value>
  </data>
  <data name="INVALID_PHONE_NUMBER" xml:space="preserve">
    <value>رقم الهاتف غير صحيح</value>
  </data>
  <data name="PROPERTY_MUST_NOT_HAVE_NUMERIC_CHARACTER" xml:space="preserve">
    <value>يجب ألا يكون له حرف رقمي</value>
  </data>
  <data name="PASSWORD_MUST_NOT_SAME" xml:space="preserve">
    <value>يجب ألا تكون كلمة المرور هي نفسها</value>
  </data>
  <data name="TRANSLATION_NOT_FOUND" xml:space="preserve">
    <value>لم يتم العثور على الترجمة</value>
  </data>
  <data name="COMPANY_NOT_FOUND" xml:space="preserve">
    <value>لم يتم العثور على الشركة</value>
  </data>
  <data name="EXTERNAL_REGISTER_NOTIFICATION" xml:space="preserve">
    <value>تم الانتهاء من التسجيل الخاص بك. كلمة المرور المؤقتة الخاصة بك هي [PASSWORD] يمكنك تغييره من النظام بعد تسجيل الدخول بكلمة المرور المؤقتة الخاصة بك.</value>
  </data>
  <data name="NO_DATA_FOUND_TO_REPORT" xml:space="preserve">
    <value>لم يتم العثور على بيانات للإبلاغ عنها</value>
  </data>
  <data name="CREATED_BY" xml:space="preserve">
    <value>انشأ من قبل</value>
  </data>
  <data name="ORDER" xml:space="preserve">
    <value>طلب</value>
  </data>
  <data name="REPORT_DATE" xml:space="preserve">
    <value>تاريخ التقرير</value>
  </data>
  <data name="SAME_PASSPORT_USED_BETWEEN_APPLICANTS" xml:space="preserve">
    <value>نفس جواز السفر المستخدم بين المتقدمين</value>
  </data>
  <data name="COMPANY_USER_REPORT" xml:space="preserve">
    <value>تقرير مستخدم الشركة</value>
  </data>
  <data name="COMPANY_SLOT_DEMAND_REPORT" xml:space="preserve">
    <value>تقرير الطلب على فتحات الشركة</value>
  </data>
  <data name="COMPANY_APPOINTMENT_DEMAND_REPORT" xml:space="preserve">
    <value>تقرير طلب تعيين الشركة</value>
  </data>
  <data name="APPOINTMENT_DEMAND_NOT_FOUND" xml:space="preserve">
    <value>لم يتم العثور على طلب الموعد</value>
  </data>
  <data name="COMPANY_APPLICATION_REPORT" xml:space="preserve">
    <value>تقرير تطبيق الشركة</value>
  </data>
  <data name="PNL_REPORT" xml:space="preserve">
    <value>تقرير PNL</value>
  </data>
  <data name="PNL_ALREADY_REGISTERED" xml:space="preserve">
    <value>تم تحميل الملف مسبقًا</value>
  </data>
  <data name="INVALID_PARAMETER" xml:space="preserve">
    <value>{0} معلمة غير صالحة</value>
  </data>
  <data name="INVALID_PASSPORT_NUMBER" xml:space="preserve">
    <value>رقم جواز السفر غير صالح</value>
  </data>
  <data name="DATE_PARAMETERS_MISMATCH" xml:space="preserve">
    <value>معلمات التاريخ غير متطابقة</value>
  </data>
  <data name="FILE_SIZE_LIMIT_EXCEEDED" xml:space="preserve">
    <value>تم تجاوز الحد الأقصى لحجم الملف</value>
  </data>
  <data name="INVALID_FILE_EXTENSION" xml:space="preserve">
    <value>ملحق الملف غير صالح</value>
  </data>
  <data name="EMAIL_MISMATCH_WITH_TOKEN" xml:space="preserve">
    <value>يجب أن يكون البريد الإلكتروني هو نفس البريد الإلكتروني المستخدم في تسجيل الدخول</value>
  </data>
  <data name="APPOINTMENT_FOUND_WITH_SAME_PASSPORT" xml:space="preserve">
    <value>تم العثور على موعد بنفس رقم جواز السفر</value>
  </data>
  <data name="BAD_REQUEST" xml:space="preserve">
    <value>BAD_REQUEST</value>
  </data>
  <data name="CANNOT_CONTINUE_APPOINTMENT" xml:space="preserve">
    <value>لا يمكنك متابعة الموعد برقم جواز السفر هذا</value>
  </data>
  <data name="PRE_CONDITION_FAILED" xml:space="preserve">
    <value>فشل الشرط المسبق</value>
  </data>
  <data name="INVALID_PARAMETER_LOWER" xml:space="preserve">
    <value>معلمة غير صالحة:{0} يجب أن تكون أقل من {1}</value>
  </data>
  <data name="CANNOT_UPDATE_WALKIN" xml:space="preserve">
    <value>لا يمكن تحديث مواعيد الحضور
</value>
  </data>
  <data name="MOBILE_SLOT_NOT_FOUND" xml:space="preserve">
    <value>لا يوجد موعد متاح للتاريخ المحدد. يُرجى تجربة تاريخ آخر.</value>
  </data>
</root>