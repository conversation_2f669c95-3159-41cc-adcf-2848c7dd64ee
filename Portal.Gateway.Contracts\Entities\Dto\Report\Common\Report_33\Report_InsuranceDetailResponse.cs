﻿using System;
using System.Collections.Generic;

namespace Portal.Gateway.Contracts.Entities.Dto.Report.Common.Report_33
{
	public class Report_InsuranceDetailResponse
	{
		public IEnumerable<Branch> Branches { get; set; }
		public class Branch
		{
			public string BranchName { get; set; }
			public IEnumerable<Insurance> Insurances { get; set; }

			public class Insurance
			{
				//public string CountryName { get; set; }

				//public string BranchName { get; set; }

				public int ApplicationTypeId { get; set; }

				public string ReferenceNumber { get; set; }

				public string PassportNumber { get; set; }

				public string PolicyNumber { get; set; }

				public string ApplicantName { get; set; }

				public string ApplicantSurname { get; set; }

				public DateTimeOffset ApplicationDate { get; set; }

				public DateTimeOffset InsuranceStartDate { get; set; }

				public DateTimeOffset InsuranceEndDate { get; set; }

				public int PolicyDays { get; set; }

				public decimal Price { get; set; }
				public decimal? CompanyPrice { get; set; }
				public string? CompanyPriceCurrency { get; set; }
				public decimal? Price2 { get; set; }
				public string? Price2Currency { get; set; }

				public string Currency { get; set; }

				public string PolicyStatus { get; set; }
                public bool IsValid { get; set; }

                public int? CancellationReasonId { get; set; }

				public DateTime? CancellationDate { get; set; }
				public int? ApplicationInsuranceCategory { get; set; }
				public DateTime? CancelleationOfRejectedDate { get; set; }
			}
		}
	}
}
