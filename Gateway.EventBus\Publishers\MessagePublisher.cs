﻿using Gateway.EventBus.Connections;
using RabbitMQ.Client;
using System;
using System.Collections.Generic;
using System.Text;
using System.Text.Json;
using System.Threading.Tasks;

namespace Gateway.EventBus.Publishers
{
    internal sealed class MessagePublisher : IMessagePublisher
    {
        private readonly IChannelFactory _channel;

        public MessagePublisher(IChannelFactory channelFactory) => _channel = channelFactory;

        public async Task PublishAsync<TMessage>(string queue, TMessage message, bool isPriorityQueue, int priority,
            string messageId = default)
            where TMessage : class, IMessage
        {
            var json = JsonSerializer.Serialize(message);
            var body = Encoding.UTF8.GetBytes(json);
            var properties = new BasicProperties();

            properties.MessageId = messageId ?? Guid.NewGuid().ToString("N");

            using (var channel = await _channel.CreateChannelAsync())
            {
                if (isPriorityQueue)
                {
                    properties.Priority = (byte)priority;

                    IDictionary<string, object> args = new Dictionary<string, object>
                {
                    { "x-max-priority", 10 }
                };

                    await channel.QueueDeclareAsync(queue, true, false, false, args);
                    await channel.BasicPublishAsync(exchange: string.Empty, mandatory: true, routingKey: queue, basicProperties: properties, body: body);
                }
                else
                {

                    await channel.QueueDeclareAsync(queue, true, false, false, null);
                    await channel.BasicPublishAsync(exchange: string.Empty, routingKey: queue, body: body);
                }
            }
        }
    }
}
