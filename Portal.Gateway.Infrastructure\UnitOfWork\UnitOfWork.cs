﻿using Microsoft.EntityFrameworkCore;
using Portal.Gateway.Contracts.Repository;
using Portal.Gateway.Contracts.UnitOfWork;
using Portal.Gateway.Infrastructure.Repository;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Threading.Tasks;
using Portal.Gateway.Entity.Entities.Portal;

namespace Portal.Gateway.Infrastructure.UnitOfWork
{
    public class UnitOfWork<TDbContext> : IUnitOfWork<TDbContext> where TDbContext : DbContext
    {
        private readonly TDbContext _dbContext;

        public UnitOfWork(TDbContext dbContext)
        {
            _dbContext = dbContext;
        }

        public IRepository<T> GetRepository<T>() where T : class
        {
            return new Repository<T>(_dbContext);
        }

        #region Sync

        public bool CanConnect()
        {
            return _dbContext.Database.CanConnect();
        }

        public void SaveChanges()
        {
            using (var dbContextTransaction = _dbContext.Database.BeginTransaction())
            {
                try
                {
	                #region ApplicationConfirmationCodeHistory

	                var newApplicationConfirmationCodeHistories = GetApplicationConfirmationCodeHistories();
	                if (newApplicationConfirmationCodeHistories != null && newApplicationConfirmationCodeHistories.Any())
		                _dbContext.AddRange(newApplicationConfirmationCodeHistories);

	                #endregion

					#region ApplicationHistory

					var newApplicationHistories = GetApplicationHistories();
                    if (newApplicationHistories != null && newApplicationHistories.Any())
                        _dbContext.AddRange(newApplicationHistories);

                    #endregion

                    #region PreApplicationHistory

                    var newPreApplicationHistories = GetPreApplicationHistories();
                    if (newPreApplicationHistories != null && newPreApplicationHistories.Any())
                        _dbContext.AddRange(newPreApplicationHistories);

                    #endregion

                    #region PreApplicationApplicantHistory

                    var newPreApplicationApplicantHistories = GetPreApplicationApplicantHistories();
                    if (newPreApplicationApplicantHistories != null && newPreApplicationApplicantHistories.Any())
                        _dbContext.AddRange(newPreApplicationApplicantHistories);

                    #endregion

                    #region PreApplicationApplicantFileHistory

                    var newPreApplicationApplicantFileHistories = GetPreApplicationApplicantFileHistories();
                    if (newPreApplicationApplicantFileHistories != null && newPreApplicationApplicantFileHistories.Any())
                        _dbContext.AddRange(newPreApplicationApplicantFileHistories);

                    #endregion

                    #region ApplicationLastMileDataHistory

                    var newApplicationLastMileDataHistories = GetApplicationLastMileDataHistories();
                    if (newApplicationLastMileDataHistories != null && newApplicationLastMileDataHistories.Any())
                        _dbContext.AddRange(newApplicationLastMileDataHistories);

                    #endregion

                    #region ApplicationExtraFeeHistory

                    var newApplicationExtraFeeHistories = GetApplicationExtraFeeHistories();
                    if (newApplicationExtraFeeHistories != null && newApplicationExtraFeeHistories.Any())
                        _dbContext.AddRange(newApplicationExtraFeeHistories);
                    #endregion

                    #region ApplicationVisaHistoryHistory

                    var newApplicationVisaHistoryHistories = GetApplicationVisaHistoryHistories();
                    if (newApplicationVisaHistoryHistories != null && newApplicationVisaHistoryHistories.Any())
                        _dbContext.AddRange(newApplicationVisaHistoryHistories);
					#endregion

					_dbContext.SaveChanges();
                    dbContextTransaction.Commit();
                }
                catch (Exception ex)
                {
                    dbContextTransaction.Rollback();
                    throw ex;
                }
            }
        }

        public IQueryable<T> FromSqlRaw<T>(string sql) where T : class
        {
            return _dbContext.Set<T>().FromSqlRaw<T>(sql);
        }

        public int ExecuteSqlRaw(string sql, params object[] parameters)
        {
            return _dbContext.Database.ExecuteSqlRaw(sql, parameters);
        }

        #endregion

        #region Async

        public async Task<bool> CanConnectAsync()
        {
            return await _dbContext.Database.CanConnectAsync();
        }

        public async Task SaveChangesAsync()
        {
	        using (var dbContextTransaction = await _dbContext.Database.BeginTransactionAsync())
            {
                try
                {
                    #region ApplicationRejectionApprovalStatusHistory
                    SetCreatedAtUnmodifiedForRejectionApprovalStatusHistory();
                    #endregion

                    #region ApplicationConfirmationCodeHistory

                    var newApplicationConfirmationCodeHistories = GetApplicationConfirmationCodeHistories();
	                if (newApplicationConfirmationCodeHistories != null && newApplicationConfirmationCodeHistories.Any())
		                _dbContext.AddRange(newApplicationConfirmationCodeHistories);

	                #endregion

					#region ApplicationHistory

					var newApplicationHistories = GetApplicationHistories();
                    if (newApplicationHistories != null && newApplicationHistories.Any())
                        _dbContext.AddRange(newApplicationHistories);

                    #endregion

                    #region PreApplicationHistory

                    var newPreApplicationHistories = GetPreApplicationHistories();
                    if (newPreApplicationHistories != null && newPreApplicationHistories.Any())
                        _dbContext.AddRange(newPreApplicationHistories);

                    #endregion

                    #region PreApplicationApplicantHistory

                    var newPreApplicationApplicantHistories = GetPreApplicationApplicantHistories();
                    if (newPreApplicationApplicantHistories != null && newPreApplicationApplicantHistories.Any())
                        _dbContext.AddRange(newPreApplicationApplicantHistories);

                    #endregion

                    #region PreApplicationApplicantFileHistory

                    var newPreApplicationApplicantFileHistories = GetPreApplicationApplicantFileHistories();
                    if (newPreApplicationApplicantFileHistories != null && newPreApplicationApplicantFileHistories.Any())
                        _dbContext.AddRange(newPreApplicationApplicantFileHistories);

                    #endregion

                    #region ApplicationLastMileDataHistory

                    var newApplicationLastMileDataHistories = GetApplicationLastMileDataHistories();
                    if (newApplicationLastMileDataHistories != null && newApplicationLastMileDataHistories.Any())
                        _dbContext.AddRange(newApplicationLastMileDataHistories);

                    #endregion

                    #region ApplicationExtraFeeHistory

                    var newApplicationExtraFeeHistories = GetApplicationExtraFeeHistories();
                    if (newApplicationExtraFeeHistories != null && newApplicationExtraFeeHistories.Any())
                        _dbContext.AddRange(newApplicationExtraFeeHistories);

                    #endregion

                    #region ApplicationVisaHistoryHistory

                    var newApplicationVisaHistoryHistories = GetApplicationVisaHistoryHistories();
                    if (newApplicationVisaHistoryHistories != null && newApplicationVisaHistoryHistories.Any())
                        _dbContext.AddRange(newApplicationVisaHistoryHistories);
					#endregion

					await _dbContext.SaveChangesAsync();
                    await dbContextTransaction.CommitAsync();
                }
                catch (DbUpdateConcurrencyException concurrencyEx)
                {
                    throw;
                }
                catch (Exception ex)
                {
                    await dbContextTransaction.RollbackAsync();
                }
            }
        }

        private List<Entity.Entities.Portal.ApplicationHistory> GetApplicationHistories()
        {
            var newApplicationHistories = new List<Entity.Entities.Portal.ApplicationHistory>();

            try
            {
                var entitiesToCheck = new List<string>
                {
                    nameof(Entity.Entities.Portal.Application),
                    nameof(Entity.Entities.Portal.ApplicationDocument)
                };

                var modifiedEntities = _dbContext.ChangeTracker.Entries().Where(p => (p.State == EntityState.Added || p.State == EntityState.Modified || p.State == EntityState.Deleted) && entitiesToCheck.Contains(p.Entity.GetType().Name)).ToList();

                foreach (var modifiedEntity in modifiedEntities)
                {
                    int applicationId = 0;
                    int createdBy = 0;

                    if (modifiedEntity.Entity.GetType().Name == nameof(Entity.Entities.Portal.Application))
                    {
                        var application = modifiedEntity.Entity as Entity.Entities.Portal.Application;
                        applicationId = application.Id;
                        createdBy = application.UpdatedBy.GetValueOrDefault();
                    }
                    else if (modifiedEntity.Entity.GetType().Name == nameof(Entity.Entities.Portal.ApplicationDocument))
                    {
                        var applicationDocument = modifiedEntity.Entity as Entity.Entities.Portal.ApplicationDocument;
                        applicationId = applicationDocument.ApplicationId;
                        createdBy = applicationDocument.Application.UpdatedBy.GetValueOrDefault();
                    }

                    if (applicationId != 0 && createdBy != 0)
                    {
                        foreach (var prop in modifiedEntity.Properties)
                        {
                            var propertyName = prop.Metadata.Name;
                            var originalValue = prop.OriginalValue;
                            var currentValue = prop.CurrentValue;

                            if (propertyName != "UpdatedAt" && propertyName != "UpdatedBy" && /*originalValue != null && currentValue != null && */$"{originalValue}" != $"{currentValue}")
                            {
                                var newApplicationHistory = new Entity.Entities.Portal.ApplicationHistory
                                {
                                    ApplicationId = applicationId,
                                    PropertyName = propertyName,
                                    PreviousValue = $"{originalValue}",
                                    CurrentValue = $"{currentValue}",
                                    CreatedBy = createdBy,
                                    CreatedAt = DateTimeOffset.UtcNow
                                };

                                newApplicationHistories.Add(newApplicationHistory);
                            }
                        }
                    }
                }
            }
            catch { }

            return newApplicationHistories;
        }
        private List<Entity.Entities.Portal.PreApplicationHistory> GetPreApplicationHistories()
        {
            var newPreApplicationHistories = new List<Entity.Entities.Portal.PreApplicationHistory>();

            try
            {
                var entitiesToCheck = new List<string>
            {
                nameof(Entity.Entities.Portal.PreApplication),
            };

                var modifiedEntities = _dbContext.ChangeTracker.Entries().Where(p => p.State == EntityState.Modified && entitiesToCheck.Contains(p.Entity.GetType().Name)).ToList();

                foreach (var modifiedEntity in modifiedEntities)
                {
                    int preApplicationId = 0;
                    int createdBy = 0;

                    if (modifiedEntity.Entity.GetType().Name == nameof(Entity.Entities.Portal.PreApplication))
                    {
                        var preApplication = modifiedEntity.Entity as Entity.Entities.Portal.PreApplication;
                        preApplicationId = preApplication.Id;
                        createdBy = preApplication.UpdatedBy.GetValueOrDefault();
                    }

                    if (preApplicationId != 0 && createdBy != 0)
                    {
                        foreach (var prop in modifiedEntity.Properties)
                        {
                            var propertyName = prop.Metadata.Name;
                            var originalValue = prop.OriginalValue;
                            var currentValue = prop.CurrentValue;

                            if (ShouldSkipVasTypeIdChange(propertyName, originalValue, currentValue))
                                continue;

                            if (propertyName != "UpdatedAt" && $"{originalValue}" != $"{currentValue}")
                            {
                                var newPreApplicationHistory = new Entity.Entities.Portal.PreApplicationHistory
                                {
                                    PreApplicationId = preApplicationId,
                                    PropertyName = propertyName,
                                    PreviousValue = $"{originalValue}",
                                    CurrentValue = $"{currentValue}",
                                    CreatedBy = createdBy,
                                    CreatedAt = DateTimeOffset.UtcNow
                                };

                                newPreApplicationHistories.Add(newPreApplicationHistory);
                            }
                        }
                    }
                }
            }
            catch { }

            return newPreApplicationHistories;
        }

        private List<Entity.Entities.Portal.PreApplicationApplicantHistory> GetPreApplicationApplicantHistories()
        {
            var newPreApplicationApplicantHistories = new List<Entity.Entities.Portal.PreApplicationApplicantHistory>();

            try
            {
                var entitiesToCheck = new List<string>
                {
                    nameof(Entity.Entities.Portal.PreApplicationApplicant),
                };

                var modifiedEntities = _dbContext.ChangeTracker.Entries().Where(p =>
                    p.State == EntityState.Modified && entitiesToCheck.Contains(p.Entity.GetType().Name)).ToList();

                foreach (var modifiedEntity in modifiedEntities)
                {
                    int preApplicationApplicantId = 0;
                    int createdBy = 0;

                    if (modifiedEntity.Entity.GetType().Name == nameof(Entity.Entities.Portal.PreApplicationApplicant))
                    {
                        var preApplicationApplicant =
                            modifiedEntity.Entity as Entity.Entities.Portal.PreApplicationApplicant;
                        preApplicationApplicantId = preApplicationApplicant.Id;
                        createdBy = preApplicationApplicant.PreApplication.UpdatedBy.GetValueOrDefault();
                    }

                    if (preApplicationApplicantId != 0 && createdBy != 0)
                    {
                        foreach (var prop in modifiedEntity.Properties)
                        {
                            var propertyName = prop.Metadata.Name;
                            var originalValue = prop.OriginalValue;
                            var currentValue = prop.CurrentValue;

                            if (propertyName != "UpdatedAt" && originalValue != null && currentValue != null &&
                                $"{originalValue}" != $"{currentValue}")
                            {
                                var newPreApplicationApplicantHistory =
                                    new Entity.Entities.Portal.PreApplicationApplicantHistory
                                    {
                                        PreApplicationApplicantId = preApplicationApplicantId,
                                        PropertyName = propertyName,
                                        PreviousValue = $"{originalValue}",
                                        CurrentValue = $"{currentValue}",
                                        CreatedBy = createdBy,
                                        CreatedAt = DateTimeOffset.UtcNow
                                    };

                                newPreApplicationApplicantHistories.Add(newPreApplicationApplicantHistory);
                            }
                        }
                    }
                }
            }
            catch
            {

            }

            return newPreApplicationApplicantHistories;
        }
        private List<Entity.Entities.Portal.PreApplicationApplicantFileHistory> GetPreApplicationApplicantFileHistories()
        {
            var newPreApplicationApplicantFileHistories = new List<Entity.Entities.Portal.PreApplicationApplicantFileHistory>();

            try
            {
                var entitiesToCheck = new List<string>
            {
                nameof(Entity.Entities.Portal.PreApplicationApplicantFile),
            };

                var modifiedEntities = _dbContext.ChangeTracker.Entries().Where(p => p.State == EntityState.Modified && entitiesToCheck.Contains(p.Entity.GetType().Name)).ToList();

                foreach (var modifiedEntity in modifiedEntities)
                {
                    int preApplicationApplicantFileId = 0;
                    int createdBy = 0;

                    if (modifiedEntity.Entity.GetType().Name == nameof(Entity.Entities.Portal.PreApplicationApplicantFile))
                    {
                        var preApplicationApplicantFile = modifiedEntity.Entity as Entity.Entities.Portal.PreApplicationApplicantFile;
                        preApplicationApplicantFileId = preApplicationApplicantFile.Id;
                        createdBy = preApplicationApplicantFile.PreApplicationApplicant.PreApplication.UpdatedBy.GetValueOrDefault();
                    }

                    if (preApplicationApplicantFileId != 0 && createdBy != 0)
                    {
                        foreach (var prop in modifiedEntity.Properties)
                        {
                            var propertyName = prop.Metadata.Name;
                            var originalValue = prop.OriginalValue;
                            var currentValue = prop.CurrentValue;

                            if ((propertyName != "UpdatedAt" || propertyName != "UpdatedBy") && originalValue != null && currentValue != null && $"{originalValue}" != $"{currentValue}")
                            {
                                var newPreApplicationApplicantFileHistory = new Entity.Entities.Portal.PreApplicationApplicantFileHistory
                                {
                                    PreApplicationApplicantFileId = preApplicationApplicantFileId,
                                    PropertyName = propertyName,
                                    PreviousValue = $"{originalValue}",
                                    CurrentValue = $"{currentValue}",
                                    CreatedBy = createdBy,
                                    CreatedAt = DateTimeOffset.UtcNow
                                };

                                newPreApplicationApplicantFileHistories.Add(newPreApplicationApplicantFileHistory);
                            }
                        }
                    }
                }
            }
            catch
            {
            }

            return newPreApplicationApplicantFileHistories;
        }
        private List<Entity.Entities.Portal.ApplicationExtraFeeHistory> GetApplicationExtraFeeHistories()
        {
            var newApplicationExtraFeeHistories = new List<Entity.Entities.Portal.ApplicationExtraFeeHistory>();

            try
            {
                var entitiesToCheck = new List<string>
            {
                nameof(Entity.Entities.Portal.ApplicationExtraFee),
            };
                var modifiedEntities = _dbContext.ChangeTracker.Entries().Where(p => (p.State == EntityState.Modified || p.State == EntityState.Added || p.State == EntityState.Deleted) && entitiesToCheck.Contains(p.Entity.GetType().Name)).ToList();
                var addedEntities = _dbContext.ChangeTracker.Entries().Where(p => p.State == EntityState.Added && entitiesToCheck.Contains(p.Entity.GetType().Name)).ToList();

                _dbContext.SaveChanges();
                int CreateBy = 0;
                foreach (var modifiedEntity in modifiedEntities)
                {
                    int applicationExtraFeeId = 0;
                    int createdBy = 0;
                    
                    var applicationExtraFee = modifiedEntity.Entity as Entity.Entities.Portal.ApplicationExtraFee;
                    Boolean firstInsertCheck = applicationExtraFee.Application.UpdatedAt != null;
                    if (modifiedEntity.Entity.GetType().Name == nameof(Entity.Entities.Portal.ApplicationExtraFee))
                    {
                        applicationExtraFeeId = applicationExtraFee.Id;
                        createdBy = applicationExtraFee.CreatedBy.GetValueOrDefault();
                    }

                    if (applicationExtraFeeId != 0 && createdBy != 0 && firstInsertCheck)
                    {
                        foreach (var prop in modifiedEntity.Properties)
                        {
                            var propertyName = prop.Metadata.Name;
                            var originalValue = prop.OriginalValue;
                            var currentValue = prop.CurrentValue;

                            if (propertyName == "ExtraFeeId")
                            {
                                Boolean addEntitesCheck = addedEntities.Any(q => q.Properties.Where(r => r.Metadata.Name == "ExtraFeeId").Any(k => k.CurrentValue.Equals(prop.CurrentValue)));
                                String originalVal = "N/A";
                                String currentVal = "N/A";
                                if(CreateBy == 0)
                                    CreateBy = createdBy;

                                if ($"{originalValue}" != $"{currentValue}" || !addEntitesCheck)
                                    originalVal = originalValue.ToString();
                                if ($"{currentValue}" != null && addEntitesCheck)
                                    currentVal = currentValue.ToString();
                                var newApplicationExtraFeeHistory = new Entity.Entities.Portal.ApplicationExtraFeeHistory
                                {
                                    ApplicationExtraFeeId = applicationExtraFeeId,
                                    PropertyName = propertyName,
                                    PreviousValue = originalVal,
                                    CurrentValue = currentVal,
                                    CreatedBy = CreateBy,
                                    CreatedAt = DateTimeOffset.UtcNow
                                };

                                newApplicationExtraFeeHistories.Add(newApplicationExtraFeeHistory);
                            }
                        }
                    }
                }
            }
            catch
            {
            }

            return newApplicationExtraFeeHistories;
        }

        private List<Entity.Entities.Portal.ApplicationVisaHistoryHistory> GetApplicationVisaHistoryHistories()
        {
            var newApplicationVisaHistoryHistories = new List<Entity.Entities.Portal.ApplicationVisaHistoryHistory>();

            try
            {
                var entitiesToCheck = new List<string>
            {
                nameof(Entity.Entities.Portal.ApplicationVisaHistory),
            };

                var modifiedEntities = _dbContext.ChangeTracker.Entries().Where(p => p.State == EntityState.Unchanged && entitiesToCheck.Contains(p.Entity.GetType().Name)).ToList();


                foreach (var modifiedEntity in modifiedEntities)
                {
                    int applicationVisaHistoryId = 0;
                    int createdBy = 0;

                    if (modifiedEntity.Entity.GetType().Name == nameof(Entity.Entities.Portal.ApplicationVisaHistory))
                    {
                        var applicationVisaHistory = modifiedEntity.Entity as Entity.Entities.Portal.ApplicationVisaHistory;
                        applicationVisaHistoryId = applicationVisaHistory.Id;
                        createdBy = applicationVisaHistory.Application.UpdatedBy.GetValueOrDefault();
                    }

                    if (applicationVisaHistoryId != 0 && createdBy != 0)
                    {
                        Boolean addEntitesCheck = modifiedEntity.Properties.Where(q => q.Metadata.Name == "IsActive").Any(k => $"{k.CurrentValue}"=="True");

                        foreach (var prop in modifiedEntity.Properties)
                        {
                            var propertyName = prop.Metadata.Name;
                            var originalValue = prop.OriginalValue;
                            var currentValue = prop.CurrentValue;


                            if (propertyName != "UpdatedAt" && propertyName != "Id" && propertyName != "ApplicationId" && propertyName != "IsActive" && propertyName != "IsDeleted"
                                && propertyName != "CreatedBy" && propertyName != "CreatedAt" && propertyName != "UpdatedBy" && propertyName != "DeletedBy" && propertyName != "DeletedAt"
                                && originalValue != null && currentValue != null)
                            {
                                String originalVal = "N/A";
                                String currentVal = "N/A";
                                if (!addEntitesCheck)
                                    originalVal = originalValue.ToString();
                                if (addEntitesCheck)
                                    currentVal = currentValue.ToString();

                                var newApplicationVisaHistoryHistory = new Entity.Entities.Portal.ApplicationVisaHistoryHistory
                                {
                                    ApplicationVisaHistoryId = applicationVisaHistoryId,
                                    PropertyName = propertyName,
                                    PreviousValue = originalVal,
                                    CurrentValue = currentVal,
                                    CreatedBy = createdBy,
                                    CreatedAt = DateTimeOffset.UtcNow
                                };

                                newApplicationVisaHistoryHistories.Add(newApplicationVisaHistoryHistory);
                            }
                        }
                    }
                }
            }
            catch { }

            return newApplicationVisaHistoryHistories;
        }

		private List<Entity.Entities.Portal.ApplicationConfirmationCodeHistory> GetApplicationConfirmationCodeHistories()
		{
			System.Diagnostics.Debug.WriteLine(_dbContext.ChangeTracker.Entries().FirstOrDefault(p => p.Entity.GetType().Name == "ApplicationConfirmationCode")?.State);
			var newApplicationConfirmationCodeHistories = new List<Entity.Entities.Portal.ApplicationConfirmationCodeHistory>();

			try
			{
				var entitiesToCheck = new List<string>
				{
					nameof(Entity.Entities.Portal.ApplicationConfirmationCode),
				};

				var modifiedEntities = _dbContext.ChangeTracker.Entries().Where(p => p.State is EntityState.Modified && entitiesToCheck.Contains(p.Entity.GetType().Name)).ToList();

				foreach (var modifiedEntity in modifiedEntities)
				{
					var applicationConfirmationCodeId = 0;
					var createdBy = 0;

					if (modifiedEntity.Entity.GetType().Name == nameof(Entity.Entities.Portal.ApplicationConfirmationCode))
					{
						var applicationConfirmationCode = modifiedEntity.Entity as Entity.Entities.Portal.ApplicationConfirmationCode;
						applicationConfirmationCodeId = applicationConfirmationCode.Id;
						createdBy = applicationConfirmationCode.UpdatedBy.GetValueOrDefault();
					}

					if (applicationConfirmationCodeId != 0 && createdBy != 0)
					{
						foreach (var prop in modifiedEntity.Properties)
						{
							var propertyName = prop.Metadata.Name;
							var originalValue = prop.OriginalValue;
							var currentValue = prop.CurrentValue;

							if (originalValue != null && currentValue != null && (propertyName is "SendCount" or "ConfirmationCode" or "ConfirmationTypeId") && $"{originalValue}" != $"{currentValue}")
							{
								var newApplicationVisaHistoryHistory = new Entity.Entities.Portal.ApplicationConfirmationCodeHistory
								{
									ApplicationConfirmationCodeId = applicationConfirmationCodeId,
									PropertyName = propertyName,
									PreviousValue = $"{originalValue}",
									CurrentValue = $"{currentValue}",
									CreatedBy = createdBy,
									CreatedAt = DateTimeOffset.UtcNow
								};

								newApplicationConfirmationCodeHistories.Add(newApplicationVisaHistoryHistory);
							}
						}
					}
				}
			}
			catch
			{
				// ignored
			}

			return newApplicationConfirmationCodeHistories;
		}

        private List<Entity.Entities.Portal.ApplicationLastMileDataHistory> GetApplicationLastMileDataHistories()
        {
            System.Diagnostics.Debug.WriteLine(_dbContext.ChangeTracker.Entries().FirstOrDefault(p => p.Entity.GetType().Name == "ApplicationLastMileData")?.State);
            var newApplicationLastMileDataHistories = new List<Entity.Entities.Portal.ApplicationLastMileDataHistory>();

            try
            {
                var entitiesToCheck = new List<string>
                {
                    nameof(Entity.Entities.Portal.ApplicationLastMileData),
                };

                var modifiedEntities = _dbContext.ChangeTracker.Entries().Where(p => p.State is EntityState.Modified && entitiesToCheck.Contains(p.Entity.GetType().Name)).ToList();

                foreach (var modifiedEntity in modifiedEntities)
                {
                    var applicationLastMileDataId = 0;
                    var createdBy = 0;

                    if (modifiedEntity.Entity.GetType().Name == nameof(Entity.Entities.Portal.ApplicationLastMileData))
                    {
                        var applicationLastMileData = modifiedEntity.Entity as Entity.Entities.Portal.ApplicationLastMileData;
                        applicationLastMileDataId = applicationLastMileData.Id;
                        createdBy = applicationLastMileData.UpdatedBy.GetValueOrDefault();
                    }

                    if (applicationLastMileDataId != 0 && createdBy != 0)
                    {
                        foreach (var prop in modifiedEntity.Properties)
                        {
                            var propertyName = prop.Metadata.Name;
                            var originalValue = prop.OriginalValue;
                            var currentValue = prop.CurrentValue;

                            if (propertyName is not ("UpdatedAt" or "UpdatedBy") && originalValue != null && currentValue != null && $"{originalValue}" != $"{currentValue}")
                            {
                                var newApplicationLastMileDataHistory = new Entity.Entities.Portal.ApplicationLastMileDataHistory
                                {
                                    ApplicationLastMileDataId = applicationLastMileDataId,
                                    PropertyName = propertyName,
                                    PreviousValue = $"{originalValue}",
                                    CurrentValue = $"{currentValue}",
                                    CreatedBy = createdBy,
                                    CreatedAt = DateTimeOffset.UtcNow
                                };

                                newApplicationLastMileDataHistories.Add(newApplicationLastMileDataHistory);
                            }
                        }
                    }
                }
            }
            catch
            {
                // ignored
            }

            return newApplicationLastMileDataHistories;
        }

        private void SetCreatedAtUnmodifiedForRejectionApprovalStatusHistory()
        {
            var modifiedEntries = _dbContext.ChangeTracker.Entries()
                .Where(e => e.State == EntityState.Modified)
                .ToList();

            foreach (var entry in modifiedEntries
                         .Where(entry => entry.Entity is ApplicationRejectionApprovalStatusHistory && entry.Entity.GetType().GetProperty("CreatedAt") != null))
            {
                entry.Property("CreatedAt").IsModified = false;
            }
        }

        private bool ShouldSkipVasTypeIdChange(string propertyName, object originalValue, object currentValue)
            => propertyName == "VasTypeId" && $"{originalValue}" == "0" && string.IsNullOrEmpty($"{currentValue}");

        #endregion

        #region IDisposable Members

        private bool disposed = false;

        protected virtual void Dispose(bool disposing)
        {
            if (!this.disposed)
            {
                if (disposing)
                {
                    _dbContext.Dispose();
                }
            }

            this.disposed = true;
        }

        public void Dispose()
        {
            Dispose(true);
            GC.SuppressFinalize(this);
        }

        #endregion

    }
}
