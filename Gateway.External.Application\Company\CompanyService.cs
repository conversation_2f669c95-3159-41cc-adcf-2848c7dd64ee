﻿using Gateway.Core.Pagination;
using Gateway.Extensions;
using Gateway.External.Application.Company.Dto;
using Gateway.External.Application.Company.Dto.Requests;
using Gateway.External.Application.Company.Dto.Response;
using Gateway.External.Application.Company.Validator;
using Gateway.External.Application.Enums;
using Gateway.External.Application.IdentityServer;
using Gateway.External.Application.IdentityServer.Dto;
using Gateway.External.Application.Lookup;
using Gateway.External.Application.Notification;
using Gateway.External.Application.Notification.Events;
using Gateway.External.Application.Slot.Dto;
using Gateway.External.Application.Socket.Dto.B2B;
using Gateway.External.Core.Context;
using Gateway.External.Entity.Entities.B2B.Application;
using Gateway.External.Entity.Entities.B2B.Company;
using Gateway.External.Entity.Entities.B2B.Company.CompanyFile;
using Gateway.External.Entity.Entities.B2B.CompanyUser;
using Gateway.External.Entity.Entities.B2B.CompanyUser.CompanyUserFile;
using Gateway.External.Persistence;
using Gateway.External.Resources;
using Gateway.Http;
using Gateway.ObjectStoring;
using Gateway.Redis;
using Gateway.Validation;
using Microsoft.EntityFrameworkCore;
using Microsoft.Extensions.Configuration;
using System;
using System.Collections.Generic;
using System.IO;
using System.Linq;
using System.Net;
using System.Text;
using System.Threading.Tasks;
using Serilog;
using Log = Serilog.Log;
using Portal.Gateway.Common.Utility.Helpers;
using static Gateway.External.Application.Enums.Enums;
using Gateway.External.Application.Extensions;
using EnumExtensions = Gateway.Extensions.EnumExtensions;

namespace Gateway.External.Application.Company
{
    public class CompanyService : ICompanyService
    {
        private static readonly ILogger Logger = Log.ForContext<CompanyService>();
        private readonly ApiDbContext _dbContext;
        private readonly IValidationService _validationService;
        private readonly IConfiguration _configuration;
        private readonly INotificationEventService _notificationEventService;
        private readonly IContext _context;
        private readonly IRedisClient _redisClient;
        private IFileStorage FileStorage { get; }
        public IdentityServerEndpointRouting IsRouting;
        public WebHeaderCollection Header;

        private readonly string _b2BBucketName;

        public CompanyService(IValidationService validationService, IConfiguration configuration, ApiDbContext dbContext, IFileStorage fileStorage, INotificationEventService notificationEventService, IContext context, IRedisClient redisClient)
        {
            _validationService = validationService;
            _dbContext = dbContext;
            _configuration = configuration;
            _notificationEventService = notificationEventService;
            _context = context;

            FileStorage = fileStorage;

            Header = new WebHeaderCollection();
            IsRouting = new IdentityServerEndpointRouting();
            _redisClient = redisClient;

            _b2BBucketName = configuration["MinioConfiguration:B2BBucketName"];
        }

        public async Task<GetCompanyAlertsResult> GetCompanyAlerts(GetCompanyAlertsRequest request)
        {
            var validationResult = _validationService.Validate(typeof(GetCompanyAlertsValidator), request);

            if (!validationResult.IsValid)
                return new GetCompanyAlertsResult
                {
                    Status = GetCompanyAlertStatus.InvalidInput,
                    Message = ServiceResources.INVALID_INPUT_ERROR,
                    ValidationMessages = validationResult.ErrorMessages
                };

            try
            {
                var companyAlerts = await _dbContext.CompanyAlert
                    .Include(i => i.CompanyAlertMessages)
                    .Where(w => w.CompanyId == request.CompanyId).AsNoTracking().ToListAsync();

                if (!companyAlerts.Any())
                    return new GetCompanyAlertsResult
                    {
                        Status = GetCompanyAlertStatus.NotFound,
                        Message = ServiceResources.RESOURCE_NOT_FOUND,
                    };

                var resultDto = companyAlerts.Select(s => new CompanyAlertDto
                {
                    Id = s.Id,
                    RedirectType = GetLookupValue((int)LookupType.RedirectType, s.RedirectTypeId),
                    Text = s.CompanyAlertMessages.FirstOrDefault(f => f.LanguageId == request.Context.LanguageId) != null ?
                        s.CompanyAlertMessages.FirstOrDefault(f => f.LanguageId == request.Context.LanguageId)?.Text :
                        s.CompanyAlertMessages.FirstOrDefault(f => f.LanguageId == 2)?.Text,
                    CreatedAt = s.CreatedAt.Value
                }).ToList();

                return new GetCompanyAlertsResult
                {
                    Status = GetCompanyAlertStatus.Successful,
                    Message = ServiceResources.RESOURCE_RETRIEVED,
                    Alerts = resultDto
                };
            }
            catch (Exception exc)
            {
                Logger.Error($@"Exception:{exc.Message}:{DateTime.Now}");

                return new GetCompanyAlertsResult
                {
                    Status = GetCompanyAlertStatus.InternalServerError,
                    Message = ServiceResources.INTERNAL_SERVER_ERROR
                };
            }
        }

        public async Task<DeleteCompanyAlertResult> DeleteCompanyAlert(DeleteCompanyAlertRequest request)
        {
            var validationResult = _validationService.Validate(typeof(DeleteCompanyAlertValidator), request);

            if (!validationResult.IsValid)
                return new DeleteCompanyAlertResult
                {
                    Status = DeleteCompanyAlertStatus.InvalidInput,
                    Message = ServiceResources.INVALID_INPUT_ERROR,
                    ValidationMessages = validationResult.ErrorMessages
                };

            //var alertKey = await _redisClient.GetAsync<AlertDto>($"b2b:alert:{request.CompanyId}"); //Socket ile çalışır ise

            //if(alertKey == null || alertKey.AlertCount == 0)
            //    return new DeleteCompanyAlertResult
            //    {
            //        Status = DeleteCompanyAlertStatus.NotFound,
            //        Message = ServiceResources.RESOURCE_NOT_FOUND
            //    };

            await using var transaction = await _dbContext.Database.BeginTransactionAsync();

            try
            {
                var alert = await _dbContext.CompanyAlert
                    .Include(i => i.CompanyAlertMessages)
                    .Where(w => w.Id == request.AlertId && w.CompanyId == request.CompanyId).FirstOrDefaultAsync();

                if (alert == null)
                    return new DeleteCompanyAlertResult
                    {
                        Status = DeleteCompanyAlertStatus.NotFound,
                        Message = ServiceResources.RESOURCE_NOT_FOUND
                    };

                alert.IsDeleted = true;
                alert.IsActive = false;
                alert.DeletedAt = DateTime.Now;
                alert.DeletedBy = request.Context.Identity.Email;

                _dbContext.CompanyAlert.Update(alert);

                await _dbContext.SaveChangesAsync();

                foreach (var alertCompanyAlertMessage in alert.CompanyAlertMessages)
                {
                    alertCompanyAlertMessage.IsActive = false;
                    alertCompanyAlertMessage.IsDeleted = true;
                    alertCompanyAlertMessage.DeletedAt = DateTime.Now;
                    alertCompanyAlertMessage.DeletedBy = request.Context.Identity.Email;

                    _dbContext.CompanyAlertMessage.Update(alertCompanyAlertMessage);

                    await _dbContext.SaveChangesAsync();
                }

                await transaction.CommitAsync();

                //alertKey.AlertCount--;

                //await _redisClient.AddTimelessAsync($"b2b:alert:{request.CompanyId}", alertKey); //Socket ile çalışır ise

                return new DeleteCompanyAlertResult
                {
                    Status = DeleteCompanyAlertStatus.Successful,
                    Message = ServiceResources.RESOURCE_DELETED,
                    Id = alert.Id
                };

            }
            catch (Exception exc)
            {
                Logger.Error($@"Exception:{exc.Message}:{DateTime.Now}");

                return new DeleteCompanyAlertResult
                {
                    Status = DeleteCompanyAlertStatus.InternalServerError,
                    Message = ServiceResources.INTERNAL_SERVER_ERROR
                };
            }
        }

        public async Task<AddCompanyResult> AddCompany(AddCompanyRequest request)
        {
            var validationResult = _validationService.Validate(typeof(AddCompanyValidator), request);

            if (!validationResult.IsValid)
                return new AddCompanyResult
                {
                    Status = AddCompanyStatus.InvalidInput,
                    Message = ServiceResources.INVALID_INPUT_ERROR,
                    ValidationMessages = validationResult.ErrorMessages
                };

            await using var transaction = await _dbContext.Database.BeginTransactionAsync();

            try
            {
                var existingCompany = await _dbContext.Company
                    .Where(r => r.Email.Equals(request.Email))
                    .AnyAsync();

                if (existingCompany)
                    return new AddCompanyResult
                    {
                        Status = AddCompanyStatus.AlreadyExist,
                        Message = ServiceResources.RESOURCE_ALREADY_REGISTERED,
                    };

                var queryCompanyUser = await _dbContext.CompanyUser
                    .Where(r => r.Email.Equals(request.Email)).AsNoTracking()
                    .AnyAsync();

                if (queryCompanyUser)
                    return new AddCompanyResult
                    {
                        Status = AddCompanyStatus.AlreadyExist,
                        Message = ServiceResources.USER_ALREADY_REGISTERED,
                    };

                var companyEntity = new Entity.Entities.B2B.Company.Company()
                {
                    CommercialName = request.CommercialName,
                    TaxNumber = request.TaxNumber,
                    PhoneNumber = request.PhoneNumber,
                    Email = request.Email,
                    CreatedBy = request.Email,
                    CreatedAt = DateTime.Now
                };

                var entityCompanyResponse = await _dbContext.Company.AddAsync(companyEntity);

                await _dbContext.SaveChangesAsync();

                var companyUserEntity = new CompanyUser()
                {
                    CompanyId = entityCompanyResponse.Entity.Id,
                    IsAuthorizedUser = true,
                    Name = request.AuthorizedUser.Name,
                    Surname = request.AuthorizedUser.Surname,
                    PhoneNumber = request.PhoneNumber,
                    Email = request.Email,
                    StatusId = (int)Enums.Enums.B2BApprovalStatus.Approved,
                    CreatedBy = request.Email,
                    CreatedAt = DateTime.Now,
                };

                await _dbContext.CompanyUser.AddAsync(companyUserEntity);

                await _dbContext.SaveChangesAsync();

                #region completeRate

                companyUserEntity.ProfileCompletedPercentage = await CalculateCompletionRate(companyEntity, companyUserEntity, null, null);

                _dbContext.CompanyUser.Update(companyUserEntity);

                await _dbContext.SaveChangesAsync();

                #endregion

                #region is Register

                var existingCompanyAuthorizedRole = await _dbContext.CompanyRoles
                    .Where(r => r.RoleName.Equals("AdminUser"))
                    .FirstOrDefaultAsync();

                var iSResponse = await IdentityServerPublicRegister(request.Email, request.Password, existingCompanyAuthorizedRole == null ? null : new List<RoleAssignDto>
                {
                    new()
                    {
                        Id = existingCompanyAuthorizedRole.ExternalId,
                        RoleName = existingCompanyAuthorizedRole.RoleName,
                    }
                });

                if (iSResponse.Status != "SUCCESS")
                {
                    await transaction.RollbackAsync();

                    return new AddCompanyResult
                    {
                        Status = AddCompanyStatus.ExternalServiceError,
                        Message = iSResponse.Message,
                        ValidationMessages = iSResponse.ValidationMessages
                    };
                }

                #endregion

                await transaction.CommitAsync();

                return new AddCompanyResult
                {
                    Status = AddCompanyStatus.Successful,
                    Message = ServiceResources.RESOURCE_CREATED,
                    Id = entityCompanyResponse.Entity.Id
                };
            }
            catch (Exception exc)
            {
                Logger.Error($@"Exception:{exc.Message}:{DateTime.Now}");

                await transaction.RollbackAsync();
                return new AddCompanyResult
                {
                    Status = AddCompanyStatus.InternalServerError,
                    Message = ServiceResources.INTERNAL_SERVER_ERROR
                };
            }
        }

        public async Task<GetCompanyResult> GetCompany(GetCompanyRequest request)
        {
            var validationResult = _validationService.Validate(typeof(GetCompanyValidator), request);

            if (!validationResult.IsValid)
                return new GetCompanyResult
                {
                    Status = GetCompanyStatus.InvalidInput,
                    Message = ServiceResources.INVALID_INPUT_ERROR,
                    ValidationMessages = validationResult.ErrorMessages
                };

            try
            {
                var companyEntity =
                await _dbContext.Company
                    .Include(i => i.CompanyUsers)
                    .Include(i => i.CompanyAlerts)
                    .AsNoTracking()
                    .FirstOrDefaultAsync(r => r.Id == request.CompanyId);

                if (companyEntity == null)
                    return new GetCompanyResult
                    {
                        Status = GetCompanyStatus.NotFound,
                        Message = ServiceResources.RESOURCE_NOT_FOUND,
                    };

                var authorizedUser = companyEntity.CompanyUsers.FirstOrDefault(r => r.IsAuthorizedUser);

                if (authorizedUser == null)
                    return new GetCompanyResult
                    {
                        Status = GetCompanyStatus.NotFound,
                        Message = ServiceResources.USER_NOT_FOUND,
                    };

                var companyApprovalEntity = await _dbContext.CompanyProfileApproval
                    .FirstOrDefaultAsync(r => r.CompanyId == request.CompanyId && (r.ApprovalStatusId == (int)Enums.Enums.B2BApprovalStatus.WaitingApproval || r.ApprovalStatusId == (int)Enums.Enums.B2BApprovalStatus.Revision));

                var result = new GetCompanyResult()
                {
                    Company = new CompanyDto()
                    {
                        Id = companyEntity.Id,
                        CommercialName = companyEntity.CommercialName,
                        TaxNumber = companyEntity.TaxNumber,
                        Email = companyEntity.Email,
                        PhoneNumber = companyEntity.PhoneNumber,
                        CountryId = companyEntity.CountryId,
                        CityId = companyEntity.CityId,
                        Address = companyEntity.Address,
                        CompanyTypeId = companyEntity.CompanyTypeId,
                        HasApprovalRequest = companyApprovalEntity != null,
                        ApprovalStatus = companyApprovalEntity != null ? GetLookupValue((int)LookupType.CompanyApprovalStatus, companyApprovalEntity.ApprovalStatusId) : null,
                        AlertCount = companyEntity.CompanyAlerts.Count,
                        AuthorizedUser = new AuthorizedUserDto()
                        {
                            Name = authorizedUser.Name,
                            Surname = authorizedUser.Surname,
                            PositionId = authorizedUser.PositionId
                        }
                    }
                };

                return new GetCompanyResult
                {
                    Status = GetCompanyStatus.Successful,
                    Message = ServiceResources.RESOURCE_RETRIEVED,
                    Company = result.Company
                };
            }
            catch (Exception exc)
            {
                Logger.Error($@"Exception:{exc.Message}:{DateTime.Now}");

                return new GetCompanyResult
                {
                    Status = GetCompanyStatus.InternalServerError,
                    Message = ServiceResources.INTERNAL_SERVER_ERROR
                };
            }
        }

        public async Task<AddCompanyProfileApprovalResult> AddCompanyProfileApproval(AddCompanyProfileApprovalRequest request)
        {
            var validationResult = _validationService.Validate(typeof(AddCompanyProfileApprovalValidator), request);

            if (!validationResult.IsValid)
                return new AddCompanyProfileApprovalResult
                {
                    Status = AddCompanyProfileApprovalStatus.InvalidInput,
                    Message = ServiceResources.INVALID_INPUT_ERROR,
                    ValidationMessages = validationResult.ErrorMessages
                };

            await using var transaction = await _dbContext.Database.BeginTransactionAsync();

            try
            {
                var existingCompany = await _dbContext.Company
                    .Include(i => i.CompanyFiles)
                    .Where(r => r.Id == request.CompanyId).FirstOrDefaultAsync();

                if (existingCompany == null)
                    return new AddCompanyProfileApprovalResult
                    {
                        Status = AddCompanyProfileApprovalStatus.NotFound,
                        Message = ServiceResources.RESOURCE_NOT_FOUND,
                    };

                var existingCompanyAuthorizedUser = await _dbContext.CompanyUser
                    .Include(i => i.CompanyUserFiles)
                    .Where(r => r.CompanyId == existingCompany.Id && r.IsAuthorizedUser).FirstOrDefaultAsync();

                if (existingCompanyAuthorizedUser == null)
                    return new AddCompanyProfileApprovalResult
                    {
                        Status = AddCompanyProfileApprovalStatus.NotFound,
                        Message = ServiceResources.USER_NOT_FOUND,
                    };

                var companyUserBranches = await _dbContext.CompanyUserBranch
                    .Where(s => s.CompanyUserId == existingCompanyAuthorizedUser.Id && !s.IsDeleted &&
                                s.StatusId == (int)Enums.Enums.B2BApprovalStatus.Approved)
                    .ToListAsync();

                var companyApprovalEntity = new CompanyProfileApproval()
                {
                    CompanyId = request.CompanyId,
                    PhoneNumber = request.PhoneNumber,
                    Address = request.Address,
                    CompanyTypeId = request.CompanyTypeId,
                    CountryId = request.CountryId,
                    CityId = request.CityId,
                    AuthorizedUserName = request.AuthorizedUser.Name,
                    AuthorizedUserSurname = request.AuthorizedUser.Surname,
                    AuthorizedUserPositionId = request.AuthorizedUser.PositionId,
                    ApprovalStatusId = (int)Enums.Enums.B2BApprovalStatus.WaitingApproval,
                    CreatedBy = request.Context.Identity.Email,
                    CreatedAt = DateTime.Now,
                };

                var companyApprovalResult = await _dbContext.CompanyProfileApproval.AddAsync(companyApprovalEntity);

                await _dbContext.SaveChangesAsync();

                var companyFiles = new List<CompanyFile>();

                foreach (var file in request.Files)
                {
                    var name = $"{Guid.NewGuid()}-{file.FileData.FileName}";
                    var stream = new MemoryStream(await file.FileData.GetFileBytes());
                    var result = await FileStorage.SaveFileAsync(_b2BBucketName, $"company/{request.CompanyId}/{file.FileTypeId}/{name}", stream, "");

                    if (!result)
                        return new AddCompanyProfileApprovalResult
                        {
                            Status = AddCompanyProfileApprovalStatus.InternalServerError,
                            Message = ServiceResources.FILE_OPERATION_FAILED
                        };

                    var fileEntity = new CompanyFile()
                    {
                        CompanyId = request.CompanyId,
                        CompanyProfileApprovalId = companyApprovalResult.Entity.Id,
                        FileName = file.FileData.FileName,
                        StorageName = name,
                        Extension = Path.GetExtension(file.FileData.FileName),
                        FileTypeId = file.FileTypeId,
                        CreatedAt = DateTime.Now,
                        CreatedBy = request.Context.Identity.Email,
                        StatusId = (int)Enums.Enums.B2BApprovalStatus.WaitingApproval,
                        IsActive = true
                    };

                    companyFiles.Add(fileEntity);
                }

                foreach (var branchId in request.BranchIds)
                {
                    if (!companyUserBranches.Exists(s => s.BranchId == branchId))
                    {
                        await _dbContext.CompanyUserBranch.AddAsync(new CompanyUserBranch()
                        {
                            CompanyUserId = existingCompanyAuthorizedUser.Id,
                            BranchId = branchId,
                            StatusId = (int)Enums.Enums.B2BApprovalStatus.WaitingApproval,
                            CreatedAt = DateTime.Now,
                            CreatedBy = request.Context.Identity.Email,
                            IsActive = true
                        });

                        await _dbContext.SaveChangesAsync();
                    }
                }

                if (companyFiles.Any())
                {
                    await _dbContext.CompanyFile.AddRangeAsync(companyFiles);
                    await _dbContext.SaveChangesAsync();
                }

                #region Completion rate

                companyApprovalEntity.ProfileCompletedPercentage = await CalculateCompletionRate(existingCompany,
                    existingCompanyAuthorizedUser,
                    existingCompany.CompanyFiles,
                    existingCompanyAuthorizedUser.CompanyUserFiles);

                _dbContext.CompanyProfileApproval.Update(companyApprovalEntity);

                await _dbContext.SaveChangesAsync();

                #endregion

                await transaction.CommitAsync();

                return new AddCompanyProfileApprovalResult
                {
                    Status = AddCompanyProfileApprovalStatus.Successful,
                    Message = ServiceResources.RESOURCE_UPDATED,
                    Id = existingCompany.Id
                };
            }
            catch (Exception exc)
            {
                Logger.Error($@"Exception:{exc.Message}:{DateTime.Now}");

                await transaction.RollbackAsync();
                return new AddCompanyProfileApprovalResult
                {
                    Status = AddCompanyProfileApprovalStatus.InternalServerError,
                    Message = ServiceResources.INTERNAL_SERVER_ERROR
                };
            }
        }

        public async Task<GetProfileResult> GetProfile(GetProfileRequest request)
        {
            try
            {
                var companyUserEntity = await _dbContext.CompanyUser
               .Include(i => i.Company)
               .ThenInclude(i => i.CompanyAlerts)
               .Where(r => r.Email.Equals(request.Context.Identity.Email))
               .AsNoTracking()
               .FirstOrDefaultAsync();

                if (companyUserEntity == null)
                    return new GetProfileResult
                    {
                        Status = GetProfileStatus.NotFound,
                        Message = ServiceResources.RESOURCE_NOT_FOUND,
                    };

                var authorizedUser = await _dbContext.CompanyUser
                    .Where(r => r.CompanyId == companyUserEntity.CompanyId && r.IsAuthorizedUser)
                    .FirstOrDefaultAsync();

                if (authorizedUser == null)
                    return new GetProfileResult
                    {
                        Status = GetProfileStatus.NotFound,
                        Message = ServiceResources.AUTHORIZED_USER_NOT_FOUND,
                    };

                var companyApprovalEntity = await _dbContext.CompanyProfileApproval
                    .FirstOrDefaultAsync(r => r.CompanyId == companyUserEntity.CompanyId && (r.ApprovalStatusId == (int)Enums.Enums.B2BApprovalStatus.WaitingApproval || r.ApprovalStatusId == (int)Enums.Enums.B2BApprovalStatus.Revision));

                var result = new GetProfileResult()
                {
                    CompanyUser = new CompanyUserDto()
                    {
                        Id = companyUserEntity.Id,
                        Name = companyUserEntity.Name,
                        Surname = companyUserEntity.Surname,
                        Email = companyUserEntity.Email,
                        PhoneNumber = companyUserEntity.PhoneNumber,
                        Status = GetLookupValue((int)LookupType.CompanyApprovalStatus, companyUserEntity.StatusId),
                        IsVerifiedAccount = companyUserEntity.IsVerifiedAccount,
                        IsAuthorizedUser = companyUserEntity.IsAuthorizedUser,
                        ProfileCompletedPercentage = companyUserEntity.ProfileCompletedPercentage,
                        PositionId = companyUserEntity.PositionId,
                        ProfilePicture = companyUserEntity.ProfilePicture,
                        Company = new CompanyDto()
                        {
                            Id = companyUserEntity.Company.Id,
                            TaxNumber = companyUserEntity.Company.TaxNumber,
                            CommercialName = companyUserEntity.Company.CommercialName,
                            Address = companyUserEntity.Company.Address,
                            CountryId = companyUserEntity.Company.CountryId,
                            CityId = companyUserEntity.Company.CityId,
                            CompanyTypeId = companyUserEntity.Company.CompanyTypeId,
                            HasApprovalRequest = companyApprovalEntity != null,
                            ApprovalStatus = companyApprovalEntity != null ? GetLookupValue((int)LookupType.CompanyApprovalStatus, companyApprovalEntity.ApprovalStatusId) : null,
                            Email = companyUserEntity.Company.Email,
                            PhoneNumber = companyUserEntity.Company.PhoneNumber,
                            AlertCount = companyUserEntity.Company.CompanyAlerts.Count,
                            AuthorizedUser = new AuthorizedUserDto()
                            {
                                Name = authorizedUser.Name,
                                Surname = authorizedUser.Surname,
                                PositionId = authorizedUser.PositionId
                            },
                        }
                    }
                };

                if (!companyUserEntity.ProfilePicture.IsNullOrWhitespace())
                {
                    var profilePicture =
                        await FileStorage.GetFileStreamAsync(_b2BBucketName, $"companyuser/{companyUserEntity.Id}/ProfileImage/{companyUserEntity.ProfilePicture}");

                    if (profilePicture != null)
                    {
                        result.CompanyUser.ProfilePicture = Gateway.Extensions.StringExtensions.ConvertToBase64(profilePicture);
                    }
                }

                return new GetProfileResult
                {
                    Status = GetProfileStatus.Successful,
                    Message = ServiceResources.RESOURCE_RETRIEVED,
                    CompanyUser = result.CompanyUser
                };
            }
            catch (Exception exc)
            {
                Logger.Error($@"Exception:{exc.Message}:{DateTime.Now}");

                return new GetProfileResult
                {
                    Status = GetProfileStatus.InternalServerError,
                    Message = ServiceResources.INTERNAL_SERVER_ERROR
                };
            }
        }

        public async Task<ChangeUserProfilePictureResult> ChangeUserProfilePicture(ChangeUserProfilePictureRequest request)
        {
            var validationResult = _validationService.Validate(typeof(ChangeUserProfilePictureValidator), request);

            if (!validationResult.IsValid)
                return new ChangeUserProfilePictureResult
                {
                    Status = ChangeUserProfilePictureStatus.InvalidInput,
                    Message = ServiceResources.INVALID_INPUT_ERROR,
                    ValidationMessages = validationResult.ErrorMessages
                };

            await using var transaction = await _dbContext.Database.BeginTransactionAsync();

            try
            {
                var existingCompanyUser = await _dbContext.CompanyUser
                    .Include(i => i.CompanyUserFiles)
                    .Where(r => r.Email.Equals(request.Context.Identity.Email))
                    .FirstOrDefaultAsync();

                if (existingCompanyUser == null)
                    return new ChangeUserProfilePictureResult
                    {
                        Status = ChangeUserProfilePictureStatus.NotFound,
                        Message = ServiceResources.USER_NOT_FOUND
                    };

                var existingCompany = await _dbContext.Company
                    .Include(i => i.CompanyFiles)
                    .Where(r => r.Id == existingCompanyUser.CompanyId)
                    .FirstOrDefaultAsync();

                if (existingCompany == null)
                    return new ChangeUserProfilePictureResult
                    {
                        Status = ChangeUserProfilePictureStatus.NotFound,
                        Message = ServiceResources.RESOURCE_NOT_FOUND
                    };

                var name = $"{Guid.NewGuid()}-{request.FileData.FileName}";
                var stream = new MemoryStream(await request.FileData.GetFileBytes());
                var result = await FileStorage.SaveFileAsync(_b2BBucketName, $"companyuser/{existingCompanyUser.Id}/ProfileImage/{name}", stream, "");

                if (!result)
                    return new ChangeUserProfilePictureResult
                    {
                        Status = ChangeUserProfilePictureStatus.InternalServerError,
                        Message = ServiceResources.INTERNAL_SERVER_ERROR
                    };

                existingCompanyUser.ProfilePicture = name;
                _dbContext.CompanyUser.Update(existingCompanyUser);
                await _dbContext.SaveChangesAsync();

                existingCompanyUser.ProfileCompletedPercentage = await CalculateCompletionRate(existingCompany,
                    existingCompanyUser,
                    existingCompany.CompanyFiles,
                    existingCompanyUser.CompanyUserFiles);

                _dbContext.CompanyUser.Update(existingCompanyUser);

                await _dbContext.SaveChangesAsync();

                await transaction.CommitAsync();

                return new ChangeUserProfilePictureResult
                {
                    Status = ChangeUserProfilePictureStatus.Successful,
                    Message = ServiceResources.RESOURCE_UPDATED
                };
            }
            catch (Exception exc)
            {
                Logger.Error($@"Exception:{exc.Message}:{DateTime.Now}");

                await transaction.RollbackAsync();
                return new ChangeUserProfilePictureResult
                {
                    Status = ChangeUserProfilePictureStatus.InternalServerError,
                    Message = ServiceResources.INTERNAL_SERVER_ERROR
                };
            }
        }

        public async Task<GetCompanyUserBranchesResult> GetCompanyUserBranchesByCountry(GetCompanyUserBranchesByCountryRequest request)
        {
            var validationResult = _validationService.Validate(typeof(GetCompanyUserBranchesByCountryValidator), request);

            if (!validationResult.IsValid)
                return new GetCompanyUserBranchesResult
                {
                    Status = GetCompanyUserBranchesStatus.InvalidInput,
                    Message = ServiceResources.INVALID_INPUT_ERROR,
                    ValidationMessages = validationResult.ErrorMessages
                };

            try
            {
                var userEntity = await _dbContext.CompanyUser
                    .Where(r => r.CompanyId == request.CompanyId && r.Id == request.UserId)
                    .AsNoTracking()
                    .FirstOrDefaultAsync();

                if (userEntity == null)
                    return new GetCompanyUserBranchesResult
                    {
                        Status = GetCompanyUserBranchesStatus.NotFound,
                        Message = ServiceResources.USER_NOT_FOUND,
                    };

                var countryEntity = await _dbContext.Country.Where(r => r.Id == request.CountryId).AsNoTracking().FirstOrDefaultAsync();

                if (countryEntity == null)
                    return new GetCompanyUserBranchesResult
                    {
                        Status = GetCompanyUserBranchesStatus.NotFound,
                        Message = ServiceResources.COUNTRY_NOT_FOUND,
                    };

                var entityCompanyUserBranches = await _dbContext.CompanyUserBranch
                    .Include(i => i.Branch)
                    .ThenInclude(i => i.BranchTranslations)
                    .Where(r => r.CompanyUserId == userEntity.Id && r.Branch.CountryId == request.CountryId && r.StatusId == (int)Enums.Enums.B2BApprovalStatus.Approved && !r.IsDeleted)
                    .AsNoTracking()
                    .ToListAsync();

                if (!entityCompanyUserBranches.Any())
                    return new GetCompanyUserBranchesResult
                    {
                        Status = GetCompanyUserBranchesStatus.NotFound,
                        Message = ServiceResources.RESOURCE_NOT_FOUND,
                    };

                var result = new GetCompanyUserBranchesResult()
                {
                    CompanyUserBranches = entityCompanyUserBranches.Select(s => new CompanyUserBranchDto()
                    {
                        Id = s.BranchId,
                        Name = s.Branch.BranchTranslations.FirstOrDefault(r => r.LanguageId == request.Context.LanguageId) != null ?
                            s.Branch.BranchTranslations.FirstOrDefault(r => r.LanguageId == request.Context.LanguageId)?.Name :
                            s.Branch.BranchTranslations.FirstOrDefault()?.Name
                    }).ToList()
                };

                return new GetCompanyUserBranchesResult
                {
                    Status = GetCompanyUserBranchesStatus.Successful,
                    Message = ServiceResources.RESOURCE_RETRIEVED,
                    CompanyUserBranches = result.CompanyUserBranches
                };
            }
            catch (Exception exc)
            {
                Logger.Error($@"Exception:{exc.Message}:{DateTime.Now}");

                return new GetCompanyUserBranchesResult
                {
                    Status = GetCompanyUserBranchesStatus.InternalServerError,
                    Message = ServiceResources.INTERNAL_SERVER_ERROR
                };
            }
        }

        public async Task<GetCompanyUserBranchesResult> GetCompanyUserBranches(GetCompanyUserBranchesRequest request)
        {
            var validationResult = _validationService.Validate(typeof(GetCompanyUserBranchesValidator), request);

            if (!validationResult.IsValid)
                return new GetCompanyUserBranchesResult
                {
                    Status = GetCompanyUserBranchesStatus.InvalidInput,
                    Message = ServiceResources.INVALID_INPUT_ERROR,
                    ValidationMessages = validationResult.ErrorMessages
                };

            try
            {
                var userEntity = await _dbContext.CompanyUser
                    .Where(r => r.CompanyId == request.CompanyId && r.Id == request.UserId)
                    .AsNoTracking()
                    .FirstOrDefaultAsync();

                if (userEntity == null)
                    return new GetCompanyUserBranchesResult
                    {
                        Status = GetCompanyUserBranchesStatus.NotFound,
                        Message = ServiceResources.USER_NOT_FOUND,
                    };

                var entityCompanyUserBranches = await _dbContext.CompanyUserBranch
                    .Include(i => i.Branch)
                    .ThenInclude(i => i.BranchTranslations)
                    .Where(r => r.CompanyUserId == userEntity.Id && r.StatusId == (int)Enums.Enums.B2BApprovalStatus.Approved && !r.IsDeleted)
                    .AsNoTracking()
                    .ToListAsync();

                if (!entityCompanyUserBranches.Any())
                    return new GetCompanyUserBranchesResult
                    {
                        Status = GetCompanyUserBranchesStatus.NotFound,
                        Message = ServiceResources.RESOURCE_NOT_FOUND,
                    };

                var result = new GetCompanyUserBranchesResult()
                {
                    CompanyUserBranches = entityCompanyUserBranches.Select(s => new CompanyUserBranchDto()
                    {
                        Id = s.BranchId,
                        Name = s.Branch.BranchTranslations.FirstOrDefault(r => r.LanguageId == request.Context.LanguageId) != null ?
                            s.Branch.BranchTranslations.FirstOrDefault(r => r.LanguageId == request.Context.LanguageId)?.Name :
                            s.Branch.BranchTranslations.FirstOrDefault()?.Name
                    }).ToList()
                };

                return new GetCompanyUserBranchesResult
                {
                    Status = GetCompanyUserBranchesStatus.Successful,
                    Message = ServiceResources.RESOURCE_RETRIEVED,
                    CompanyUserBranches = result.CompanyUserBranches
                };
            }
            catch (Exception exc)
            {
                Logger.Error($@"Exception:{exc.Message}:{DateTime.Now}");

                return new GetCompanyUserBranchesResult
                {
                    Status = GetCompanyUserBranchesStatus.InternalServerError,
                    Message = ServiceResources.INTERNAL_SERVER_ERROR
                };
            }
        }

        public async Task<GetPaginatedCompanyApplicationsResult> GetPaginatedCompanyApplications(GetPaginatedCompanyApplicationsRequest request)
        {
            var validationResult = _validationService.Validate(typeof(GetPaginatedCompanyApplicationsValidator), request);

            if (!validationResult.IsValid)
                return new GetPaginatedCompanyApplicationsResult
                {
                    Status = GetPaginatedCompanyApplicationsStatus.InvalidInput,
                    Message = ServiceResources.INVALID_INPUT_ERROR,
                    ValidationMessages = validationResult.ErrorMessages
                };

            try
            {
                var queryApplications = _dbContext.Application
                .Include(i => i.ApplicationDocument)
                .Include(i => i.ApplicationStatusHistories)
                .ThenInclude(i => i.ApplicationStatus)
                .ThenInclude(i => i.ApplicationStatusTranslations)
                .Include(i => i.BranchApplicationCountry)
                .ThenInclude(i => i.Branch)
                .ThenInclude(i => i.BranchTranslations)
                .Include(i => i.BranchApplicationCountry)
                .ThenInclude(i => i.Branch)
                .ThenInclude(i => i.Country)
                .Include(i => i.PreApplicationApplicant)
                .ThenInclude(i => i.PreApplication)
                .Include(i => i.ApplicationPnlFiles)
                .Where(r => r.IsActive && !r.IsDeleted && r.PreApplicationApplicant.PreApplication.CompanyId == request.CompanyId).AsNoTracking();

                if (request.Filters != null)
                {
                    if (!request.Filters.Name.IsNullOrWhitespace())
                        queryApplications = queryApplications.Where(r => r.Name.Contains(request.Filters.Name));

                    if (!request.Filters.Surname.IsNullOrWhitespace())
                        queryApplications = queryApplications.Where(r => r.Surname.Contains(request.Filters.Surname));

                    if (!request.Filters.PassportNumber.IsNullOrWhitespace())
                        queryApplications = queryApplications.Where(r => r.PassportNumber.Contains(request.Filters.PassportNumber));

                    if (request.Filters.BranchId.IsNumericAndGreaterThenZero())
                        queryApplications = queryApplications.Where(r => r.BranchApplicationCountry.BranchId == request.Filters.BranchId);

                    if (!request.Filters.ApplicationNumber.IsNullOrWhitespace())
                        queryApplications = queryApplications.Where(r => r.Id.ToString().PadLeft(13, '0').Contains(request.Filters.ApplicationNumber));

                    if (request.Filters.ApplicationStatusId.IsNumericAndGreaterThenZero())
                        queryApplications = queryApplications.Where(r =>
                            r.ApplicationStatusHistories.Where(s => s.IsActive && !s.IsDeleted).OrderByDescending(x => x.CreatedAt).First().ApplicationStatusId ==
                            request.Filters.ApplicationStatusId);

                    if (request.Filters.ApplicationTime.HasValue && request.Filters.ApplicationTime.IsDatetimeTrueFormat())
                        queryApplications = queryApplications.Where(r => r.ApplicationTime >= request.Filters.ApplicationTime.Value.Date && r.ApplicationTime < request.Filters.ApplicationTime.Value.AddDays(1).Date);

                    if (request.Filters.EntryDate.HasValue && request.Filters.EntryDate.IsDatetimeTrueFormat())
                        queryApplications = queryApplications.Where(r => r.ApplicationDocument.EntryDate.Date == request.Filters.EntryDate.Value.Date);

                    if (request.Filters.ExitDate.HasValue && request.Filters.ExitDate.IsDatetimeTrueFormat())
                        queryApplications = queryApplications.Where(r => r.ApplicationDocument.ExitDate.Date == request.Filters.ExitDate.Value.Date);

                    if (request.Filters.PnlStatusId.IsNumericAndGreaterThenZero())
                        queryApplications = request.Filters.PnlStatusId == (int)Enums.Enums.PnlFileStatus.NotUploaded ? queryApplications.Where(r => r.ApplicationPnlFiles.All(s => s.CompanyId != request.CompanyId)) : queryApplications.Where(r => r.ApplicationPnlFiles.Any(s => s.StatusId == request.Filters.PnlStatusId && s.CompanyId == request.CompanyId));
                }

                var applications = await queryApplications.ToListAsync();

                var result = new GetPaginatedCompanyApplicationsResult
                {
                    CompanyApplications = applications.Select(p => new CompanyApplicationDto()
                    {
                        Id = p.Id,
                        ApplicationNumber = p.Id.ToString().PadLeft(13, '0'),
                        Branch = new LookupValue()
                        {
                            Id = p.BranchApplicationCountry.Branch.Id.ToString(),
                            DisplayValue = p.BranchApplicationCountry.Branch.BranchTranslations
                                               .FirstOrDefault(branchTranslation => branchTranslation.LanguageId == _context.LanguageId)?.Name ??
                                           p.BranchApplicationCountry.Branch.BranchTranslations.FirstOrDefault(r => r.LanguageId == 2)?.Name,
                        },
                        Country = new LookupValue()
                        {
                            Id = p.BranchApplicationCountry.Branch.Country.Id.ToString(),
                            DisplayValue = GetCountryName(p.BranchApplicationCountry.Branch.Country, _context.LanguageId)
                        },
                        Name = p.Name,
                        Surname = p.Surname,
                        NameSurname = $"{p.Name} {p.Surname}",
                        PassportNumber = p.PassportNumber,
                        ApplicationTime = p.ApplicationTime.Date.ToString("dd/MM/yyyy"),
                        ApplicantType = GetLookupValue((int)LookupType.ApplicantType, p.ApplicantTypeId),
                        AppointmentCategory = GetLookupValue((int)LookupType.VasType, p.PreApplicationApplicant.PreApplication.VasTypeId),
                        ApplicationStatus = new LookupValue()
                        {
                            Id = p.ApplicationStatusHistories.Where(r => r.IsActive && !r.IsDeleted).MaxBy(o => o.Id)?.ApplicationStatusId.ToString(),
                            DisplayValue = p.ApplicationStatusHistories.Where(r => r.IsActive && !r.IsDeleted).MaxBy(o => o.Id)?
                                .ApplicationStatus.ApplicationStatusTranslations.FirstOrDefault(w => w.IsActive && !w.IsDeleted && w.LanguageId == _context.LanguageId) == null ? p.ApplicationStatusHistories.Where(r => r.IsActive && !r.IsDeleted).MaxBy(o => o.Id)?
                                .ApplicationStatus.ApplicationStatusTranslations.FirstOrDefault(w => w.IsActive && !w.IsDeleted && w.LanguageId == 2)?.Name :
                                p.ApplicationStatusHistories.Where(r => r.IsActive && !r.IsDeleted).MaxBy(o => o.Id)?
                                .ApplicationStatus.ApplicationStatusTranslations.FirstOrDefault(w => w.IsActive && !w.IsDeleted && w.LanguageId == _context.LanguageId)?.Name,
                        },
                        Status = GetLookupValue((int)LookupType.ApplicationActiveStatus, p.ActiveStatusId),
                        EntryDate = p.ApplicationDocument.EntryDate.Date.ToString("dd/MM/yyyy"),
                        ExitDate = p.ApplicationDocument.ExitDate.Date.ToString("dd/MM/yyyy"),
                        LastUploadDate = p.ApplicationDocument.ExitDate.AddDays(5).Date.ToString("dd/MM/yyyy"),
                        PnlFileName = p.ApplicationPnlFiles.Any() ? p.ApplicationPnlFiles.Where(r => r.IsActive && !r.IsDeleted).MaxBy(o => o.Id)?.FileName : null,
                        PnlStatus = p.ApplicationPnlFiles.Any() ? GetLookupValue((int)LookupType.PnlStatus, p.ApplicationPnlFiles.Where(r => r.IsActive && !r.IsDeleted).MaxBy(o => o.Id)?.StatusId) : GetLookupValue((int)LookupType.PnlStatus, 1),
                    }).ToList()
                };

                var paginationResult = PagedResultsFactory.CreatePagedResult(
                    result.CompanyApplications.AsQueryable(), request.Pagination.PageNumber, request.Pagination.PageSize,
                    request.Pagination.OrderBy, request.Pagination.Ascending);

                return paginationResult == null
                    ? new GetPaginatedCompanyApplicationsResult
                    {
                        CompanyApplications = null,
                        Status = GetPaginatedCompanyApplicationsStatus.NotFound,
                        Message = ServiceResources.RESOURCE_NOT_FOUND
                    }
                    : new GetPaginatedCompanyApplicationsResult
                    {
                        CompanyApplications = paginationResult.Results.ToList(),
                        TotalNumberOfPages = paginationResult.TotalNumberOfPages,
                        TotalNumberOfRecords = paginationResult.TotalNumberOfRecords,
                        Status = GetPaginatedCompanyApplicationsStatus.Successful,
                        Message = ServiceResources.RESOURCE_RETRIEVED
                    };
            }
            catch (Exception exc)
            {
                Logger.Error($@"Exception:{exc.Message}:{DateTime.Now}");

                return new GetPaginatedCompanyApplicationsResult
                {
                    Status = GetPaginatedCompanyApplicationsStatus.InternalServerError,
                    Message = ServiceResources.INTERNAL_SERVER_ERROR
                };
            }
        }

        public async Task<GetCompanyApplicationResult> GetCompanyApplication(GetCompanyApplicationRequest request)
        {
            var validationResult = _validationService.Validate(typeof(GetCompanyApplicationValidator), request);

            if (!validationResult.IsValid)
                return new GetCompanyApplicationResult
                {
                    Status = GetCompanyApplicationStatus.InvalidInput,
                    Message = ServiceResources.INVALID_INPUT_ERROR,
                    ValidationMessages = validationResult.ErrorMessages
                };

            try
            {
                var applicationEntity =
                await _dbContext.Application
                    .Include(i => i.ApplicationDocument)
                    .Include(i => i.ApplicationStatusHistories)
                    .ThenInclude(i => i.ApplicationStatus)
                    .ThenInclude(i => i.ApplicationStatusTranslations)
                    .Include(i => i.BranchApplicationCountry)
                    .ThenInclude(i => i.Branch)
                    .ThenInclude(i => i.BranchTranslations)
                    .Include(i => i.BranchApplicationCountry)
                    .ThenInclude(i => i.Branch)
                    .ThenInclude(i => i.Country)
                    .Include(i => i.PreApplicationApplicant)
                    .ThenInclude(i => i.PreApplication)
                    .Include(i => i.ApplicationPnlFiles).AsNoTracking()
                    .FirstOrDefaultAsync(r => r.IsActive && !r.IsDeleted &&
                                              r.PreApplicationApplicant.PreApplication.CompanyId == request.CompanyId && r.Id == request.ApplicationId);

                if (applicationEntity == null)
                    return new GetCompanyApplicationResult
                    {
                        Status = GetCompanyApplicationStatus.NotFound,
                        Message = ServiceResources.RESOURCE_NOT_FOUND,
                    };

                var result = new GetCompanyApplicationResult()
                {
                    CompanyApplication = new CompanyApplicationDto()
                    {
                        Id = applicationEntity.Id,
                        ApplicationNumber = applicationEntity.Id.ToString().PadLeft(13, '0'),
                        Branch = new LookupValue()
                        {
                            Id = applicationEntity.BranchApplicationCountry.Branch.Id.ToString(),
                            DisplayValue = applicationEntity.BranchApplicationCountry.Branch.BranchTranslations
                                               .FirstOrDefault(branchTranslation => branchTranslation.LanguageId == _context.LanguageId)?.Name ??
                                           applicationEntity.BranchApplicationCountry.Branch.BranchTranslations.FirstOrDefault(r => r.LanguageId == 2)?.Name,
                        },
                        Country = new LookupValue()
                        {
                            Id = applicationEntity.BranchApplicationCountry.Branch.Country.Id.ToString(),
                            DisplayValue = GetCountryName(applicationEntity.BranchApplicationCountry.Branch.Country, _context.LanguageId)
                        },
                        Name = applicationEntity.Name,
                        Surname = applicationEntity.Surname,
                        NameSurname = $"{applicationEntity.Name} {applicationEntity.Surname}",
                        PassportNumber = applicationEntity.PassportNumber,
                        ApplicationTime = applicationEntity.ApplicationTime.Date.ToString("dd/MM/yyyy"),
                        ApplicantType = GetLookupValue((int)LookupType.ApplicantType, applicationEntity.ApplicantTypeId),
                        AppointmentCategory = GetLookupValue((int)LookupType.VasType, applicationEntity.PreApplicationApplicant.PreApplication.VasTypeId),
                        ApplicationStatus = new LookupValue()
                        {
                            Id = applicationEntity.ApplicationStatusHistories.Where(r => r.IsActive && !r.IsDeleted).MaxBy(o => o.Id)?.ApplicationStatusId.ToString(),
                            DisplayValue = applicationEntity.ApplicationStatusHistories.Where(r => r.IsActive && !r.IsDeleted).MaxBy(o => o.Id)?
                                .ApplicationStatus.ApplicationStatusTranslations.FirstOrDefault(w => w.IsActive && !w.IsDeleted && w.LanguageId == _context.LanguageId) == null ? applicationEntity.ApplicationStatusHistories.Where(r => r.IsActive && !r.IsDeleted).MaxBy(o => o.Id)?
                                .ApplicationStatus.ApplicationStatusTranslations.FirstOrDefault(w => w.IsActive && !w.IsDeleted && w.LanguageId == 2)?.Name :
                                applicationEntity.ApplicationStatusHistories.Where(r => r.IsActive && !r.IsDeleted).MaxBy(o => o.Id)?
                                .ApplicationStatus.ApplicationStatusTranslations.FirstOrDefault(w => w.IsActive && !w.IsDeleted && w.LanguageId == _context.LanguageId)?.Name,
                        },
                        Status = GetLookupValue((int)LookupType.ApplicationActiveStatus, applicationEntity.ActiveStatusId),
                        EntryDate = applicationEntity.ApplicationDocument.EntryDate.Date.ToString("dd/MM/yyyy"),
                        ExitDate = applicationEntity.ApplicationDocument.ExitDate.Date.ToString("dd/MM/yyyy"),
                        LastUploadDate = applicationEntity.ApplicationDocument.ExitDate.AddDays(5).Date.ToString("dd/MM/yyyy"),
                        PnlFileName = applicationEntity.ApplicationPnlFiles.Any(r => r.IsActive && !r.IsDeleted) ? applicationEntity.ApplicationPnlFiles.Where(r => r.IsActive && !r.IsDeleted).MaxBy(o => o.Id)?.FileName : null,
                        PnlStatus = applicationEntity.ApplicationPnlFiles.Any(r => r.IsActive && !r.IsDeleted) ? GetLookupValue((int)LookupType.PnlStatus, applicationEntity.ApplicationPnlFiles.Where(r => r.IsActive && !r.IsDeleted).MaxBy(o => o.Id)?.StatusId) : GetLookupValue((int)LookupType.PnlStatus, 1),
                    }
                };

                var fileModuleTypes = EnumHelper.GetEnumAsDictionary(typeof(FileModuleType));

                // Files

                //Apppointment Demand

                var demandFiles = await _dbContext.AppointmentDemandFile
                    .Where(r => r.CompanyId == request.CompanyId && r.AppointmentDemandId ==
                        applicationEntity.PreApplicationApplicant.PreApplication.AppointmentDemandId)
                .ToListAsync();

                foreach (var file in demandFiles)
                {
                    result.CompanyApplication.Files.Add(new BaseReturnFileDto()
                    {
                        FileName = file.FileName,
                        FileExtension = file.Extension,
                        Id = file.Id,
                        FileTypeId = file.FileTypeId,
                        ModuleType = new LookupValue
                        {
                            Id = fileModuleTypes.FirstOrDefault(r => r.Key == (int)FileModuleType.AppointmentDemand).Key.ToString(),
                            DisplayValue = fileModuleTypes.FirstOrDefault(r => r.Key == (int)FileModuleType.AppointmentDemand).Value
                        }
                    });
                }

                //PNL

                var pnlFile = await _dbContext.ApplicationPnlFile
                    .Where(r => r.CompanyId == request.CompanyId && r.ApplicationId == applicationEntity.Id)
                        .FirstOrDefaultAsync();

                if (pnlFile != null)
                {
                    result.CompanyApplication.Files.Add(new BaseReturnFileDto()
                    {
                        FileName = pnlFile.FileName,
                        FileExtension = pnlFile.Extension,
                        Id = pnlFile.Id,
                        ModuleType = new LookupValue
                        {
                            Id = fileModuleTypes.FirstOrDefault(r => r.Key == (int)FileModuleType.ApplicationPnl).Key.ToString(),
                            DisplayValue = fileModuleTypes.FirstOrDefault(r => r.Key == (int)FileModuleType.ApplicationPnl).Value
                        }
                    });
                }

                return new GetCompanyApplicationResult
                {
                    Status = GetCompanyApplicationStatus.Successful,
                    Message = ServiceResources.RESOURCE_RETRIEVED,
                    CompanyApplication = result.CompanyApplication
                };
            }
            catch (Exception exc)
            {
                Logger.Error($@"Exception:{exc.Message}:{DateTime.Now}");

                return new GetCompanyApplicationResult
                {
                    Status = GetCompanyApplicationStatus.InternalServerError,
                    Message = ServiceResources.INTERNAL_SERVER_ERROR
                };
            }
        }

        public async Task<AddCompanyApplicationPnlResult> AddCompanyApplicationPnl(AddCompanyApplicationPnlRequest request)
        {
            var validationResult = _validationService.Validate(typeof(AddCompanyApplicationPnlValidator), request);

            if (!validationResult.IsValid)
                return new AddCompanyApplicationPnlResult
                {
                    Status = AddCompanyApplicationPnlStatus.InvalidInput,
                    Message = ServiceResources.INVALID_INPUT_ERROR,
                    ValidationMessages = validationResult.ErrorMessages
                };

            await using var transaction = await _dbContext.Database.BeginTransactionAsync();

            try
            {
                var existingCompanyUser = await _dbContext.CompanyUser
                    .Where(r => r.Email.Equals(request.Context.Identity.Email))
                    .FirstOrDefaultAsync();

                if (existingCompanyUser == null)
                    return new AddCompanyApplicationPnlResult
                    {
                        Status = AddCompanyApplicationPnlStatus.NotFound,
                        Message = ServiceResources.USER_NOT_FOUND
                    };

                var existingCompany = await _dbContext.Company
                    .Where(r => r.Id == request.CompanyId)
                    .FirstOrDefaultAsync();

                if (existingCompany == null)
                    return new AddCompanyApplicationPnlResult
                    {
                        Status = AddCompanyApplicationPnlStatus.NotFound,
                        Message = ServiceResources.COMPANY_NOT_FOUND
                    };

                var existingApplication = await _dbContext.Application
                    .Include(i => i.PreApplicationApplicant)
                    .ThenInclude(i => i.PreApplication)
                    .Where(r => r.IsActive && !r.IsDeleted && r.Id == request.ApplicationId &&
                                r.PreApplicationApplicant.PreApplication.CompanyId == request.CompanyId)
                    .FirstOrDefaultAsync();

                if (existingApplication == null)
                {
                    return new AddCompanyApplicationPnlResult
                    {
                        Status = AddCompanyApplicationPnlStatus.NotFound,
                        Message = ServiceResources.APPLICATION_NOT_FOUND
                    };
                }

                var existingApplicationPnl = await _dbContext.ApplicationPnlFile.Where(r =>
                        r.CompanyId == request.CompanyId && r.ApplicationId == request.ApplicationId)
                    .FirstOrDefaultAsync();

                if (existingApplicationPnl != null)
                {
                    return new AddCompanyApplicationPnlResult
                    {
                        Status = AddCompanyApplicationPnlStatus.AlreadyExist,
                        Message = ServiceResources.PNL_ALREADY_REGISTERED
                    };
                }

                var name = $"{Guid.NewGuid()}-{request.FileData.FileName}";
                var stream = new MemoryStream(await request.FileData.GetFileBytes());
                var result = await FileStorage.SaveFileAsync(_b2BBucketName, $"pnl/{request.ApplicationId}/{name}", stream, "");

                if (!result)
                    return new AddCompanyApplicationPnlResult
                    {
                        Status = AddCompanyApplicationPnlStatus.ExternalServiceError,
                        Message = ServiceResources.FILE_OPERATION_FAILED
                    };

                var applicationPnlEntity = await _dbContext.AddAsync(new ApplicationPnlFile()
                {
                    ApplicationId = request.ApplicationId,
                    CompanyId = request.CompanyId,
                    FileName = request.FileData.FileName,
                    StorageName = name,
                    Extension = Path.GetExtension(request.FileData.FileName),
                    IsActive = true,
                    IsDeleted = false,
                    StatusId = (int)Enums.Enums.PnlFileStatus.Uploaded,
                    CreatedAt = DateTime.Now,
                    CreatedBy = request.Context.Identity.Email
                });

                await _dbContext.SaveChangesAsync();

                await transaction.CommitAsync();

                return new AddCompanyApplicationPnlResult
                {
                    Status = AddCompanyApplicationPnlStatus.Successful,
                    Message = ServiceResources.RESOURCE_CREATED,
                    Id = applicationPnlEntity.Entity.Id,
                };
            }
            catch (Exception exc)
            {
                Logger.Error($@"Exception:{exc.Message}:{DateTime.Now}");

                await transaction.RollbackAsync();
                return new AddCompanyApplicationPnlResult
                {
                    Status = AddCompanyApplicationPnlStatus.InternalServerError,
                    Message = ServiceResources.INTERNAL_SERVER_ERROR
                };
            }
        }

        public async Task<GetCompanyUserPositionsResult> GetCompanyUserPositions(GetCompanyUserPositionsRequest request)
        {
            try
            {
                var entityCompanyUserPositions = await _dbContext.CompanyUserPosition
                    .Include(i => i.CompanyUserPositionTranslations)
                    .AsNoTracking()
                    .ToListAsync();

                if (!entityCompanyUserPositions.Any())
                    return new GetCompanyUserPositionsResult
                    {
                        Status = GetCompanyUserPositionsStatus.NotFound,
                        Message = ServiceResources.RESOURCE_NOT_FOUND,
                    };

                var hasTranslation =
                    await _dbContext.CompanyUserPositionTranslation.AnyAsync(
                        r => r.LanguageId == request.Context.LanguageId);

                if (!hasTranslation)
                    return new GetCompanyUserPositionsResult
                    {
                        Status = GetCompanyUserPositionsStatus.NotFound,
                        Message = ServiceResources.TRANSLATION_NOT_FOUND,
                    };

                var result = new GetCompanyUserPositionsResult()
                {
                    CompanyUserPositions = entityCompanyUserPositions.Select(r => new CompanyUserPositionDto()
                    {
                        Id = r.Id,
                        Name = r.CompanyUserPositionTranslations
                            .FirstOrDefault(s => s.LanguageId == request.Context.LanguageId)?.Name
                    }).ToList()
                };

                return new GetCompanyUserPositionsResult
                {
                    Status = GetCompanyUserPositionsStatus.Successful,
                    Message = ServiceResources.RESOURCE_RETRIEVED,
                    CompanyUserPositions = result.CompanyUserPositions
                };
            }
            catch (Exception exc)
            {
                Logger.Error($@"Exception:{exc.Message}:{DateTime.Now}");

                return new GetCompanyUserPositionsResult
                {
                    Status = GetCompanyUserPositionsStatus.InternalServerError,
                    Message = ServiceResources.INTERNAL_SERVER_ERROR
                };
            }
        }

        public async Task<GetCompanySlotsResult> GetCompanySlots(GetCompanySlotsRequest request)
        {
            var validationResult = _validationService.Validate(typeof(GetCompanySlotsValidator), request);

            if (!validationResult.IsValid)
                return new GetCompanySlotsResult
                {
                    Status = GetCompanySlotStatus.InvalidInput,
                    Message = ServiceResources.INVALID_INPUT_ERROR,
                    ValidationMessages = validationResult.ErrorMessages
                };

            try
            {
                var slotQuery = _dbContext.Slot
                    .Include(i => i.Agency)
                    .Include(i => i.BranchApplicationCountry)
                    .ThenInclude(i => i.Branch)
                    .ThenInclude(i => i.BranchTranslations)
                    .Include(i => i.BranchApplicationCountry)
                    .ThenInclude(i => i.Country)
                    .Where(p => !p.IsDeleted && p.IsActive &&
                                p.BranchApplicationCountry.BranchId == request.BranchId && p.Quota > -1
                                && p.SlotTime >= request.Date.Date && p.SlotTypeId == (int)Enums.Enums.SlotType.B2B && p.CompanyId == request.CompanyId);

                var newSlots = await slotQuery.ToListAsync();

                if (newSlots.Count == 0)
                    return new GetCompanySlotsResult
                    {
                        Status = GetCompanySlotStatus.NotFound,
                        Message = ServiceResources.RESOURCE_NOT_FOUND
                    };

                var addDaysSlotTime = AddDaysWithLocalTime(request.Date, 1);

                var availableSlots = newSlots.Where(p => p.SlotTime >= request.Date.Date && p.SlotTime < addDaysSlotTime && p.Quota >= request.ApplicantCount).ToList();

                if (availableSlots.Count > 0)
                {
                    return new GetCompanySlotsResult
                    {
                        Status = GetCompanySlotStatus.Successful,
                        Message = ServiceResources.RESOURCE_RETRIEVED,
                        Slots = availableSlots.Select(p => new SlotDto
                        {
                            Id = p.Id,
                            SlotTime = p.SlotTime,
                            Quota = p.Quota,
                            BranchName = p.BranchApplicationCountry.Branch.BranchTranslations
                                             .FirstOrDefault(branchTranslation => branchTranslation.LanguageId == request.Context.LanguageId)?.Name ??
                                         p.BranchApplicationCountry.Branch.BranchTranslations.FirstOrDefault(r => r.LanguageId == 2)?.Name,
                            CountryName = GetCountryName(p.BranchApplicationCountry.Country, request.Context.LanguageId)
                        }).ToList()
                    };
                }

                return new GetCompanySlotsResult
                {
                    Status = GetCompanySlotStatus.NotFound,
                    Message = ServiceResources.RESOURCE_NOT_FOUND
                };
            }
            catch (Exception exc)
            {
                Logger.Error($@"Exception:{exc.Message}:{DateTime.Now}");

                return new GetCompanySlotsResult
                {
                    Status = GetCompanySlotStatus.InternalServerError,
                    Message = ServiceResources.INTERNAL_SERVER_ERROR
                };
            }
        }

        public async Task<AddCompanyUserResult> AddCompanyUser(AddCompanyUserRequest request)
        {
            var validationResult = _validationService.Validate(typeof(AddCompanyUserValidator), request);

            if (!validationResult.IsValid)
                return new AddCompanyUserResult
                {
                    Status = AddCompanyUserStatus.InvalidInput,
                    Message = ServiceResources.INVALID_INPUT_ERROR,
                    ValidationMessages = validationResult.ErrorMessages
                };

            await using var transaction = await _dbContext.Database.BeginTransactionAsync();

            try
            {
                var queryCompanyUser = await _dbContext.CompanyUser
                  .Where(r => r.Email.Equals(request.Email))
                  .AnyAsync();

                if (queryCompanyUser)
                    return new AddCompanyUserResult
                    {
                        Status = AddCompanyUserStatus.AlreadyExist,
                        Message = ServiceResources.RESOURCE_ALREADY_REGISTERED,
                    };

                var queryCompany = await _dbContext.Company.Include(i => i.CompanyFiles)
                    .Where(r => r.Id == request.CompanyId)
                    .FirstOrDefaultAsync();

                if (queryCompany == null)
                    return new AddCompanyUserResult
                    {
                        Status = AddCompanyUserStatus.NotFound,
                        Message = ServiceResources.COMPANY_NOT_FOUND,
                    };

                var branches = await _dbContext.Branch.Where(r => r.IsActive && !r.IsDeleted).ToListAsync();

                if (!branches.Any())
                    return new AddCompanyUserResult
                    {
                        Status = AddCompanyUserStatus.NotFound,
                        Message = ServiceResources.BRANCH_NOT_FOUND,
                    };

                var entityUserResponse = await _dbContext.CompanyUser.AddAsync(new CompanyUser()
                {
                    CompanyId = request.CompanyId,
                    Name = request.Name,
                    Surname = request.Surname,
                    PhoneNumber = request.PhoneNumber,
                    Email = request.Email,
                    StatusId = (int)Enums.Enums.B2BApprovalStatus.WaitingApproval,
                    CreatedBy = request.Context.Identity.Email,
                    CreatedAt = DateTime.Now,
                });

                await _dbContext.SaveChangesAsync();

                foreach (var branchId in request.BranchIds)
                {
                    if (!branches.Exists(e => e.Id == branchId))
                    {
                        await transaction.RollbackAsync();

                        return new AddCompanyUserResult
                        {
                            Status = AddCompanyUserStatus.NotFound,
                            Message = ServiceResources.BRANCH_NOT_FOUND,
                        };
                    }

                    var companyUserBranch = new CompanyUserBranch()
                    {
                        CompanyUserId = entityUserResponse.Entity.Id,
                        BranchId = branchId,
                        CreatedAt = DateTime.Now,
                        CreatedBy = request.Context.Identity.Email,
                        StatusId = (int)Enums.Enums.B2BApprovalStatus.Approved
                    };

                    await _dbContext.CompanyUserBranch.AddAsync(companyUserBranch);
                    await _dbContext.SaveChangesAsync();
                }

                if (request.File != null)
                {
                    var name = $"{Guid.NewGuid()}-{request.File.FileData.FileName}";
                    var stream = new MemoryStream(await request.File.FileData.GetFileBytes());
                    var result = await FileStorage.SaveFileAsync(_b2BBucketName, $"companyuser/{entityUserResponse.Entity.Id}/{request.File.FileTypeId}/{name}", stream, "");

                    if (!result)
                    {
                        await transaction.RollbackAsync();

                        return new AddCompanyUserResult
                        {
                            Status = AddCompanyUserStatus.InternalServerError,
                            Message = ServiceResources.FILE_OPERATION_FAILED
                        };
                    }

                    var userFileEntity = new CompanyUserFile()
                    {
                        CompanyId = request.CompanyId,
                        CompanyUserId = entityUserResponse.Entity.Id,
                        FileName = request.File.FileData.FileName,
                        StorageName = name,
                        Extension = Path.GetExtension(request.File.FileData.FileName),
                        FileTypeId = request.File.FileTypeId,
                        CreatedAt = DateTime.Now,
                        CreatedBy = request.Context.Identity.Email,
                        StatusId = (int)Enums.Enums.B2BApprovalStatus.WaitingApproval,
                        IsActive = true
                    };

                    await _dbContext.CompanyUserFile.AddAsync(userFileEntity);
                    await _dbContext.SaveChangesAsync();
                }

                #region Update Complete Rate

                entityUserResponse.Entity.ProfileCompletedPercentage = await CalculateCompletionRate(
                    queryCompany,
                    entityUserResponse.Entity,
                    queryCompany.CompanyFiles,
                    entityUserResponse.Entity.CompanyUserFiles);

                _dbContext.CompanyUser.Update(entityUserResponse.Entity);

                await _dbContext.SaveChangesAsync();

                #endregion

                #region is register

                var existingCompanyUserRole = await _dbContext.CompanyRoles
                    .Where(r => r.RoleName.Equals("CompanyUser"))
                    .FirstOrDefaultAsync();

                var dummyPassword = GenerateDummyPassword();

                var isResponse = await IdentityServerExternalRegister(request.Context, request.Email, dummyPassword, existingCompanyUserRole == null ? null : new List<RoleAssignDto>
                {
                    new()
                    {
                        Id = existingCompanyUserRole.ExternalId,
                        RoleName = existingCompanyUserRole.RoleName,
                    }
                });

                if (isResponse.Status != "SUCCESS")
                {
                    await transaction.RollbackAsync();

                    return new AddCompanyUserResult
                    {
                        Status = AddCompanyUserStatus.ExternalServiceError,
                        Message = isResponse.ErrorDescription ?? isResponse.Message,
                        ValidationMessages = isResponse.ValidationMessages
                    };
                }

                //extra mail

                await _notificationEventService.Raise(new ExternalRegisterSendNotificationEvent()
                {
                    Email = request.Email,
                    LanguageId = request.Context.LanguageId,
                    Password = dummyPassword,
                    UserId = entityUserResponse.Entity.Id.ToString(),
                });

                #endregion

                await transaction.CommitAsync();

                return new AddCompanyUserResult
                {
                    Status = AddCompanyUserStatus.Successful,
                    Message = ServiceResources.RESOURCE_CREATED,
                    Id = entityUserResponse.Entity.Id
                };
            }
            catch (Exception exc)
            {
                Logger.Error($@"Exception:{exc.Message}:{DateTime.Now}");

                await transaction.RollbackAsync();
                return new AddCompanyUserResult
                {
                    Status = AddCompanyUserStatus.InternalServerError,
                    Message = ServiceResources.INTERNAL_SERVER_ERROR
                };
            }
        }

        public async Task<UpdateCompanyUserResult> UpdateCompanyUser(UpdateCompanyUserRequest request)
        {
            var validationResult = _validationService.Validate(typeof(UpdateCompanyUserValidator), request);

            if (!validationResult.IsValid)
                return new UpdateCompanyUserResult
                {
                    Status = UpdateCompanyUserStatus.InvalidInput,
                    Message = ServiceResources.INVALID_INPUT_ERROR,
                    ValidationMessages = validationResult.ErrorMessages
                };

            await using var transaction = await _dbContext.Database.BeginTransactionAsync();

            try
            {
                var entityCompany =
                    await _dbContext.Company.Where(r => r.Id == request.CompanyId).AsNoTracking().FirstOrDefaultAsync();

                if (entityCompany == null)
                    return new UpdateCompanyUserResult
                    {
                        Status = UpdateCompanyUserStatus.NotFound,
                        Message = ServiceResources.COMPANY_NOT_FOUND,
                    };

                var queryCompanyUser = await _dbContext.CompanyUser
                    .Where(r => r.Id == request.UserId && r.CompanyId == entityCompany.Id)
                  .FirstOrDefaultAsync();

                if (queryCompanyUser == null)
                    return new UpdateCompanyUserResult
                    {
                        Status = UpdateCompanyUserStatus.NotFound,
                        Message = ServiceResources.RESOURCE_NOT_FOUND,
                    };

                var queryCompanyUserBranches = await _dbContext.CompanyUserBranch.Where(r => r.CompanyUserId == queryCompanyUser.Id && r.StatusId == (int)Enums.Enums.B2BApprovalStatus.Approved && !r.IsDeleted).ToListAsync();

                queryCompanyUserBranches.ForEach(s => s.IsDeleted = true);
                _dbContext.CompanyUserBranch.UpdateRange(queryCompanyUserBranches);

                await _dbContext.SaveChangesAsync();

                if (request.BranchIds.Any())
                {
                    // add new branches

                    var notIncludedDataListForAdd = new List<CompanyUserBranch>();

                    foreach (var notIncludedBranchId in request.BranchIds)
                    {
                        var branchEntry = new CompanyUserBranch()
                        {
                            BranchId = notIncludedBranchId,
                            CompanyUserId = queryCompanyUser.Id,
                            CreatedAt = DateTime.Now,
                            CreatedBy = request.Context.Identity.Email,
                            IsActive = true,
                            StatusId = (int)Enums.Enums.B2BApprovalStatus.WaitingApproval
                        };

                        notIncludedDataListForAdd.Add(branchEntry);
                    }

                    await _dbContext.CompanyUserBranch.AddRangeAsync(notIncludedDataListForAdd);

                    await _dbContext.SaveChangesAsync();
                }

                var isSameFile = false;

                if (request.File != null)
                {
                    var oldFileEntity = await _dbContext.CompanyUserFile.Where(r => r.FileTypeId == request.File.FileTypeId && r.CompanyUserId == queryCompanyUser.Id).FirstOrDefaultAsync();

                    //check same file sended in request

                    if (oldFileEntity != null)
                    {
                        var oldFile =
                                await FileStorage.GetFileStreamAsync(_b2BBucketName, $"companyuser/{queryCompanyUser.Id}/{request.File.FileTypeId}/{oldFileEntity.StorageName}");

                        if (oldFile != null)
                        {
                            var oldFileData = Gateway.Extensions.StringExtensions.ConvertToBase64(oldFile);

                            var newFileData = Convert.ToBase64String(await request.File.FileData.GetFileBytes());

                            if (newFileData == oldFileData && oldFileEntity.FileName == request.File.FileData.FileName)
                            {
                                isSameFile = true;
                            }
                        }
                    }

                    if (!isSameFile)
                    {
                        var name = $"{Guid.NewGuid()}-{request.File.FileData.FileName}";
                        var stream = new MemoryStream(await request.File.FileData.GetFileBytes());
                        var result = await FileStorage.SaveFileAsync(_b2BBucketName, $"companyuser/{queryCompanyUser.Id}/{request.File.FileTypeId}/{name}", stream, "");

                        if (!result)
                        {
                            await transaction.RollbackAsync();

                            return new UpdateCompanyUserResult
                            {
                                Status = UpdateCompanyUserStatus.InternalServerError,
                                Message = ServiceResources.FILE_OPERATION_FAILED
                            };
                        }

                        //if file exist deactivate it

                        if (oldFileEntity != null)
                        {
                            oldFileEntity.IsActive = false;
                            oldFileEntity.UpdatedAt = DateTime.Now;
                            oldFileEntity.UpdatedBy = request.Context.Identity.Email;

                            _dbContext.CompanyUserFile.Update(oldFileEntity);
                            await _dbContext.SaveChangesAsync();
                        }

                        var userFileEntity = new CompanyUserFile()
                        {
                            CompanyId = request.CompanyId,
                            CompanyUserId = queryCompanyUser.Id,
                            FileName = request.File.FileData.FileName,
                            StorageName = name,
                            Extension = Path.GetExtension(request.File.FileData.FileName),
                            FileTypeId = request.File.FileTypeId,
                            CreatedAt = DateTime.Now,
                            CreatedBy = request.Context.Identity.Email,
                            StatusId = (int)Enums.Enums.B2BApprovalStatus.WaitingApproval,
                            IsActive = true
                        };

                        await _dbContext.CompanyUserFile.AddAsync(userFileEntity);
                        await _dbContext.SaveChangesAsync();
                    }
                }

                queryCompanyUser.PhoneNumber = request.PhoneNumber;
                queryCompanyUser.Name = request.Name;
                queryCompanyUser.Surname = request.Surname;
                queryCompanyUser.UpdatedAt = DateTime.Now;
                queryCompanyUser.UpdatedBy = request.Context.Identity.Email;

                if (!queryCompanyUser.IsAuthorizedUser)
                    queryCompanyUser.StatusId = (int)Enums.Enums.B2BApprovalStatus.WaitingApproval;

                _dbContext.CompanyUser.Update(queryCompanyUser);
                await _dbContext.SaveChangesAsync();

                await transaction.CommitAsync();

                return new UpdateCompanyUserResult
                {
                    Status = UpdateCompanyUserStatus.Successful,
                    Message = ServiceResources.RESOURCE_UPDATED,
                    Id = queryCompanyUser.Id,
                    IsSameFile = true,
                };
            }
            catch (Exception exc)
            {
                Logger.Error($@"Exception:{exc.Message}:{DateTime.Now}");

                await transaction.RollbackAsync();
                return new UpdateCompanyUserResult
                {
                    Status = UpdateCompanyUserStatus.InternalServerError,
                    Message = ServiceResources.INTERNAL_SERVER_ERROR
                };
            }
        }

        public async Task<GetCompanyUserResult> GetCompanyUser(GetCompanyUserRequest request)
        {
            var validationResult = _validationService.Validate(typeof(GetCompanyUserValidator), request);

            if (!validationResult.IsValid)
                return new GetCompanyUserResult
                {
                    Status = GetCompanyUserStatus.InvalidInput,
                    Message = ServiceResources.INVALID_INPUT_ERROR,
                    ValidationMessages = validationResult.ErrorMessages
                };

            try
            {
                var entityCompany =
                    await _dbContext.Company.Where(r => r.Id == request.CompanyId).AsNoTracking().FirstOrDefaultAsync();

                if (entityCompany == null)
                    return new GetCompanyUserResult
                    {
                        Status = GetCompanyUserStatus.NotFound,
                        Message = ServiceResources.COMPANY_NOT_FOUND,
                    };

                var queryCompanyUser = await _dbContext.CompanyUser
                    .Include(i => i.CompanyUserFiles)
                    .Where(r => r.Id == request.UserId && r.CompanyId == request.CompanyId)
                    .AsNoTracking()
                    .FirstOrDefaultAsync();

                if (queryCompanyUser == null)
                    return new GetCompanyUserResult
                    {
                        Status = GetCompanyUserStatus.NotFound,
                        Message = ServiceResources.RESOURCE_NOT_FOUND,
                    };

                var userBranches = await _dbContext.CompanyUserBranch
                    .Include(i => i.Branch)
                    .ThenInclude(i => i.BranchTranslations).Where(r => r.CompanyUserId == queryCompanyUser.Id && r.StatusId == (int)Enums.Enums.B2BApprovalStatus.Approved && !r.IsDeleted).AsNoTracking().ToListAsync();

                var result = new GetCompanyUserResult
                {
                    CompanyUser = new CompanyUserDto()
                    {
                        Id = queryCompanyUser.Id,
                        Email = queryCompanyUser.Email,
                        Name = queryCompanyUser.Name,
                        Surname = queryCompanyUser.Surname,
                        PhoneNumber = queryCompanyUser.PhoneNumber,
                        Status = GetLookupValue((int)LookupType.CompanyApprovalStatus, queryCompanyUser.StatusId),
                        IsVerifiedAccount = queryCompanyUser.IsVerifiedAccount,
                        IsAuthorizedUser = queryCompanyUser.IsAuthorizedUser,
                        ProfileCompletedPercentage = queryCompanyUser.ProfileCompletedPercentage,
                        PositionId = queryCompanyUser.PositionId,
                        Branches = userBranches.Select(s => new CompanyUserBranchDto
                        {
                            Id = s.BranchId,
                            Name = s.Branch.BranchTranslations.FirstOrDefault(s => s.LanguageId == request.Context.LanguageId)?.Name != null ?
                            s.Branch.BranchTranslations.FirstOrDefault(s => s.LanguageId == request.Context.LanguageId)?.Name :
                            s.Branch.BranchTranslations.FirstOrDefault(s => s.LanguageId == 2)?.Name,
                        }).ToList(),
                    }
                };

                if (!queryCompanyUser.ProfilePicture.IsNullOrWhitespace())
                {
                    var profilePicture =
                        await FileStorage.GetFileStreamAsync(_b2BBucketName, $"companyuser/{queryCompanyUser.Id}/ProfileImage/{queryCompanyUser.ProfilePicture}");

                    if (profilePicture != null)
                    {
                        result.CompanyUser.ProfilePicture = Gateway.Extensions.StringExtensions.ConvertToBase64(profilePicture);
                    }
                }

                var fileModuleTypes = EnumHelper.GetEnumAsDictionary(typeof(FileModuleType));

                foreach (var file in queryCompanyUser.CompanyUserFiles)
                {
                    if (file != null)
                    {
                        result.CompanyUser.Files.Add(new BaseReturnFileDto()
                        {
                            FileTypeId = file.FileTypeId,
                            FileName = file.FileName,
                            FileExtension = file.Extension,
                            Id = file.Id,
                            ModuleType = new LookupValue
                            {
                                Id = fileModuleTypes.FirstOrDefault(r => r.Key == (int)FileModuleType.CompanyUser).Key.ToString(),
                                DisplayValue = fileModuleTypes.FirstOrDefault(r => r.Key == (int)FileModuleType.CompanyUser).Value
                            }
                        });
                    }
                }

                return new GetCompanyUserResult
                {
                    Status = GetCompanyUserStatus.Successful,
                    Message = ServiceResources.RESOURCE_RETRIEVED,
                    CompanyUser = result.CompanyUser
                };
            }
            catch (Exception exc)
            {
                Logger.Error($@"Exception:{exc.Message}:{DateTime.Now}");

                return new GetCompanyUserResult
                {
                    Status = GetCompanyUserStatus.InternalServerError,
                    Message = ServiceResources.INTERNAL_SERVER_ERROR
                };
            }
        }

        public async Task<GetPaginatedCompanyUsersResult> GetPaginatedCompanyUsers(GetPaginatedCompanyUsersRequest request)
        {
            var validationResult = _validationService.Validate(typeof(GetPaginatedCompanyUsersValidator), request);

            if (!validationResult.IsValid)
                return new GetPaginatedCompanyUsersResult
                {
                    Status = GetPaginatedCompanyUsersStatus.InvalidInput,
                    Message = ServiceResources.INVALID_INPUT_ERROR,
                    ValidationMessages = validationResult.ErrorMessages
                };

            try
            {
                var queryCompanyUsers = _dbContext.CompanyUser.Where(r => r.CompanyId == request.CompanyId).AsNoTracking();

                if (request.Filters != null)
                {
                    if (!request.Filters.Name.IsNullOrWhitespace())
                        queryCompanyUsers = queryCompanyUsers.Where(r => r.Name.Contains(request.Filters.Name));

                    if (!request.Filters.Surname.IsNullOrWhitespace())
                        queryCompanyUsers = queryCompanyUsers.Where(r => r.Surname.Contains(request.Filters.Surname));

                    if (request.Filters.StatusId.IsNumericAndGreaterThenZero())
                        queryCompanyUsers = queryCompanyUsers.Where(r => r.StatusId == request.Filters.StatusId);
                }

                var users = await queryCompanyUsers.ToListAsync();

                //if (!users.Any())
                // return new GetPaginatedCompanyUsersResult
                // {
                //  Status = GetPaginatedCompanyUsersStatus.NotFound,
                //  Message = ServiceResources.RESOURCE_NOT_FOUND,
                // };

                var result = new GetPaginatedCompanyUsersResult
                {
                    CompanyUsers = users.Select(p => new CompanyUserDto()
                    {
                        Id = p.Id,
                        Name = p.Name,
                        Surname = p.Surname,
                        Email = p.Email,
                        PhoneNumber = p.PhoneNumber,
                        IsVerifiedAccount = p.IsVerifiedAccount,
                        IsAuthorizedUser = p.IsAuthorizedUser,
                        ProfileCompletedPercentage = p.ProfileCompletedPercentage,
                        Status = GetLookupValue((int)LookupType.CompanyApprovalStatus, p.StatusId),
                    }).ToList()
                };

                var paginationResult = PagedResultsFactory.CreatePagedResult(
                    result.CompanyUsers.AsQueryable(), request.Pagination.PageNumber, request.Pagination.PageSize,
                    request.Pagination.OrderBy, request.Pagination.Ascending);

                return paginationResult == null
                    ? new GetPaginatedCompanyUsersResult
                    {
                        CompanyUsers = null,
                        Status = GetPaginatedCompanyUsersStatus.NotFound,
                        Message = ServiceResources.RESOURCE_NOT_FOUND
                    }
                    : new GetPaginatedCompanyUsersResult
                    {
                        CompanyUsers = paginationResult.Results.ToList(),
                        TotalNumberOfPages = paginationResult.TotalNumberOfPages,
                        TotalNumberOfRecords = paginationResult.TotalNumberOfRecords,
                        Status = GetPaginatedCompanyUsersStatus.Successful,
                        Message = ServiceResources.RESOURCE_RETRIEVED
                    };
            }
            catch (Exception exc)
            {
                Logger.Error($@"Exception:{exc.Message}:{DateTime.Now}");

                return new GetPaginatedCompanyUsersResult
                {
                    Status = GetPaginatedCompanyUsersStatus.InternalServerError,
                    Message = ServiceResources.INTERNAL_SERVER_ERROR
                };
            }
        }

        public async Task<LoginResult> Login(LoginRequest request)
        {
            try
            {
                var existingUser = await _dbContext.CompanyUser.Where(w => w.Email.Equals(request.Username))
                    .AsNoTracking()
                    .FirstOrDefaultAsync();

                if (existingUser == null)
                    return new LoginResult
                    {
                        Status = LoginStatus.NotFound,
                        Message = ServiceResources.RESOURCE_NOT_FOUND
                    };

                var isResponse = await IdentityServerLogin(request);

                if (isResponse.AccessToken == null)
                {
                    return new LoginResult
                    {
                        Status = LoginStatus.ExternalServiceError,
                        Message = isResponse.ErrorDescription ?? isResponse.Message,
                        ValidationMessages = isResponse.ValidationMessages
                    };
                }

                await _dbContext.CompanyUserLogin.AddAsync(new CompanyUserLogin()
                {
                    CompanyUserId = existingUser.Id,
                    CreatedBy = existingUser.Email,
                    CreatedAt = DateTime.Now,
                });

                await _dbContext.SaveChangesAsync();

                return new LoginResult
                {
                    Status = LoginStatus.Successful,
                    Message = ServiceResources.RESOURCE_RETRIEVED,
                    Data = new LoginDto()
                    {
                        AccessToken = isResponse.AccessToken,
                        Scope = isResponse.Scope,
                        ExpiresIn = isResponse.ExpiresIn,
                        RefreshToken = isResponse.RefreshToken,
                        TokenType = isResponse.TokenType
                    }
                };
            }
            catch (Exception exc)
            {
                Logger.Error($@"Exception:{exc.Message}:{DateTime.Now}");

                return new LoginResult
                {
                    Status = LoginStatus.InternalServerError,
                    Message = ServiceResources.INTERNAL_SERVER_ERROR
                };
            }
        }

        public async Task<ChangePasswordResult> ChangePassword(ChangePasswordRequest request)
        {
            var validationResult = _validationService.Validate(typeof(ChangePasswordValidator), request);

            if (!validationResult.IsValid)
                return new ChangePasswordResult
                {
                    Status = ChangePasswordStatus.InvalidInput,
                    Message = ServiceResources.INVALID_INPUT_ERROR,
                    ValidationMessages = validationResult.ErrorMessages
                };

            try
            {
                var existingUser = await _dbContext.CompanyUser.Where(w => w.Email.Equals(request.Context.Identity.Email))
                    .AsNoTracking()
                    .FirstOrDefaultAsync();

                if (existingUser == null)
                {
                    return new ChangePasswordResult
                    {
                        Status = ChangePasswordStatus.NotFound,
                        Message = ServiceResources.RESOURCE_NOT_FOUND
                    };
                }

                var iSResponse = await IdentityServerChangePassword(request.Context, request.NewPassword, request.CurrentPassword, existingUser.Email);

                if (iSResponse.Status != "SUCCESS")
                {
                    return new ChangePasswordResult
                    {
                        Status = ChangePasswordStatus.ExternalServiceError,
                        Message = iSResponse.ErrorDescription ?? iSResponse.Message,
                        ValidationMessages = iSResponse.ValidationMessages
                    };
                }

                return new ChangePasswordResult
                {
                    Status = ChangePasswordStatus.Successful,
                    Message = ServiceResources.RESOURCE_UPDATED,
                };
            }
            catch (Exception exc)
            {
                Logger.Error($@"Exception:{exc.Message}:{DateTime.Now}");

                return new ChangePasswordResult
                {
                    Status = ChangePasswordStatus.InternalServerError,
                    Message = ServiceResources.INTERNAL_SERVER_ERROR
                };
            }
        }

        public async Task<ForgotPasswordResult> ForgotPassword(ForgotPasswordRequest request)
        {
            var validationResult = _validationService.Validate(typeof(ForgotPasswordValidator), request);

            if (!validationResult.IsValid)
                return new ForgotPasswordResult
                {
                    Status = ForgotPasswordStatus.InvalidInput,
                    Message = ServiceResources.INVALID_INPUT_ERROR,
                    ValidationMessages = validationResult.ErrorMessages
                };

            try
            {
                var existingUser = await _dbContext.CompanyUser.Where(w => w.Email.Equals(request.Email))
                    .AsNoTracking()
                    .FirstOrDefaultAsync();

                if (existingUser == null)
                {
                    return new ForgotPasswordResult
                    {
                        Status = ForgotPasswordStatus.NotFound,
                        Message = ServiceResources.RESOURCE_NOT_FOUND
                    };
                }

                var iSResponse = await IdentityServerForgotPassword(existingUser.Email);

                if (iSResponse.Status != "SUCCESS")
                {
                    return new ForgotPasswordResult
                    {
                        Status = ForgotPasswordStatus.ExternalServiceError,
                        Message = iSResponse.ErrorDescription ?? iSResponse.Message,
                        ValidationMessages = iSResponse.ValidationMessages
                    };
                }

                return new ForgotPasswordResult
                {
                    Status = ForgotPasswordStatus.Successful,
                    Message = ServiceResources.SUCCESS,
                };
            }
            catch (Exception exc)
            {
                Logger.Error($@"Exception:{exc.Message}:{DateTime.Now}");

                return new ForgotPasswordResult
                {
                    Status = ForgotPasswordStatus.InternalServerError,
                    Message = ServiceResources.INTERNAL_SERVER_ERROR
                };
            }
        }

        public async Task<ResetPasswordResult> ResetPassword(ResetPasswordRequest request)
        {
            var validationResult = _validationService.Validate(typeof(ResetPasswordValidator), request);

            if (!validationResult.IsValid)
                return new ResetPasswordResult
                {
                    Status = ResetPasswordStatus.InvalidInput,
                    Message = ServiceResources.INVALID_INPUT_ERROR,
                    ValidationMessages = validationResult.ErrorMessages
                };

            try
            {
                var existingUser = await _dbContext.CompanyUser.Where(w => w.Email.Equals(request.Email)).AsNoTracking().FirstOrDefaultAsync();

                if (existingUser == null)
                {
                    return new ResetPasswordResult
                    {
                        Status = ResetPasswordStatus.NotFound,
                        Message = ServiceResources.RESOURCE_NOT_FOUND
                    };
                }

                var iSResponse = await IdentityServerResetPassword(existingUser.Email, request.Code, request.Password, request.ConfirmPassword);

                if (iSResponse.Status != "SUCCESS")
                {
                    return new ResetPasswordResult
                    {
                        Status = ResetPasswordStatus.ExternalServiceError,
                        Message = iSResponse.ErrorDescription ?? iSResponse.Message,
                        ValidationMessages = iSResponse.ValidationMessages
                    };
                }

                return new ResetPasswordResult
                {
                    Status = ResetPasswordStatus.Successful,
                    Message = ServiceResources.SUCCESS,
                };
            }
            catch (Exception exc)
            {
                Logger.Error($@"Exception:{exc.Message}:{DateTime.Now}");

                return new ResetPasswordResult
                {
                    Status = ResetPasswordStatus.InternalServerError,
                    Message = ServiceResources.INTERNAL_SERVER_ERROR
                };
            }
        }

        public async Task<ConfirmEmailResult> ConfirmEmail(ConfirmEmailRequest request)
        {
            var validationResult = _validationService.Validate(typeof(ConfirmEmailValidator), request);

            if (!validationResult.IsValid)
                return new ConfirmEmailResult
                {
                    Status = ConfirmEmailStatus.InvalidInput,
                    Message = ServiceResources.INVALID_INPUT_ERROR,
                    ValidationMessages = validationResult.ErrorMessages
                };

            try
            {
                //var existingUser = await _dbContext.CompanyUser.Where(w => w.Email.Equals(request.Context.Identity.Email)).AsNoTracking().FirstOrDefaultAsync();

                //if (existingUser == null)
                //{
                //    return new ConfirmEmailResult
                //    {
                //        Status = ConfirmEmailStatus.NotFound,
                //        Message = ServiceResources.RESOURCE_NOT_FOUND
                //    };
                //}

                var iSResponse = await IdentityServerConfirmEmail(request.UserId, request.Code);

                if (iSResponse.Status != "SUCCESS")
                {
                    return new ConfirmEmailResult
                    {
                        Status = ConfirmEmailStatus.ExternalServiceError,
                        Message = iSResponse.ErrorDescription ?? iSResponse.Message,
                        ValidationMessages = iSResponse.ValidationMessages
                    };
                }

                return new ConfirmEmailResult
                {
                    Status = ConfirmEmailStatus.Successful,
                    Message = ServiceResources.SUCCESS,
                };
            }
            catch (Exception exc)
            {
                Logger.Error($@"Exception:{exc.Message}:{DateTime.Now}");

                return new ConfirmEmailResult
                {
                    Status = ConfirmEmailStatus.InternalServerError,
                    Message = ServiceResources.INTERNAL_SERVER_ERROR
                };
            }
        }

        public async Task<GetCompanyTypesResult> GetCompanyTypes(GetCompanyTypesRequest request)
        {
            try
            {
                var entityCompanyType = await _dbContext.CompanyType
                    .Include(i => i.CompanyTypeTranslations)
                    .AsNoTracking()
                    .ToListAsync();

                if (!entityCompanyType.Any())
                    return new GetCompanyTypesResult
                    {
                        Status = GetCompanyTypesStatus.NotFound,
                        Message = ServiceResources.RESOURCE_NOT_FOUND,
                    };

                var hasTranslation =
                    await _dbContext.CompanyTypeTranslation.AnyAsync(
                        r => r.LanguageId == request.Context.LanguageId);

                if (!hasTranslation)
                    return new GetCompanyTypesResult
                    {
                        Status = GetCompanyTypesStatus.NotFound,
                        Message = ServiceResources.TRANSLATION_NOT_FOUND,
                    };

                var result = new GetCompanyTypesResult()
                {
                    CompanyTypes = entityCompanyType.Select(r => new CompanyTypeDto()
                    {
                        Id = r.Id,
                        Name = r.CompanyTypeTranslations
                            .FirstOrDefault(r => r.LanguageId == request.Context.LanguageId)?.Name
                    }).ToList()
                };

                return new GetCompanyTypesResult
                {
                    Status = GetCompanyTypesStatus.Successful,
                    Message = ServiceResources.RESOURCE_RETRIEVED,
                    CompanyTypes = result.CompanyTypes
                };
            }
            catch (Exception exc)
            {
                Logger.Error($@"Exception:{exc.Message}:{DateTime.Now}");

                return new GetCompanyTypesResult
                {
                    Status = GetCompanyTypesStatus.InternalServerError,
                    Message = ServiceResources.INTERNAL_SERVER_ERROR
                };
            }
        }

        public async Task<GetCompanyFilesByCompanyTypeResult> GetCompanyFilesByCompanyType(GetCompanyFilesByCompanyTypeRequest request)
        {
            var validationResult = _validationService.Validate(typeof(GetCompanyFilesByCompanyTypeValidator), request);

            if (!validationResult.IsValid)
                return new GetCompanyFilesByCompanyTypeResult
                {
                    Status = GetCompanyFilesByCompanyTypeStatus.InvalidInput,
                    Message = ServiceResources.INVALID_INPUT_ERROR,
                    ValidationMessages = validationResult.ErrorMessages
                };

            try
            {
                var entityCompanyTypeFiles = await _dbContext.CompanyTypeFile
                    .Include(i => i.CompanyFileType)
                    .ThenInclude(i => i.CompanyFileTypeTranslations)
                    .Where(r => r.CompanyTypeId == request.CompanyTypeId)
                    .AsNoTracking()
                    .ToListAsync();

                if (!entityCompanyTypeFiles.Any())
                    return new GetCompanyFilesByCompanyTypeResult
                    {
                        Status = GetCompanyFilesByCompanyTypeStatus.NotFound,
                        Message = ServiceResources.RESOURCE_NOT_FOUND,
                    };

                var hasTranslation =
                    await _dbContext.CompanyFileTypeTranslation.AnyAsync(
                        r => r.LanguageId == request.Context.LanguageId);

                if (!hasTranslation)
                    return new GetCompanyFilesByCompanyTypeResult
                    {
                        Status = GetCompanyFilesByCompanyTypeStatus.NotFound,
                        Message = ServiceResources.TRANSLATION_NOT_FOUND,
                    };

                var result = new GetCompanyFilesByCompanyTypeResult()
                {
                    CompanyFiles = entityCompanyTypeFiles.Select(r => new CompanyTypeFileDto()
                    {
                        Id = r.CompanyFileTypeId,
                        Name = r.CompanyFileType.CompanyFileTypeTranslations
                            .FirstOrDefault(r => r.LanguageId == request.Context.LanguageId)?.Name
                    }).ToList()
                };

                return new GetCompanyFilesByCompanyTypeResult
                {
                    Status = GetCompanyFilesByCompanyTypeStatus.Successful,
                    Message = ServiceResources.RESOURCE_RETRIEVED,
                    CompanyFiles = result.CompanyFiles
                };
            }
            catch (Exception exc)
            {
                Logger.Error($@"Exception:{exc.Message}:{DateTime.Now}");

                return new GetCompanyFilesByCompanyTypeResult
                {
                    Status = GetCompanyFilesByCompanyTypeStatus.InternalServerError,
                    Message = ServiceResources.INTERNAL_SERVER_ERROR
                };
            }
        }

        public async Task<GetCompanyTermsOfServicesResult> GetCompanyTermsOfServices(GetCompanyTermsOfServicesRequest request)
        {
            try
            {
                var entityCompanyTos = await _dbContext.CompanyTermsOfServices
                    .Where(r => r.LanguageId == request.Context.LanguageId && r.TypeId == request.ResourceId)
                    .AsNoTracking()
                    .FirstOrDefaultAsync();

                if (entityCompanyTos == null)
                    return new GetCompanyTermsOfServicesResult
                    {
                        Status = GetCompanyTermsOfServicesStatus.NotFound,
                        Message = ServiceResources.RESOURCE_NOT_FOUND,
                    };

                var result = new GetCompanyTermsOfServicesResult()
                {
                    TermsOfServices = new CompanyTermsOfServicesDto
                    {
                        HtmlContent = entityCompanyTos.HtmlContent,
                        Name = entityCompanyTos.Name
                    }
                };

                return new GetCompanyTermsOfServicesResult
                {
                    Status = GetCompanyTermsOfServicesStatus.Successful,
                    Message = ServiceResources.RESOURCE_RETRIEVED,
                    TermsOfServices = result.TermsOfServices
                };
            }
            catch (Exception exc)
            {
                Logger.Error($@"Exception:{exc.Message}:{DateTime.Now}");

                return new GetCompanyTermsOfServicesResult
                {
                    Status = GetCompanyTermsOfServicesStatus.InternalServerError,
                    Message = ServiceResources.INTERNAL_SERVER_ERROR
                };
            }
        }

        public async Task<GetCompanyTermsOfServiceTypeResult> GetCompanyTermsOfServiceTypes(GetCompanyTermsOfServiceTypeRequest request)
        {
            try
            {
                var entityCompanyTos = await _dbContext.CompanyTermsOfServices
                    .Where(r => r.LanguageId == request.Context.LanguageId)
                    .AsNoTracking()
                    .ToListAsync();

                if (!entityCompanyTos.Any())
                    return new GetCompanyTermsOfServiceTypeResult
                    {
                        Status = GetCompanyTermsOfServiceTypeStatus.NotFound,
                        Message = ServiceResources.RESOURCE_NOT_FOUND,
                    };

                var result = new GetCompanyTermsOfServiceTypeResult()
                {
                    TermsOfServiceTypes = entityCompanyTos.Select(r => new CompanyTermsOfServiceTypeDto
                    {
                        Id = r.TypeId,
                        Name = r.Name,
                    }).ToList(),
                };

                return new GetCompanyTermsOfServiceTypeResult
                {
                    Status = GetCompanyTermsOfServiceTypeStatus.Successful,
                    Message = ServiceResources.RESOURCE_RETRIEVED,
                    TermsOfServiceTypes = result.TermsOfServiceTypes
                };
            }
            catch (Exception exc)
            {
                Logger.Error($@"Exception:{exc.Message}:{DateTime.Now}");

                return new GetCompanyTermsOfServiceTypeResult
                {
                    Status = GetCompanyTermsOfServiceTypeStatus.InternalServerError,
                    Message = ServiceResources.INTERNAL_SERVER_ERROR
                };
            }
        }

        public async Task<CompleteCompanyPaymentOrderResult> CompleteCompanyPaymentOrder(CompleteCompanyPaymentOrderRequest request)
        {
            var validationResult = _validationService.Validate(typeof(UpdateCompanyPaymentStatusValidator), request);

            if (!validationResult.IsValid)
                return new CompleteCompanyPaymentOrderResult
                {
                    Status = CompleteCompanyPaymentOrderStatus.InvalidInput,
                    Message = ServiceResources.INVALID_INPUT_ERROR,
                    ValidationMessages = validationResult.ErrorMessages
                };

            await using var transaction = await _dbContext.Database.BeginTransactionAsync();

            try
            {
                var entityCompany =
                    await _dbContext.Company.Where(r => r.Id == request.CompanyId).AsNoTracking().FirstOrDefaultAsync();

                if (entityCompany == null)
                    return new CompleteCompanyPaymentOrderResult
                    {
                        Status = CompleteCompanyPaymentOrderStatus.NotFound,
                        Message = ServiceResources.COMPANY_NOT_FOUND,
                    };

                var entityCompanyPayment = await _dbContext.CompanyPayment
                    .Where(r => r.CompanyId == request.CompanyId && r.Id == request.PaymentId).FirstOrDefaultAsync();

                if (entityCompanyPayment == null)
                    return new CompleteCompanyPaymentOrderResult
                    {
                        Status = CompleteCompanyPaymentOrderStatus.NotFound,
                        Message = ServiceResources.RESOURCE_NOT_FOUND,
                    };

                entityCompanyPayment.IsPaymentTaken = true;
                entityCompanyPayment.PaymentOrderId = request.OrderId;
                entityCompanyPayment.UpdatedAt = DateTime.Now;
                entityCompanyPayment.UpdatedBy = request.Context.Identity.Email;

                _dbContext.CompanyPayment.Update(entityCompanyPayment);
                await _dbContext.SaveChangesAsync();

                if (entityCompanyPayment.PaymentType == (int)Enums.Enums.PaymentType.Slot)
                {
                    var slotEntities = await _dbContext.SlotDemand
                        .Where(r => r.CompanyPaymentId == entityCompanyPayment.Id).ToListAsync();

                    slotEntities.ForEach(f => f.StatusId = (int)Enums.Enums.B2BApprovalStatusWithPayment.PaymentCompleted);

                    _dbContext.UpdateRange(slotEntities);

                    await _dbContext.SaveChangesAsync();
                }

                await transaction.CommitAsync();

                return new CompleteCompanyPaymentOrderResult
                {
                    Status = CompleteCompanyPaymentOrderStatus.Successful,
                    Message = ServiceResources.RESOURCE_UPDATED,
                    Id = entityCompanyPayment.Id
                };
            }
            catch (Exception exc)
            {
                Logger.Error($@"Exception:{exc.Message}:{DateTime.Now}");

                await transaction.RollbackAsync();
                return new CompleteCompanyPaymentOrderResult
                {
                    Status = CompleteCompanyPaymentOrderStatus.InternalServerError,
                    Message = ServiceResources.INTERNAL_SERVER_ERROR
                };
            }
        }

        public async Task<AddCompanyUserMessageResult> AddCompanyUserMessage(AddCompanyUserMessageRequest request)
        {
            var validationResult = _validationService.Validate(typeof(AddCompanyUserMessageValidator), request);

            if (!validationResult.IsValid)
                return new AddCompanyUserMessageResult
                {
                    Status = AddCompanyUserMessageStatus.InvalidInput,
                    Message = ServiceResources.INVALID_INPUT_ERROR,
                    ValidationMessages = validationResult.ErrorMessages
                };

            await using var transaction = await _dbContext.Database.BeginTransactionAsync();

            try
            {
                var entityCompany =
                    await _dbContext.Company.Where(r => r.Id == request.CompanyId).AsNoTracking().FirstOrDefaultAsync();

                if (entityCompany == null)
                    return new AddCompanyUserMessageResult
                    {
                        Status = AddCompanyUserMessageStatus.NotFound,
                        Message = ServiceResources.COMPANY_NOT_FOUND,
                    };

                var entityCompanyUser =
                     await _dbContext.CompanyUser.Where(r => r.Id == request.UserId && r.CompanyId == request.CompanyId).AsNoTracking().FirstOrDefaultAsync();

                if (entityCompanyUser == null)
                    return new AddCompanyUserMessageResult
                    {
                        Status = AddCompanyUserMessageStatus.NotFound,
                        Message = ServiceResources.USER_NOT_FOUND,
                    };

                var entityUserResponse = await _dbContext.CompanyUserChat.AddAsync(new CompanyUserChat()
                {
                    CompanyId = request.CompanyId,
                    CompanyUserId = request.UserId,
                    Message = request.Message,
                    CreatedBy = request.Context.Identity.Email,
                    CreatedAt = DateTime.Now,
                });

                await _dbContext.SaveChangesAsync();

                await transaction.CommitAsync();

                var chatKey = await _redisClient.GetAsync<List<ChatDto>>($"b2b:chat:{request.CompanyId}:{request.UserId}");

                if (chatKey == null || !chatKey.Any())
                {
                    await _redisClient.AddTimelessAsync($"b2b:chat:{request.CompanyId}:{request.UserId}", new List<ChatDto> { new ChatDto
                    {
                      Message = request.Message,
                      CreatedAt = DateTime.Now,
                      CreatedBy = $"{entityCompanyUser.Name} {entityCompanyUser.Surname}"
                    }});

                    chatKey = new List<ChatDto>()
                    {
                        new ChatDto()
                        {
                            Message = request.Message,
                            CreatedAt = DateTime.Now,
                            CreatedBy = $"{entityCompanyUser.Name} {entityCompanyUser.Surname}"
                        }
                    };
                }
                else
                {
                    chatKey.Add(new ChatDto
                    {
                        Message = request.Message,
                        CreatedAt = DateTime.Now,
                        CreatedBy = $"{entityCompanyUser.Name} {entityCompanyUser.Surname}"
                    });

                    await _redisClient.AddTimelessAsync($"b2b:chat:{request.CompanyId}:{request.UserId}", chatKey);
                }

                return new AddCompanyUserMessageResult
                {
                    Status = AddCompanyUserMessageStatus.Successful,
                    Message = ServiceResources.RESOURCE_CREATED,
                    Id = entityUserResponse.Entity.Id,
                    Messages = chatKey
                };
            }
            catch
            {
                await transaction.RollbackAsync();
                return new AddCompanyUserMessageResult
                {
                    Status = AddCompanyUserMessageStatus.InternalServerError,
                    Message = ServiceResources.INTERNAL_SERVER_ERROR
                };
            }
        }

        #region Identity Server

        private async Task<IdentityServerLoginResult> IdentityServerLogin(LoginRequest request)
        {
            var baseAddress = _configuration.GetValue<string>("B2BIdentityServer:BaseAddress");
            var clientId = _configuration.GetValue<string>("B2BIdentityServer:ClientId");
            var clientSecret = _configuration.GetValue<string>("B2BIdentityServer:ClientSecret");

            var loginRequest = new IdentityServerLoginRequest()
            {
                client_id = clientId,
                client_secret = clientSecret,
                grant_type = request.GrantType,
                password = request.Password,
                scope = request.Scope,
                username = request.Username
            };

            return await RestHttpClient.Create().PostForm<IdentityServerLoginResult>
                (baseAddress + IsRouting.Login, Header, loginRequest).ConfigureAwait(false);
        }

        private async Task<IdentityServerPublicRegisterResult> IdentityServerPublicRegister(string email, string password, List<RoleAssignDto> roles)
        {
            var baseAddress = _configuration.GetValue<string>("B2BIdentityServer:BaseAddress");
            var applicationResourceId = _configuration.GetValue<string>("B2BIdentityServer:ApplicationResourceId");

            var registerRequest = new IdentityServerExternalRegisterRequest()
            {
                Password = password,
                Email = email,
                ApplicationResourceId = applicationResourceId,
                Roles = roles
            };

            return await RestHttpClient.Create().Post<IdentityServerPublicRegisterResult>
                (baseAddress + IsRouting.PublicRegister, Header, registerRequest).ConfigureAwait(false);
        }

        private async Task<IdentityServerExternalRegisterResult> IdentityServerExternalRegister(IContext context, string email, string password, List<RoleAssignDto> roles)
        {
            var baseAddress = _configuration.GetValue<string>("B2BIdentityServer:BaseAddress");
            var applicationResourceId = _configuration.GetValue<string>("B2BIdentityServer:ApplicationResourceId");

            Header.Add("Authorization", $"Bearer {context.Identity.Token}");

            var registerRequest = new IdentityServerExternalRegisterRequest()
            {
                Password = password,
                Email = email,
                ApplicationResourceId = applicationResourceId,
                Roles = roles
            };

            return await RestHttpClient.Create().Post<IdentityServerExternalRegisterResult>
                (baseAddress + IsRouting.ExternalRegister, Header, registerRequest).ConfigureAwait(false);
        }

        private async Task<IdentityServerChangePasswordResult> IdentityServerChangePassword(IContext context, string newPassword, string currentPassword, string email)
        {
            var baseAddress = _configuration.GetValue<string>("B2BIdentityServer:BaseAddress");
            var applicationResourceId = _configuration.GetValue<string>("B2BIdentityServer:ApplicationResourceId");

            Header.Add("Authorization", $"Bearer {context.Identity.Token}");

            var changePasswordRequest = new IdentityServerChangePasswordRequest()
            {
                NewPassword = newPassword,
                ApplicationResourceId = applicationResourceId,
                Email = email,
                CurrentPassword = currentPassword
            };

            return await RestHttpClient.Create().Post<IdentityServerChangePasswordResult>
                (baseAddress + IsRouting.ChangePassword, Header, changePasswordRequest).ConfigureAwait(false);
        }

        private async Task<IdentityServerForgotPasswordResult> IdentityServerForgotPassword(string email)
        {
            var baseAddress = _configuration.GetValue<string>("B2BIdentityServer:BaseAddress");
            var applicationResourceId = _configuration.GetValue<string>("B2BIdentityServer:ApplicationResourceId");

            var forgotPasswordRequest = new IdentityServerForgotPasswordRequest()
            {
                Email = email,
                ApplicationId = applicationResourceId,
            };

            return await RestHttpClient.Create().Post<IdentityServerForgotPasswordResult>
                (baseAddress + IsRouting.ForgotPassword, Header, forgotPasswordRequest).ConfigureAwait(false);
        }

        private async Task<IdentityServerResetPasswordResult> IdentityServerResetPassword(string email, string code, string password, string confirmPassword)
        {
            var baseAddress = _configuration.GetValue<string>("B2BIdentityServer:BaseAddress");
            var applicationResourceId = _configuration.GetValue<string>("B2BIdentityServer:ApplicationResourceId");

            var forgotPasswordRequest = new IdentityServerResetPasswordRequest()
            {
                Email = email,
                Code = code,
                ConfirmPassword = confirmPassword,
                Password = password,
                ApplicationId = applicationResourceId,
            };

            return await RestHttpClient.Create().Post<IdentityServerResetPasswordResult>
                (baseAddress + IsRouting.ResetPassword, Header, forgotPasswordRequest).ConfigureAwait(false);
        }

        private async Task<IdentityServerConfirmEmailResult> IdentityServerConfirmEmail(string userId, string code)
        {
            var baseAddress = _configuration.GetValue<string>("B2BIdentityServer:BaseAddress");

            var forgotPasswordRequest = new IdentityServerConfirmEmailRequest()
            {
                UserId = userId,
                Code = code,
            };

            return await RestHttpClient.Create().Post<IdentityServerConfirmEmailResult>
                (baseAddress + IsRouting.ResetPassword, Header, forgotPasswordRequest).ConfigureAwait(false);
        }

        #endregion

        #region Private Methods

        private async Task<decimal> CalculateCompletionRate(Entity.Entities.B2B.Company.Company companyEntity, CompanyUser companyUser,
            List<CompanyFile> companyFiles, List<CompanyUserFile> companyUserFiles)
        {
            var companyCompletionEntity = await _dbContext.CompanyProfileCompletion.ToListAsync();

            decimal rate = 0;

            foreach (var property in companyCompletionEntity)
            {
                object propertyValue;
                switch (property.PropertyType)
                {
                    case (int)Enums.Enums.CompanyCompletionPropertyType.Company:
                        propertyValue = companyEntity.GetType().GetProperty(property.PropertyName).GetValue(companyEntity, null);
                        if (propertyValue != null)
                        {
                            if (propertyValue.IsNumeric() && !propertyValue.IsNumericAndGreaterThenZero())
                                rate -= property.Rate;
                            else if (propertyValue.IsString() &&
                                     propertyValue is "")
                                rate -= property.Rate;
                            else
                                rate += property.Rate;
                        }
                        else
                            rate -= property.Rate;
                        break;
                    case (int)Enums.Enums.CompanyCompletionPropertyType.User:
                        propertyValue = companyUser.GetType().GetProperty(property.PropertyName).GetValue(companyUser, null);
                        if (propertyValue != null)
                        {
                            if (propertyValue.IsNumeric() && !propertyValue.IsNumericAndGreaterThenZero())
                                rate -= property.Rate;
                            else if (propertyValue.IsString() &&
                                     propertyValue is null or "")
                                rate -= property.Rate;
                            else
                                rate += property.Rate;
                        }
                        else
                            rate -= property.Rate;
                        break;
                    case (int)Enums.Enums.CompanyCompletionPropertyType.CompanyFile:
                        if (companyFiles != null && companyFiles.Any())
                        {
                            foreach (var file in companyFiles)
                            {
                                propertyValue = file.GetType().GetProperty(property.PropertyName).GetValue(file, null);
                                if (propertyValue != null)
                                {
                                    if (propertyValue.IsNumeric() && !propertyValue.IsNumericAndGreaterThenZero())
                                        rate -= property.Rate;
                                    else if (propertyValue.IsString() &&
                                             propertyValue is "")
                                        rate -= property.Rate;
                                    else
                                        rate += property.Rate;
                                }
                                else
                                    rate -= property.Rate;
                            }
                        }
                        break;
                    case (int)Enums.Enums.CompanyCompletionPropertyType.UserFile:
                        if (companyUserFiles != null && companyUserFiles.Any())
                        {
                            foreach (var file in companyUserFiles)
                            {
                                propertyValue = file.GetType().GetProperty(property.PropertyName).GetValue(file, null);
                                if (propertyValue != null)
                                {
                                    if (propertyValue.IsNumeric() && !propertyValue.IsNumericAndGreaterThenZero())
                                        rate -= property.Rate;
                                    else if (propertyValue.IsString() &&
                                             propertyValue is "")
                                        rate -= property.Rate;
                                    else
                                        rate += property.Rate;
                                }
                                else
                                    rate -= property.Rate;
                            }
                        }
                        break;
                }
            }

            return rate;
        }

        private static string GenerateDummyPassword()
        {
            const string letters = "abcdefghijklmnopqrstuvwxyzABCDEFGHIJKLMNOPQRSTUVWXYZ";
            const string numbers = "1234567890";
            var res = new StringBuilder();
            var rnd = new Random();
            for (var i = 0; i < 8; i++)
            {
                if (i == 1)
                    res.Append(letters[rnd.Next(26)]);

                if (i == 2)
                    res.Append(numbers[rnd.Next(numbers.Length)]);

                if (i == 4)
                    res.Append(numbers[rnd.Next(numbers.Length)]);

                if (i == 7)
                    res.Append(letters[rnd.Next(27, 52)]);

                res.Append(letters[rnd.Next(letters.Length)]);
            }

            return res.ToString();
        }

        private static LookupValue GetLookupValue(int enumType, int? value)
        {
            return EnumExtensions.GetEnumAsDictionary(LookupTypeFactory.GetInstance(enumType))
                .Select(x => new LookupValue { Id = x.Key.ToString(), DisplayValue = x.Value })
                .FirstOrDefault(p => p.Id == value.ToString());
        }

        private static DateTimeOffset AddDaysWithLocalTime(DateTimeOffset date, int days)
        {
            var requestedDate = new DateTime(date.Year, date.Month, date.Day, 0, 0, 0);
            requestedDate = DateTime.SpecifyKind(requestedDate, DateTimeKind.Local);
            DateTimeOffset dateWithOffset = requestedDate;
            var addedDays = dateWithOffset.AddDays(days);
            return addedDays;
        }

        private static string GetCountryName(Entity.Entities.Country.Country country, int languageId)
        {
            return languageId switch
            {
                1 => country.NameTr,
                3 => country.NameAr,
                _ => country.Name
            };
        }


        #endregion
    }
}
