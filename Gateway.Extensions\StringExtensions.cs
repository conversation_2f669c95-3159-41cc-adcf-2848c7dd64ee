﻿using System;
using System.Globalization;
using System.IO;
using System.Linq;
using System.Net.Mail;
using System.Text;
using PhoneNumbers;

namespace Gateway.Extensions
{
    public static class StringExtensions
    {
        public static string UppercaseFirstCharOfWord(this string value)
        {
            if (string.IsNullOrEmpty(value))
                return string.Empty;

            return value.Length > 1
                ? $"{char.ToUpper(value[0])}{value.Substring(1).ToLower()}"
                : char.ToUpper(value[0]).ToString();
        }

        public static string UppercaseFirstCharOfWords(this string value)
        {
            var array = value.ToLower().ToCharArray();

            if (array.Length >= 1 && char.IsLower(array[0])) array[0] = char.ToUpper(array[0]);

            for (var i = 1; i < array.Length; i++)
            {
                if (array[i - 1] != ' ') continue;
                if (char.IsLower(array[i]))
                    array[i] = char.ToUpper(array[i]);
            }

            return new string(array);
        }

        public static bool IsNullOrWhitespace(this string value)
        {
            return value == null || value.Trim().Length == 0;
        }

        public static bool IsValidEmail(this string emailaddress)
        {
            try
            {
                MailAddress m = new MailAddress(emailaddress);

                return true;
            }
            catch (FormatException)
            {
                return false;
            }
        }

        public static bool ValidTelephoneNo(this string telNo)
        {
            try
            {
                return PhoneNumberUtil.IsViablePhoneNumber(telNo);
            }
            catch
            {
                return false;
            }
        }

        public static bool IsDateTime(this DateTime date)
        {
            return DateTime.TryParse(date.ToString(), out var tempDate);
        }

        public static string SanitizeJson(this string value)
        {
            var sb = new StringBuilder(value);

            sb.Replace(@"\\", @"\").Replace(@"\\\\", @"\\");

            return sb.ToString();
        }

        public static T StringToEnum<T>(this string value, bool ignoreCase = true)
        {
            try
            {
                return (T)Enum.Parse(typeof(T), value, ignoreCase);
            }
            catch
            {
                return (T)Enum.Parse(typeof(T), "Unknown", ignoreCase);
            }
        }

        public static int GetDeterministicHashCode(this string value)
        {
            unchecked
            {
                var hash1 = (5381 << 16) + 5381;
                var hash2 = hash1;

                var input = value.ToLower();

                for (var i = 0; i < input.Length; i += 2)
                {
                    hash1 = ((hash1 << 5) + hash1) ^ input[i];
                    if (i == input.Length - 1)
                        break;
                    hash2 = ((hash2 << 5) + hash2) ^ input[i + 1];
                }

                var result = hash1 + (hash2 * 1566083941);

                if (result < 0)
                    result *= -1;

                return result;
            }
        }

        public static Guid ToGuid(this string value)
        {
            Guid.TryParse(value, out var guid);
            return guid;
        }

        public static string RemoveLastChar(this string value)
        {
            if (!value.IsNullOrWhitespace())
                value = value.Remove(value.Length - 1, 1);

            return value;
        }

        public static bool IsGuid(this string value)
        {
            return Guid.TryParse(value, out _);
        }

        public static bool IsAlphaNumeric(this string value)
        {
            return value.All(char.IsLetterOrDigit);
        }
        
        public static DateTime StringToDateTime(this string value, string format, CultureInfo culture)
        {
            try
            {
                return DateTime.ParseExact(s: value, format: format, provider: culture);
            }
            catch 
            {
                return DateTime.MinValue;
            }
        }

        public static string StringTakeBeforeSpace(this string value)
        {
            try
            {
                return new string(value.Trim().TakeWhile(c => c != ' ').ToArray()).Trim();
            }
            catch
            {
                return value;
            }
        }

        public static string ConvertToBase64(Stream stream)
        {
            byte[] bytes;
            using (var memoryStream = new MemoryStream())
            {
                stream.CopyTo(memoryStream);
                bytes = memoryStream.ToArray();
            }

            return Convert.ToBase64String(bytes);
        }

        public static bool HasNumeric(this string value) => value.Any(char.IsNumber);

        public static string TrimStartUntil(this string target, string trimString)
        {
            if (string.IsNullOrEmpty(trimString)) return target;

            string result = target;
            while (result.StartsWith(trimString))
            {
                result = result.Substring(trimString.Length);
            }

            return result;
        }

        public static bool BeThreeDigitNumber(string value)
        {
            if (string.IsNullOrEmpty(value) || value.Length != 3)
                return false;

            if (!int.TryParse(value, out var number))
                return false;

            return number is >= 0 and <= 999;
        }
    }
}
