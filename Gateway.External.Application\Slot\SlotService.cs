﻿using System;
using System.Linq;
using System.Threading.Tasks;
using DocumentFormat.OpenXml.InkML;
using Gateway.Extensions;
using Gateway.External.Application.Slot.Dto;
using Gateway.External.Application.Slot.Validator;
using Gateway.External.Persistence;
using Gateway.External.Resources;
using Gateway.Validation;
using Microsoft.EntityFrameworkCore;
using Serilog;
using static Gateway.External.Application.Enums.Enums;

namespace Gateway.External.Application.Slot
{
    public class SlotService : ISlotService
    {
        private static readonly ILogger Logger = Log.ForContext<SlotService>();
        private readonly ApiDbContext _dbContext;
        private readonly IValidationService _validationService;

        public SlotService(IValidationService validationService, ApiDbContext dbContext)
        {
            _validationService = validationService;
            _dbContext = dbContext;
        }

        public async Task<GetSlotsResult> GetSlots(GetSlotsRequest request)
        {
            var validationResult = _validationService.Validate(typeof(GetSlotsValidator), request);

            if (!validationResult.IsValid)
                return new GetSlotsResult
                {
                    Status = GetSlotsStatus.InvalidInput,
                    Message = ServiceResources.INVALID_INPUT_ERROR,
                    ValidationMessages = validationResult.ErrorMessages
                };

            try
            {
                var addDaysSlotTime = AddDaysWithLocalTime(request.Date, 1);

                var slotQuery = _dbContext.Slot
                .Include(i => i.Agency)
                .Include(i => i.BranchApplicationCountry)
                .ThenInclude(i => i.Branch)
                .ThenInclude(i => i.BranchTranslations)
                .Include(i => i.BranchApplicationCountry)
                .ThenInclude(i => i.Country)
                .Where(p => !p.IsDeleted && p.IsActive &&
                          p.BranchApplicationCountry.BranchId == request.BranchId && p.Quota > request.ApplicantCount && p.SlotTime >= request.Date.Date && p.SlotTime < addDaysSlotTime
                          && p.SlotTime >= request.Date.Date && (p.SlotTypeId == (int)ChannelType.Portal || p.SlotTypeId == request.Context.SlotTypeId));

                var availableSlots = await slotQuery.ToListAsync();

                if (availableSlots.Any(s => s.BranchApplicationCountry.Branch.IsSlotTypesConnected))
                    availableSlots = availableSlots.Where(s => s.SlotTypeId == s.BranchApplicationCountry.Branch.ConnectedSlotTypeId).ToList();
                else
                    availableSlots = availableSlots.Where(s => s.SlotTypeId == request.Context.SlotTypeId).ToList();

                if (request.SlotId.IsNumericAndGreaterThenZero())
                {
                    var existingSlot = await _dbContext.Slot.Include(i => i.Agency)
                    .Include(i => i.BranchApplicationCountry)
                    .ThenInclude(i => i.Branch)
                    .ThenInclude(i => i.BranchTranslations)
                    .Include(i => i.BranchApplicationCountry)
                    .ThenInclude(i => i.Country)
                    .Where(s => s.Id == request.SlotId)
                    .FirstOrDefaultAsync();

                    availableSlots.Add(existingSlot);
                }

                if (availableSlots.Count == 0)
                    return new GetSlotsResult
                    {
                        Status = GetSlotsStatus.ResourceNotFound,
                        Message = request.Context.ChannelTypeId == (int)ChannelType.Mobile ?
                            ServiceResources.MOBILE_SLOT_NOT_FOUND :
                            ServiceResources.RESOURCE_NOT_FOUND
                    };

                if (availableSlots.Count > 0)
                {
                    return new GetSlotsResult
                    {
                        Status = GetSlotsStatus.Successful,
                        Message = ServiceResources.RESOURCE_RETRIEVED,
                        Slots = availableSlots.OrderBy(s => s.SlotTime).Select(p => new SlotDto
                        {
                            Id = p.Id,
                            SlotTime = p.SlotTime,
                            SlotTimeText = p.SlotTime.ToString("dd/MM/yyyy HH:mm"),
                            Quota = p.Quota,
                            BranchName = p.BranchApplicationCountry.Branch.BranchTranslations
                                             .FirstOrDefault(branchTranslation => branchTranslation.LanguageId == request.Context.LanguageId)?.Name ??
                                         p.BranchApplicationCountry.Branch.BranchTranslations.FirstOrDefault(r => r.LanguageId == 2)?.Name,
                            CountryName = GetCountryName(p.BranchApplicationCountry.Country, request.Context.LanguageId)
                        }).AsEnumerable()
                    };
                }

                return new GetSlotsResult
                {
                    Status = GetSlotsStatus.ResourceNotFound,
                    Message = request.Context.ChannelTypeId == (int)ChannelType.Mobile ?
                        ServiceResources.MOBILE_SLOT_NOT_FOUND :
                        ServiceResources.RESOURCE_NOT_FOUND
                };
            }
            catch (Exception exc)
            {
                Logger.Error($@"Exception:{exc.Message}:{DateTime.Now}");

                return new GetSlotsResult
                {
                    Status = GetSlotsStatus.InternalServerError,
                    Message = ServiceResources.INTERNAL_SERVER_ERROR
                };
            }
        }

        #region Private Methods

        private static DateTimeOffset AddDaysWithLocalTime(DateTimeOffset date, int days)
        {
            var requestedDate = new DateTime(date.Year, date.Month, date.Day, 0, 0, 0);
            requestedDate = DateTime.SpecifyKind(requestedDate, DateTimeKind.Local);
            DateTimeOffset dateWithOffset = requestedDate;
            var addedDays = dateWithOffset.AddDays(days);
            return addedDays;
        }

        private static string GetCountryName(Entity.Entities.Country.Country country, int languageId)
        {
            return languageId switch
            {
                1 => country.NameTr,
                3 => country.NameAr,
                _ => country.Name
            };
        }

        #endregion

    }
}