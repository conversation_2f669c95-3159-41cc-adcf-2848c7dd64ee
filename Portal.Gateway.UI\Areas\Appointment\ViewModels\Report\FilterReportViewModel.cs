﻿using FluentValidation;
using Portal.Gateway.Contracts.Entities.Enums;
using Portal.Gateway.Resources;
using System;
using System.Collections.Generic;

namespace Portal.Gateway.UI.Areas.Appointment.ViewModels.Report
{
    public class FilterReportViewModel
    {
        public FilterReportViewModel()
        {
        }
        public int FilterReportTypeId { get; set; }

        public int? FilterUserId { get; set; }

        public int? FilterBranchId { get; set; }

        public int? FilterBranchApplicationCountryId { get; set; }

        public DateTime? FilterStartDate { get; set; }

        public DateTime? FilterEndDate { get; set; }

        public bool? FilterCCError { get; set; }

        public List<int> FilterApplicationStatusIds { get; set; }

        public List<int> FilterBranchIds { get; set; }

        public DateTime? FilterReportDate { get; set; }

        public bool IsExternalReport => FilterReportTypeId is 
            (int)ExternalReportType.ReportQMSDepartment or
            (int)ExternalReportType.ReportQMS or
            (int)ExternalReportType.ReportQMSAction or
            (int)ExternalReportType.ReportQMSPersonal or
            (int)ExternalReportType.ReportCargoDeliveredToCourier or
            (int)ExternalReportType.CourierCheckReport;
    }

    public class FilterReportViewModelValidator : AbstractValidator<FilterReportViewModel>
    {
        public FilterReportViewModelValidator()
        {
            RuleFor(x => x.FilterReportTypeId)
                .NotEmpty().WithMessage(SiteResources.RequiredField)
                .GreaterThan(0).WithMessage(SiteResources.RequiredField);

            //Report 1-4-5-6-7-8-9-11-12-13-17-18-19-22-23-24-27-30-31-32-34-35-36-37-57
            When(x => x.FilterReportTypeId == (int)ReportType.AllStaffApplicationsByBranch
                    || x.FilterReportTypeId == (int)ReportType.DailyBalance
                    || x.FilterReportTypeId == (int)ReportType.DeletedApplications
                    || x.FilterReportTypeId == (int)ReportType.FreeApplications
                    || x.FilterReportTypeId == (int)ReportType.PartiallyRefundedApplications
                    //|| x.FilterReportTypeId == (int)ReportType.PcrStatus
                    // || x.FilterReportTypeId == (int)ReportType.Photobooth
                    || x.FilterReportTypeId == (int)ReportType.InsuranceApplications
                    || x.FilterReportTypeId == (int)ReportType.Insurance
                    //|| x.FilterReportTypeId == (int)ReportType.CashReport_1
                    //|| x.FilterReportTypeId == (int)ReportType.CashReport_2
                    || x.FilterReportTypeId == (int)ReportType.NonApplicationInsurance
                    //|| x.FilterReportTypeId == (int)ReportType.RejectionStatus
                    || x.FilterReportTypeId == (int)ReportType.Detail
                    || x.FilterReportTypeId == (int)ReportType.CancelCompletedApplications
                    //|| x.FilterReportTypeId == (int)ReportType.VisaRejection
                    || x.FilterReportTypeId == (int)ReportType.ExtraFees
                    //|| x.FilterReportTypeId == (int)ReportType.PCRCancellation
                    //|| x.FilterReportTypeId == (int)ReportType.InsuranceCancellation
                    //|| x.FilterReportTypeId == (int)ReportType.PCRPayment
                    //|| x.FilterReportTypeId == (int)ReportType.PCRGeneral
                    || x.FilterReportTypeId == (int)ReportType.Consular
                    //|| x.FilterReportTypeId == (int)ReportType.PCRCompanyPayment
                    || x.FilterReportTypeId == (int)ReportType.CargoCompanyPayment
                    || x.FilterReportTypeId == (int)ReportType.IndiaCargo
                    || x.FilterReportTypeId == (int)ReportType.IncorrectApplicationStatusReport
                , () =>
                {
                    RuleFor(x => x.FilterBranchId)
                        .NotEmpty().WithMessage(SiteResources.RequiredField)
                        .GreaterThan(0).WithMessage(SiteResources.RequiredField);

                    RuleFor(x => x.FilterStartDate)
                        .NotEmpty().WithMessage(SiteResources.RequiredField);

                    RuleFor(x => x.FilterEndDate)
                        .NotEmpty().WithMessage(SiteResources.RequiredField);
                });

            //Report 2
            When(x => x.FilterReportTypeId == (int)ReportType.StaffExtraFeeSales, () =>
            {
                RuleFor(x => x.FilterBranchId)
                    .NotEmpty().WithMessage(SiteResources.RequiredField)
                    .GreaterThan(0).WithMessage(SiteResources.RequiredField);

                RuleFor(x => x.FilterUserId)
                    .NotEmpty().WithMessage(SiteResources.RequiredField)
                    .GreaterThan(0).WithMessage(SiteResources.RequiredField);

                RuleFor(x => x.FilterStartDate)
                    .NotEmpty().WithMessage(SiteResources.RequiredField);

                RuleFor(x => x.FilterEndDate)
                    .NotEmpty().WithMessage(SiteResources.RequiredField);
            });

            //Report 3
            When(x => x.FilterReportTypeId == (int)ReportType.ScanCycle, () =>
            {
                RuleFor(x => x.FilterStartDate)
                    .NotEmpty().WithMessage(SiteResources.RequiredField);

                RuleFor(x => x.FilterEndDate)
                    .NotEmpty().WithMessage(SiteResources.RequiredField);

                RuleFor(x => x.FilterBranchIds)
                    .NotEmpty().WithMessage(SiteResources.RequiredField);
            });

            //Report 15-16-20-21-26-29-41-43-44-47-58-68-74-76-77-78-80
            When(x => x.FilterReportTypeId == (int)ReportType.CashReport_1
                    || x.FilterReportTypeId == (int)ReportType.CashReport_2
                    || x.FilterReportTypeId == (int)ReportType.OldCashReport_1
                    || x.FilterReportTypeId == (int)ReportType.OldCashReport_2
                    || x.FilterReportTypeId == (int)ReportType.AllApplications
                    || x.FilterReportTypeId == (int)ReportType.InsuranceCancellation
                    || x.FilterReportTypeId == (int)ReportType.ApplicationReportOfRejectedPassports
                    || x.FilterReportTypeId == (int)ReportType.AllBranchesDailyInsurance
                    || x.FilterReportTypeId == (int)ReportType.CountryBasedApplicationReport
                    || x.FilterReportTypeId == (int)ReportType.ForeignHealthInsuranceReport
                    || x.FilterReportTypeId == (int)ReportType.DeliveredToCargo
                    || x.FilterReportTypeId == (int)ReportType.Cargo
                    || x.FilterReportTypeId == (int)ReportType.CourierCheckReport
                    || x.FilterReportTypeId == (int)ReportType.IndiaAllApplications
                    || x.FilterReportTypeId == (int)ReportType.UnifiedCashReport_1 
                    || x.FilterReportTypeId == (int)ReportType.UnifiedCashReport_2
                    || x.FilterReportTypeId == (int)ReportType.MediationReport
                    || x.FilterReportTypeId == (int)ReportType.ReceivedAtEmbassyReport
                    || x.FilterReportTypeId == (int)ReportType.RejectedReport 
                    || x.FilterReportTypeId == (int)ReportType.CashReport_3
                    || x.FilterReportTypeId == (int)ReportType.RelatedInsurance
                    || x.FilterReportTypeId == (int)ReportType.ReportFreeInsurance
                    , () =>
                    {
                RuleFor(x => x.FilterStartDate)
                    .NotEmpty().WithMessage(SiteResources.RequiredField);

                RuleFor(x => x.FilterEndDate)
                    .NotEmpty().WithMessage(SiteResources.RequiredField);

                RuleFor(x => x.FilterBranchIds)
                    .NotEmpty().WithMessage(SiteResources.RequiredField);
            });

            //Report 28-56
            When(x => x.FilterReportTypeId == (int)ReportType.AllBranchesInsurance
            || x.FilterReportTypeId == (int)ReportType.TurkmenistanApplicationStatisticsReport
            || x.FilterReportTypeId == (int)ReportType.TurkmenistanStatisticReport
            || x.FilterReportTypeId == (int)ReportType.TurkmenistanApplicationStatisticsReport
            , () =>
            {
                RuleFor(x => x.FilterStartDate)
                    .NotEmpty().WithMessage(SiteResources.RequiredField);

                RuleFor(x => x.FilterEndDate)
                    .NotEmpty().WithMessage(SiteResources.RequiredField);
            });

            //Report 33-66
            When(x => x.FilterReportTypeId == (int)ReportType.InsuranceDetail
            || x.FilterReportTypeId == (int)ReportType.CourierCheckReport, () =>
            {
                RuleFor(x => x.FilterStartDate)
                    .NotEmpty().WithMessage(SiteResources.RequiredField);

                RuleFor(x => x.FilterEndDate)
                    .NotEmpty().WithMessage(SiteResources.RequiredField);

                RuleFor(x => x.FilterBranchIds)
                    .NotEmpty().WithMessage(SiteResources.RequiredField);
            });

            //Report 38-39-40
            //When(x => x.FilterReportTypeId == (int)ReportType.QMSReport
            //        || x.FilterReportTypeId == (int)ReportType.QMSTimelineReport
            //        || x.FilterReportTypeId == (int)ReportType.QMSPersonalReport, () =>
            //        {
            //            RuleFor(x => x.FilterReportDate)
            //                .NotEmpty().WithMessage(SiteResources.RequiredField);

            //            RuleFor(x => x.FilterBranchId)
            //                .NotEmpty().WithMessage(SiteResources.RequiredField)
            //                .GreaterThan(0).WithMessage(SiteResources.RequiredField);
            //        });
        }
    }
}
