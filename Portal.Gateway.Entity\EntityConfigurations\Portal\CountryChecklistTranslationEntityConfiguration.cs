﻿using Microsoft.EntityFrameworkCore;
using Microsoft.EntityFrameworkCore.Metadata.Builders;
using Portal.Gateway.Entity.Entities.Portal;

namespace Portal.Gateway.Entity.EntityConfigurations.Portal
{
    public class CountryChecklistTranslationEntityConfiguration : IEntityTypeConfiguration<CountryChecklistTranslation>
    {
        public void Configure(EntityTypeBuilder<CountryChecklistTranslation> builder)
        {
            builder.ToTable("CountryChecklistTranslation");

            builder.HasIndex(e => e.CountryChecklistId, "IX_CountryChecklistTranslation_CountryChecklistId");

            builder.HasIndex(e => e.LanguageId, "IX_CountryChecklistTranslation_LanguageId");

            #region StaticAuditableEntityConfig

            builder.HasKey(e => e.Id);
            builder.Property(e => e.Id).IsRequired().ValueGeneratedOnAdd();
            builder.Property(e => e.IsActive).IsRequired();
            builder.Property(e => e.IsDeleted).IsRequired();
            builder.Property(e => e.CreatedBy);
            builder.Property(e => e.CreatedAt).HasColumnType("timestamp with time zone");
            builder.Property(e => e.UpdatedBy);
            builder.Property(e => e.UpdatedAt).HasColumnType("timestamp with time zone");
            builder.Property(e => e.DeletedBy);
            builder.Property(e => e.DeletedAt).HasColumnType("timestamp with time zone");

            #endregion

            builder.Property(e => e.Name)
                .IsRequired()
                .HasColumnType("citext");

            builder.Property(e => e.CountryChecklistId).IsRequired();
            builder.Property(e => e.LanguageId).IsRequired();


            builder.HasOne(d => d.CountryChecklist)
                .WithMany(p => p.CountryChecklistTranslations)
                .HasForeignKey(d => d.CountryChecklistId)
                .OnDelete(DeleteBehavior.ClientSetNull);
        }
    }
}
