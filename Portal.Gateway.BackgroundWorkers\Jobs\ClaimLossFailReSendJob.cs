﻿using System.Globalization;
using Portal.Gateway.Contracts.Entities.Dto.Appointment.Application.Requests;
using Portal.Gateway.Contracts.Entities.Dto.Appointment.Insurance;
using Portal.Gateway.Contracts.Entities.Dto.DocumentManagement.Requests;
using Portal.Gateway.Contracts.Entities.Enums;
using Portal.Gateway.Contracts.Services;
using Portal.Gateway.ExternalServices.Contracts;
using Quartz;

namespace Portal.Gateway.BackgroundWorkers.Jobs
{
    [DisallowConcurrentExecution]
    internal class ClaimLossFailReSendJob : IJob
    {
        private readonly IInsuranceService _insuranceService;
        private readonly IAppointmentService _appointmentService;
        private readonly IDocumentManagementService _documentManagementService;
        private readonly ILogger<ClaimLossFailReSendJob> _logger;

        public ClaimLossFailReSendJob(
            IInsuranceService insuranceService,
            IAppointmentService appointmentService,
            IDocumentManagementService documentManagementService, 
            ILogger<ClaimLossFailReSendJob> logger)
        {
            _appointmentService = appointmentService;
            _insuranceService = insuranceService;
            _documentManagementService = documentManagementService;
            _logger = logger;
        }

        public async Task Execute(IJobExecutionContext context)
        {
            var claimFound = true;

            while (claimFound)
            {
                var getClaimLossIdAndClaimAmountListResponse = await _appointmentService.GetFailReSendClaimLossIdAndClaimAmountForJobAsync(1);

                if (getClaimLossIdAndClaimAmountListResponse != null)
                {
                    for (int i = 0; i < getClaimLossIdAndClaimAmountListResponse.ClaimLossIdAndClaimAmount.Count(); i++)
                    {
                        int id = getClaimLossIdAndClaimAmountListResponse.ClaimLossIdAndClaimAmount[i].ApplicationId;
                        String claimAmount = getClaimLossIdAndClaimAmountListResponse.ClaimLossIdAndClaimAmount[i].ClaimAmount;
                        DateTimeOffset lossDate = getClaimLossIdAndClaimAmountListResponse.ClaimLossIdAndClaimAmount[i].LossDate;
                        var request = new ApplicationRequestDto { Id = id };

                        var apiResponse = await _appointmentService.GetApplicationInsuranceInformationAsync(request);

                        var insuranceDocumentsApiRequest = new InsuranceDocumentsRequestDto
                        {
                            ReferenceIds = new List<int> { apiResponse.Id },
                        };

                        var getInsuranceDocumentsApiResponse = await _documentManagementService.GetInsuranceDocumentsAsync(insuranceDocumentsApiRequest);

                        if (getInsuranceDocumentsApiResponse.InsuranceDocuments != null)
                        {
                            var result = await _insuranceService.ClaimLossEntry(new ExternalServices.Models.Insurance.Requests.ClaimEntryServiceRequest()
                            {
                                PolicyNo = long.Parse(apiResponse.Insurance.Number[0]),
                                LossDate = lossDate.ToString("dd/MM/yyyy",CultureInfo.InvariantCulture),
                                LossTime = lossDate.ToString("HH:mm"),
                                Sufferers = new ExternalServices.Models.Insurance.Requests.Sufferers
                                {
                                    DataContractSufferer = new ExternalServices.Models.Insurance.Requests.DataContractSufferer
                                    {
                                        ClaimDate = DateTime.Today.ToString("dd/MM/yyyy",CultureInfo.InvariantCulture),
                                        ClaimTime = DateTime.Now.ToString("HH:mm"),
                                        SuffererName = apiResponse.Name,
                                        SuffererSurname = apiResponse.Surname,
                                        SuffererFirmName = apiResponse.Name + " " + apiResponse.Surname,
                                        ClaimAmount = claimAmount,
                                        ClaimerName = apiResponse.Name,
                                        ClaimerSurname = apiResponse.Surname,
                                        ClaimerFirmName = apiResponse.Name + " " + apiResponse.Surname,
                                        FirstClaimAmount = claimAmount,
                                        Outstandings = new ExternalServices.Models.Insurance.Requests.Outstandings
                                        {
                                            DataContractOutstanding = new ExternalServices.Models.Insurance.Requests.DataContractOutstanding
                                            {
                                                OutsDetail = new ExternalServices.Models.Insurance.Requests.OutsDetail
                                                {
                                                    DataContractOutsDetail = new ExternalServices.Models.Insurance.Requests.DataContractOutsDetail
                                                    {
                                                        OutstandingClaimAmount = claimAmount,
                                                        OutstandingClaimDate = DateTime.Today.ToString("dd/MM/yyyy",CultureInfo.InvariantCulture)
                                                    }
                                                },
                                                OutstandingClaimAmount = claimAmount,
                                                OutstandingClaimDate = DateTime.Today.ToString("dd/MM/yyyy",CultureInfo.InvariantCulture)
                                            }

                                        },
                                        Documents = new ExternalServices.Models.Insurance.Requests.Documents
                                        {
                                            DataContractDocument = new List<ExternalServices.Models.Insurance.Requests.DataContractDocument> {
                                  new ExternalServices.Models.Insurance.Requests.DataContractDocument
                                    {
                                        DocumentRequestDate = DateTime.Today.ToString("dd/MM/yyyy",CultureInfo.InvariantCulture),
                                        DocumentRequestTime = DateTime.Now.ToString("HH:mm"),
                                        DocumentCompletionDate = apiResponse.Document.EntryDate.ToString("dd/MM/yyyy",CultureInfo.InvariantCulture),
                                        DocumentCompletionTime = DateTime.Now.ToString("HH:mm"),
                                        DocumentData = getInsuranceDocumentsApiResponse.InsuranceDocuments.Count(q => q.FileTypeId==(int)ApplicationFileType.RejectionReturnStatement) > 0 ? Convert.ToBase64String(getInsuranceDocumentsApiResponse.InsuranceDocuments.Where(q => q.FileTypeId==(int)ApplicationFileType.RejectionReturnStatement).Select(q=>q.FileContent).FirstOrDefault()) : "",
                                        DocumentExtension =getInsuranceDocumentsApiResponse.InsuranceDocuments.Count(q => q.FileTypeId==(int)ApplicationFileType.RejectionReturnStatement) > 0 ? getInsuranceDocumentsApiResponse.InsuranceDocuments.Where(q => q.FileTypeId == (int)ApplicationFileType.RejectionReturnStatement).Select(q => q.FileExtension).FirstOrDefault() : ""
                                    },
                                    new ExternalServices.Models.Insurance.Requests.DataContractDocument
                                    {
                                        DocumentRequestDate = DateTime.Today.ToString("dd/MM/yyyy",CultureInfo.InvariantCulture),
                                        DocumentRequestTime = DateTime.Now.ToString("HH:mm"),
                                        DocumentCompletionDate = apiResponse.Document.EntryDate.ToString("dd/MM/yyyy",CultureInfo.InvariantCulture),
                                        DocumentCompletionTime = DateTime.Now.ToString("HH:mm"),
                                        DocumentData = getInsuranceDocumentsApiResponse.InsuranceDocuments.Count(q => q.FileTypeId==(int)ApplicationFileType.RejectionPassport) > 0 ? Convert.ToBase64String(getInsuranceDocumentsApiResponse.InsuranceDocuments.Where(q => q.FileTypeId==(int)ApplicationFileType.RejectionPassport).Select(q=>q.FileContent).FirstOrDefault()) : "",
                                        DocumentExtension = getInsuranceDocumentsApiResponse.InsuranceDocuments.Count(q => q.FileTypeId==(int)ApplicationFileType.RejectionPassport) > 0 ? getInsuranceDocumentsApiResponse.InsuranceDocuments.Where(q => q.FileTypeId == (int)ApplicationFileType.RejectionPassport).Select(q => q.FileExtension).FirstOrDefault() : ""
                                    },
                                    new ExternalServices.Models.Insurance.Requests.DataContractDocument
                                    {
                                        DocumentRequestDate = DateTime.Today.ToString("dd/MM/yyyy",CultureInfo.InvariantCulture),
                                        DocumentRequestTime = DateTime.Now.ToString("HH:mm"),
                                        DocumentCompletionDate = apiResponse.Document.EntryDate.ToString("dd/MM/yyyy",CultureInfo.InvariantCulture),
                                        DocumentCompletionTime = DateTime.Now.ToString("HH:mm"),
                                        DocumentData = getInsuranceDocumentsApiResponse.InsuranceDocuments.Count(q => q.FileTypeId==(int)ApplicationFileType.RejectionDataPage) > 0 ? Convert.ToBase64String(getInsuranceDocumentsApiResponse.InsuranceDocuments.Where(q => q.FileTypeId==(int)ApplicationFileType.RejectionDataPage).Select(q=>q.FileContent).FirstOrDefault()) : "",
                                        DocumentExtension = getInsuranceDocumentsApiResponse.InsuranceDocuments.Count(q => q.FileTypeId==(int)ApplicationFileType.RejectionDataPage) > 0 ? getInsuranceDocumentsApiResponse.InsuranceDocuments.Where(q => q.FileTypeId == (int)ApplicationFileType.RejectionDataPage).Select(q => q.FileExtension).FirstOrDefault() : ""
                                    },
                                }
                                        },
                                        PaymentDesicions = new ExternalServices.Models.Insurance.Requests.PaymentDesicions
                                        {
                                            PaymentDecisions = new ExternalServices.Models.Insurance.Requests.PaymentDecisions
                                            {
                                                DataContractPaymentDecision = new ExternalServices.Models.Insurance.Requests.DataContractPaymentDecision
                                                {
                                                    InvoiceDate = DateTime.Today.ToString("dd/MM/yyyy",CultureInfo.InvariantCulture),
                                                    PaymentDecisionAmount = claimAmount

                                                }
                                            },
                                            PaymentDetails = new ExternalServices.Models.Insurance.Requests.PaymentDetails
                                            {
                                                DataContractPaymentDetails = new ExternalServices.Models.Insurance.Requests.DataContractPaymentDetails
                                                {
                                                    EntryDate = DateTime.Today.ToString("dd/MM/yyyy",CultureInfo.InvariantCulture),
                                                    NextPaymentDate = DateTime.Today.ToString("dd/MM/yyyy",CultureInfo.InvariantCulture),
                                                    PaymentDate = DateTime.Today.ToString("dd/MM/yyyy",CultureInfo.InvariantCulture),
                                                    TotalPaymentAmount = claimAmount,
                                                    PaidPerson = apiResponse.Name + " " + apiResponse.Surname
                                                }
                                            }
                                        },
                                        ExLaws = new ExternalServices.Models.Insurance.Requests.ExLaws
                                        {
                                            DataContractExLaw = new ExternalServices.Models.Insurance.Requests.DataContractExLaw
                                            {
                                                AppointmentDate = apiResponse.ApplicationTime.DateTime.ToString("dd/MM/yyyy",CultureInfo.InvariantCulture),
                                                ReportAmount = claimAmount
                                            }
                                        }
                                    }
                                }
                            }).ConfigureAwait(false);

                            if (result.Result?.ClaimNo != null && result.Result?.ClaimNo != "")
                            {
                                await _appointmentService.UpdateFailReSendClaimLossStatusAsync(new AddClaimLossEntryLogDto()
                                {
                                    ApplicationId = apiResponse.Id,
                                    ServiceResponse = result.Result.Message,
                                    ClaimNo = result.Result.ClaimNo,
                                    CreatedBy = 1
                                }).ConfigureAwait(false);
                            }
                            else if (result.ErrorMessage != null)
                            {
                                await _appointmentService.UpdateFailReSendClaimLossStatusAsync(new AddClaimLossEntryLogDto()
                                {
                                    ApplicationId = apiResponse.Id,
                                    ServiceResponse = result.ErrorMessage,
                                    ClaimNo = result.ErrorMessage,
                                    CreatedBy = 1
                                }).ConfigureAwait(false);
                            }
                            else
                            {
                                await _appointmentService.UpdateFailReSendClaimLossStatusAsync(new AddClaimLossEntryLogDto()
                                {
                                    ApplicationId = apiResponse.Id,
                                    ServiceResponse = result.Result.Message,
                                    ClaimNo = "Tekrar İşleme Alındı",
                                    CreatedBy = 1
                                }).ConfigureAwait(false);

                                await _appointmentService.AddClaimLossEntryLogAsync(new AddClaimLossEntryLogDto()
                                {
                                    ApplicationId = apiResponse.Id,
                                    ServiceResponse = result.Result.Message,
                                    ClaimNo = result.Result.ClaimNo,
                                    CreatedBy = 1
                                }).ConfigureAwait(false);
                            }
                        }
                    }
                }

                claimFound = getClaimLossIdAndClaimAmountListResponse!=null && getClaimLossIdAndClaimAmountListResponse.ClaimLossIdAndClaimAmount.Count() > 0;
            }
        }
    }
}
