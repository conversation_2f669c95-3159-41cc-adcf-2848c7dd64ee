﻿using System;

namespace Portal.Gateway.Contracts.Entities.Dto.Appointment.Insurance
{
    public class AddClaimLossEntryLogDto : BaseRequestDto
    {
        public int Id { get; set; }
        public int ApplicationId { get; set; }
        public string ServiceResponse { get; set; }
        public string ClaimNo { get; set; }
        public int CreatedBy { get; set; }
        public DateTimeOffset? CreatedAt { get; set; }
		public string ClaimAmount { get; set; }
        public int? Status { get; set; }
        public DateTime? NextAttemptDateTime { get; set; }
        public int? RetryCount { get; set; }
    }
}
