﻿using System;
using System.Collections.Generic;
using System.Text;

namespace Portal.Gateway.Contracts.Entities.Dto.Report.Common.Report_26
{
    public class Report_InsuranceCancellationResponse
    {
        public IEnumerable<Branch> Branches { get; set; }

        public class Branch
        {
            public string BranchName { get; set; }

            public IEnumerable<ApplicationResponse> Applications { get; set; }

            public class ApplicationResponse
            {
                public string ApplicationId { get; set; }

                public string PassportNumber { get; set; }

                public string ApplicantName { get; set; }

                public string PolicyNumber { get; set; }

                public DateTime ApplicationDate { get; set; }

                public decimal CompanyPrice { get; set; }

                public decimal ProviderPrice { get; set; }

                public int CurrencyId { get; set; }

                public DateTime InsuranceStartDate { get; set; }

                public DateTime InsuranceEndDate { get; set; }

                public DateTime CancellationDate { get; set; }

                public int CancellationReasonId { get; set; }

                public int CancellationDateTypeId { get; set; }
                public int ApplicationExtraFeeId { get; set; }
                public decimal? CompanyExtraFeePrice { get; set; }
                public string? CompanyExtraFeePriceCurrency { get; set; }
                public decimal? Price2 { get; set; }
                public string? Price2Currency { get; set; }
                public int? ApplicationInsuranceCategory { get; set; }

            }
        }
    }
}
