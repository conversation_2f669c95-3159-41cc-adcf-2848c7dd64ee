﻿using System;
using System.Collections.Generic;
using System.ComponentModel.DataAnnotations;

namespace Portal.Gateway.Contracts.Entities.Dto.Report.Common.Report_80
{
    public class Report_FreeInsuranceRequest : IReportDetail
    {
        [Required]
        public IEnumerable<int> BranchIds { get; set; }

        [Required]
        public DateTime StartDate { get; set; }

        [Required]
        public DateTime EndDate { get; set; }
    }
}
