using Gateway.ObjectStoring;
using Gateway.ObjectStoring.Minio;
using Microsoft.EntityFrameworkCore;
using Portal.Gateway.BackgroundWorkers.Jobs;
using Portal.Gateway.Contracts.Entities.Constants;
using Portal.Gateway.Contracts.Repository;
using Portal.Gateway.Contracts.Settings.AppSettings;
using Portal.Gateway.Contracts.UnitOfWork;
using Portal.Gateway.Entity.Context;
using Portal.Gateway.Infrastructure.Dapper;
using Portal.Gateway.Infrastructure.Repository;
using Portal.Gateway.Infrastructure.UnitOfWork;
using Quartz;
using StackExchange.Redis;
using System.Reflection;
using Gateway.EventBus;
using Serilog;
using Gateway.Logger.Core.Configuration;
using Gateway.Logger.Core.Models;
using Portal.Gateway.BackgroundWorkers.Extensions;
using Portal.Gateway.BackgroundWorkers.Workers;
using Gateway.Notification.Application.Consumers;
using Gateway.Notification.Application.Handlers;
using Gateway.Notification.Application.Repository;
using Gateway.Email.SendGrid;
using Gateway.Sms.Clickatell;
using Gateway.Sms.Horisen;
using Gateway.Sms.TTMesaj;
using Gateway.Sms.Horisen.Policies;
using Gateway.Notification.Application.Email;
using Gateway.Notification.Application.Sms;
using Gateway.Logger.Worker.Consumer;
using Gateway.Logger.Worker.Handler;
using Gateway.Logger.Worker.Provider;

var builder = Host.CreateApplicationBuilder(args);

builder.Configuration
    .SetBasePath(Directory.GetCurrentDirectory())
    .AddJsonFile("config/appsettings.json", optional: true, reloadOnChange: true)
    .AddJsonFile($"appsettings.{builder.Environment.EnvironmentName}.json", optional: true)
    .AddEnvironmentVariables()
    .AddCommandLine(args);

builder.Services.Configure<AppSettings>(builder.Configuration.GetSection("AppSettings"));
builder.Services.Configure<IntegrationSettings>(builder.Configuration.GetSection("IntegrationSettings"));
builder.Services.Configure<LdapSettings>(builder.Configuration.GetSection("AppSettings:Ldap"));

builder.Services.AddDbContext<PortalDbContext>(options =>
    options.UseNpgsql(builder.Configuration.GetConnectionString(ProjectConst.DbConnectionStringName.PortalDb)));
builder.Services.AddScoped<IDapperRepository>(s =>
    new DapperRepository(builder.Configuration.GetConnectionString(ProjectConst.DbConnectionStringName.PortalDb)));

builder.Services.AddScoped(typeof(IUnitOfWork<>), typeof(UnitOfWork<>));
builder.Services.AddScoped(typeof(IRepository<>), typeof(Repository<>));

var serviceList = Assembly.Load("Portal.Gateway.Services").GetTypes()
    .Where(t => t.IsClass && t.Name.EndsWith("Service"))
    .Concat(Assembly.Load("Portal.Gateway.ExternalServices").GetTypes()
    .Where(t => t.IsClass && t.Name.EndsWith("Service")));

foreach (var item in serviceList)
{
    if (item.GetInterface("I" + item.Name) != null)
    {
        builder.Services.AddScoped(item.GetInterface("I" + item.Name), item);
    }
}

builder.Services.AddRabbitMq(builder.Configuration);
builder.Services.AddScoped<IFileStorage, MinioFileStorage>();
builder.Services.AddScoped<IBucketNamingNormalizer, MinioBucketNamingNormalizer>();

var redisUrl = builder.Configuration["Redis:Url"];
var redisPort = builder.Configuration["Redis:Port"];
var defaultDatabase = builder.Configuration["Redis:PrinterServiceDefaultDatabase"];
builder.Services.AddStackExchangeRedisCache(options =>
{
    options.ConfigurationOptions = new ConfigurationOptions
    {
        DefaultDatabase = int.Parse(defaultDatabase),
        Ssl = false,
        ClientName = "Portal.Gateway.Api",
        ConnectRetry = 5,
        EndPoints = { redisUrl, redisPort },
        AbortOnConnectFail = false,
    };
});

Log.Logger = LoggerConfigurationBuilder.GetLoggerConfiguration(new LogConfiguration("Portal.Gateway.BackgroundWorkers", Environment.MachineName, builder.Configuration));
builder.Logging.AddSerilog(Log.Logger);

bool isNewClaimLossSendJobEnabled;
bool.TryParse(builder.Configuration["AppSettings:BackgroundWorkers:IsNewClaimLossSendJobEnabled"], out isNewClaimLossSendJobEnabled);

// Register jobs
builder.Services.AddQuartz(q =>
{
    if (isNewClaimLossSendJobEnabled)
    {
        var keyClaimLossSendJob2 = new JobKey("ClaimLossSendJob2");
        q.AddJob<ClaimLossSendJob2>(opts => opts.WithIdentity(keyClaimLossSendJob2));
        q.AddTrigger(opts => opts
            .ForJob(keyClaimLossSendJob2)
            .WithIdentity("ClaimLossSendJob2-trigger")
            .StartNow()
            .WithSimpleSchedule(x => x
                .WithIntervalInSeconds(60)
                .RepeatForever()));
    }
    else
    {
        var keyClaimLossSendJob = new JobKey("ClaimLossSendJob");
        q.AddJob<ClaimLossSendJob>(opts => opts.WithIdentity(keyClaimLossSendJob));
        q.AddTrigger(opts => opts
            .ForJob(keyClaimLossSendJob)
            .WithIdentity("ClaimLossSendJob-trigger")
            .StartNow()
            .WithSimpleSchedule(x => x
                .WithIntervalInSeconds(60)
                .RepeatForever()));
    }

    var keyClaimLossFailReSendJob = new JobKey("ClaimLossFailReSendJob");
    q.AddJob<ClaimLossFailReSendJob>(opts => opts.WithIdentity(keyClaimLossFailReSendJob));
    q.AddTrigger(opts => opts
        .ForJob(keyClaimLossFailReSendJob)
        .WithIdentity("ClaimLossFailReSendJob-trigger")
        .StartNow()
        .WithSimpleSchedule(x => x
            .WithIntervalInSeconds(60)
            .RepeatForever()));

    var automaticIhbCreationJob = new JobKey("AutomaticIhbCreationJob");
    q.AddJob<AutomaticIhbCreationJob>(opts => opts.WithIdentity(automaticIhbCreationJob));
    q.AddTrigger(opts => opts
        .ForJob(automaticIhbCreationJob)
        .WithIdentity("AutomaticIhbCreationJob-trigger")
        .WithSimpleSchedule(x => x
            .WithIntervalInSeconds(60)
            .RepeatForever()));

    var automaticIhbCreationFailResendJob = new JobKey("AutomaticIhbCreationFailResendJob");
    q.AddJob<AutomaticIhbCreationFailResendJob>(opts => opts.WithIdentity(automaticIhbCreationFailResendJob));
    q.AddTrigger(opts => opts
        .ForJob(automaticIhbCreationFailResendJob)
        .WithIdentity("AutomaticIhbCreationFailResendJob-trigger")
        .WithSimpleSchedule(x => x
            .WithIntervalInSeconds(120)
            .RepeatForever()));

    var keyCargoStatusJob = new JobKey("CargoStatusJob");
    q.AddJob<CargoStatusJob>(opts => opts.WithIdentity(keyCargoStatusJob));
    q.AddTrigger(opts => opts
        .ForJob(keyCargoStatusJob)
        .WithIdentity("CargoStatusJob-trigger")
        .StartNow()
        .WithCronSchedule("0 0 7,9,12,15,17,21 * * ?"));

    var keyLdapSyncJob = new JobKey("LdapSyncJob");
    q.AddJob<LdapSyncJob>(opts => opts.WithIdentity(keyLdapSyncJob));
    q.AddTrigger(opts => opts
        .ForJob(keyLdapSyncJob)
        .WithIdentity("LdapSyncJob-trigger")
        .StartNow()
        .WithSimpleSchedule(x => x
            .WithIntervalInMinutes(Convert.ToInt32(builder.Configuration["AppSettings:Ldap:SyncInterval"]))
            .RepeatForever()));

    var insurancePolicyCancelJob = new JobKey("insurancePolicyCancelJob");
    q.AddJob<InsurancePolicyCancelJob>(opts => opts.WithIdentity(insurancePolicyCancelJob));
    q.AddTrigger(opts => opts
        .ForJob(insurancePolicyCancelJob)
        .WithIdentity("insurancePolicyCancelJob-trigger")
        .WithSimpleSchedule(x => x
            .WithIntervalInSeconds(90)
            .RepeatForever()));

    var insurancePolicyCancelFailResendJob = new JobKey("insurancePolicyCancelFailResendJob");
    q.AddJob<InsurancePolicyCancelFailResendJob>(opts => opts.WithIdentity(insurancePolicyCancelFailResendJob));
    q.AddTrigger(opts => opts
        .ForJob(insurancePolicyCancelFailResendJob)
        .WithIdentity("insurancePolicyCancelFailResendJob-trigger")
        .WithSimpleSchedule(x => x
            .WithIntervalInSeconds(180)
            .RepeatForever()));
});

builder.Services.AddQuartzHostedService(q => q.WaitForJobsToComplete = true);

builder.Services.AddElasticLogger(builder.Configuration);
builder.Services.AddScoped<LoggerProcessConsumer>();
builder.Services.AddScoped<ILoggerCollectorHandler, LoggerCollectorHandler>();
builder.Services.AddScoped<Gateway.Logger.Worker.Provider.ILoggerProvider, LoggerProvider>();
builder.Services.AddHostedService<LoggerWorker>();

builder.Services.RegisterDbContext(builder.Configuration);
builder.Services.RegisterDapper(builder.Configuration);
builder.Services.AddScoped<EmailSenderConsumer>();
builder.Services.AddScoped<SmsSenderConsumer>();
builder.Services.AddScoped<IEmailCollectorHandler, EmailCollectorHandler>();
builder.Services.AddScoped<ISmsCollectorHandler, SmsCollectorHandler>();
builder.Services.AddScoped<IMailQueueRepository, MailQueueRepository>();
builder.Services.AddScoped<ISmsQueueRepository, SmsQueueRepository>();
builder.Services.AddScoped<ISendGridMailProvider, SendGridMailProvider>();
builder.Services.AddScoped<ITTMesajSmsProvider, TTMesajSmsProvider>();
builder.Services.AddScoped<IHorisenSmsProvider, HorisenSmsProvider>();
builder.Services.AddScoped<IClickatellSmsProvider, ClickatellSmsProvider>();
builder.Services.AddScoped<IEmailService, EmailService>();
builder.Services.AddScoped<ISmsService, SmsService>();
builder.Services.AddSingleton<HorisenClientPolicy>();

builder.Services.AddHostedService<SmsSenderWorker>();
builder.Services.AddHostedService<EmailSenderWorker>();

var initializer = new RabbitMQInitializer(builder.Configuration);
await initializer.Initialize();

var host = builder.Build();
host.Run();
