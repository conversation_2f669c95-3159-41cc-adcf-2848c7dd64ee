﻿using System;

namespace Gateway.External.Entity.Entities.Country
{
    public class CountryChecklistTranslation
    {
        public int Id { get; set; }

        public int CountryChecklistId { get; set; }

        public int LanguageId { get; set; }

        public string Name { get; set; }

        public int? CreatedBy { get; set; }

        public DateTime? CreatedAt { get; set; }

        public int? UpdatedBy { get; set; }

        public DateTime? UpdatedAt { get; set; }

        public bool IsActive { get; set; } = true;

        public bool IsDeleted { get; set; } = false;

        public int? DeletedBy { get; set; }
        
        public DateTime? DeletedAt { get; set; }

        public CountryChecklist CountryChecklist { get; set; }
    }
}
