﻿namespace Portal.Gateway.Entity.Entities.Portal
{
    public class CancelInsuranceOrder : AuditableEntity
    {
        public int Id { get; set; }
        public int ApplicationId { get; set; }
        public int ApplicationInsuranceId { get; set; }
        public byte Status { get; set; }
        public int ProviderId { get; set; }
        public string PolicyNumber { get; set; }
        public int RejectedPolicyType { get; set; }

        public Application Application { get; set; }
        public ApplicationInsurance ApplicationInsurance { get; set; }
    }
}
