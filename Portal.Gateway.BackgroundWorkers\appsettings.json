{
  "AppSettings": {
    "ApiKey": "Gateway.ApiKey.2021",
    "ActiveDirectoryUrl": "gateway.com.tr"
  },
  "IntegrationSettings": {
    "UnicoInsurance": {
      "Id": 1, // DB Provider Id
      "SecurityServiceEndpointUrl": "https://previva.unicosigorta.com.tr/Public/SecurityService.svc",
      "IntegrationServiceEndpointUrl": "https://previva.unicosigorta.com.tr/Public/Integration017.svc",
      "PolicyServiceEndpointUrl": "https://previva.unicosigorta.com.tr/Public/PolicyService.svc",
      "CertificateServiceEndpointUrl": "http://PolicyCertificateWS.unicosigorta.com.tr/Certificate.asmx",
      "Environment": "PreProd",
      "AppSecurityKey": "Gateway#",
      "UserName": "GRAVIS_WEB3",
      "Password": "EVVANAL1",
      "BranchCode": "13",
      "SourceCode": "3",
      "AgentNo": "0516052"
    },
    "Sap": {
      "Id": 2, // DB Provider Id
      "CreateOrderEndpointUrl": "https://sapqa.gateway.com.tr/sap/bc/srt/rfc/sap/zgwsd_fg004_001/400/zgwsd_fg004_001/zgwsd_fg004_001",
      "QueryInvoiceEndpointUrl": "https://sapqa.gateway.com.tr/sap/bc/srt/rfc/sap/zgwsd_fg004_003/400/zgwsd_fg004_003/zgwsd_fg004_003",
      "RefundInvoiceEndpointUrl": "https://sapqa.gateway.com.tr/sap/bc/srt/rfc/sap/zgwsd_fg004_004/400/zgwsd_fg004_004/zgwsd_fg004_004",
      "CancelInvoiceEndpointUrl": "https://sapqa.gateway.com.tr/sap/bc/srt/rfc/sap/zgwsd_fg004_005/400/zgwsd_fg004_005/zgwsd_fg004_005",
      "ExchangeInvoiceEndpointUrl": "https://sapqa.gateway.com.tr/sap/bc/srt/rfc/sap/zgwsd_fg004_009/400/zgwsd_fg004_009/zgwsd_fg004_009",
      "GetSapRateInformationUrl": "http://GWS4QA.gateway.com.tr:8000/sap/bc/srt/rfc/sap/zgwfi_ws_fg007/400/zgwfi_ws_fg007/zgwfi_ws_fg007_lp",
      "Binding": "SapWebServiceBinding",
      "UserName": "WEBSERVICE",
      "Password": "WEBSERVICE2020"
    },
    "Emaa": {
      "Id": 3, // DB Provider Id
      "ServiceUrl": "http://10.31.55.30/polsvc/sfspolsvcws.asmx",
      "UserName": "85OXVNKmCLfDvRmC0QVB/A==",
      "Password": "WF7CZ5EyNYMLlqJOgoYdEQ=="
    },
    "Ministry": {
      "Token": "3e23e8160039594a33894f6564e1b1348bbd7a0088d42c4acb73eeaed59c009e",
      "VisaEndpointBaseUrl": "http://test.konsolosluk.gov.tr/api/v1/visapack/"
    },
    "Sms": {
      "Clickatell": {
        "ClickatellServiceEndpointUrl": "https://platform.clickatell.com/v1/",
        "ClickatellApiMethod": "message/",
        "ApiKey": "h5NEfz9rQ42LJgAKVETYYw==",
        "Channel": "sms"
      },
      "TurkTelekom": {
        "TurkTelekomServiceEndpointUrl": "https://ws.ttmesaj.com/Service1.asmx",
        "Username": "gateway",
        "Password": "G4A9T1M6",
        "Origin": "Gateway"
      },
      "Turkcell": {
        "TurkcellServiceEndpointUrl": "https://api.dataport.com.tr/restapi/",
        "RegisterApiMethod": "Register/",
        "SendSmsApiMethod": "api/Messages/SendSMS/",
        "AccountNumber": "???",
        "UserName": "???",
        "Password": "???",
        "GrantType": "???"
      }
    },
    "Email": {
      "SmtpOffice365": {
        "UserName": "<EMAIL>",
        "Password": "Vp1KQ83g5P9",
        "Smtp": "smtp.office365.com",
        "Port": "587"
      },
      "SendGrid": {
        "Sender": "<EMAIL>",
        "Credential": "*********************************************************************"
      }
    }
  },
  "elasticsearch": {
    "logindex": "slog-portal",
    "index": "slog-portal",
    "username": "elastic",
    "password": "htJsew49I8jPiL6How7U",
    "url": "http://***********:9200"
  },
  "ConnectionStrings": {
    "GatewayPortalDbConnection": "Server=**********;Username=gw_portal_stg_app_usr;Password=*****************;Database=MigrationTestPortal;"
  },
  "Logging": {
    "LogLevel": {
      "Default": "Information",
      "Microsoft": "Warning",
      "Microsoft.Hosting.Lifetime": "Information"
    }
  },
  "AllowedHosts": "*",

  "Redis": {
    "Url": "***********",
    "Port": "6379",
    "ConnectTimeout": 10000,
    "ConnectRetry": 3,
    "DefaultDatabase": 0,
    "PrinterServiceDefaultDatabase": 2,
    "DigitalSignatureDatabase": 3
  },
  "ContactInformationVerificationSecret": "OvwtalPB3YjeeLReriRWjJyzt3phRCax"
}
