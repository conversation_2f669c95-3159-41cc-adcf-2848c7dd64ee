﻿using Gateway.Extensions;
using Gateway.IO;
using Gateway.ObjectStoring;
using Microsoft.EntityFrameworkCore;
using Microsoft.Extensions.Options;
using Portal.Gateway.ApiModel;
using Portal.Gateway.Common.Utility.Extensions;
using Portal.Gateway.Common.Utility.Helpers;
using Portal.Gateway.Contracts.Entities;
using Portal.Gateway.Contracts.Entities.Dto;
using Portal.Gateway.Contracts.Entities.Dto.General;
using Portal.Gateway.Contracts.Entities.Dto.Management.Action.Requests;
using Portal.Gateway.Contracts.Entities.Dto.Management.Announcement.Requests;
using Portal.Gateway.Contracts.Entities.Dto.Management.Announcement.Responses;
using Portal.Gateway.Contracts.Entities.Dto.Management.ApplicationFormElement.Requests;
using Portal.Gateway.Contracts.Entities.Dto.Management.ApplicationFormElement.Responses;
using Portal.Gateway.Contracts.Entities.Dto.Management.ApplicationSale;
using Portal.Gateway.Contracts.Entities.Dto.Management.ApplicationSale.Request;
using Portal.Gateway.Contracts.Entities.Dto.Management.ApplicationSale.Response;
using Portal.Gateway.Contracts.Entities.Dto.Management.Bank.Requests;
using Portal.Gateway.Contracts.Entities.Dto.Management.Bank.Responses;
using Portal.Gateway.Contracts.Entities.Dto.Management.Branch.Requests;
using Portal.Gateway.Contracts.Entities.Dto.Management.Branch.Responses;
using Portal.Gateway.Contracts.Entities.Dto.Management.BranchApplicationCountry.Requests;
using Portal.Gateway.Contracts.Entities.Dto.Management.BranchApplicationCountry.Responses;
using Portal.Gateway.Contracts.Entities.Dto.Management.BranchApplicationCountryCompanyExtraFee.Requests;
using Portal.Gateway.Contracts.Entities.Dto.Management.BranchApplicationCountryCompanyExtraFee.Responses;
using Portal.Gateway.Contracts.Entities.Dto.Management.BranchApplicationCountryExtraFee.Requests;
using Portal.Gateway.Contracts.Entities.Dto.Management.BranchApplicationCountryExtraFee.Responses;
using Portal.Gateway.Contracts.Entities.Dto.Management.BranchApplicationCountryExtraFeeCommission.Requests;
using Portal.Gateway.Contracts.Entities.Dto.Management.BranchApplicationCountryExtraFeeCommission.Responses;
using Portal.Gateway.Contracts.Entities.Dto.Management.BranchApplicationCountryExtraFeeCost.Requests;
using Portal.Gateway.Contracts.Entities.Dto.Management.BranchApplicationCountryExtraFeeCost.Responses;
using Portal.Gateway.Contracts.Entities.Dto.Management.BranchApplicationCountryFile.Requests;
using Portal.Gateway.Contracts.Entities.Dto.Management.BranchApplicationCountryFile.Responses;
using Portal.Gateway.Contracts.Entities.Dto.Management.BranchApplicationCountryVisaCategory.Requests;
using Portal.Gateway.Contracts.Entities.Dto.Management.BranchApplicationCountryVisaCategory.Responses;
using Portal.Gateway.Contracts.Entities.Dto.Management.BranchApplicationStatus;
using Portal.Gateway.Contracts.Entities.Dto.Management.BranchDepartment.Requests;
using Portal.Gateway.Contracts.Entities.Dto.Management.BranchDepartment.Responses;
using Portal.Gateway.Contracts.Entities.Dto.Management.BranchDigitalSignatureDocument.Requests;
using Portal.Gateway.Contracts.Entities.Dto.Management.BranchDigitalSignatureDocument.Responses;
using Portal.Gateway.Contracts.Entities.Dto.Management.BranchIcrNote.Requests;
using Portal.Gateway.Contracts.Entities.Dto.Management.BranchIcrNote.Responses;
using Portal.Gateway.Contracts.Entities.Dto.Management.BranchInsuranceRefundSetting.Request;
using Portal.Gateway.Contracts.Entities.Dto.Management.BranchInsuranceRefundSetting.Responses;
using Portal.Gateway.Contracts.Entities.Dto.Management.BranchShiftHoliday.Requests;
using Portal.Gateway.Contracts.Entities.Dto.Management.BranchShiftHoliday.Responses;
using Portal.Gateway.Contracts.Entities.Dto.Management.ClientDevice.Requests;
using Portal.Gateway.Contracts.Entities.Dto.Management.ClientDevice.Responses;
using Portal.Gateway.Contracts.Entities.Dto.Management.CustomerCard;
using Portal.Gateway.Contracts.Entities.Dto.Management.CustomerCard.Requests;
using Portal.Gateway.Contracts.Entities.Dto.Management.CustomerCardNote.Requests;
using Portal.Gateway.Contracts.Entities.Dto.Management.CustomerCardNote.Responses;
using Portal.Gateway.Contracts.Entities.Dto.Management.Department.Requests;
using Portal.Gateway.Contracts.Entities.Dto.Management.Department.Responses;
using Portal.Gateway.Contracts.Entities.Dto.Management.Inquiry.Requests;
using Portal.Gateway.Contracts.Entities.Dto.Management.Inquiry.Responses;
using Portal.Gateway.Contracts.Entities.Dto.Management.Inventory.Requests;
using Portal.Gateway.Contracts.Entities.Dto.Management.Inventory.Responses;
using Portal.Gateway.Contracts.Entities.Dto.Management.Menu.Requests;
using Portal.Gateway.Contracts.Entities.Dto.Management.Menu.Responses;
using Portal.Gateway.Contracts.Entities.Dto.Management.Role.Requests;
using Portal.Gateway.Contracts.Entities.Dto.Management.RoleAction.Requests;
using Portal.Gateway.Contracts.Entities.Dto.Management.Slot;
using Portal.Gateway.Contracts.Entities.Dto.Management.Slot.Requests;
using Portal.Gateway.Contracts.Entities.Dto.Management.Slot.Responses;
using Portal.Gateway.Contracts.Entities.Dto.Management.User.Requests;
using Portal.Gateway.Contracts.Entities.Dto.Management.User.Responses;
using Portal.Gateway.Contracts.Entities.Dto.Management.UserBranch;
using Portal.Gateway.Contracts.Entities.Dto.Management.UserBranch.Requests;
using Portal.Gateway.Contracts.Entities.Dto.Management.UserModule;
using Portal.Gateway.Contracts.Entities.Dto.Management.UserModule.Requests;
using Portal.Gateway.Contracts.Entities.Dto.Management.Vms;
using Portal.Gateway.Contracts.Entities.Enums;
using Portal.Gateway.Contracts.Extensions;
using Portal.Gateway.Contracts.Services;
using Portal.Gateway.Contracts.Settings.AppSettings;
using Portal.Gateway.Contracts.UnitOfWork;
using Portal.Gateway.Entity.Context;
using Portal.Gateway.Entity.Entities.Portal;
using Portal.Gateway.ExternalServices.Common;
using Portal.Gateway.Resources;
using SharpCompress.Archives.Rar;
using System;
using System.Collections.Generic;
using System.ComponentModel;
using System.IO;
using System.IO.Compression;
using System.Linq;
using System.Threading.Tasks;
using Action = Portal.Gateway.Entity.Entities.Portal.Action;
using ApplicationFormElement = Portal.Gateway.Entity.Entities.Portal.ApplicationFormElement;

namespace Portal.Gateway.Services
{
    public class ManagementService : BaseService, IManagementService
    {
        private readonly AppSettings _appSettings;
        private readonly IUnitOfWork<PortalDbContext> _unitOfWorkPortalDb;
        private readonly IFileStorage _fileStorage;

        public ManagementService(
            IOptions<AppSettings> appSettings,
            IUnitOfWork<PortalDbContext> unitOfWorkPortalDb, IFileStorage fileStorage) : base(appSettings)
        {
            _appSettings = appSettings.Value;
            _unitOfWorkPortalDb = unitOfWorkPortalDb;
            _fileStorage = fileStorage;
        }

        #region Branch

        public async Task<AddResponseDto> AddBranchAsync(AddBranchRequestDto request)
        {
            var branchList = await _unitOfWorkPortalDb.GetRepository<Branch>().Entities
                                    .Include(i => i.BranchTranslations)
                                    .Include(i => i.BranchDataTranslation)
                                    .Where(p => !p.IsDeleted && p.CountryId == request.CountryId && p.BranchTranslations.Any(p2 => p2.IsActive && !p2.IsDeleted) && p.BranchDataTranslation.Any(p2 => p2.IsActive && !p2.IsDeleted))
                                    .ToListAsync();


            if (branchList != null)
            {
                foreach (var item in request.BranchTranslations)
                {
                    if (branchList.SelectMany(p => p.BranchTranslations).Any(p => p.LanguageId == item.LanguageId && p.Name == item.Name))
                        throw new AlreadyExistsDataPortalException($"{nameof(SiteResources.Exception_ExistingRecord).ToSiteResourcesValue(request.LanguageId)} ({nameof(SiteResources.Branch).ToSiteResourcesValue(request.LanguageId)})");
                }
                foreach (var item in request.BranchDataTranslation)
                {
                    if (branchList.SelectMany(p => p.BranchDataTranslation).Any(p => p.LanguageId == item.LanguageId && p.Address == item.Address))
                        throw new AlreadyExistsDataPortalException($"{nameof(SiteResources.Exception_ExistingRecord).ToSiteResourcesValue(request.LanguageId)} ({nameof(SiteResources.Branch).ToSiteResourcesValue(request.LanguageId)})");
                }
            }

            var newBranch = new Branch
            {
                CountryId = request.CountryId,
                Address = request.Address,
                Email = request.Email,
                Telephone = request.Telephone,
                CityName = request.CityName,
                Mission = request.Mission,
                CorporateName = request.CorporateName,
                InvoiceNumber = request.InvoiceNumber,
                LocalTimeOffset = request.TimeZoneOffset,
                CheckRejectedStatus = request.CheckRejectedStatus,
                CheckRejectedStatusPeriod = request.CheckRejectedStatusPeriod,
                CheckUnrealDocumentStatus = request.CheckUnrealDocumentStatus,
                CheckUnrealDocumentStatusPeriod = request.CheckUnrealDocumentStatusPeriod,
                CheckRejectionWithCountryEntryBannedStatus = request.CheckRejectionWithCountryEntryBannedStatus,
                CheckRejectionWithCountryEntryBannedStatusPeriod = request.CheckRejectionWithCountryEntryBannedStatusPeriod,
                RejectionRefundDonePermissionNumber = request.RejectionRefundDonePermissionNumber,
                IsValidAMS = request.IsValidAMS,
                IsSlotTypesConnected = request.IsSlotTypesConnected,
                ConnectedSlotTypeId = request.IsSlotTypesConnected ? request.ConnectedSlotTypeId : null,
                IsPassportScanRequired = request.IsPassportScanRequired,
                EmailProviderId = request.EmailProviderId,
                SmsProviderId = request.SmsProviderId,
                QmsCompanyId = request.QmsCompanyId,
                QmsWalkinVipControl = request.QmsWalkinVipControl,
                QueueMaticPlaylistId = request.QueueMaticPlaylistId,
                SmsSender = request.SmsSender,
                IsSendByPrefix = request.IsSendByPrefix,
                MaxAppointmentDay = request.MaxAppointmentDay,
                SendBasicGuidelineInAms = request.SendBasicGuidelineInAms,
                BasicGuidelineId = request.SendBasicGuidelineInAms ? request.BasicGuidelineId : null,
                InsuranceProviderId = request.InsuranceProviderId,
                IsCargoIntegrationActive = request.IsCargoIntegrationActive,
                ShowCityDropdown = request.ShowCityDropdown,
                IsPrintAllIntegrationActive = request.IsPrintAllIntegrationActive,
                IsPhotoBoothIntegrationActive = request.IsPhotoBoothIntegrationActive,
                IsApplicationUpdateStatusCheckActive = request.IsApplicationUpdateStatusCheckActive,
                IsPreApplicationConnectionActive = request.IsPreApplicationConnectionActive,
                IsRejectionApprovalControl = request.IsRejectionApprovalControl,
                CreatedBy = request.UserAuditId,
                ShowInB2c = request.ShowInB2c,
                IsDigitalSignatureIntegrationActive = request.IsDigitalSignatureIntegrationActive,
                IsRelatedInsuranceForExempt = request.IsRelatedInsuranceForExempt,
                ShowPaymentMethods = request.ShowPaymentMethods,
                QmsScreenTitle = request.QmsScreenTitle,
                CreatedAt = DateTime.Now,
				ShowBranchInMobile = request.ShowBranchInMobile,
				SapBranch = new SapBranch
                {
                    SapBranchId = request.SapBranchId
                },
                CargoBranch = request.IsCargoIntegrationActive && request.CargoProviderId != 0 ? new CargoBranch
                {
                    CargoProviderId = request.CargoProviderId
                } : null,
                BranchTranslations = request.BranchTranslations.Select(p => new BranchTranslation
                {
                    LanguageId = p.LanguageId,
                    Name = p.Name
                }).ToList(),
                BranchDataTranslation = request.BranchDataTranslation?.Select(p => new BranchDataTranslation
                {
                    Address = p.Address,
                    CorporateName = p.CorporateName,
                    InvoiceNumber = p.InvoiceNumber,
                    Mission = p.Mission,
                    CityName = p.CityName,
                    LanguageId = p.LanguageId
                }).ToList()
            };

            await _unitOfWorkPortalDb.GetRepository<Branch>().AddAsync(newBranch);
            await _unitOfWorkPortalDb.SaveChangesAsync();

            return new AddResponseDto { Id = newBranch.Id };
        }

        public async Task<UpdateResponseDto> UpdateBranchAsync(UpdateBranchRequestDto request)
        {
            var languagesToCheck = request.BranchTranslations.Select(s => s.LanguageId).ToList();
            var namesToCheck = request.BranchTranslations.Select(s => s.Name).ToList();

            var anyExistingBranch = await _unitOfWorkPortalDb.GetRepository<Branch>().Entities
                                        .Include(i => i.BranchTranslations)
                                        .Include(i => i.SapBranch)
                                        .Where(p => !p.IsDeleted && p.Id != request.Id && p.CountryId == request.CountryId &&
                                                    p.BranchTranslations.Any(p2 => p2.IsActive && !p2.IsDeleted && languagesToCheck.Contains(p2.LanguageId) && namesToCheck.Contains(p2.Name)))
                                        .AnyAsync();

            if (anyExistingBranch)
                throw new AlreadyExistsDataPortalException($"{nameof(SiteResources.Exception_ExistingRecord).ToSiteResourcesValue(request.LanguageId)} ({nameof(SiteResources.Branch).ToSiteResourcesValue(request.LanguageId)})");

            var existingBranch = await _unitOfWorkPortalDb.GetRepository<Branch>().Entities
                                        .Include(i => i.SapBranch)
                                        .Include(i => i.CargoBranch)
                                        .Include(i => i.BranchTranslations)
                                        .Include(i => i.BranchDataTranslation)
                                        .Where(p => !p.IsDeleted && p.Id == request.Id && p.BranchTranslations.Any(p2 => p2.IsActive && !p2.IsDeleted) && p.BranchDataTranslation.Any(p2 => p2.IsActive && !p2.IsDeleted))
                                        .FirstOrDefaultAsync();

            if (existingBranch == null)
                throw new NoFoundDataPortalException($"{nameof(SiteResources.Exception_NoRecordsFound).ToSiteResourcesValue(request.LanguageId)} ({nameof(SiteResources.Branch).ToSiteResourcesValue(request.LanguageId)})");

            existingBranch.CountryId = request.CountryId;
            existingBranch.Address = request.Address;
            existingBranch.Email = request.Email;
            existingBranch.Telephone = request.Telephone;
            existingBranch.CityName = request.CityName;
            existingBranch.Mission = request.Mission;
            existingBranch.IsActive = request.IsActive;
            existingBranch.CorporateName = request.CorporateName;
            existingBranch.InvoiceNumber = request.InvoiceNumber;
            existingBranch.LocalTimeOffset = request.TimeZoneOffset;
            existingBranch.CheckRejectedStatus = request.CheckRejectedStatus;
            existingBranch.CheckRejectedStatusPeriod = request.CheckRejectedStatusPeriod;
            existingBranch.CheckUnrealDocumentStatus = request.CheckUnrealDocumentStatus;
            existingBranch.CheckUnrealDocumentStatusPeriod = request.CheckUnrealDocumentStatusPeriod;
            existingBranch.CheckRejectionWithCountryEntryBannedStatus = request.CheckRejectionWithCountryEntryBannedStatus;
            existingBranch.CheckRejectionWithCountryEntryBannedStatusPeriod = request.CheckRejectionWithCountryEntryBannedStatusPeriod;
            existingBranch.RejectionRefundDonePermissionNumber = request.RejectionRefundDonePermissionNumber;
            existingBranch.IsValidAMS = request.IsValidAMS;
            existingBranch.IsSlotTypesConnected = request.IsSlotTypesConnected;
            existingBranch.ConnectedSlotTypeId = request.IsSlotTypesConnected ? request.ConnectedSlotTypeId : null;
            existingBranch.IsPassportScanRequired = request.IsPassportScanRequired;
            existingBranch.EmailProviderId = request.EmailProviderId;
            existingBranch.SmsProviderId = request.SmsProviderId;
            existingBranch.QmsCompanyId = request.QmsCompanyId;
            existingBranch.QmsWalkinVipControl = request.QmsWalkinVipControl;
            existingBranch.QueueMaticPlaylistId = request.QueueMaticPlaylistId;
            existingBranch.SmsSender = request.SmsSender;
            existingBranch.IsSendByPrefix = request.IsSendByPrefix;
            existingBranch.MaxAppointmentDay = request.MaxAppointmentDay;
            existingBranch.SendBasicGuidelineInAms = request.SendBasicGuidelineInAms;
            existingBranch.BasicGuidelineId = request.SendBasicGuidelineInAms ? request.BasicGuidelineId : null;
            existingBranch.InsuranceProviderId = request.InsuranceProviderId;
            existingBranch.IsCargoIntegrationActive = request.IsCargoIntegrationActive;
            existingBranch.ShowCityDropdown = request.ShowCityDropdown;
            existingBranch.IsPrintAllIntegrationActive = request.IsPrintAllIntegrationActive;
            existingBranch.IsPhotoBoothIntegrationActive = request.IsPhotoBoothIntegrationActive;
            existingBranch.IsApplicationUpdateStatusCheckActive = request.IsApplicationUpdateStatusCheckActive;
            existingBranch.IsPrinterIntegrationActive = request.IsPrinterIntegrationActive;
            existingBranch.IsPreApplicationConnectionActive = request.IsPreApplicationConnectionActive;
            existingBranch.IsVisaRejectionDocumentsControl = request.IsVisaRejectionDocumentsControl;
            existingBranch.IsRejectionApprovalControl = request.IsRejectionApprovalControl;
            existingBranch.UpdatedAt = DateTime.Now;
            existingBranch.UpdatedBy = request.UserAuditId;
            existingBranch.DisableContactInformationVerification = request.DisableContactInformationVerification;
            existingBranch.ShowInB2c = request.ShowInB2c;
            existingBranch.IsDigitalSignatureIntegrationActive = request.IsDigitalSignatureIntegrationActive;
            existingBranch.IsRelatedInsuranceForExempt = request.IsRelatedInsuranceForExempt;
            existingBranch.ShowPaymentMethods = request.ShowPaymentMethods;
            existingBranch.QmsScreenTitle = request.QmsScreenTitle;
            existingBranch.ShowBranchInMobile = request.ShowBranchInMobile;

            foreach (var item in request.BranchTranslations)
            {
                var existingBranchTranslation = existingBranch.BranchTranslations.FirstOrDefault(p => p.Id == item.Id);

                if (existingBranchTranslation != null)
                {
                    existingBranchTranslation.LanguageId = item.LanguageId;
                    existingBranchTranslation.Name = item.Name;
                }
            }

            foreach (var item in request.BranchDataTranslations)
            {
                var existingBranchDataTranslation = existingBranch.BranchDataTranslation.FirstOrDefault(p => p.Id == item.Id);

                if (existingBranchDataTranslation != null)
                {
                    existingBranchDataTranslation.LanguageId = item.LanguageId;
                    existingBranchDataTranslation.Address = item.Address;
                    existingBranchDataTranslation.InvoiceNumber = item.InvoiceNumber;
                    existingBranchDataTranslation.Mission = item.Mission;
                    existingBranchDataTranslation.CityName = item.CityName;
                    existingBranchDataTranslation.CorporateName = item.CorporateName;
                }
            }

            _unitOfWorkPortalDb.GetRepository<Branch>().Update(existingBranch);

            if (existingBranch.SapBranch == null)
            {
                var newSapBranch = new SapBranch()
                {
                    BranchId = request.Id,
                    SapBranchId = request.SapBranchId
                };

                await _unitOfWorkPortalDb.GetRepository<SapBranch>().AddAsync(newSapBranch);
            }
            else
            {
                var sapBranch = await _unitOfWorkPortalDb.GetRepository<SapBranch>().Entities
                                        .Where(p => !p.IsDeleted && p.BranchId == request.Id)
                                        .FirstOrDefaultAsync();

                sapBranch.SapBranchId = request.SapBranchId;

                _unitOfWorkPortalDb.GetRepository<SapBranch>().Update(sapBranch);
            }

            if (existingBranch.CargoBranch == null && request.IsCargoIntegrationActive && request.CargoProviderId != 0)
            {
                var newCargoBranch = new CargoBranch
                {
                    CargoProviderId = request.CargoProviderId,
                    BranchId = existingBranch.Id,
                    CreatedBy = request.UserAuditId,
                    CreatedAt = DateTime.Now
                };

                await _unitOfWorkPortalDb.GetRepository<CargoBranch>().AddAsync(newCargoBranch);
            }
            else if (existingBranch.CargoBranch != null && request.IsCargoIntegrationActive && existingBranch.CargoBranch.CargoProviderId != request.CargoProviderId)
            {
                var cargoBranch = await _unitOfWorkPortalDb.GetRepository<CargoBranch>().Entities
                                        .Where(p => !p.IsDeleted && p.BranchId == request.Id)
                                        .FirstOrDefaultAsync();

                cargoBranch.CargoProviderId = request.CargoProviderId;
                cargoBranch.UpdatedAt = DateTime.Now;
                cargoBranch.UpdatedBy = request.UserAuditId;

                _unitOfWorkPortalDb.GetRepository<CargoBranch>().Update(cargoBranch);
            }
            else if (existingBranch.CargoBranch != null && !request.IsCargoIntegrationActive)
            {
                var cargoBranch = await _unitOfWorkPortalDb.GetRepository<CargoBranch>().Entities
                                        .Where(p => !p.IsDeleted && p.BranchId == request.Id)
                                        .FirstOrDefaultAsync();

                cargoBranch.IsActive = false;
                cargoBranch.UpdatedAt = DateTime.Now;
                cargoBranch.UpdatedBy = request.UserAuditId;

                _unitOfWorkPortalDb.GetRepository<CargoBranch>().Update(cargoBranch);
            }
            else if (existingBranch.CargoBranch != null && request.IsCargoIntegrationActive && !existingBranch.CargoBranch.IsActive)
            {
                var cargoBranch = await _unitOfWorkPortalDb.GetRepository<CargoBranch>().Entities
                    .Where(p => !p.IsDeleted && p.BranchId == request.Id)
                    .FirstOrDefaultAsync();

                cargoBranch.CargoProviderId = request.CargoProviderId;
                cargoBranch.IsActive = true;
                cargoBranch.UpdatedAt = DateTime.Now;
                cargoBranch.UpdatedBy = request.UserAuditId;

                _unitOfWorkPortalDb.GetRepository<CargoBranch>().Update(cargoBranch);
            }

            await _unitOfWorkPortalDb.SaveChangesAsync();

            return new UpdateResponseDto { Result = true };
        }

        public async Task<DeleteResponseDto> DeleteBranchAsync(DeleteRequestDto request)
        {
            var existingBranch = await _unitOfWorkPortalDb.GetRepository<Branch>().Entities
                                        .Where(p => !p.IsDeleted && p.Id == request.Id)
                                        .FirstOrDefaultAsync();

            if (existingBranch == null)
                throw new NoFoundDataPortalException($"{nameof(SiteResources.Exception_NoRecordsFound).ToSiteResourcesValue()} ({nameof(SiteResources.Branch).ToSiteResourcesValue()})");

            existingBranch.DeletedBy = request.UserAuditId;

            _unitOfWorkPortalDb.GetRepository<Branch>().MarkAsDeleted(existingBranch.Id);
            await _unitOfWorkPortalDb.SaveChangesAsync();

            return new DeleteResponseDto { Result = true };
        }

        public async Task<BranchDto> GetBranchAsync(BranchRequestDto request)
        {
            var existingBranch = await _unitOfWorkPortalDb.GetRepository<Branch>().Entities
                                    .Include(i => i.BranchTranslations)
                                    .Include(i => i.BranchDataTranslation)
                                    .Include(p => p.Country)
                                    .Include(p => p.SapBranch)
                                    .Include(p => p.CargoBranch)
                                    .Where(p => !p.IsDeleted && p.Id == request.Id && p.BranchTranslations.Any(p2 => p2.IsActive && !p2.IsDeleted))
                                    .FirstOrDefaultAsync();

            if (existingBranch == null)
                throw new NoFoundDataPortalException($"{nameof(SiteResources.Exception_NoRecordsFound).ToSiteResourcesValue(request.LanguageId)} ({nameof(SiteResources.Branch).ToSiteResourcesValue(request.LanguageId)})");

            var isUserSysdmin = _unitOfWorkPortalDb.GetRepository<UserRole>().Entities.Any(p => p.IsActive && !p.IsDeleted && p.UserId == request.UserAuditId && p.RoleId == 1); //Sysadmin

            var result = new BranchDto
            {
                Id = existingBranch.Id,
                Address = existingBranch.Address,
                Email = existingBranch.Email,
                Telephone = existingBranch.Telephone,
                CityName = existingBranch.CityName,
                Mission = existingBranch.Mission,
                CorporateName = existingBranch.CorporateName,
                InvoiceNumber = existingBranch.InvoiceNumber,
                SapBranchId = existingBranch.SapBranch?.SapBranchId,
                IsCargoIntegrationActive = existingBranch.IsCargoIntegrationActive,
                ShowCityDropdown = existingBranch.ShowCityDropdown,
                IsPrintAllIntegrationActive = existingBranch.IsPrintAllIntegrationActive,
                IsPhotoBoothIntegrationActive = existingBranch.IsPhotoBoothIntegrationActive,
                IsApplicationUpdateStatusCheckActive = existingBranch.IsApplicationUpdateStatusCheckActive,
                IsPrinterIntegrationActive = existingBranch.IsPrinterIntegrationActive,
                IsPreApplicationConnectionActive = existingBranch.IsPreApplicationConnectionActive,
                IsVisaRejectionDocumentsControl = existingBranch.IsVisaRejectionDocumentsControl,
                IsRejectionApprovalControl = existingBranch.IsRejectionApprovalControl,
                IsActive = existingBranch.IsActive,
                TimeZoneOffset = existingBranch.LocalTimeOffset,
                CheckRejectedStatus = existingBranch.CheckRejectedStatus,
                CheckRejectedStatusPeriod = existingBranch.CheckRejectedStatusPeriod,
                CheckUnrealDocumentStatus = existingBranch.CheckUnrealDocumentStatus,
                CheckUnrealDocumentStatusPeriod = existingBranch.CheckUnrealDocumentStatusPeriod,
                CheckRejectionWithCountryEntryBannedStatus = existingBranch.CheckRejectionWithCountryEntryBannedStatus,
                CheckRejectionWithCountryEntryBannedStatusPeriod = existingBranch.CheckRejectionWithCountryEntryBannedStatusPeriod,
                RejectionRefundDonePermissionNumber = existingBranch.RejectionRefundDonePermissionNumber,
                IsValidAMS = existingBranch.IsValidAMS,
                IsSlotTypesConnected = existingBranch.IsSlotTypesConnected,
                ConnectedSlotTypeId = existingBranch.ConnectedSlotTypeId,
                QmsCompanyId = existingBranch.QmsCompanyId,
                IsPassportScanRequired = existingBranch.IsPassportScanRequired,
                QmsWalkinVipControl = existingBranch.QmsWalkinVipControl,
                QueueMaticPlaylistId = existingBranch.QueueMaticPlaylistId,
                MaxAppointmentDay = existingBranch.MaxAppointmentDay,
                BasicGuidelineId = existingBranch.BasicGuidelineId,
                InsuranceProviderId = existingBranch.InsuranceProviderId,
                SmsProviderId = existingBranch.SmsProviderId,
                SmsSender = existingBranch.SmsSender,
                EmailProviderId = existingBranch.EmailProviderId,
                IsSendByPrefix = existingBranch.IsSendByPrefix,
                IsUserSysdmin = isUserSysdmin,
                DisableContactInformationVerification = existingBranch.DisableContactInformationVerification,
                ShowInB2c = existingBranch.ShowInB2c,
                SendBasicGuidelineInAms = existingBranch.SendBasicGuidelineInAms,
                IsDigitalSignatureIntegrationActive = existingBranch.IsDigitalSignatureIntegrationActive,
                IsRelatedInsuranceForExempt = existingBranch.IsRelatedInsuranceForExempt,
                ShowPaymentMethods = existingBranch.ShowPaymentMethods,
                QmsScreenTitle = existingBranch.QmsScreenTitle,
                ShowBranchInMobile = existingBranch.ShowBranchInMobile
            };

            if (existingBranch.Country != null)
            {
                result.Country = new CountryDto
                {
                    Id = existingBranch.Country.Id,
                    Name = existingBranch.Country.Name
                };
            }

            if (existingBranch.BranchTranslations.Any(p => !p.IsDeleted))
            {
                var branchTranslations = existingBranch.BranchTranslations.Where(p => !p.IsDeleted);

                if (branchTranslations.Any(p => p.LanguageId == request.LanguageId))
                    result.Name = branchTranslations.First(p => p.LanguageId == request.LanguageId).Name;
                else
                    result.Name = branchTranslations.First().Name;

                result.BranchTranslations = branchTranslations.Select(p => new BranchTranslationDto
                {
                    Id = p.Id,
                    LanguageId = p.LanguageId,
                    Name = p.Name
                }).ToList();
            }

            if (existingBranch.BranchDataTranslation.Any(p => !p.IsDeleted))
            {
                var branchDataTranslations = existingBranch.BranchDataTranslation.Where(p => !p.IsDeleted);

                result.BranchDataTranslations = branchDataTranslations.Select(p => new BranchDataTranslationDto
                {
                    Id = p.Id,
                    Address = p.Address,
                    CorporateName = p.CorporateName,
                    InvoiceNumber = p.InvoiceNumber,
                    Mission = p.Mission,
                    CityName = p.CityName,
                    LanguageId = p.LanguageId
                }).ToList();
            }

            if (existingBranch.CargoBranch?.IsActive != null && (bool)existingBranch.CargoBranch?.IsActive)
            {
                result.CargoProviderId = existingBranch.CargoBranch?.CargoProviderId;
            }

            return result;
        }

        public async Task<Pagination<BranchesDto>> GetPaginatedBranchesAsync(PaginatedBranchesRequestDto request)
        {
            var paginationResult = new Pagination<BranchesDto> { Page = request.Pagination.Page, PageSize = request.Pagination.PageSize };

            var queryBranch = _unitOfWorkPortalDb.GetRepository<Branch>().Entities
                                    .Include(p => p.BranchTranslations)
                                    .Include(p => p.Country)
                                    .Where(p => !p.IsDeleted);

            if (request.CountryId.HasValue)
                queryBranch = queryBranch.Where(p => p.CountryId == request.CountryId.Value);

            paginationResult.TotalItemCount = queryBranch.Count();

            var branchList = new List<Branch>();

            if (request.Pagination != null && request.Pagination.Page > 0 && request.Pagination.PageSize > 0)
            {
                branchList = await queryBranch
                    .OrderByDescending(o => o.Id)
                    .Skip((request.Pagination.Page - 1) * request.Pagination.PageSize)
                    .Take(request.Pagination.PageSize)
                    .ToListAsync();
            }
            else
            {
                branchList = await queryBranch
                    .OrderByDescending(o => o.Id)
                    .ToListAsync();
            }

            var newBranches = new BranchesDto() { Branches = new List<BranchDto>() };

            foreach (var item in branchList)
            {
                var newBranch = new BranchDto
                {
                    Id = item.Id,
                    Address = item.Address,
                    Email = item.Email,
                    Telephone = item.Telephone,
                    CityName = item.CityName,
                    Mission = item.Mission,
                    IsActive = item.IsActive,
                    Country = new CountryDto
                    {
                        Id = item.Country.Id,
                        Name = item.Country.Name
                    }
                };

                if (item.BranchTranslations.Any(p => !p.IsDeleted))
                {
                    var branchTranslations = item.BranchTranslations.Where(p => !p.IsDeleted);

                    if (branchTranslations.Any(p => p.LanguageId == request.LanguageId))
                        newBranch.Name = branchTranslations.First(p => p.LanguageId == request.LanguageId).Name;
                    else
                        newBranch.Name = branchTranslations.First().Name;
                }

                newBranches.Branches.Add(newBranch);
            }

            paginationResult.Items.Add(newBranches);
            return paginationResult;
        }

        public async Task<GetBranchAuthoritiesResponseDto> GetBranchAuthoritiesAsync(GetBranchAuthoritiesRequestDto request)
        {
            var existingBranch = await _unitOfWorkPortalDb.GetRepository<Branch>().Entities
                .Include(i => i.Country)
                .Include(i => i.BranchTranslations)
                .Include(i => i.BranchSupervisorDefinitions)
                .Where(p => !p.IsDeleted && p.Id == request.Id)
                .FirstOrDefaultAsync();

            if (existingBranch == null)
                throw new NoFoundDataPortalException($"{nameof(SiteResources.Exception_NoRecordsFound).ToSiteResourcesValue()} ({nameof(SiteResources.Branch).ToSiteResourcesValue()})");

            var roles = await _unitOfWorkPortalDb.GetRepository<RoleTranslation>().Entities
                .Where(s => s.IsActive && !s.IsDeleted && existingBranch.BranchSupervisorDefinitions.Select(b => b.RoleId).Contains(s.RoleId))
                .ToListAsync();

            var result = new GetBranchAuthoritiesResponseDto()
            {
                BranchName =
                    existingBranch.BranchTranslations.FirstOrDefault(f =>
                        f.IsActive && !f.IsDeleted && f.LanguageId == request.LanguageId) == null
                        ? existingBranch.BranchTranslations
                            .FirstOrDefault(f => f.IsActive && !f.IsDeleted && f.LanguageId == 2)?.Name
                        : existingBranch.BranchTranslations.FirstOrDefault(f =>
                            f.IsActive && !f.IsDeleted && f.LanguageId == request.LanguageId)?.Name,
                CountryName = existingBranch.Country.Name,
                SupervisorRoleList = existingBranch.BranchSupervisorDefinitions.Where(w => w.IsActive && !w.IsDeleted).OrderBy(o => o.RoleId)
                    .Select(s => new GetBranchAuthoritiesResponseDto.AuthorityResponse()
                    {
                        UserId = s.UserId,
                        IsSelected = s.IsSelected,
                        Role = new GetBranchAuthoritiesResponseDto.AuthorityResponse.RoleResponse()
                        {
                            Id = s.RoleId,
                            Name = roles.FirstOrDefault(w => w.LanguageId == request.LanguageId && w.RoleId == s.RoleId) == null ?
                                roles.FirstOrDefault(w => w.LanguageId == 2 && w.RoleId == s.RoleId)?.Name :
                                roles.FirstOrDefault(w => w.LanguageId == request.LanguageId && w.RoleId == s.RoleId)?.Name
                        }
                    }).ToList()
            };

            return result;
        }

        public async Task<AddOrUpdateBranchAuthoritiesResponseDto> AddOrUpdateBranchAuthoritiesAsync(
            AddOrUpdateBranchAuthoritiesRequestDto request)
        {
            var existingBranch = await _unitOfWorkPortalDb.GetRepository<Branch>().Entities
                .Include(i => i.Country)
                .Include(i => i.BranchTranslations)
                .Include(i => i.BranchSupervisorDefinitions)
                .Where(p => !p.IsDeleted && p.Id == request.BranchId)
                .FirstOrDefaultAsync();

            if (existingBranch == null)
                throw new NoFoundDataPortalException($"{nameof(SiteResources.Exception_NoRecordsFound).ToSiteResourcesValue()} ({nameof(SiteResources.Branch).ToSiteResourcesValue()})");

            foreach (var supervisorData in request.SupervisorRoleList)
            {
                var existingSupervisorData =
                    existingBranch.BranchSupervisorDefinitions.FirstOrDefault(
                        a => a.RoleId == supervisorData.RoleId);

                if (existingSupervisorData == null) // ekleme
                {
                    await _unitOfWorkPortalDb.GetRepository<BranchSupervisorDefinition>().AddAsync(
                        new BranchSupervisorDefinition()
                        {
                            BranchId = request.BranchId,
                            CreatedAt = DateTime.Now,
                            CreatedBy = request.UserAuditId,
                            IsActive = true,
                            IsDeleted = false,
                            RoleId = supervisorData.RoleId,
                            UserId = supervisorData.UserId,
                            IsSelected = supervisorData.IsSelected
                        });

                    await _unitOfWorkPortalDb.SaveChangesAsync();
                }
                else // güncelleme
                {
                    existingSupervisorData.UserId = supervisorData.UserId;
                    existingSupervisorData.IsSelected = supervisorData.IsSelected;
                    existingSupervisorData.UpdatedAt = DateTime.Now;
                    existingSupervisorData.UpdatedBy = request.UserAuditId;

                    _unitOfWorkPortalDb.GetRepository<BranchSupervisorDefinition>().Update(existingSupervisorData);
                    await _unitOfWorkPortalDb.SaveChangesAsync();
                }
            }

            return new AddOrUpdateBranchAuthoritiesResponseDto()
            {
                Result = true
            };
        }

        #endregion

        #region Role

        public async Task<EmptyResponseDto> AddRoleAsync(AddRoleRequestDto request)
        {
            var roleList = await _unitOfWorkPortalDb.GetRepository<Role>().Entities
                                    .Include(i => i.RoleTranslations)
                                    .Where(p => p.IsActive && !p.IsDeleted && p.RoleTranslations.Any(p2 => p2.IsActive && !p2.IsDeleted))
                                    .ToListAsync();

            if (roleList != null)
            {
                foreach (var item in request.RoleTranslations)
                {
                    // Check for existing role
                    if (roleList.SelectMany(p => p.RoleTranslations).Any(p => p.LanguageId == item.LanguageId && p.Name == item.Name.Normalize()))
                        throw new AlreadyExistsDataPortalException($"{nameof(SiteResources.Exception_ExistingRecord).ToSiteResourcesValue(request.LanguageId)} ({nameof(SiteResources.Role).ToSiteResourcesValue(request.LanguageId)})");
                }
            }

            var newRole = new Role
            {
                RoleTranslations = request.RoleTranslations.Select(p => new RoleTranslation
                {
                    LanguageId = p.LanguageId,
                    Name = p.Name.Normalize(),
                    Description = p.Description,
                    CreatedBy = request.UserAuditId,
                    CreatedAt = DateTime.Now
                }).ToList()
            };

            await _unitOfWorkPortalDb.GetRepository<Role>().AddAsync(newRole);
            await _unitOfWorkPortalDb.SaveChangesAsync();

            return new EmptyResponseDto();
        }

        public async Task<RoleDto> GetRoleAsync(RoleRequestDto request)
        {
            var existingRole = await _unitOfWorkPortalDb.GetRepository<Role>().Entities
                                    .Include(i => i.RoleTranslations)
                                    .Where(p => p.IsActive && !p.IsDeleted && p.Id == request.Id && p.RoleTranslations.Any(p2 => p2.IsActive && !p2.IsDeleted))
                                    .FirstOrDefaultAsync();

            if (existingRole == null)
                throw new NoFoundDataPortalException($"{nameof(SiteResources.Exception_NoRecordsFound).ToSiteResourcesValue(request.LanguageId)} ({nameof(SiteResources.Role).ToSiteResourcesValue(request.LanguageId)})");

            var result = new RoleDto
            {
                Id = existingRole.Id
            };

            if (existingRole.RoleTranslations.Any(p => p.IsActive && !p.IsDeleted))
            {
                var roleTranslations = existingRole.RoleTranslations.Where(p => p.IsActive && !p.IsDeleted);

                if (roleTranslations.Any(p => p.LanguageId == request.LanguageId))
                {
                    result.Name = roleTranslations.First(p => p.LanguageId == request.LanguageId).Name;
                    result.Description = roleTranslations.First(p => p.LanguageId == request.LanguageId).Description;
                }
                else
                {
                    result.Name = roleTranslations.First().Name;
                    result.Description = roleTranslations.First().Description;
                }

                result.RoleTranslations = roleTranslations.Select(p => new RoleTranslationDto
                {
                    Id = p.Id,
                    LanguageId = p.LanguageId,
                    Name = p.Name,
                    Description = p.Description
                }).ToList();
            }

            return result;
        }

        public async Task<UpdateResponseDto> UpdateRoleAsync(UpdateRoleRequestDto request)
        {
            var entity = await _unitOfWorkPortalDb.GetRepository<Role>().Entities
                                    .Include(i => i.RoleTranslations)
                                    .Where(p => p.Id == request.Id && p.IsActive && !p.IsDeleted && p.RoleTranslations.Any(p2 => p2.IsActive && !p2.IsDeleted))
                                    .FirstOrDefaultAsync();

            if (entity == null)
                throw new NoFoundDataPortalException($"{nameof(SiteResources.Exception_NoRecordsFound).ToSiteResourcesValue(request.LanguageId)} ({nameof(SiteResources.Role).ToSiteResourcesValue(request.LanguageId)})");

            foreach (var item in request.RoleTranslations)
            {
                var entityRoleTranslation = entity.RoleTranslations.FirstOrDefault(p => p.LanguageId == item.LanguageId);

                if (entityRoleTranslation == null)
                    continue;

                entityRoleTranslation.Name = item.Name.Normalize();
                entityRoleTranslation.Description = item.Description;
                entityRoleTranslation.UpdatedBy = request.UserAuditId;
                entityRoleTranslation.UpdatedAt = DateTime.Now;

                _unitOfWorkPortalDb.GetRepository<RoleTranslation>().Update(entityRoleTranslation);
            }

            await _unitOfWorkPortalDb.SaveChangesAsync();

            return new UpdateResponseDto()
            {
                Result = true
            };
        }

        public async Task<DeleteResponseDto> DeleteRoleAsync(int id)
        {
            // Get entity by id
            var entity = await _unitOfWorkPortalDb.GetRepository<Role>().Entities
                                    .Where(p => !p.IsDeleted && p.Id == id).FirstOrDefaultAsync();

            if (entity == null)
                throw new NoFoundDataPortalException($"{nameof(SiteResources.Exception_NoRecordsFound).ToSiteResourcesValue()} ({nameof(SiteResources.Role).ToSiteResourcesValue()})");

            _unitOfWorkPortalDb.GetRepository<Role>().MarkAsDeleted(entity.Id);

            await _unitOfWorkPortalDb.SaveChangesAsync();

            return new DeleteResponseDto()
            {
                Result = true
            };
        }

        public async Task<Pagination<RolesDto>> GetPaginatedRolesAsync(PaginatedRolesRequestDto request)
        {
            var paginationResult = new Pagination<RolesDto> { Page = request.Pagination.Page, PageSize = request.Pagination.PageSize };

            var queryBranch = _unitOfWorkPortalDb.GetRepository<Role>().Entities
                                    .Include(p => p.RoleTranslations)
                                    .Where(p => p.IsActive && !p.IsDeleted);

            paginationResult.TotalItemCount = queryBranch.Count();

            var roleList = await queryBranch
                    .OrderBy(request.Pagination.OrderBy, request.Pagination.SortDirection == ListSortDirection.Ascending ? true : false)
                    .Skip((request.Pagination.Page - 1) * request.Pagination.PageSize)
                    .Take(request.Pagination.PageSize)
                    .ToListAsync();

            var newRoles = new RolesDto() { Roles = new List<RoleDto>() };

            foreach (var item in roleList)
            {
                var newRole = new RoleDto
                {
                    Id = item.Id
                };

                if (item.RoleTranslations.Any(p => p.IsActive && !p.IsDeleted))
                {
                    var roleTranslations = item.RoleTranslations.Where(p => p.IsActive && !p.IsDeleted);

                    if (roleTranslations.Any(p => p.LanguageId == request.LanguageId))
                    {
                        newRole.Name = roleTranslations.First(p => p.LanguageId == request.LanguageId).Name;
                        newRole.Description = roleTranslations.First(p => p.LanguageId == request.LanguageId).Description;
                    }
                    else
                    {
                        newRole.Name = roleTranslations.First().Name;
                        newRole.Description = roleTranslations.First().Description;
                    }
                }

                newRoles.Roles.Add(newRole);
            }

            paginationResult.Items.Add(newRoles);
            return paginationResult;
        }

        #endregion

        #region Action

        public async Task<AddResponseDto> AddActionsAsync(AddActionsRequestDto request)
        {
            var entityInsertList = new List<Action>();

            foreach (var itemAction in request.Actions)
            {
                var entity = await _unitOfWorkPortalDb.GetRepository<Action>().Entities
                                    .Where(p => (!string.IsNullOrEmpty(itemAction.Area) ? p.Area.Equals(itemAction.Area) : true)
                                                && p.Controller.Equals(itemAction.Controller)
                                                && p.Method.Equals(itemAction.Method))
                                    .FirstOrDefaultAsync();

                if (entity != null)
                    continue;

                entityInsertList.Add(new Action()
                {
                    Area = itemAction.Area,
                    Controller = itemAction.Controller,
                    Method = itemAction.Method,
                    IsMenuItem = false,
                    IsPublic = false,
                    Order = null,
                    ParentId = null,
                    CreatedAt = DateTime.Now,
                    CreatedBy = request.UserAuditId,
                    ActionTranslations = new List<ActionTranslation>()
                    {
                        new ActionTranslation()
                        {
                            LanguageId = (int)Portal.Gateway.Contracts.Entities.Enums.Language.Turkish,
                            Name = "",
                            Description = $"{itemAction.Area}/{itemAction.Controller}/{itemAction.Method}"
                        }
                    }
                });
            }

            await _unitOfWorkPortalDb.GetRepository<Action>().AddRangeAsync(entityInsertList);
            await _unitOfWorkPortalDb.SaveChangesAsync();

            return new AddResponseDto();
        }

        public async Task<EmptyResponseDto> AddActionAsync(AddActionRequestDto request)
        {
            var actionList = await _unitOfWorkPortalDb.GetRepository<Action>().Entities
                                    .Include(i => i.ActionTranslations)
                                    .Where(p => !p.IsDeleted && p.ActionTranslations.Any(p2 => !p2.IsDeleted))
                                    .ToListAsync();

            if (actionList != null)
            {
                if (request.ParentId.HasValue && actionList.FirstOrDefault(p => p.Id == request.ParentId) == null)
                    throw new NoFoundDataPortalException($"{nameof(SiteResources.Exception_NoRecordsFound).ToSiteResourcesValue(request.LanguageId)} ({nameof(SiteResources.Action).ToSiteResourcesValue(request.LanguageId)})");

                foreach (var item in request.ActionTranslations)
                {
                    // Check for existing action
                    if (actionList.Any(p => (p.Area?.Equals(request.Area) ?? false) && (p.Controller?.Equals(request.Controller) ?? false) && (p.Method?.Equals(request.Method) ?? false)))
                        throw new AlreadyExistsDataPortalException($"{nameof(SiteResources.Exception_ExistingRecord).ToSiteResourcesValue(request.LanguageId)} ({nameof(SiteResources.Action).ToSiteResourcesValue(request.LanguageId)})");

                    // Check for existing action translation
                    if (actionList.SelectMany(p => p.ActionTranslations).Any(p => p.LanguageId == item.LanguageId && p.Name == item.Name.Normalize()))
                        throw new AlreadyExistsDataPortalException($"{nameof(SiteResources.Exception_ExistingRecord).ToSiteResourcesValue(request.LanguageId)} ({nameof(SiteResources.Action).ToSiteResourcesValue(request.LanguageId)})");
                }
            }

            var newAction = new Action
            {
                Area = request.Area,
                Controller = request.Controller,
                Method = request.Method,
                IsMenuItem = request.IsMenuItem,
                IsPublic = request.IsPublic,
                Order = request.Order,
                ParentId = request.ParentId,
                CreatedBy = request.UserAuditId,
                CreatedAt = DateTime.Now,
                ActionTranslations = request.ActionTranslations.Select(p => new ActionTranslation
                {
                    LanguageId = p.LanguageId,
                    Name = p.Name,
                    Description = p.Description
                }).ToList()
            };

            await _unitOfWorkPortalDb.GetRepository<Action>().AddAsync(newAction);
            await _unitOfWorkPortalDb.SaveChangesAsync();

            return new EmptyResponseDto();
        }

        public async Task<ActionDto> GetActionAsync(ActionRequestDto request)
        {
            var action = await _unitOfWorkPortalDb.GetRepository<Action>().Entities
                                    .Include(i => i.ActionTranslations)
                                    .Where(p => !p.IsDeleted && p.Id == request.Id)
                                    .FirstOrDefaultAsync();

            if (action == null)
                throw new NoFoundDataPortalException($"{nameof(SiteResources.Exception_NoRecordsFound).ToSiteResourcesValue(request.LanguageId)} ({nameof(SiteResources.Action).ToSiteResourcesValue(request.LanguageId)})");

            var result = new ActionDto
            {
                Id = action.Id,
                Area = action.Area,
                Controller = action.Controller,
                IsMenuItem = action.IsMenuItem,
                IsPublic = action.IsPublic,
                Method = action.Method,
                Order = action.Order,
                ParentId = action.ParentId,
                IsActive = action.IsActive
            };

            if (action.ActionTranslations.Any(p => p.IsActive && !p.IsDeleted))
            {
                var actionTranslations = action.ActionTranslations
                                        .Where(p => !p.IsDeleted);

                if (actionTranslations != null)
                {
                    result.ActionTranslations = actionTranslations.Select(p => new ActionTranslationDto
                    {
                        LanguageId = p.LanguageId,
                        Name = p.Name,
                        Description = p.Description
                    }).ToList();
                }
                else
                {
                    result.ActionTranslations = actionTranslations.Select(p => new ActionTranslationDto
                    {
                        LanguageId = actionTranslations.First().LanguageId,
                        Name = actionTranslations.First().Name,
                        Description = actionTranslations.First().Description
                    }).ToList();
                }
            }

            return result;
        }

        public async Task<UpdateResponseDto> UpdateActionAsync(UpdateActionRequestDto request)
        {
            var entity = await _unitOfWorkPortalDb.GetRepository<Action>().Entities
                                    .Include(i => i.ActionTranslations)
                                    .Where(p => p.Id == request.Id && !p.IsDeleted && p.ActionTranslations.Any(p2 => !p2.IsDeleted))
                                    .FirstOrDefaultAsync();

            if (entity == null)
                throw new NoFoundDataPortalException($"{nameof(SiteResources.Exception_NoRecordsFound).ToSiteResourcesValue(request.LanguageId)} ({nameof(SiteResources.Action).ToSiteResourcesValue(request.LanguageId)})");

            entity.Area = request.Area;
            entity.Controller = request.Controller;
            entity.Method = request.Method;
            entity.IsMenuItem = request.IsMenuItem;
            entity.IsPublic = request.IsPublic;
            entity.Order = request.Order;
            entity.ParentId = request.ParentId;
            entity.IsActive = request.IsActive;
            entity.UpdatedAt = DateTime.Now;
            entity.UpdatedBy = request.UserAuditId;

            foreach (var item in request.ActionTranslations)
            {
                var entityActionTranslation = entity.ActionTranslations.FirstOrDefault(p => p.LanguageId == item.LanguageId);

                if (entityActionTranslation == null)
                    continue;

                entityActionTranslation.Name = item.Name;
                entityActionTranslation.Description = item.Description;

                _unitOfWorkPortalDb.GetRepository<ActionTranslation>().Update(entityActionTranslation);
            }

            await _unitOfWorkPortalDb.SaveChangesAsync();

            return new UpdateResponseDto()
            {
                Result = true
            };
        }

        public async Task<DeleteResponseDto> DeleteActionAsync(DeleteRequestDto request)
        {
            var existingBranch = await _unitOfWorkPortalDb.GetRepository<Action>().Entities
                                        .Where(p => p.Id == request.Id)
                                        .FirstOrDefaultAsync();

            if (existingBranch == null)
                throw new NoFoundDataPortalException($"{nameof(SiteResources.Exception_NoRecordsFound).ToSiteResourcesValue()} ({nameof(SiteResources.Action).ToSiteResourcesValue()})");

            existingBranch.DeletedBy = request.UserAuditId;

            _unitOfWorkPortalDb.GetRepository<Action>().MarkAsDeleted(existingBranch.Id);
            await _unitOfWorkPortalDb.SaveChangesAsync();

            return new DeleteResponseDto()
            {
                Result = true
            };
        }

        public async Task<Pagination<ActionsDto>> GetPaginatedActionsAsync(PaginatedActionsRequestDto request)
        {
            var paginationResult = new Pagination<ActionsDto> { Page = request.Pagination.Page, PageSize = request.Pagination.PageSize };

            var queryAction = _unitOfWorkPortalDb.GetRepository<Action>().Entities
                                    .Include(p => p.ActionTranslations)
                                    .Where(p => !p.IsDeleted);


            if (request.HidePublicActions)
                queryAction = queryAction.Where(p => !p.IsPublic);

            paginationResult.TotalItemCount = queryAction.Count();

            var actionList = await queryAction
                    .OrderBy(request.Pagination.OrderBy, request.Pagination.SortDirection == ListSortDirection.Ascending ? true : false)
                    .Skip((request.Pagination.Page - 1) * request.Pagination.PageSize)
                    .Take(request.Pagination.PageSize)
                    .ToListAsync();

            var newActions = new ActionsDto() { Actions = new List<ActionDto>() };

            foreach (var item in actionList)
            {
                var newAction = new ActionDto
                {
                    Id = item.Id,
                    Area = item.Area,
                    ParentId = item.ParentId,
                    Order = item.Order,
                    IsPublic = item.IsPublic,
                    IsMenuItem = item.IsMenuItem,
                    Method = item.Method,
                    Controller = item.Controller,
                    IsActive = item.IsActive,
                    ActionTranslations = new List<ActionTranslationDto>()
                };

                if (item.ActionTranslations.Any(p => p.IsActive && !p.IsDeleted))
                {
                    var actionTranslations = item.ActionTranslations.Where(p => p.IsActive && !p.IsDeleted);

                    if (actionTranslations.Any(p => p.LanguageId == request.LanguageId))
                    {
                        newAction.ActionTranslations.Add(new ActionTranslationDto()
                        {
                            Name = actionTranslations.First(p => p.LanguageId == request.LanguageId).Name,
                            Description = actionTranslations.First(p => p.LanguageId == request.LanguageId).Description
                        });
                    }
                    else
                    {
                        newAction.ActionTranslations.Add(new ActionTranslationDto()
                        {
                            Name = actionTranslations.First().Name,
                            Description = actionTranslations.First().Description
                        });
                    }
                }

                newActions.Actions.Add(newAction);
            }

            paginationResult.Items.Add(newActions);
            return paginationResult;
        }

        #endregion

        #region Role Action

        public async Task<UpdateResponseDto> UpdateRoleActionAsync(UpdateRoleActionRequestDto request)
        {
            var entityRole = await _unitOfWorkPortalDb.GetRepository<Role>().Entities
                                   .Where(p => p.Id == request.RoleId && !p.IsDeleted)
                                   .FirstOrDefaultAsync();

            if (entityRole == null)
                throw new NoFoundDataPortalException($"{nameof(SiteResources.Exception_NoRecordsFound).ToSiteResourcesValue(request.LanguageId)} ({nameof(SiteResources.Role).ToSiteResourcesValue(request.LanguageId)})");

            var entityRoleActions = await _unitOfWorkPortalDb.GetRepository<RoleAction>().Entities
                                    .Where(p => p.RoleId == request.RoleId
                                                && !p.IsDeleted
                                    ).ToListAsync();

            var entityRoleActionsDeactivated = entityRoleActions.Where(p => request.Actions
                                                        .Where(x => !x.IsActive)
                                                        .Any(x => x.ActionId == p.ActionId)).AsEnumerable();
            // Deactivate existing role actions which request active = false
            if (entityRoleActionsDeactivated.Count() > 0)
            {
                entityRoleActionsDeactivated.ToList().ForEach(p => p.IsActive = false);

                _unitOfWorkPortalDb.GetRepository<RoleAction>().UpdateRange(entityRoleActionsDeactivated);

                await _unitOfWorkPortalDb.SaveChangesAsync();
            }

            var entityRoleActionsActivated = entityRoleActions.Where(p => request.Actions
                                                        .Where(x => x.IsActive)
                                                        .Any(x => x.ActionId == p.ActionId)).AsEnumerable();

            // Activate existing role actions which request active = true
            if (entityRoleActionsActivated.Count() > 0)
            {
                entityRoleActionsActivated.ToList().ForEach(p => p.IsActive = true);

                _unitOfWorkPortalDb.GetRepository<RoleAction>().UpdateRange(entityRoleActionsActivated);

                await _unitOfWorkPortalDb.SaveChangesAsync();
            }

            var newActionRoles = request.Actions
                                        .Where(p => p.IsActive && !entityRoleActions.Exists(m => m.ActionId == p.ActionId))
                                        .Select(p => new RoleAction
                                        {
                                            ActionId = p.ActionId,
                                            RoleId = request.RoleId,
                                            IsActive = p.IsActive,
                                            CreatedAt = DateTime.Now,
                                            CreatedBy = request.UserAuditId,
                                        }).AsEnumerable();

            // Add new role actions which do not exist
            if (newActionRoles.Count() > 0)
            {
                await _unitOfWorkPortalDb.GetRepository<RoleAction>().AddRangeAsync(newActionRoles);
                await _unitOfWorkPortalDb.SaveChangesAsync();
            }

            return new UpdateResponseDto()
            {
                Result = true
            };
        }

        public async Task<RoleActionDto> GetRoleActionAsync(RoleActionRequestDto request)
        {
            var entityRoleAction = await _unitOfWorkPortalDb.GetRepository<RoleAction>().Entities
                               .AsNoTracking()
                               .Where(p => !p.IsDeleted)
                               .ToListAsync();

            var actionEntity = await _unitOfWorkPortalDb.GetRepository<Action>().Entities
                                .Include(i => i.ActionTranslations)
                                .AsNoTracking()
                                .Where(p => !p.IsDeleted)
                                .ToListAsync();

            if (actionEntity == null || actionEntity.Count() == 0)
                throw new NoFoundDataPortalException($"{nameof(SiteResources.Exception_NoRecordsFound).ToSiteResourcesValue(request.LanguageId)} ({nameof(SiteResources.Action).ToSiteResourcesValue(request.LanguageId)})");

            var roleEntity = await _unitOfWorkPortalDb.GetRepository<Role>().Entities
                                .AsNoTracking()
                                .Where(p => !p.IsDeleted)
                                .ToListAsync();

            if (roleEntity == null || roleEntity.Count() == 0)
                throw new NoFoundDataPortalException($"{nameof(SiteResources.Exception_NoRecordsFound).ToSiteResourcesValue(request.LanguageId)} ({nameof(SiteResources.Role).ToSiteResourcesValue(request.LanguageId)})");

            var roleActionSite = new List<RoleActionSiteDto>();

            roleEntity.ForEach(r =>
            {
                roleActionSite.Add(new RoleActionSiteDto
                {
                    Role = new RoleDto
                    {
                        Id = r.Id
                    },
                    RoleActions = actionEntity.Select(p => new RoleActionsDto
                    {
                        Id = null,
                        Action = new ActionDto
                        {
                            Id = p.Id,
                            Area = p.Area,
                            Controller = p.Controller,
                            Method = p.Method,
                            IsMenuItem = p.IsMenuItem,
                            IsPublic = p.IsPublic,
                            Order = p.Order,
                            ParentId = p.ParentId,
                            IsActive = entityRoleAction.FirstOrDefault(x => x.ActionId == p.Id && x.RoleId == r.Id)?.IsActive ?? false,
                            ActionTranslations = p.ActionTranslations
                                                    .Where(m => !m.IsDeleted)
                                                    .Select(m => new ActionTranslationDto
                                                    {
                                                        Description = m.Description,
                                                        Name = m.Name,
                                                        LanguageId = m.LanguageId
                                                    })
                                                    .ToList()
                        }
                    }).ToList()
                });
            });

            return new RoleActionDto
            {
                RoleActionSites = roleActionSite
            };
        }

        #endregion

        #region BranchApplicationCountry 

        public async Task<AddResponseDto> AddBranchApplicationCountryAsync(AddBranchApplicationCountryRequestDto request)
        {
            var branchApplicationCountryList = await _unitOfWorkPortalDb.GetRepository<BranchApplicationCountry>().Entities
                .Include(p => p.Branch)
                .Where(p => !p.IsDeleted && p.BranchId == request.BranchId && p.CountryId == request.CountryId && !p.Branch.IsDeleted)
                .ToListAsync();

            var branchList = await _unitOfWorkPortalDb.GetRepository<Branch>().Entities
                        .Include(i => i.BranchTranslations)
                        .Where(p => p.IsActive && !p.IsDeleted && p.CountryId == request.CountryId && p.BranchTranslations.Any(p2 => p2.IsActive && !p2.IsDeleted))
                        .ToListAsync();

            if (branchApplicationCountryList != null && branchApplicationCountryList.Count != 0)
                throw new AlreadyExistsDataPortalException($"{nameof(SiteResources.Exception_ExistingRecord).ToSiteResourcesValue(request.LanguageId)} ({nameof(SiteResources.BranchApplicationCountry).ToSiteResourcesValue(request.LanguageId)})");

            var newBranchApplicationCountry = new BranchApplicationCountry
            {
                CountryId = request.CountryId,
                BranchId = request.BranchId,
                Note = request.Note
            };

            await _unitOfWorkPortalDb.GetRepository<BranchApplicationCountry>().AddAsync(newBranchApplicationCountry);
            await _unitOfWorkPortalDb.SaveChangesAsync();

            return new AddResponseDto() { Id = newBranchApplicationCountry.Id };
        }

        public async Task<UpdateResponseDto> UpdateBranchApplicationCountryAsync(UpdateBranchApplicationCountryRequestDto request)
        {
            var anyExistingBranchApplicationCountry = await _unitOfWorkPortalDb.GetRepository<BranchApplicationCountry>().Entities
                .Where(p => !p.IsDeleted && p.BranchId == request.BranchId && p.CountryId == request.CountryId && p.Id != request.Id)
                .AnyAsync();

            if (anyExistingBranchApplicationCountry)
                throw new AlreadyExistsDataPortalException($"{nameof(SiteResources.Exception_ExistingRecord).ToSiteResourcesValue(request.LanguageId)} ({nameof(SiteResources.BranchApplicationCountry).ToSiteResourcesValue(request.LanguageId)})");

            var existingBranchApplicationCountry = await _unitOfWorkPortalDb.GetRepository<BranchApplicationCountry>().Entities
                .Include(p => p.Branch)
                .Where(p => !p.IsDeleted && p.Id == request.Id && !p.Branch.IsDeleted)
                .FirstOrDefaultAsync();

            if (existingBranchApplicationCountry == null)
                throw new NoFoundDataPortalException($"{nameof(SiteResources.Exception_NoRecordsFound).ToSiteResourcesValue(request.LanguageId)} ({nameof(SiteResources.BranchApplicationCountry).ToSiteResourcesValue(request.LanguageId)})");

            existingBranchApplicationCountry.BranchId = request.BranchId;
            existingBranchApplicationCountry.CountryId = request.CountryId;
            existingBranchApplicationCountry.Note = request.Note;
            existingBranchApplicationCountry.IsActive = request.IsActive;

            _unitOfWorkPortalDb.GetRepository<BranchApplicationCountry>().Update(existingBranchApplicationCountry);
            await _unitOfWorkPortalDb.SaveChangesAsync();

            return new UpdateResponseDto()
            {
                Result = true
            };
        }

        public async Task<DeleteResponseDto> DeleteBranchApplicationCountryAsync(int id)
        {
            var existingBranchApplicationCountry = await _unitOfWorkPortalDb.GetRepository<BranchApplicationCountry>().Entities
                                        .Where(p => !p.IsDeleted && p.Id == id)
                                        .FirstOrDefaultAsync();

            if (existingBranchApplicationCountry == null)
                throw new NoFoundDataPortalException($"{nameof(SiteResources.Exception_NoRecordsFound).ToSiteResourcesValue()} ({nameof(SiteResources.BranchApplicationCountry).ToSiteResourcesValue()})");

            _unitOfWorkPortalDb.GetRepository<BranchApplicationCountry>().MarkAsDeleted(existingBranchApplicationCountry.Id);
            await _unitOfWorkPortalDb.SaveChangesAsync();

            return new DeleteResponseDto()
            {
                Result = true
            };
        }

        public async Task<BranchApplicationCountryResponseDto> GetBranchApplicationCountryAsync(BranchApplicationCountryRequestDto request)
        {
            var existingBranchApplicationCountry = await _unitOfWorkPortalDb.GetRepository<BranchApplicationCountry>().Entities
                                        .Include(i => i.Branch)
                                        .ThenInclude(i => i.BranchTranslations)
                                        .Include(i => i.Country)
                                        .Include(i => i.BranchApplicationCountryFiles)
                                            .ThenInclude(i => i.Document)
                                        .Where(p => !p.IsDeleted && p.Id == request.Id && p.Branch.BranchTranslations.Any(p2 => !p2.IsDeleted) && !p.Branch.IsDeleted)
                                        .FirstOrDefaultAsync();

            var countryFromBranch = await _unitOfWorkPortalDb.GetRepository<Branch>().Entities
                                .Include(i => i.Country)
                                .Where(p => !p.IsDeleted && p.IsActive && p.Id == existingBranchApplicationCountry.BranchId).FirstOrDefaultAsync();

            if (existingBranchApplicationCountry == null)
                throw new NoFoundDataPortalException($"{nameof(SiteResources.Exception_NoRecordsFound).ToSiteResourcesValue(request.LanguageId)} ({nameof(SiteResources.BranchApplicationCountry).ToSiteResourcesValue(request.LanguageId)})");

            var result = new BranchApplicationCountryResponseDto
            {
                Id = existingBranchApplicationCountry.Id,
                CountryName = existingBranchApplicationCountry.Country.Name,
                CountryId = existingBranchApplicationCountry.CountryId,
                BranchId = existingBranchApplicationCountry.BranchId,
                BranchCountryId = countryFromBranch.CountryId,
                BranchEmailProviderId = existingBranchApplicationCountry.Branch.EmailProviderId,
                BranchSmsProviderId = existingBranchApplicationCountry.Branch.SmsProviderId,
                BranchSmsIsSendByPrefix = existingBranchApplicationCountry.Branch.IsSendByPrefix,
                BranchSmsSender = existingBranchApplicationCountry.Branch.SmsSender,
                BranchNameEn = existingBranchApplicationCountry.Branch.BranchTranslations.First(p => p.LanguageId == 2).Name,
                IsActive = existingBranchApplicationCountry.IsActive,
                Note = existingBranchApplicationCountry.Note,
                BranchApplicationFiles = existingBranchApplicationCountry.BranchApplicationCountryFiles.Select(p => new BranchApplicationCountryResponseDto.BranchApplicationFileDto()
                {
                    Id = p.Document.Id
                }).AsEnumerable()
            };

            if (existingBranchApplicationCountry.Branch.BranchTranslations.Any(p => p.LanguageId == request.LanguageId))
                result.BranchName = existingBranchApplicationCountry.Branch.BranchTranslations.First(p => p.LanguageId == request.LanguageId).Name;
            else
                result.BranchName = existingBranchApplicationCountry.Branch.BranchTranslations.First().Name;

            return result;
        }

        public async Task<Pagination<PaginatedBranchApplicationCountryResponseDto>> GetPaginatedBranchApplicationCountryAsync(PaginatedBranchApplicationCountryRequestDto request)
        {
            var paginationResult = new Pagination<PaginatedBranchApplicationCountryResponseDto> { Page = request.Pagination.Page, PageSize = request.Pagination.PageSize };

            var queryBranchApplicationCountry = _unitOfWorkPortalDb.GetRepository<BranchApplicationCountry>().Entities
                                    .Include(p => p.Country)
                                    .Include(p => p.Branch)
                                    .ThenInclude(p => p.BranchTranslations)
                                    .Where(p => !p.IsDeleted);

            if (request.CountryId.HasValue && request.CountryId != 0)
                queryBranchApplicationCountry = queryBranchApplicationCountry.Where(p => p.CountryId == request.CountryId);

            if (request.BranchId.HasValue)
                queryBranchApplicationCountry = queryBranchApplicationCountry.Where(p => p.BranchId == request.BranchId);

            paginationResult.TotalItemCount = queryBranchApplicationCountry.Count();

            var branchApplicationCountryList = await queryBranchApplicationCountry
                    //.OrderBy(request.Pagination.OrderBy, request.Pagination.SortDirection == ListSortDirection.Ascending ? true : false)
                    .OrderByDescending(o => o.Id)
                    .Skip((request.Pagination.Page - 1) * request.Pagination.PageSize)
                    .Take(request.Pagination.PageSize)
                    .ToListAsync();

            var branchApplicationCountries = new PaginatedBranchApplicationCountryResponseDto() { BranchApplicationCountries = new List<BranchApplicationCountryItemResponseDto>() };

            foreach (var item in branchApplicationCountryList)
            {
                var branchApplicationCountry = new BranchApplicationCountryItemResponseDto
                {
                    Id = item.Id,
                    CountryName = item.Country.Name,
                    IsActive = item.IsActive,
                    BranchId = item.Branch.Id,
                    BranchName = item.Branch.BranchTranslations.Any(p => p.LanguageId == request.LanguageId) ?
                        item.Branch.BranchTranslations.First(p => p.LanguageId == request.LanguageId).Name :
                        item.Branch.BranchTranslations.First().Name
                };

                branchApplicationCountries.BranchApplicationCountries.Add(branchApplicationCountry);
            }

            paginationResult.Items.Add(branchApplicationCountries);
            return paginationResult;
        }

        public async Task<BranchesApplicationCountryByCountryResponseDto> GetBranchesApplicationCountryByCountry(BranchesApplicationCountryByCountryRequestDto request)
        {
            var queryBranchApplicationCountry = await _unitOfWorkPortalDb.GetRepository<BranchApplicationCountry>().Entities
                                                           .Include(p => p.Country)
                                                           .Include(p => p.Branch)
                                                           .ThenInclude(p => p.BranchTranslations)
                                                           .Where(p => !p.IsDeleted && p.Country.ISO2.Equals(request.CountryIso2Code))
                                                           .ToListAsync();

            if (queryBranchApplicationCountry.Count() == 0)
                throw new NoFoundDataPortalException($"{nameof(SiteResources.Exception_NoRecordsFound).ToSiteResourcesValue(request.LanguageId)} ({nameof(SiteResources.BranchApplicationCountry).ToSiteResourcesValue(request.LanguageId)})");

            return new BranchesApplicationCountryByCountryResponseDto()
            {
                BranchApplicationCountries = queryBranchApplicationCountry.Select(item => new BranchApplicationCountryItemResponseDto()
                {
                    Id = item.Id,
                    CountryName = item.Country.Name,
                    IsActive = item.IsActive,
                    BranchName = item.Branch.BranchTranslations.Any(p => p.LanguageId == request.LanguageId) ?
                       item.Branch.BranchTranslations.First(p => p.LanguageId == request.LanguageId).Name :
                       item.Branch.BranchTranslations.First().Name
                }).AsEnumerable()
            };
        }

        #endregion

        #region User

        public async Task<AddResponseDto> AddUserAsync(AddUserRequestDto request)
        {
            var anyExistingUser = await _unitOfWorkPortalDb.GetRepository<User>().Entities
                                            .Where(p => !p.IsDeleted && p.Email == request.Email)
                                            .AnyAsync();

            if (anyExistingUser)
                throw new AlreadyExistsDataPortalException($"{nameof(SiteResources.Exception_ExistingRecord).ToSiteResourcesValue(request.LanguageId)} ({nameof(SiteResources.User).ToSiteResourcesValue(request.LanguageId)})");

            var newUser = new User
            {
                Name = request.Name,
                Surname = request.Surname,
                Email = request.Email,
                Password = request.Password,
                PhoneNumber = request.PhoneNumber,
                IsActive = true,
                CreatedBy = request.UserAuditId,
                CreatedAt = DateTime.Now,
                ShowInPmsPage = false,
                IsBiometricsDesktopUser = false
            };

            if (!string.IsNullOrEmpty(request.SapUserId))
            {
                newUser.SapUser = new SapUser()
                {
                    SapUserId = request.SapUserId
                };
            }

            await _unitOfWorkPortalDb.GetRepository<User>().AddAsync(newUser);
            await _unitOfWorkPortalDb.SaveChangesAsync();

            return new AddResponseDto { Id = newUser.Id };
        }

        public async Task<UpdateResponseDto> UpdateUserAsync(UpdateUserRequestDto request)
        {
            var anyExistingUser = await _unitOfWorkPortalDb.GetRepository<User>().Entities
                                            .Where(p => p.IsActive && !p.IsDeleted && p.Id != request.Id && p.Email == request.Email)
                                            .AnyAsync();

            if (anyExistingUser)
                throw new AlreadyExistsDataPortalException($"{nameof(SiteResources.Exception_ExistingRecord).ToSiteResourcesValue(request.LanguageId)} ({nameof(SiteResources.User).ToSiteResourcesValue(request.LanguageId)})");

            var existingUser = await _unitOfWorkPortalDb.GetRepository<User>().Entities
                                        .Include(i => i.SapUser)
                                        .Where(p => p.IsActive && !p.IsDeleted && p.Id == request.Id)
                                        .FirstOrDefaultAsync();

            if (existingUser == null)
                throw new NoFoundDataPortalException($"{nameof(SiteResources.Exception_NoRecordsFound).ToSiteResourcesValue(request.LanguageId)} ({nameof(SiteResources.User).ToSiteResourcesValue(request.LanguageId)})");

            if (request.UpdatePassword)
            {
                if (string.IsNullOrEmpty(request.Password))
                    return new UpdateResponseDto { Result = false, Message = $"{nameof(SiteResources.Password).ToSiteResourcesValue(request.LanguageId)} {nameof(SiteResources.FillRequiredFields).ToSiteResourcesValue(request.LanguageId)}" };

                var pass = request.Password.ToCharArray();
                var upperCase = false;
                var lowerCase = false;
                var isNumber = false;
                var isSymbol = false;
                foreach (char c in pass)
                {
                    if (c >= 'A' && c <= 'Z')
                        upperCase = true;

                    if (c >= 'a' && c <= 'z')
                        lowerCase = true;

                    if (Char.IsNumber(c))
                        isNumber = true;

                    if (!Char.IsLetterOrDigit(c))
                        isSymbol = true;
                }

                var message = "";

                if (!(upperCase && lowerCase && isNumber && isSymbol && pass.Count() >= 8 && pass.Count() <= 20))
                    message = nameof(SiteResources.UserPasswordRequirements).ToSiteResourcesValue(request.LanguageId);
                else if (existingUser.Password == request.Password.ToHash() || existingUser.FirstOldPassword == request.Password.ToHash() || existingUser.SecondOldPassword == request.Password.ToHash())
                    message = nameof(SiteResources.PasswordCannotSame).ToSiteResourcesValue(request.LanguageId);

                if (message != "")
                    return new UpdateResponseDto { Result = false, Message = message };
            }

            existingUser.Name = request.Name;
            existingUser.Surname = request.Surname;
            existingUser.Email = request.Email;
            if (request.UpdatePassword)
            {
                existingUser.SecondOldPassword = existingUser.FirstOldPassword;
                existingUser.FirstOldPassword = existingUser.Password;
                existingUser.Password = request.Password.ToHash();
                existingUser.PasswordUpdatedAt = DateTime.Now;
            }
            existingUser.PhoneNumber = request.PhoneNumber;
            existingUser.IsActive = request.IsActive;
            existingUser.UpdatedBy = request.UserAuditId;
            existingUser.UpdatedAt = DateTime.Now;
            existingUser.ShowInPmsPage = request.ShowInPmsPage;
            existingUser.IsBiometricsDesktopUser = request.IsBiometricsDesktopUser;

            if (existingUser.SapUser != null)
            {
                existingUser.SapUser.SapUserId = request.SapUserId;
            }
            else
            {
                if (request.SapUserId != null)
                {
                    var newSapUser = new SapUser()
                    {
                        UserId = existingUser.Id,
                        SapUserId = request.SapUserId
                    };

                    await _unitOfWorkPortalDb.GetRepository<SapUser>().AddAsync(newSapUser);
                }
            }

            _unitOfWorkPortalDb.GetRepository<User>().Update(existingUser);
            await _unitOfWorkPortalDb.SaveChangesAsync();

            return new UpdateResponseDto { Result = true };
        }

        public async Task<UpdateResponseDto> UpdateUserPasswordAsync(UpdateUserPasswordRequestDto request)
        {
            var existingUser = await _unitOfWorkPortalDb.GetRepository<User>().Entities
                                .Where(p => p.IsActive && !p.IsDeleted && p.Id == request.Id).FirstOrDefaultAsync();

            if (existingUser == null)
                throw new AlreadyExistsDataPortalException($"{nameof(SiteResources.Exception_ExistingRecord).ToSiteResourcesValue(request.LanguageId)} ({nameof(SiteResources.User).ToSiteResourcesValue(request.LanguageId)})");

            existingUser.SecondOldPassword = existingUser.FirstOldPassword;
            existingUser.FirstOldPassword = existingUser.Password;
            existingUser.Password = request.Password;
            existingUser.UpdatedBy = request.Id;
            existingUser.UpdatedAt = DateTime.Now;
            existingUser.PasswordUpdatedAt = DateTime.Now;

            _unitOfWorkPortalDb.GetRepository<User>().Update(existingUser);
            await _unitOfWorkPortalDb.SaveChangesAsync();

            return new UpdateResponseDto { Result = true };
        }

        public async Task<DeleteResponseDto> DeleteUserAsync(DeleteRequestDto requset)
        {
            var existingUser = await _unitOfWorkPortalDb.GetRepository<User>().Entities
                                        .Where(p => !p.IsDeleted && p.Id == requset.Id)
                                        .FirstOrDefaultAsync();

            if (existingUser == null)
                throw new NoFoundDataPortalException($"{nameof(SiteResources.Exception_NoRecordsFound).ToSiteResourcesValue()} ({nameof(SiteResources.User).ToSiteResourcesValue()})");

            existingUser.DeletedBy = requset.UserAuditId;

            _unitOfWorkPortalDb.GetRepository<User>().MarkAsDeleted(existingUser.Id);
            await _unitOfWorkPortalDb.SaveChangesAsync();

            return new DeleteResponseDto() { Result = true };
        }

        public async Task<Contracts.Entities.Dto.Management.User.UserDto> GetUserAsync(UserRequestDto request)
        {
            var queryUser = _unitOfWorkPortalDb.GetRepository<User>().Entities
                                    .Include(i => i.SapUser)
                                    .Where(p => !p.IsDeleted);

            if (request.Id.HasValue)
                queryUser = queryUser.Where(p => p.Id == request.Id);

            if (!string.IsNullOrEmpty(request.Email))
                queryUser = queryUser.Where(p => p.Email == request.Email);

            var existingUser = await queryUser.FirstOrDefaultAsync();

            if (existingUser == null)
                throw new NoFoundDataPortalException($"{nameof(SiteResources.Exception_NoRecordsFound).ToSiteResourcesValue(request.LanguageId)} ({nameof(SiteResources.User).ToSiteResourcesValue(request.LanguageId)})");

            var result = new Contracts.Entities.Dto.Management.User.UserDto
            {
                Id = existingUser.Id,
                Name = existingUser.Name,
                Surname = existingUser.Surname,
                Email = existingUser.Email,
                PhoneNumber = existingUser.PhoneNumber,
                IsActive = existingUser.IsActive,
                SapUserId = existingUser.SapUser?.SapUserId,
                ShowInPmsPage = existingUser.ShowInPmsPage,
                IsBiometricsDesktopUser = existingUser.IsBiometricsDesktopUser,
                AdAccountName = existingUser.Username
            };

            return result;
        }

        public async Task<Pagination<Contracts.Entities.Dto.Management.User.UsersDto>> GetPaginatedUsersAsync(PaginatedUsersRequestDto request)
        {
            var paginationResult = new Pagination<Contracts.Entities.Dto.Management.User.UsersDto> { Page = request.Pagination.Page, PageSize = request.Pagination.PageSize };

            var queryUser = _unitOfWorkPortalDb.GetRepository<User>().Entities
                                    .Include(i => i.SapUser)
                                    .Where(p => !p.IsDeleted);

            if (!string.IsNullOrEmpty(request.Name))
                queryUser = queryUser.Where(p => p.Name.Contains(request.Name));

            if (!string.IsNullOrEmpty(request.Surname))
                queryUser = queryUser.Where(p => p.Surname.Contains(request.Surname));

            if (!string.IsNullOrEmpty(request.Email))
                queryUser = queryUser.Where(p => p.Email.Contains(request.Email));

            if (!string.IsNullOrEmpty(request.FilterAdAccountName))
            {
                string filter = request.FilterAdAccountName.ToLower();
                queryUser = queryUser.Where(p => p.Username != null && p.Username.ToLower().Contains(filter));
            }

            if (request.FilterCountryId.IsNumericAndGreaterThenZero() && request.FilterBranchId.IsNumericAndGreaterThenZero())
            {
                queryUser = queryUser.Where(p => p.UserBranches.Any(ub =>
                    ub.Branch.CountryId == request.FilterCountryId && ub.BranchId == request.FilterBranchId && !ub.IsDeleted));
            }
            else if (request.FilterCountryId.IsNumericAndGreaterThenZero())
            {
                queryUser = queryUser.Where(p => p.UserBranches.Any(ub => ub.Branch.CountryId == request.FilterCountryId && !ub.IsDeleted));
            }
            else if (request.FilterBranchId.IsNumericAndGreaterThenZero())
            {
                queryUser = queryUser.Where(p => p.UserBranches.Any(ub => ub.BranchId == request.FilterBranchId && !ub.IsDeleted));
            }


            paginationResult.TotalItemCount = queryUser.Count();

            var userList = new List<User>();

            if (request.Pagination != null && request.Pagination.Page > 0 && request.Pagination.PageSize > 0)
            {
                userList = await queryUser
                    .OrderByDescending(o => o.Id)
                    .Skip((request.Pagination.Page - 1) * request.Pagination.PageSize)
                    .Take(request.Pagination.PageSize)
                    .ToListAsync();
            }
            else
            {
                userList = await queryUser
                    .OrderByDescending(o => o.Id)
                    .ToListAsync();
            }

            var users = new Contracts.Entities.Dto.Management.User.UsersDto() { Users = new List<Contracts.Entities.Dto.Management.User.UserDto>() };

            users.Users = userList.Select(p => new Contracts.Entities.Dto.Management.User.UserDto
            {
                Id = p.Id,
                Name = p.Name,
                Surname = p.Surname,
                Email = p.Email,
                PhoneNumber = p.PhoneNumber,
                IsActive = p.IsActive,
                SapUserId = p.SapUser?.SapUserId,
                BranchId = request.FilterBranchId,
                CountryId = request.FilterCountryId,
                AdAccountName = p.Username
            }).ToList();

            paginationResult.Items.Add(users);
            return paginationResult;
        }

        public async Task<UpdateResponseDto> UpdateUserRoleAsync(UpdateUserRoleRequestDto request)
        {
            var entityUser = await _unitOfWorkPortalDb.GetRepository<User>().Entities
                                   .Where(p => p.Id == request.UserId && !p.IsDeleted)
                                   .FirstOrDefaultAsync();

            if (entityUser == null)
                throw new NoFoundDataPortalException($"{nameof(SiteResources.Exception_NoRecordsFound).ToSiteResourcesValue(request.LanguageId)} ({nameof(SiteResources.User).ToSiteResourcesValue(request.LanguageId)})");

            var entityUserRole = await _unitOfWorkPortalDb.GetRepository<UserRole>().Entities
                                        .Where(p => p.UserId == request.UserId
                                                    && !p.IsDeleted
                                        ).ToListAsync();

            var entityUserRoleDeactivated = entityUserRole.Where(p => request.Roles
                                                        .Where(x => !x.IsActive)
                                                        .Any(x => x.RoleId == p.RoleId)).AsEnumerable();

            // Deactivate existing role actions which request active = false
            if (entityUserRoleDeactivated.Count() > 0)
            {
                entityUserRoleDeactivated.ToList().ForEach(p => p.IsActive = false);

                _unitOfWorkPortalDb.GetRepository<UserRole>().UpdateRange(entityUserRoleDeactivated);

                await _unitOfWorkPortalDb.SaveChangesAsync();
            }

            var entityUserRoleActivated = entityUserRole.Where(p => request.Roles
                                                        .Where(x => x.IsActive)
                                                        .Any(x => x.RoleId == p.RoleId)).AsEnumerable();

            // Activate existing user roles which request active = true
            if (entityUserRoleActivated.Count() > 0)
            {
                entityUserRoleActivated.ToList().ForEach(p => p.IsActive = true);

                _unitOfWorkPortalDb.GetRepository<UserRole>().UpdateRange(entityUserRoleActivated);

                await _unitOfWorkPortalDb.SaveChangesAsync();
            }

            var newUserRoles = request.Roles
                                        .Where(p => p.IsActive && !entityUserRole.Exists(m => m.RoleId == p.RoleId))
                                        .Select(p => new UserRole
                                        {
                                            RoleId = p.RoleId,
                                            UserId = request.UserId,
                                            IsActive = p.IsActive,
                                            CreatedAt = DateTime.Now,
                                            CreatedBy = request.UserAuditId
                                        }).AsEnumerable();

            // Add new user role which do not exist
            if (newUserRoles.Count() > 0)
            {
                await _unitOfWorkPortalDb.GetRepository<UserRole>().AddRangeAsync(newUserRoles);
                await _unitOfWorkPortalDb.SaveChangesAsync();
            }

            return new UpdateResponseDto()
            {
                Result = true
            };
        }

        public async Task<UserRoleResponseDto> GetUserRoleAsync(UserRoleRequestDto request)
        {
            var entityUserRole = _unitOfWorkPortalDb.GetRepository<UserRole>().Entities
                                .Where(p => !p.IsDeleted && p.UserId == request.UserId)
                                .ToList();

            var entityRole = _unitOfWorkPortalDb.GetRepository<Role>().Entities
                                .Include(p => p.RoleTranslations)
                                .Where(p => !p.IsDeleted)
                                .AsEnumerable();

            if (entityRole == null || entityRole.Count() == 0)
                throw new NoFoundDataPortalException($"{nameof(SiteResources.Exception_NoRecordsFound).ToSiteResourcesValue(request.LanguageId)} ({nameof(SiteResources.Role).ToSiteResourcesValue(request.LanguageId)})");

            var userRoles = new List<UserRolesResponseDto>();

            await entityRole.ToList().ForEachAsync(async roleEntity =>
            {
                userRoles.Add(new UserRolesResponseDto()
                {
                    Role = new RoleDto()
                    {
                        Id = roleEntity.Id,
                        Name = roleEntity.RoleTranslations.FirstOrDefault(p => p.LanguageId == request.LanguageId)?.Name,
                        Description = roleEntity.RoleTranslations.FirstOrDefault(p => p.LanguageId == request.LanguageId)?.Description
                    },
                    IsActive = entityUserRole.FirstOrDefault(x => x.UserId == request.UserId && x.RoleId == roleEntity.Id)?.IsActive ?? false
                });
            }).ConfigureAwait(false);


            return new UserRoleResponseDto()
            {
                UserId = request.UserId,
                UserRoles = userRoles
            };
        }

        #endregion

        #region UserModule

        public async Task<AddResponseDto> AddUserModuleAsync(AddUserModuleRequestDto request)
        {
            var existingUser = await _unitOfWorkPortalDb.GetRepository<User>().Entities
                                            .Where(p => !p.IsDeleted && p.Id == request.UserId)
                                            .FirstOrDefaultAsync();

            if (existingUser == null)
                throw new NoFoundDataPortalException($"{nameof(SiteResources.Exception_NoRecordsFound).ToSiteResourcesValue(request.LanguageId)} ({nameof(SiteResources.User).ToSiteResourcesValue(request.LanguageId)})");

            var existingCompanyModule = await _unitOfWorkPortalDb.GetRepository<MasterCompanyModule>().Entities
                                            .Where(p => !p.IsDeleted && p.CompanyId == request.CompanyId && p.ModuleId == request.ModuleId)
                                            .FirstOrDefaultAsync();

            if (existingCompanyModule == null)
                throw new NoFoundDataPortalException($"{nameof(SiteResources.Exception_NoRecordsFound).ToSiteResourcesValue(request.LanguageId)} ({nameof(SiteResources.CompanyModule).ToSiteResourcesValue(request.LanguageId)})");

            var userModuleCount = await _unitOfWorkPortalDb.GetRepository<UserModule>().Entities
                                            .CountAsync(p => !p.IsDeleted && p.ModuleId == request.ModuleId);

            if (userModuleCount >= existingCompanyModule.LicenseCount)
                throw new BadRequestPortalException($"{nameof(SiteResources.Exception_ExceedsLicenseCountLimit).ToSiteResourcesValue(request.LanguageId)}");

            var anyExistingUserModule = await _unitOfWorkPortalDb.GetRepository<UserModule>().Entities
                                            .Where(p => !p.IsDeleted && p.UserId == request.UserId && p.ModuleId == request.ModuleId)
                                            .AnyAsync();

            if (anyExistingUserModule)
                throw new AlreadyExistsDataPortalException($"{nameof(SiteResources.Exception_ExistingRecord).ToSiteResourcesValue(request.LanguageId)} ({nameof(SiteResources.UserModule).ToSiteResourcesValue(request.LanguageId)})");

            var newUserModule = new UserModule
            {
                UserId = request.UserId,
                ModuleId = request.ModuleId,
                IsActive = true,
                CreatedBy = request.UserAuditId,
                CreatedAt = DateTime.Now
            };

            await _unitOfWorkPortalDb.GetRepository<UserModule>().AddAsync(newUserModule);
            await _unitOfWorkPortalDb.SaveChangesAsync();

            return new AddResponseDto { Id = newUserModule.Id };
        }

        public async Task<DeleteResponseDto> DeleteUserModuleAsync(int id)
        {
            var existingUserModule = await _unitOfWorkPortalDb.GetRepository<UserModule>().Entities
                                        .Where(p => !p.IsDeleted && p.Id == id)
                                        .FirstOrDefaultAsync();

            if (existingUserModule == null)
                throw new NoFoundDataPortalException($"{nameof(SiteResources.Exception_NoRecordsFound).ToSiteResourcesValue()} ({nameof(SiteResources.UserModule).ToSiteResourcesValue()})");

            _unitOfWorkPortalDb.GetRepository<UserModule>().MarkAsDeleted(existingUserModule.Id);
            await _unitOfWorkPortalDb.SaveChangesAsync();

            return new DeleteResponseDto() { Result = true };
        }

        public async Task<UserModulesDto> GetUserModulesAsync(UserModulesRequestDto request)
        {
            var result = new UserModulesDto();

            result.UserModules = await _unitOfWorkPortalDb.GetRepository<UserModule>().Entities
                .Where(p => p.IsActive && !p.IsDeleted && p.UserId == request.UserId)
                .Select(p => new UserModuleDto
                {
                    Id = p.Id,
                    UserId = p.UserId,
                    ModuleId = p.ModuleId,
                    User = new Contracts.Entities.Dto.Management.User.UserDto
                    {
                        Id = p.User.Id,
                        Name = p.User.Name,
                        Surname = p.User.Surname,
                        Email = p.User.Email,
                        PhoneNumber = p.User.PhoneNumber,
                        IsActive = p.User.IsActive
                    },
                    IsActive = p.IsActive
                })
                .ToListAsync();

            return result;
        }

        #endregion

        #region UserBranch

        public async Task<AddResponseDto> AddUserBranchAsync(AddUserBranchRequestDto request)
        {
            var existingUser = await _unitOfWorkPortalDb.GetRepository<User>().Entities
                                            .Where(p => !p.IsDeleted && p.Id == request.UserId)
                                            .FirstOrDefaultAsync();

            if (existingUser == null)
                throw new NoFoundDataPortalException($"{nameof(SiteResources.Exception_NoRecordsFound).ToSiteResourcesValue(request.LanguageId)} ({nameof(SiteResources.User).ToSiteResourcesValue(request.LanguageId)})");

            var existingBranches = await _unitOfWorkPortalDb.GetRepository<Branch>().Entities
                                            .Where(p => !p.IsDeleted && request.BranchIds.Contains(p.Id))
                                            .ToListAsync();

            if (!existingBranches.Any())
                throw new NoFoundDataPortalException($"{nameof(SiteResources.Exception_NoRecordsFound).ToSiteResourcesValue(request.LanguageId)} ({nameof(SiteResources.Branch).ToSiteResourcesValue(request.LanguageId)})");

            var anyExistingUserBranch = await _unitOfWorkPortalDb.GetRepository<UserBranch>().Entities
                                            .Where(p => !p.IsDeleted && p.UserId == request.UserId && request.BranchIds.Contains(p.BranchId))
                                            .AnyAsync();

            if (anyExistingUserBranch)
                throw new AlreadyExistsDataPortalException($"{nameof(SiteResources.Exception_ExistingRecord).ToSiteResourcesValue(request.LanguageId)} ({nameof(SiteResources.UserBranch).ToSiteResourcesValue(request.LanguageId)})");


            foreach (var branch in request.BranchIds)
            {
                var newUserBranch = new UserBranch
                {
                    UserId = request.UserId,
                    BranchId = branch,
                    IsActive = true,
                    CreatedAt = DateTime.Now,
                    CreatedBy = request.UserAuditId
                };

                await _unitOfWorkPortalDb.GetRepository<UserBranch>().AddAsync(newUserBranch);
                await _unitOfWorkPortalDb.SaveChangesAsync();
            }


            return new AddResponseDto();
        }

        public async Task<DeleteResponseDto> DeleteUserBranchAsync(int id)
        {
            var existingUserBranch = await _unitOfWorkPortalDb.GetRepository<UserBranch>().Entities
                                        .Where(p => !p.IsDeleted && p.Id == id)
                                        .FirstOrDefaultAsync();

            if (existingUserBranch == null)
                throw new NoFoundDataPortalException($"{nameof(SiteResources.Exception_NoRecordsFound).ToSiteResourcesValue()} ({nameof(SiteResources.UserBranch).ToSiteResourcesValue()})");

            _unitOfWorkPortalDb.GetRepository<UserBranch>().MarkAsDeleted(existingUserBranch.Id);
            await _unitOfWorkPortalDb.SaveChangesAsync();

            return new DeleteResponseDto() { Result = true };
        }

        public async Task<UserBranchesDto> GetUserBranchesAsync(UserBranchesRequestDto request)
        {
            var result = new UserBranchesDto { UserBranches = new List<UserBranchDto>() };

            var userBranchList = await _unitOfWorkPortalDb.GetRepository<UserBranch>().Entities
                .Include(i => i.Branch)
                .Include(i => i.Branch.Country)
                .Include(i => i.Branch.BranchTranslations)
                .Where(p => !p.IsDeleted && p.UserId == request.UserId)
                .ToListAsync();

            foreach (var item in userBranchList)
            {
                var newUserBranch = new UserBranchDto
                {
                    Id = item.Id,
                    UserId = item.UserId,
                    CountryId = item.Branch.CountryId,
                    BranchId = item.BranchId,
                    IsActive = item.IsActive
                };

                newUserBranch.CountryName = item.Branch.Country.Name;

                if (item.Branch.BranchTranslations.Any(p => p.IsActive && !p.IsDeleted))
                {
                    var branchTranslations = item.Branch.BranchTranslations.Where(p => p.IsActive && !p.IsDeleted);

                    if (branchTranslations.Any(p => p.LanguageId == request.LanguageId))
                        newUserBranch.BranchName = branchTranslations.First(p => p.LanguageId == request.LanguageId).Name;
                    else
                        newUserBranch.BranchName = branchTranslations.First().Name;
                }

                result.UserBranches.Add(newUserBranch);
            }

            return result;
        }

        #endregion

        #region BranchApplicationCountryVisaCategory

        public async Task<UpdateResponseDto> UpdateBranchApplicationCountryVisaCategoryAsync(UpdateBranchApplicationCountryVisaCategoryRequestDto request)
        {
            var entityBranchApplicationCountry = await _unitOfWorkPortalDb.GetRepository<BranchApplicationCountry>().Entities
                                   .Include(p => p.Branch)
                                   .Where(p => p.Id == request.BranchApplicationCountryId && !p.IsDeleted)
                                   .FirstOrDefaultAsync();

            if (entityBranchApplicationCountry == null)
                throw new NoFoundDataPortalException($"{nameof(SiteResources.Exception_NoRecordsFound).ToSiteResourcesValue(request.LanguageId)} ({nameof(SiteResources.BranchApplicationCountry).ToSiteResourcesValue(request.LanguageId)})");

            var entityBranchApplicationCountryVisaCategory = await _unitOfWorkPortalDb.GetRepository<BranchApplicationCountryVisaCategory>().Entities
                                        .Where(p => p.BranchApplicationCountryId == request.BranchApplicationCountryId && !p.IsDeleted
                                        ).ToListAsync();

            var entityBranchApplicationCountryVisaCategoryDeactivated = entityBranchApplicationCountryVisaCategory.Where(p => request.VisaCategories
                                                        .Where(x => !x.IsActive)
                                                        .Any(x => x.VisaCategoryId == p.VisaCategoryId)).AsEnumerable();

            if (entityBranchApplicationCountryVisaCategoryDeactivated.Count() > 0)
            {
                entityBranchApplicationCountryVisaCategoryDeactivated.ToList().ForEach(p => p.IsActive = false);

                _unitOfWorkPortalDb.GetRepository<BranchApplicationCountryVisaCategory>().UpdateRange(entityBranchApplicationCountryVisaCategoryDeactivated);

                await _unitOfWorkPortalDb.SaveChangesAsync();
            }

            var entityBranchApplicationCountryVisaCategoryActivated = entityBranchApplicationCountryVisaCategory.Where(p => request.VisaCategories
                                                        .Where(x => x.IsActive)
                                                        .Any(x => x.VisaCategoryId == p.VisaCategoryId)).AsEnumerable();

            if (entityBranchApplicationCountryVisaCategoryActivated.Count() > 0)
            {
                entityBranchApplicationCountryVisaCategoryActivated.ToList().ForEach(p => p.IsActive = true);

                _unitOfWorkPortalDb.GetRepository<BranchApplicationCountryVisaCategory>().UpdateRange(entityBranchApplicationCountryVisaCategoryActivated);

                await _unitOfWorkPortalDb.SaveChangesAsync();
            }

            var newBranchVisaCategories = request.VisaCategories
                                        .Where(p => p.IsActive && !entityBranchApplicationCountryVisaCategory.Exists(m => m.VisaCategoryId == p.VisaCategoryId))
                                        .Select(p => new BranchApplicationCountryVisaCategory
                                        {
                                            VisaCategoryId = p.VisaCategoryId,
                                            BranchApplicationCountryId = request.BranchApplicationCountryId,
                                            IsActive = p.IsActive,
                                            CreatedBy = request.UserAuditId,
                                            CreatedAt = DateTime.Now
                                        }).AsEnumerable();

            if (newBranchVisaCategories.Count() > 0)
            {
                await _unitOfWorkPortalDb.GetRepository<BranchApplicationCountryVisaCategory>().AddRangeAsync(newBranchVisaCategories);
                await _unitOfWorkPortalDb.SaveChangesAsync();
            }

            return new UpdateResponseDto()
            {
                Result = true
            };
        }

        public async Task<BranchApplicationCountryVisaCategoryResponseDto> GetBranchApplicationCountryVisaCategoryAsync(BranchApplicationCountryVisaCategoryRequestDto request)
        {
            var entityBranchApplicationCountry = await _unitOfWorkPortalDb.GetRepository<BranchApplicationCountry>().Entities
                                .Include(p => p.Branch)
                                .ThenInclude(p => p.BranchTranslations)
                                .Include(p => p.Country)
                                .Where(p => !p.IsDeleted && p.Id == request.BranchApplicationCountryId)
                                .FirstOrDefaultAsync();

            if (entityBranchApplicationCountry == null)
                throw new NoFoundDataPortalException($"{nameof(SiteResources.Exception_NoRecordsFound).ToSiteResourcesValue(request.LanguageId)} ({nameof(SiteResources.BranchApplicationCountry).ToSiteResourcesValue(request.LanguageId)})");

            var entityBranchApplicationCountryVisaCategory = _unitOfWorkPortalDb.GetRepository<BranchApplicationCountryVisaCategory>().Entities
                    .Where(p => !p.IsDeleted && p.BranchApplicationCountryId == request.BranchApplicationCountryId)
                    .ToList();


            var entityVisaType = await _unitOfWorkPortalDb.GetRepository<VisaType>().Entities
                               .Include(p => p.VisaTypeTranslations)
                               .Where(p => p.IsActive && !p.IsDeleted)
                               .ToListAsync();


            Dictionary<int, string> visaCategoryTypes = entityVisaType.ToDictionary(s => s.Id, s => s.VisaTypeTranslations.Where(q => q.LanguageId == request.LanguageId).Select(q => q.Name).FirstOrDefault());

            var branchVisaCategories = new BranchApplicationCountryVisaCategoryResponseDto()
            {
                BranchApplicationCountryId = entityBranchApplicationCountry.Id,
                BranchName = entityBranchApplicationCountry.Branch.BranchTranslations.Any(p => p.LanguageId == request.LanguageId) ?
                                entityBranchApplicationCountry.Branch.BranchTranslations.First(p => p.LanguageId == request.LanguageId).Name :
                                entityBranchApplicationCountry.Branch.BranchTranslations.First().Name,
                CountryName = entityBranchApplicationCountry.Country.Name,
                VisaCategories = entityBranchApplicationCountryVisaCategory.Select(p => new VisaCategoryResponseDto()
                {
                    VisaCategoryId = p.VisaCategoryId,
                    IsActive = p.IsActive
                }).ToList(),
                VisaCategoryTypes = visaCategoryTypes
            };

            return branchVisaCategories;
        }

        #endregion

        #region BranchApplicationCountryExtraFee

        public async Task<AddResponseDto> AddBranchApplicationCountryExtraFeeAsync(AddBranchApplicationCountryExtraFeeRequestDto request)
        {
            var branchApplicationCountryExtraFee = await _unitOfWorkPortalDb.GetRepository<BranchApplicationCountryExtraFee>().Entities
                .Include(p => p.BranchApplicationCountry)
                    .ThenInclude(p => p.Branch)
                .Include(p => p.ExtraFee)
                .Where(p => !p.IsDeleted && p.BranchApplicationCountryId == request.BranchApplicationCountryId && p.ExtraFeeId == request.ExtraFeeId && !p.BranchApplicationCountry.IsDeleted && !p.BranchApplicationCountry.Branch.IsDeleted && !p.ExtraFee.IsDeleted)
                .AnyAsync();

            if (branchApplicationCountryExtraFee)
                throw new AlreadyExistsDataPortalException($"{nameof(SiteResources.Exception_ExistingRecord).ToSiteResourcesValue(request.LanguageId)} ({nameof(SiteResources.BranchApplicationCountryExtraFee).ToSiteResourcesValue(request.LanguageId)})");

            var newBranchApplicationCountryExtraFee = new BranchApplicationCountryExtraFee
            {
                BranchApplicationCountryId = request.BranchApplicationCountryId,
                ExtraFeeId = request.ExtraFeeId,
                IsAutoChecked = request.IsAutoChecked,
                Price = request.BasePrice + request.ServiceTax,
                Tax = request.Tax,
                TaxRatio = request.TaxRatio,
                ServiceTax = request.ServiceTax,
                BasePrice = request.BasePrice,
                ShowInICR = request.ShowInICR,
                ShowInSummary = request.ShowInSummary,
                IsShowInReport = request.IsShowInReport,
                IsGroupInIcr = request.IsGroupInIcr,
                CreatedAt = DateTime.Now,
                CreatedBy = request.UserAuditId,
                IsShowInRejectionList = request.IsShowInRejectionList,
                IsShowInAllApplicationsReport = request.IsShowInAllApplicationsReport,
                IsShowInApplicationAfterRejection = request.IsShowInApplicationAfterRejection,
                SapExtraFee = !string.IsNullOrEmpty(request.SapExtraFeeId) ? new SapExtraFee()
                {
                    SapExtraFeeId = request.SapExtraFeeId
                } : null,
                CurrencyId = request.CurrencyId,
            };

            await _unitOfWorkPortalDb.GetRepository<BranchApplicationCountryExtraFee>().AddAsync(newBranchApplicationCountryExtraFee);
            await _unitOfWorkPortalDb.SaveChangesAsync();

            return new AddResponseDto { Id = newBranchApplicationCountryExtraFee.Id };
        }

        public async Task<UpdateResponseDto> UpdateBranchApplicationCountryExtraFeeAsync(UpdateBranchApplicationCountryExtraFeeRequestDto request)
        {
            var anyExistingBranchApplicationCountryExtraFee = await _unitOfWorkPortalDb.GetRepository<BranchApplicationCountryExtraFee>().Entities
                .Where(p => p.Id != request.Id && !p.IsDeleted && p.ExtraFeeId == request.ExtraFeeId && p.BranchApplicationCountryId == request.BranchApplicationCountryId)
                .AnyAsync();

            if (anyExistingBranchApplicationCountryExtraFee)
                throw new AlreadyExistsDataPortalException($"{nameof(SiteResources.Exception_ExistingRecord).ToSiteResourcesValue(request.LanguageId)} ({nameof(SiteResources.BranchApplicationCountryExtraFee).ToSiteResourcesValue(request.LanguageId)})");

            var existingBranchApplicationCountryExtraFee = await _unitOfWorkPortalDb.GetRepository<BranchApplicationCountryExtraFee>().Entities
                .Include(p => p.BranchApplicationCountry)
                .ThenInclude(p => p.Branch)
                .Include(p => p.ExtraFee)
                .Include(p => p.SapExtraFee)
                .Where(p => !p.IsDeleted && p.Id == request.Id && !p.BranchApplicationCountry.IsDeleted && !p.BranchApplicationCountry.Branch.IsDeleted && !p.ExtraFee.IsDeleted)
                .FirstOrDefaultAsync();

            if (existingBranchApplicationCountryExtraFee == null)
                throw new NoFoundDataPortalException($"{nameof(SiteResources.Exception_NoRecordsFound).ToSiteResourcesValue(request.LanguageId)} ({nameof(SiteResources.BranchApplicationCountryExtraFee).ToSiteResourcesValue(request.LanguageId)})");

            existingBranchApplicationCountryExtraFee.Price = request.BasePrice + request.ServiceTax;
            existingBranchApplicationCountryExtraFee.Tax = request.Tax;
            existingBranchApplicationCountryExtraFee.TaxRatio = request.TaxRatio;
            existingBranchApplicationCountryExtraFee.ServiceTax = request.ServiceTax;
            existingBranchApplicationCountryExtraFee.BasePrice = request.BasePrice;
            existingBranchApplicationCountryExtraFee.CurrencyId = request.CurrencyId;
            existingBranchApplicationCountryExtraFee.IsAutoChecked = request.IsAutoChecked;
            existingBranchApplicationCountryExtraFee.IsActive = request.IsActive;
            existingBranchApplicationCountryExtraFee.ExtraFeeId = request.ExtraFeeId;
            existingBranchApplicationCountryExtraFee.ShowInICR = request.ShowInICR;
            existingBranchApplicationCountryExtraFee.ShowInSummary = request.ShowInSummary;
            existingBranchApplicationCountryExtraFee.IsShowInReport = request.IsShowInReport;
            existingBranchApplicationCountryExtraFee.IsGroupInIcr = request.IsGroupInIcr;
            existingBranchApplicationCountryExtraFee.IsShowInAllApplicationsReport = request.IsShowInAllApplicationsReport;
            existingBranchApplicationCountryExtraFee.IsShowInApplicationAfterRejection = request.IsShowInApplicationAfterRejection;
            existingBranchApplicationCountryExtraFee.UpdatedBy = request.UserAuditId;
            existingBranchApplicationCountryExtraFee.IsShowInRejectionList = request.IsShowInRejectionList;
            existingBranchApplicationCountryExtraFee.UpdatedAt = DateTime.Now;


            if (existingBranchApplicationCountryExtraFee.SapExtraFee == null && !string.IsNullOrEmpty(request.SapExtraFeeId))
            {
                var newSapExtraFee = new SapExtraFee()
                {
                    BranchApplicationCountryExtraFeeId = request.Id,
                    SapExtraFeeId = request.SapExtraFeeId
                };

                await _unitOfWorkPortalDb.GetRepository<SapExtraFee>().AddAsync(newSapExtraFee);
            }
            else
            {
                if (existingBranchApplicationCountryExtraFee.SapExtraFee != null)
                {
                    var sapExtraFee = await _unitOfWorkPortalDb.GetRepository<SapExtraFee>().Entities
                                            .Where(p => !p.IsDeleted && p.BranchApplicationCountryExtraFeeId == request.Id)
                                            .FirstOrDefaultAsync();

                    sapExtraFee.SapExtraFeeId = request.SapExtraFeeId;

                    _unitOfWorkPortalDb.GetRepository<SapExtraFee>().Update(sapExtraFee);
                }
            }

            _unitOfWorkPortalDb.GetRepository<BranchApplicationCountryExtraFee>().Update(existingBranchApplicationCountryExtraFee);
            await _unitOfWorkPortalDb.SaveChangesAsync();

            return new UpdateResponseDto { Result = true };
        }

        public async Task<DeleteResponseDto> DeleteBranchApplicationCountryExtraFeeAsync(DeleteRequestDto request)
        {
            var existingBranchApplicationCountryExtraFee = await _unitOfWorkPortalDb.GetRepository<BranchApplicationCountryExtraFee>().Entities
                                        .Where(p => !p.IsDeleted && p.Id == request.Id)
                                        .FirstOrDefaultAsync();

            if (existingBranchApplicationCountryExtraFee == null)
                throw new NoFoundDataPortalException($"{nameof(SiteResources.Exception_NoRecordsFound).ToSiteResourcesValue()} ({nameof(SiteResources.BranchApplicationCountryExtraFee).ToSiteResourcesValue()})");

            existingBranchApplicationCountryExtraFee.IsActive = false;
            existingBranchApplicationCountryExtraFee.DeletedBy = request.UserAuditId;
            existingBranchApplicationCountryExtraFee.IsShowInReport = false;
            existingBranchApplicationCountryExtraFee.ShowInICR = false;
            existingBranchApplicationCountryExtraFee.ShowInSummary = false;
            existingBranchApplicationCountryExtraFee.IsShowInRejectionList = false;
            existingBranchApplicationCountryExtraFee.IsGroupInIcr = false;
            existingBranchApplicationCountryExtraFee.IsAutoChecked = false;

            _unitOfWorkPortalDb.GetRepository<BranchApplicationCountryExtraFee>().MarkAsDeleted(existingBranchApplicationCountryExtraFee.Id);
            await _unitOfWorkPortalDb.SaveChangesAsync();

            return new DeleteResponseDto { Result = true };
        }

        public async Task<BranchApplicationCountryExtraFeeResponseDto> GetBranchApplicationCountryExtraFeeAsync(BranchApplicationCountryExtraFeeRequestDto request)
        {
            var existingBranchApplicationCountryExtraFee = await _unitOfWorkPortalDb.GetRepository<BranchApplicationCountryExtraFee>().Entities
                .Include(p => p.BranchApplicationCountry)
                .ThenInclude(p => p.Branch)
                .ThenInclude(p => p.BranchTranslations)
                .Include(p => p.BranchApplicationCountry)
                .ThenInclude(p => p.Country)
                .Include(p => p.SapExtraFee)
                .Where(p => !p.IsDeleted && p.Id == request.BranchApplicationCountryExtraFeeId && !p.BranchApplicationCountry.IsDeleted && !p.BranchApplicationCountry.Branch.IsDeleted && !p.ExtraFee.IsDeleted)
                .FirstOrDefaultAsync();

            if (existingBranchApplicationCountryExtraFee == null)
                throw new NoFoundDataPortalException($"{nameof(SiteResources.Exception_NoRecordsFound).ToSiteResourcesValue()} ({nameof(SiteResources.BranchApplicationCountryExtraFee).ToSiteResourcesValue()})");

            var result = new BranchApplicationCountryExtraFeeResponseDto
            {
                Id = existingBranchApplicationCountryExtraFee.Id,
                BranchApplicationCountryId = existingBranchApplicationCountryExtraFee.BranchApplicationCountryId,
                ExtraFeeId = existingBranchApplicationCountryExtraFee.ExtraFeeId,
                BranchName = existingBranchApplicationCountryExtraFee.BranchApplicationCountry.Branch.BranchTranslations.Any(p => p.LanguageId == request.LanguageId) ?
                        existingBranchApplicationCountryExtraFee.BranchApplicationCountry.Branch.BranchTranslations.First(p => p.LanguageId == request.LanguageId).Name :
                        existingBranchApplicationCountryExtraFee.BranchApplicationCountry.Branch.BranchTranslations.First().Name,
                CountryName = existingBranchApplicationCountryExtraFee.BranchApplicationCountry.Country.Name,
                IsAutoChecked = existingBranchApplicationCountryExtraFee.IsAutoChecked,
                IsActive = existingBranchApplicationCountryExtraFee.IsActive,
                Price = existingBranchApplicationCountryExtraFee.Price,
                Tax = existingBranchApplicationCountryExtraFee.Tax,
                TaxRatio = existingBranchApplicationCountryExtraFee.TaxRatio,
                ServiceTax = existingBranchApplicationCountryExtraFee.ServiceTax,
                BasePrice = existingBranchApplicationCountryExtraFee.BasePrice,
                CurrencyId = existingBranchApplicationCountryExtraFee.CurrencyId,
                SapExtraFeeId = existingBranchApplicationCountryExtraFee.SapExtraFee?.SapExtraFeeId,
                ShowInICR = existingBranchApplicationCountryExtraFee.ShowInICR,
                ShowInSummary = existingBranchApplicationCountryExtraFee.ShowInSummary,
                IsShowInReport = existingBranchApplicationCountryExtraFee.IsShowInReport,
                IsGroupInIcr = existingBranchApplicationCountryExtraFee.IsGroupInIcr,
                IsShowInRejectionList = existingBranchApplicationCountryExtraFee.IsShowInRejectionList,
                IsShowInAllApplicationsReport = existingBranchApplicationCountryExtraFee.IsShowInAllApplicationsReport,
                IsShowInApplicationAfterRejection = existingBranchApplicationCountryExtraFee.IsShowInApplicationAfterRejection
            };

            return result;
        }

        public async Task<BranchApplicationCountryExtraFeesResponseDto> GetPaginatedBranchApplicationCountryExtraFeeAsync(BranchApplicationCountryExtraFeesRequestDto request)
        {
            var result = new BranchApplicationCountryExtraFeesResponseDto();

            result.BranchApplicationCountryExtraFees = await _unitOfWorkPortalDb.GetRepository<BranchApplicationCountryExtraFee>().Entities
                .Include(p => p.BranchApplicationCountry)
                .ThenInclude(p => p.Branch)
                .ThenInclude(p => p.BranchTranslations)
                .Include(p => p.BranchApplicationCountry)
                .ThenInclude(p => p.Country)
                .Include(p => p.ExtraFee)
                .ThenInclude(p => p.ExtraFeeTranslations)
                .Where(p => !p.IsDeleted && p.BranchApplicationCountryId == request.BranchApplicationCountryId && !p.BranchApplicationCountry.Branch.IsDeleted && !p.ExtraFee.IsDeleted)
                .Select(p => new BranchApplicationCountryExtraFeeItemResponseDto
                {
                    Id = p.Id,
                    Price = p.Price,
                    Tax = p.Tax,
                    TaxRatio = p.TaxRatio,
                    ServiceTax = p.ServiceTax,
                    BasePrice = p.BasePrice,
                    CurrencyId = p.CurrencyId,
                    IsActive = p.IsActive,
                    IsAutoChecked = p.IsAutoChecked,
                    ShowInICR = p.ShowInICR,
                    ShowInSummary = p.ShowInSummary,
                    ExtraFeeName = p.ExtraFee.ExtraFeeTranslations.Any(p => p.LanguageId == request.LanguageId) ?
                        p.ExtraFee.ExtraFeeTranslations.First(p => p.LanguageId == request.LanguageId).Name :
                        p.ExtraFee.ExtraFeeTranslations.First().Name,
                    IsShowInAllApplicationsReport = p.IsShowInAllApplicationsReport,
                    IsShowInApplicationAfterRejection = p.IsShowInApplicationAfterRejection
                })
                .ToListAsync();

            return result;
        }

        #endregion

        #region BranchApplicationCountryExtraFeeCommission

        public async Task<AddResponseDto> AddUpdateBranchApplicationCountryExtraFeeCommissionAsync(AddUpdateBranchApplicationCountryExtraFeeCommissionRequestDto request)
        {
            var branchApplicationCountryExtraFeeCommission = await _unitOfWorkPortalDb.GetRepository<BranchApplicationCountryExtraFeeCommission>().Entities
                .Include(p => p.BranchApplicationCountryExtraFee)
                .Where(p => !p.IsDeleted && p.BranchApplicationCountryExtraFeeId == request.BranchApplicationCountryExtraFeeId)
                .FirstOrDefaultAsync();

            var branchApplicationCountryExtraFeeCommissionId = 0;

            if (branchApplicationCountryExtraFeeCommission != null)
            {
                branchApplicationCountryExtraFeeCommission.Price = request.Price;
                branchApplicationCountryExtraFeeCommission.Tax = request.Tax;
                branchApplicationCountryExtraFeeCommission.TaxRatio = request.TaxRatio;
                branchApplicationCountryExtraFeeCommission.CurrencyId = request.CurrencyId;
                branchApplicationCountryExtraFeeCommission.CommissionTypeId = request.CommissionTypeId;
                branchApplicationCountryExtraFeeCommission.UpdatedAt = DateTime.Now;
                branchApplicationCountryExtraFeeCommission.UpdatedBy = request.UserAuditId;

                _unitOfWorkPortalDb.GetRepository<BranchApplicationCountryExtraFeeCommission>().Update(branchApplicationCountryExtraFeeCommission);
                await _unitOfWorkPortalDb.SaveChangesAsync();
                branchApplicationCountryExtraFeeCommissionId = branchApplicationCountryExtraFeeCommission.Id;
            }
            else
            {
                var newBranchApplicationCountryExtraFeeCommission = new BranchApplicationCountryExtraFeeCommission
                {
                    BranchApplicationCountryExtraFeeId = request.BranchApplicationCountryExtraFeeId,
                    Price = request.Price,
                    Tax = request.Tax,
                    TaxRatio = request.TaxRatio,
                    CurrencyId = request.CurrencyId,
                    CommissionTypeId = request.CommissionTypeId,
                    CreatedBy = request.UserAuditId,
                    CreatedAt = DateTime.Now
                };

                await _unitOfWorkPortalDb.GetRepository<BranchApplicationCountryExtraFeeCommission>().AddAsync(newBranchApplicationCountryExtraFeeCommission);
                await _unitOfWorkPortalDb.SaveChangesAsync();
                branchApplicationCountryExtraFeeCommissionId = newBranchApplicationCountryExtraFeeCommission.Id;
            }

            return new AddResponseDto() { Id = branchApplicationCountryExtraFeeCommissionId };
        }

        public async Task<DeleteResponseDto> DeleteBranchApplicationCountryExtraFeeCommissionAsync(DeleteRequestDto request)
        {
            var existingBranchApplicationCountryExtraFeeCommission = await _unitOfWorkPortalDb.GetRepository<BranchApplicationCountryExtraFeeCommission>().Entities
                                        .Where(p => !p.IsDeleted && p.Id == request.Id)
                                        .FirstOrDefaultAsync();

            if (existingBranchApplicationCountryExtraFeeCommission == null)
                throw new NoFoundDataPortalException($"{nameof(SiteResources.Exception_NoRecordsFound).ToSiteResourcesValue()} ({nameof(SiteResources.BranchApplicationCountryExtraFeeCommission).ToSiteResourcesValue()})");

            existingBranchApplicationCountryExtraFeeCommission.DeletedBy = request.UserAuditId;

            _unitOfWorkPortalDb.GetRepository<BranchApplicationCountryExtraFeeCommission>().MarkAsDeleted(existingBranchApplicationCountryExtraFeeCommission.Id);
            await _unitOfWorkPortalDb.SaveChangesAsync();

            return new DeleteResponseDto()
            {
                Result = true
            };
        }

        public async Task<BranchApplicationCountryExtraFeeCommissionResponseDto> GetBranchApplicationCountryExtraFeeCommissionAsync(GetBranchApplicationCountryExtraFeeCommissionRequestDto request)
        {
            var existingBranchApplicationCountryExtraFee = await _unitOfWorkPortalDb.GetRepository<BranchApplicationCountryExtraFee>().Entities
              .Include(p => p.ExtraFee)
              .ThenInclude(p => p.ExtraFeeTranslations)
              .Where(p => !p.IsDeleted && p.Id == request.BranchApplicationCountryExtraFeeId && !p.BranchApplicationCountry.Branch.IsDeleted && !p.ExtraFee.IsDeleted)
              .FirstOrDefaultAsync();

            if (existingBranchApplicationCountryExtraFee == null)
                throw new NoFoundDataPortalException($"{nameof(SiteResources.Exception_NoRecordsFound).ToSiteResourcesValue()} ({nameof(SiteResources.BranchApplicationCountryExtraFee).ToSiteResourcesValue()})");

            var result = new BranchApplicationCountryExtraFeeCommissionResponseDto();

            result.BranchApplicationCountryExtraFeeName = existingBranchApplicationCountryExtraFee.ExtraFee.ExtraFeeTranslations.Any(p => p.LanguageId == request.LanguageId) ?
                        existingBranchApplicationCountryExtraFee.ExtraFee.ExtraFeeTranslations.First(p => p.LanguageId == request.LanguageId).Name :
                        existingBranchApplicationCountryExtraFee.ExtraFee.ExtraFeeTranslations.First().Name;

            var existingBranchApplicationCountryExtraFeeCommission = await _unitOfWorkPortalDb.GetRepository<BranchApplicationCountryExtraFeeCommission>().Entities
                .Include(p => p.BranchApplicationCountryExtraFee)
                .Where(p => !p.IsDeleted && p.BranchApplicationCountryExtraFeeId == request.BranchApplicationCountryExtraFeeId)
                .FirstOrDefaultAsync();

            if (existingBranchApplicationCountryExtraFeeCommission == null)
                result.IsExisting = false;
            else
            {
                result.IsExisting = true;
                result.Id = existingBranchApplicationCountryExtraFeeCommission.Id;
                result.BranchApplicationCountryExtraFeeId = existingBranchApplicationCountryExtraFeeCommission.BranchApplicationCountryExtraFeeId;
                result.IsActive = existingBranchApplicationCountryExtraFeeCommission.IsActive;
                result.Price = existingBranchApplicationCountryExtraFeeCommission.Price;
                result.Tax = existingBranchApplicationCountryExtraFeeCommission.Tax;
                result.TaxRatio = existingBranchApplicationCountryExtraFeeCommission.TaxRatio;
                result.CurrencyId = existingBranchApplicationCountryExtraFeeCommission.CurrencyId;
                result.CommissionTypeId = existingBranchApplicationCountryExtraFeeCommission.CommissionTypeId;
            }

            return result;
        }

        #endregion

        #region BranchApplicationCountryExtraFeeCost

        public async Task<AddResponseDto> AddUpdateBranchApplicationCountryExtraFeeCostAsync(AddUpdateBranchApplicationCountryExtraFeeCostRequestDto request)
        {
            var branchApplicationCountryExtraFeeCost = await _unitOfWorkPortalDb.GetRepository<BranchApplicationCountryExtraFeeCost>().Entities
                .Include(p => p.BranchApplicationCountryExtraFee)
                .Where(p => !p.IsDeleted && p.BranchApplicationCountryExtraFeeId == request.BranchApplicationCountryExtraFeeId)
                .FirstOrDefaultAsync();

            var branchApplicationCountryExtraFeeCostId = 0;

            if (branchApplicationCountryExtraFeeCost != null)
            {
                branchApplicationCountryExtraFeeCost.Price = request.Price;
                branchApplicationCountryExtraFeeCost.Tax = request.Tax;
                branchApplicationCountryExtraFeeCost.TaxRatio = request.TaxRatio;
                branchApplicationCountryExtraFeeCost.CurrencyId = request.CurrencyId;
                branchApplicationCountryExtraFeeCost.UpdatedBy = request.UserAuditId;
                branchApplicationCountryExtraFeeCost.UpdatedAt = DateTime.Now;

                _unitOfWorkPortalDb.GetRepository<BranchApplicationCountryExtraFeeCost>().Update(branchApplicationCountryExtraFeeCost);
                await _unitOfWorkPortalDb.SaveChangesAsync();
                branchApplicationCountryExtraFeeCostId = branchApplicationCountryExtraFeeCost.Id;
            }
            else
            {
                var newBranchApplicationCountryExtraFeeCost = new BranchApplicationCountryExtraFeeCost
                {
                    BranchApplicationCountryExtraFeeId = request.BranchApplicationCountryExtraFeeId,
                    Price = request.Price,
                    Tax = request.Tax,
                    TaxRatio = request.TaxRatio,
                    CurrencyId = request.CurrencyId,
                    CreatedAt = DateTime.Now,
                    CreatedBy = request.UserAuditId
                };

                await _unitOfWorkPortalDb.GetRepository<BranchApplicationCountryExtraFeeCost>().AddAsync(newBranchApplicationCountryExtraFeeCost);
                await _unitOfWorkPortalDb.SaveChangesAsync();
                branchApplicationCountryExtraFeeCostId = newBranchApplicationCountryExtraFeeCost.Id;
            }

            return new AddResponseDto() { Id = branchApplicationCountryExtraFeeCostId };
        }

        public async Task<DeleteResponseDto> DeleteBranchApplicationCountryExtraFeeCostAsync(DeleteRequestDto request)
        {
            var existingBranchApplicationCountryExtraFeeCost = await _unitOfWorkPortalDb.GetRepository<BranchApplicationCountryExtraFeeCost>().Entities
                                        .Where(p => !p.IsDeleted && p.Id == request.Id)
                                        .FirstOrDefaultAsync();

            if (existingBranchApplicationCountryExtraFeeCost == null)
                throw new NoFoundDataPortalException($"{nameof(SiteResources.Exception_NoRecordsFound).ToSiteResourcesValue()} ({nameof(SiteResources.BranchApplicationCountryExtraFeeCost).ToSiteResourcesValue()})");

            existingBranchApplicationCountryExtraFeeCost.DeletedBy = request.UserAuditId;

            _unitOfWorkPortalDb.GetRepository<BranchApplicationCountryExtraFeeCost>().MarkAsDeleted(existingBranchApplicationCountryExtraFeeCost.Id);
            await _unitOfWorkPortalDb.SaveChangesAsync();

            return new DeleteResponseDto()
            {
                Result = true
            };
        }

        public async Task<BranchApplicationCountryExtraFeeCostResponseDto> GetBranchApplicationCountryExtraFeeCostAsync(GetBranchApplicationCountryExtraFeeCostRequestDto request)
        {
            var existingBranchApplicationCountryExtraFee = await _unitOfWorkPortalDb.GetRepository<BranchApplicationCountryExtraFee>().Entities
              .Include(p => p.ExtraFee)
              .ThenInclude(p => p.ExtraFeeTranslations)
              .Where(p => !p.IsDeleted && p.Id == request.BranchApplicationCountryExtraFeeId && !p.BranchApplicationCountry.Branch.IsDeleted && !p.ExtraFee.IsDeleted)
              .FirstOrDefaultAsync();

            if (existingBranchApplicationCountryExtraFee == null)
                throw new NoFoundDataPortalException($"{nameof(SiteResources.Exception_NoRecordsFound).ToSiteResourcesValue()} ({nameof(SiteResources.BranchApplicationCountryExtraFee).ToSiteResourcesValue()})");

            var result = new BranchApplicationCountryExtraFeeCostResponseDto();

            result.BranchApplicationCountryExtraFeeName = existingBranchApplicationCountryExtraFee.ExtraFee.ExtraFeeTranslations.Any(p => p.LanguageId == request.LanguageId) ?
                        existingBranchApplicationCountryExtraFee.ExtraFee.ExtraFeeTranslations.First(p => p.LanguageId == request.LanguageId).Name :
                        existingBranchApplicationCountryExtraFee.ExtraFee.ExtraFeeTranslations.First().Name;

            var existingBranchApplicationCountryExtraFeeCost = await _unitOfWorkPortalDb.GetRepository<BranchApplicationCountryExtraFeeCost>().Entities
                .Include(p => p.BranchApplicationCountryExtraFee)
                .Where(p => !p.IsDeleted && p.BranchApplicationCountryExtraFeeId == request.BranchApplicationCountryExtraFeeId)
                .FirstOrDefaultAsync();

            if (existingBranchApplicationCountryExtraFeeCost == null)
                result.IsExisting = false;
            else
            {
                result.IsExisting = true;
                result.Id = existingBranchApplicationCountryExtraFeeCost.Id;
                result.BranchApplicationCountryExtraFeeId = existingBranchApplicationCountryExtraFeeCost.BranchApplicationCountryExtraFeeId;
                result.IsActive = existingBranchApplicationCountryExtraFeeCost.IsActive;
                result.Price = existingBranchApplicationCountryExtraFeeCost.Price;
                result.Tax = existingBranchApplicationCountryExtraFeeCost.Tax;
                result.TaxRatio = existingBranchApplicationCountryExtraFeeCost.TaxRatio;
                result.CurrencyId = existingBranchApplicationCountryExtraFeeCost.CurrencyId;
            }

            return result;
        }

        #endregion

        #region ApplicationFormElement

        public async Task<UpdateResponseDto> UpdateApplicationFormElementsAsync(UpdateApplicationFormElementsRequestDto request)
        {
            var existingBranchApplicationCountry = await _unitOfWorkPortalDb.GetRepository<BranchApplicationCountry>().Entities
                                   .Include(i => i.ApplicationInformationNote)
                                   .Where(p => p.Id == request.BranchApplicationCountryId && !p.IsDeleted)
                                   .FirstOrDefaultAsync();

            if (existingBranchApplicationCountry == null)
                throw new NoFoundDataPortalException($"{nameof(SiteResources.Exception_NoRecordsFound).ToSiteResourcesValue()} ({nameof(SiteResources.BranchApplicationCountry).ToSiteResourcesValue()})");

            if (existingBranchApplicationCountry.ApplicationInformationNote == null)
            {
                var newApplicationInformationNote = new ApplicationInformationNote
                {
                    BranchApplicationCountryId = existingBranchApplicationCountry.Id,
                    Note = request.InformationNotes
                };

                await _unitOfWorkPortalDb.GetRepository<ApplicationInformationNote>().AddAsync(newApplicationInformationNote);
            }
            else
            {
                var existingApplicationInformationNote = existingBranchApplicationCountry.ApplicationInformationNote;
                existingApplicationInformationNote.Note = request.InformationNotes;
                _unitOfWorkPortalDb.GetRepository<ApplicationInformationNote>().Update(existingApplicationInformationNote);
            }

            var applicationFormElements = await _unitOfWorkPortalDb.GetRepository<ApplicationFormElement>().Entities
                                    .Where(p => p.BranchApplicationCountryId == request.BranchApplicationCountryId && !p.IsDeleted).ToListAsync();

            var enumListApplicationFormElement = EnumHelper.GetEnumAsDictionary(typeof(Contracts.Entities.Enums.ApplicationFormElement));

            var newApplicationFormElements = new List<ApplicationFormElement>();
            var existingApplicationFormElements = new List<ApplicationFormElement>();

            foreach (var item in request.ApplicationFormElements.Where(p => enumListApplicationFormElement.ContainsKey(p.FormElementId)))
            {
                if (applicationFormElements.Any(p => p.FormElementId == item.FormElementId))
                {
                    var existingApplicationFormElement = applicationFormElements.First(p => p.FormElementId == item.FormElementId);
                    existingApplicationFormElement.BranchApplicationCountryId = request.BranchApplicationCountryId;
                    existingApplicationFormElement.FormElementId = item.FormElementId;
                    existingApplicationFormElement.IsRequired = item.IsRequired;
                    existingApplicationFormElement.IsActive = item.IsActive;

                    existingApplicationFormElements.Add(existingApplicationFormElement);
                }
                else
                {
                    var newApplicationFormElement = new ApplicationFormElement
                    {
                        BranchApplicationCountryId = request.BranchApplicationCountryId,
                        FormElementId = item.FormElementId,
                        IsRequired = item.IsRequired,
                        IsActive = true
                    };

                    newApplicationFormElements.Add(newApplicationFormElement);
                }
            }

            if (existingApplicationFormElements.Any())
                _unitOfWorkPortalDb.GetRepository<ApplicationFormElement>().UpdateRange(existingApplicationFormElements);

            if (newApplicationFormElements.Any())
                await _unitOfWorkPortalDb.GetRepository<ApplicationFormElement>().AddRangeAsync(newApplicationFormElements);

            await _unitOfWorkPortalDb.SaveChangesAsync();

            return new UpdateResponseDto() { Result = true };
        }

        public async Task<ApplicationFormElementsResponseDto> GetApplicationFormElementsAsync(ApplicationFormElementsRequestDto request)
        {
            var existingBranchApplicationCountry = await _unitOfWorkPortalDb.GetRepository<BranchApplicationCountry>().Entities
                                .Include(p => p.Branch)
                                .ThenInclude(p => p.BranchTranslations)
                                .Include(p => p.Country)
                                .Include(p => p.ApplicationInformationNote)
                                .Where(p => p.IsActive && !p.IsDeleted && p.Id == request.BranchApplicationCountryId)
                                .FirstOrDefaultAsync();

            if (existingBranchApplicationCountry == null)
                throw new NoFoundDataPortalException($"{nameof(SiteResources.Exception_NoRecordsFound).ToSiteResourcesValue()} ({nameof(SiteResources.BranchApplicationCountry).ToSiteResourcesValue()})");

            var applicationFormElements = await _unitOfWorkPortalDb.GetRepository<ApplicationFormElement>().Entities
                    .Where(p => !p.IsDeleted && p.BranchApplicationCountryId == request.BranchApplicationCountryId)
                    .ToListAsync();

            var result = new ApplicationFormElementsResponseDto
            {
                BranchApplicationCountryId = existingBranchApplicationCountry.Id
            };

            if (existingBranchApplicationCountry.Branch != null && existingBranchApplicationCountry.Branch.BranchTranslations.Any(p => !p.IsDeleted))
            {
                var branchTranslations = existingBranchApplicationCountry.Branch.BranchTranslations.Where(p => !p.IsDeleted);

                if (branchTranslations.Any(p => p.LanguageId == request.LanguageId))
                    result.BranchName = branchTranslations.First(p => p.LanguageId == request.LanguageId).Name;
                else
                    result.BranchName = branchTranslations.First().Name;
            }

            if (existingBranchApplicationCountry.Country != null)
                result.CountryName = existingBranchApplicationCountry.Country.Name;

            if (existingBranchApplicationCountry.ApplicationInformationNote != null)
                result.InformationNotes = existingBranchApplicationCountry.ApplicationInformationNote.Note;

            result.ApplicationFormElements = applicationFormElements.Select(p => new ApplicationFormElementsResponseDto.ApplicationFormElement
            {
                FormElementId = p.FormElementId,
                IsRequired = p.IsRequired,
                IsActive = p.IsActive
            }).ToList();

            return result;
        }

        #endregion

        #region BranchApplicationStatus

        public async Task<UpdateResponseDto> UpdateBranchApplicationStatusesAsync(UpdateBranchApplicationStatusesRequestDto request)
        {
            var branchApplicationStatuses = await _unitOfWorkPortalDb.GetRepository<BranchApplicationStatus>().Entities
                                            .Where(p => !p.IsDeleted && p.BranchId == request.BranchId)
                                            .ToListAsync();

            foreach (var item in request.BranchApplicationStatuses)
            {
                var existingBranchApplicationStatus = branchApplicationStatuses.Find(p => p.ApplicationStatusId == item.ApplicationStatusId);

                if (existingBranchApplicationStatus == null)
                {
                    var newBranchApplicationStatus = new BranchApplicationStatus
                    {
                        IsActive = item.IsActive,
                        BranchId = request.BranchId,
                        ApplicationStatusId = item.ApplicationStatusId,
                        IsApplicationUpdateAllowed = item.IsApplicationUpdateAllowed,
                        IsNewApplicationWithSamePassportNumberBlocked = item.IsNewApplicationWithSamePassportNumberBlocked,
                        IsApplicationStatusHidden = item.IsApplicationStatusHidden,
                        IsRestrictChangeContactInformation = item.IsRestrictChangeContactInformation,
                        IsEsimBeSent = item.IsEsimBeSent,
                        CreatedBy = request.UserAuditId,
                        CreatedAt = DateTime.Now
                    };

                    await _unitOfWorkPortalDb.GetRepository<BranchApplicationStatus>().AddAsync(newBranchApplicationStatus);
                }
                else
                {
                    existingBranchApplicationStatus.IsActive = item.IsActive;
                    existingBranchApplicationStatus.IsApplicationUpdateAllowed = item.IsApplicationUpdateAllowed;
                    existingBranchApplicationStatus.IsNewApplicationWithSamePassportNumberBlocked = item.IsNewApplicationWithSamePassportNumberBlocked;
                    existingBranchApplicationStatus.IsApplicationStatusHidden = item.IsApplicationStatusHidden;
                    existingBranchApplicationStatus.IsRestrictChangeContactInformation = item.IsRestrictChangeContactInformation;
                    existingBranchApplicationStatus.IsEsimBeSent = item.IsEsimBeSent;
                    existingBranchApplicationStatus.UpdatedAt = DateTime.Now;
                    existingBranchApplicationStatus.UpdatedBy = request.UserAuditId;

                    _unitOfWorkPortalDb.GetRepository<BranchApplicationStatus>().Update(existingBranchApplicationStatus);
                }
            }

            await _unitOfWorkPortalDb.SaveChangesAsync();

            return new UpdateResponseDto
            {
                Result = true
            };
        }

        #endregion

        #region Slot

        public async Task<Pagination<PaginatedSlotResponseDto>> GetPaginatedSlotAsync(PaginatedSlotRequestDto request)
        {
            var paginationResult = new Pagination<PaginatedSlotResponseDto> { Page = request.Pagination.Page, PageSize = request.Pagination.PageSize };

            var querySlot = _unitOfWorkPortalDb.GetRepository<Slot>().Entities
                                            .Include(i => i.Agency)
                                            .Include(i => i.BranchApplicationCountry)
                                                .ThenInclude(i => i.Branch)
                                                    .ThenInclude(i => i.BranchTranslations)
                                            .Include(i => i.BranchApplicationCountry)
                                                .ThenInclude(i => i.Country)
                                            .Where(p => !p.IsDeleted);

            if (request.BranchApplicationCountryIds != null && request.BranchApplicationCountryIds.Any())
                querySlot = querySlot.Where(p => request.BranchApplicationCountryIds.Contains(p.BranchApplicationCountryId));

            if (request.SlotTypeId.HasValue)
                querySlot = querySlot.Where(p => p.SlotTypeId == request.SlotTypeId);

            if (request.Date > DateTimeOffset.MinValue && request.EndDate > DateTimeOffset.MinValue)
                querySlot = querySlot.Where(p => p.SlotTime > request.Date && p.SlotTime < request.EndDate.AddDays(1));

            paginationResult.TotalItemCount = querySlot.Count();

            var statusList = await querySlot
                    .OrderByDescending(o => o.SlotTime)
                    .Skip((request.Pagination.Page - 1) * request.Pagination.PageSize)
                    .Take(request.Pagination.PageSize)
                    .ToListAsync();

            var slots = new PaginatedSlotResponseDto() { Slots = new List<SlotDto>() };

            foreach (var item in statusList)
            {
                var slot = new SlotDto
                {
                    Id = item.Id,
                    SlotTime = item.SlotTime,
                    SlotTypeId = item.SlotTypeId,
                    Quota = item.Quota,
                    BranchName = item.BranchApplicationCountry.Branch.BranchTranslations.FirstOrDefault(p => p.LanguageId == request.LanguageId)?.Name,
                    CountryName = item.BranchApplicationCountry.Country.Name,
                    AgencyName = item.Agency?.Name
                };

                slots.Slots.Add(slot);
            }

            paginationResult.Items.Add(slots);
            return paginationResult;
        }

        public async Task<SlotsResponseDto> GetSlotsAsync(GetSlotRequestDto request)
        {
            var querySlot = await _unitOfWorkPortalDb.GetRepository<Slot>().Entities
                                            .Include(i => i.Agency)
                                            .Include(i => i.BranchApplicationCountry)
                                                .ThenInclude(i => i.Branch)
                                                    .ThenInclude(i => i.BranchTranslations)
                                            .Include(i => i.BranchApplicationCountry)
                                                .ThenInclude(i => i.Country)
                                            .Where(p => !p.IsDeleted && p.IsActive && p.BranchApplicationCountryId == request.BranchApplicationCountryId
                                                        && p.SlotTime >= request.Date.Date && p.Quota > -1)
                                            .ToListAsync();

            var updateSlotQuery = await _unitOfWorkPortalDb.GetRepository<Slot>().Entities
                                             .Include(i => i.Agency)
                                             .Include(i => i.BranchApplicationCountry)
                                                 .ThenInclude(i => i.Branch)
                                                     .ThenInclude(i => i.BranchTranslations)
                                             .Include(i => i.BranchApplicationCountry)
                                                 .ThenInclude(i => i.Country)
                                             .Where(p => !p.IsDeleted && p.IsActive && p.BranchApplicationCountryId == request.BranchApplicationCountryId
                                                         && p.SlotTime >= request.Date.AddDays(-2).Date && p.Quota > -1)
                                             .ToListAsync();

            if (request.SlotId.HasValue)
                updateSlotQuery = updateSlotQuery.Where(p => p.Id == request.SlotId.Value).ToList();

            if (request.AgencyId.HasValue)
                querySlot = querySlot.Where(p => p.AgencyId == request.AgencyId.Value && p.SlotTypeId == (int)SlotType.Agency).ToList();
            else
                querySlot = querySlot.Where(p => p.SlotTypeId == request.SlotTypeId).ToList();

            if (querySlot.Count == 0)
                throw new NoFoundDataPortalException($"{nameof(SiteResources.Exception_NoAvailableSlotFound).ToSiteResourcesValue(request.LanguageId)} ({nameof(SiteResources.Date).ToSiteResourcesValue(request.LanguageId)}: {request.Date.ToString("dd/MM/yyyy")})");

            DateTime utcTime1 = new DateTime(request.Date.Year, request.Date.Month, request.Date.Day, 0, 0, 0);
            utcTime1 = DateTime.SpecifyKind(utcTime1, DateTimeKind.Local);
            DateTimeOffset utcTime2 = utcTime1;
            var AddDaysSlotTime = utcTime2.AddDays(1);

            var availableSlots = querySlot.Where(p => p.SlotTime >= request.Date.Date && p.SlotTime < AddDaysSlotTime).ToList();
            var daySumSlotQuota = availableSlots.Sum(p => p.Quota);
            if (!availableSlots.Any(p => p.Quota > 0) && !request.SlotId.HasValue)
            {
                var firstAvailableSlots = querySlot.Where(p => p.Quota > 0).OrderBy(p => p.SlotTime).ToList();
                if (firstAvailableSlots != null)
                {
                    for (int i = 0; i < firstAvailableSlots.Count; i++)
                    {
                        DateTime firstUtcTime = new DateTime(firstAvailableSlots[i].SlotTime.Year, firstAvailableSlots[i].SlotTime.Month, firstAvailableSlots[i].SlotTime.Day, 0, 0, 0);
                        firstUtcTime = DateTime.SpecifyKind(firstUtcTime, DateTimeKind.Local);
                        DateTimeOffset firstUtcTime1 = firstUtcTime;
                        var firstAddDaysSlotTime = firstUtcTime1.AddDays(1);
                        var firstAvailableSlotList = querySlot.Where(p => p.SlotTime >= firstAvailableSlots[i].SlotTime.Date && p.SlotTime < firstAddDaysSlotTime).ToList();
                        var firstAvailableDaySumSlotQuota = firstAvailableSlotList.Sum(p => p.Quota);

                        if (firstAvailableSlots[i].SlotTime > request.Date.Date && firstAvailableDaySumSlotQuota >= request.ApplicantCount)
                            return new SlotsResponseDto()
                            {
                                FirstAvailableSlot = firstAvailableSlots[i].SlotTime,
                                Slots = Enumerable.Empty<SlotDto>()
                            };
                        else if (i == firstAvailableSlots.Count - 1)
                            throw new NoFoundDataPortalException($"{nameof(SiteResources.Exception_NoAvailableSlotFound).ToSiteResourcesValue(request.LanguageId)} ({nameof(SiteResources.Date).ToSiteResourcesValue(request.LanguageId)}: {request.Date.ToString("dd/MM/yyyy")})");
                    }
                }
                else
                {
                    throw new NoFoundDataPortalException($"{nameof(SiteResources.Exception_NoAvailableSlotFound).ToSiteResourcesValue(request.LanguageId)} ({nameof(SiteResources.Date).ToSiteResourcesValue(request.LanguageId)}: {request.Date.ToString("dd/MM/yyyy")})");
                }
            }
            if (!updateSlotQuery.Any(p => p.Quota > 0) && request.SlotId.HasValue)
            {
                var firstAvailableSlot = querySlot.Where(p => p.Quota > 0).OrderBy(p => p.SlotTime).FirstOrDefault();
                if (firstAvailableSlot != null)
                {
                    var firstAvailableSlotFound = true;
                    var firstAvailableSlotDate = utcTime2;
                    if (daySumSlotQuota < request.ApplicantCount)
                    {
                        var firstAvailableSlots = querySlot.Where(p => p.Quota > 0).OrderBy(p => p.SlotTime).ToList();
                        for (int i = 0; i < firstAvailableSlots.Count; i++)
                        {
                            DateTime firstUtcTime = new DateTime(firstAvailableSlots[i].SlotTime.Year, firstAvailableSlots[i].SlotTime.Month, firstAvailableSlots[i].SlotTime.Day, 0, 0, 0);
                            firstUtcTime = DateTime.SpecifyKind(firstUtcTime, DateTimeKind.Local);
                            DateTimeOffset firstUtcTime1 = firstUtcTime;
                            var firstAddDaysSlotTime = firstUtcTime1.AddDays(1);
                            var firstAvailableSlotList = querySlot.Where(p => p.SlotTime >= firstAvailableSlots[i].SlotTime.Date && p.SlotTime < firstAddDaysSlotTime).ToList();
                            var firstAvailableDaySumSlotQuota = firstAvailableSlotList.Sum(p => p.Quota);
                            if (firstAvailableSlots[i].SlotTime > request.Date.Date && firstAvailableDaySumSlotQuota >= request.ApplicantCount)
                            {
                                firstAvailableSlotDate = firstAvailableSlots[i].SlotTime;
                                break;
                            }
                            else if (i == firstAvailableSlots.Count - 1)
                                firstAvailableSlotFound = false;
                        }
                    }
                    if (firstAvailableSlot.SlotTime.Date >= request.Date.Date && firstAvailableSlotFound)
                        return new SlotsResponseDto()
                        {
                            FirstAvailableSlot = firstAvailableSlotDate,
                            Slots = availableSlots.Select(p => new SlotDto
                            {
                                Id = p.Id,
                                SlotTime = p.SlotTime,
                                SlotTimeText = p.SlotTime.DateTime.ToString("HH:mm"),
                                Quota = p.Quota,
                                BranchName = p.BranchApplicationCountry.Branch.BranchTranslations.FirstOrDefault(p => p.LanguageId == request.LanguageId)?.Name,
                                CountryName = p.BranchApplicationCountry.Country.Name,
                                AgencyName = p.Agency?.Name
                            }).AsEnumerable()
                        };
                    else
                    {
                        return new SlotsResponseDto()
                        {
                            FirstAvailableSlot = DateTimeOffset.MinValue,
                            Slots = availableSlots.Select(p => new SlotDto
                            {
                                Id = p.Id,
                                SlotTime = p.SlotTime,
                                Quota = p.Quota,
                                BranchName = p.BranchApplicationCountry.Branch.BranchTranslations.FirstOrDefault(p => p.LanguageId == request.LanguageId)?.Name,
                                CountryName = p.BranchApplicationCountry.Country.Name,
                                AgencyName = p.Agency?.Name
                            }).AsEnumerable()
                        };
                    }
                }
                else
                {
                    return new SlotsResponseDto()
                    {
                        FirstAvailableSlot = DateTimeOffset.MinValue,
                        Slots = availableSlots.Select(p => new SlotDto
                        {
                            Id = p.Id,
                            SlotTime = p.SlotTime,
                            Quota = p.Quota,
                            BranchName = p.BranchApplicationCountry.Branch.BranchTranslations.FirstOrDefault(p => p.LanguageId == request.LanguageId)?.Name,
                            CountryName = p.BranchApplicationCountry.Country.Name,
                            AgencyName = p.Agency?.Name
                        }).AsEnumerable()
                    };
                }
            }
            else if (request.ApplicantCount.HasValue && (daySumSlotQuota < request.ApplicantCount))
            {
                if (!request.SlotId.HasValue)
                {
                    var firstAvailableSlots = querySlot.Where(p => p.Quota > 0).OrderBy(p => p.SlotTime).ToList();
                    for (int i = 0; i < firstAvailableSlots.Count; i++)
                    {
                        DateTime firstUtcTime = new DateTime(firstAvailableSlots[i].SlotTime.Year, firstAvailableSlots[i].SlotTime.Month, firstAvailableSlots[i].SlotTime.Day, 0, 0, 0);
                        firstUtcTime = DateTime.SpecifyKind(firstUtcTime, DateTimeKind.Local);
                        DateTimeOffset firstUtcTime1 = firstUtcTime;
                        var firstAddDaysSlotTime = firstUtcTime1.AddDays(1);
                        var firstAvailableSlotList = querySlot.Where(p => p.SlotTime >= firstAvailableSlots[i].SlotTime.Date && p.SlotTime < firstAddDaysSlotTime).ToList();
                        var firstAvailableDaySumSlotQuota = firstAvailableSlotList.Sum(p => p.Quota);

                        if (firstAvailableSlots[i].SlotTime > request.Date.Date && firstAvailableDaySumSlotQuota >= request.ApplicantCount)
                            return new SlotsResponseDto()
                            {
                                FirstAvailableSlot = firstAvailableSlots[i].SlotTime,
                                Slots = Enumerable.Empty<SlotDto>()
                            };
                        else if (i == firstAvailableSlots.Count - 1)
                            throw new NoFoundDataPortalException($"{nameof(SiteResources.Exception_NoAvailableSlotFound).ToSiteResourcesValue(request.LanguageId)} ({nameof(SiteResources.Date).ToSiteResourcesValue(request.LanguageId)}: {request.Date.ToString("dd/MM/yyyy")})");
                    }
                }
                else
                {
                    var firstAvailableSlots = querySlot.Where(p => p.Quota > 0).OrderBy(p => p.SlotTime).ToList();
                    for (int i = 0; i < firstAvailableSlots.Count; i++)
                    {
                        DateTime firstUtcTime = new DateTime(firstAvailableSlots[i].SlotTime.Year, firstAvailableSlots[i].SlotTime.Month, firstAvailableSlots[i].SlotTime.Day, 0, 0, 0);
                        firstUtcTime = DateTime.SpecifyKind(firstUtcTime, DateTimeKind.Local);
                        DateTimeOffset firstUtcTime1 = firstUtcTime;
                        var firstAddDaysSlotTime = firstUtcTime1.AddDays(1);
                        var firstAvailableSlotList = querySlot.Where(p => p.SlotTime >= firstAvailableSlots[i].SlotTime.Date && p.SlotTime < firstAddDaysSlotTime).ToList();
                        var firstAvailableDaySumSlotQuota = firstAvailableSlotList.Sum(p => p.Quota);

                        if (firstAvailableSlots[i].SlotTime > request.Date.Date && firstAvailableDaySumSlotQuota >= request.ApplicantCount)
                            return new SlotsResponseDto()
                            {
                                FirstAvailableSlot = firstAvailableSlots[i].SlotTime,
                                Slots = availableSlots.Select(p => new SlotDto
                                {
                                    Id = p.Id,
                                    SlotTime = p.SlotTime,
                                    SlotTimeText = p.SlotTime.DateTime.ToString("HH:mm"),
                                    Quota = p.Quota,
                                    BranchName = p.BranchApplicationCountry.Branch.BranchTranslations.FirstOrDefault(p => p.LanguageId == request.LanguageId)?.Name,
                                    CountryName = p.BranchApplicationCountry.Country.Name,
                                    AgencyName = p.Agency?.Name
                                }).AsEnumerable()
                            };
                        else if (i == firstAvailableSlots.Count - 1)
                            throw new NoFoundDataPortalException($"{nameof(SiteResources.Exception_NoAvailableSlotFound).ToSiteResourcesValue(request.LanguageId)} ({nameof(SiteResources.Date).ToSiteResourcesValue(request.LanguageId)}: {request.Date.ToString("dd/MM/yyyy")})");
                    }
                }
            }
            return new SlotsResponseDto()
            {
                Slots = availableSlots.Select(p => new SlotDto
                {
                    Id = p.Id,
                    SlotTime = p.SlotTime,
                    SlotTimeText = p.SlotTime.DateTime.ToString("HH:mm"),
                    Quota = p.Quota,
                    BranchName = p.BranchApplicationCountry.Branch.BranchTranslations.FirstOrDefault(p => p.LanguageId == request.LanguageId)?.Name,
                    CountryName = p.BranchApplicationCountry.Country.Name,
                    AgencyName = p.Agency?.Name
                }).AsEnumerable()
            };

        }

        public async Task<UpdateResponseDto> UpdateSlotAsync(UpdateSlotRequestDto request)
        {
            foreach (var itemSlot in request.SlotQuotas)
            {
                var entity = await _unitOfWorkPortalDb.GetRepository<Slot>().Entities
                            .Where(p => !p.IsDeleted && p.Id == itemSlot.Id)
                            .FirstOrDefaultAsync();

                if (entity == null || entity?.Quota == itemSlot.Quota)
                    continue;

                entity.Quota = itemSlot.Quota;
                entity.UpdatedBy = request.UserAuditId;
                entity.UpdatedAt = DateTime.Now;

                _unitOfWorkPortalDb.GetRepository<Slot>().Update(entity);
            }

            await _unitOfWorkPortalDb.SaveChangesAsync().ConfigureAwait(false);

            return new UpdateResponseDto()
            {
                Result = true
            };
        }

        public async Task<AddResponseDto> AddSlotAsync(AddSlotRequestDto request)
        {
            if (request.SlotTimeQuotas.Count == 0)
                throw new NoFoundDataPortalException($"{nameof(SiteResources.Exception_CreateSlot).ToSiteResourcesValue(request.LanguageId)} ({nameof(SiteResources.Slot).ToSiteResourcesValue(request.LanguageId)})");


            var entity = await _unitOfWorkPortalDb.GetRepository<Slot>().Entities
                                .Where(p => !p.IsDeleted
                                            && p.BranchApplicationCountryId == request.BranchApplicationCountryId)
                                .ToListAsync();

            var insertSlotDates = new List<Slot>();
            DateTimeOffset slotDate = request.StartDate;


            while (slotDate <= request.EndDate)
            {
                foreach (var itemSlotTime in request.SlotTimeQuotas)
                {
                    if (request.DisableDates.Count > 0 && request.DisableDates.Any(q => q.Date == slotDate.Date))
                    {
                        break;
                    }
                    else
                    {
                        // TimeOnly'den TimeSpan oluştur
                        TimeSpan timeSpan = TimeOnly.Parse(itemSlotTime.StartTime).ToTimeSpan();

                        // DateTimeOffset'e TimeSpan ekle
                        DateTimeOffset updatedDateTimeOffset = slotDate.Date.Add(timeSpan);

                        var slot = new Slot()
                        {
                            SlotTypeId = request.SlotTypeId,
                            BranchApplicationCountryId = request.BranchApplicationCountryId,
                            AgencyId = request.AgencyId,
                            CreatedAt = DateTime.Now,
                            CreatedBy = request.UserAuditId,
                            SlotTime = updatedDateTimeOffset,
                            Quota = itemSlotTime.SlotQuotas
                        };
                        insertSlotDates.Add(slot);
                    }
                }
                slotDate = slotDate.AddDays(1);
            }

            await _unitOfWorkPortalDb.GetRepository<Slot>().AddRangeAsync(insertSlotDates).ConfigureAwait(false);

            await _unitOfWorkPortalDb.SaveChangesAsync().ConfigureAwait(false);

            return new AddResponseDto();
        }

        public async Task<UpdateResponseDto> DeleteSlotAsync(DeleteSlotRequestDto request)
        {
            var querySlot = await _unitOfWorkPortalDb.GetRepository<Slot>().Entities
                                            .Where(p => !p.IsDeleted
                                                        && p.BranchApplicationCountryId == request.BranchApplicationCountryId
                                                        && p.SlotTime >= request.StartDate.Date && p.SlotTime < request.EndDate.AddDays(1).Date
                                                        && p.SlotTypeId == request.SlotTypeId)
                                            .ToListAsync();

            if (querySlot.Count == 0)
                throw new NoFoundDataPortalException($"{nameof(SiteResources.Exception_NoRecordsFound).ToSiteResourcesValue(request.LanguageId)} ({nameof(SiteResources.Slot).ToSiteResourcesValue(request.LanguageId)})");

            querySlot.Select(c => { c.IsDeleted = true; return c; }).ToList();

            _unitOfWorkPortalDb.GetRepository<Slot>().UpdateRange(querySlot);
            await _unitOfWorkPortalDb.SaveChangesAsync();

            return new UpdateResponseDto()
            {
                Result = true
            };
        }

        public async Task<SlotDto> GetSlotAsync(int slotId)
        {
            var querySlot = await _unitOfWorkPortalDb.GetRepository<Slot>().Entities
                                    .Where(p => p.Id == slotId).FirstOrDefaultAsync();

            if (querySlot == null)
                throw new NoFoundDataPortalException($"{nameof(SiteResources.Exception_NoRecordsFound).ToSiteResourcesValue()} ({nameof(SiteResources.Slot).ToSiteResourcesValue()})");

            var result = new SlotDto
            {
                Id = querySlot.Id,
                SlotTime = querySlot.SlotTime,
                Quota = querySlot.Quota
            };

            return result;
        }

        public async Task<SlotReportResponseDto> GenerateSlotReportAsync(GetSlotReportRequestDto reportRequest)
        {
            #region queries

            var branchApplicationCountryIds = reportRequest.BranchApplicationCountryIds.ToHashSet();

            var branches = await _unitOfWorkPortalDb.GetRepository<BranchApplicationCountry>().Entities
                .AsNoTracking()
                .Include(i => i.Branch)
                    .ThenInclude(i => i.BranchTranslations)
                .Where(b => b.IsActive && !b.IsDeleted && branchApplicationCountryIds.Contains(b.Id))
                .ToListAsync();

            var slots = await _unitOfWorkPortalDb.GetRepository<Slot>().Entities
                .AsNoTracking()
                .Where(slot => !slot.IsDeleted && slot.IsActive &&
                               branchApplicationCountryIds.Contains(slot.BranchApplicationCountryId) &&
                               slot.SlotTime.Date >= reportRequest.StartDate.Date &&
                               slot.SlotTime.Date < reportRequest.EndDate.AddDays(1).Date &&
                               slot.Quota > -1)
                .Select(slot => new
                {
                    slot.Id,
                    slot.SlotTime,
                    slot.Quota,
                    slot.SlotTypeId,
                    slot.PreApplicationsSlot,
                    BranchApplicationCountryId = slot.BranchApplicationCountryId,
                    BranchShiftHolidays = slot.BranchApplicationCountry.BranchShiftHolidays
                        .Where(h => h.AppointmentPeriod > 0)
                        .Select(h => h.AppointmentPeriod)
                        .ToList()
                })
                .ToListAsync();

            var basePreApplicationQuery = _unitOfWorkPortalDb.GetRepository<PreApplication>().Entities
                .Include(i => i.PreApplicationApplicants)
                .Where(preApplication => preApplication.IsActive
                                         && !preApplication.IsDeleted
                                         && reportRequest.BranchApplicationCountryIds.Contains(preApplication.BranchApplicationCountryId));


            var preApplications = await basePreApplicationQuery
                .Where(w => slots.Select(slot => slot.Id).Contains(w.SlotId))
                .ToListAsync();

            var walkinAppointments = await basePreApplicationQuery
                .Where(walkin => walkin.SlotId == 0
                                 && walkin.CreatedAt >= reportRequest.StartDate
                                 && walkin.CreatedAt < reportRequest.EndDate.AddDays(1))
                .Select(s => new
                {
                    SlotTime = s.CreatedAt,
                    s.BranchApplicationCountryId
                })
                .ToListAsync();

            #endregion

            var result = new List<SlotReportResponseDto.Branch>();

            foreach (var branchApplicationCountryId in branchApplicationCountryIds)
            {
                var branchApplicationCountry = branches.FirstOrDefault(b => b.Id == branchApplicationCountryId);
                var branchName = branchApplicationCountry?.Branch.BranchTranslations.FirstOrDefault(t => t.LanguageId == reportRequest.LanguageId)?.Name ??
                                 branchApplicationCountry?.Branch.BranchTranslations.FirstOrDefault()?.Name;

                var branchSlots = slots.Where(s => s.BranchApplicationCountryId == branchApplicationCountryId).ToList();

                var branchWalkinAppointments = walkinAppointments.Where(s => s.BranchApplicationCountryId == branchApplicationCountryId).ToList();

                var slotApplicantCounts = preApplications
                    .Where(w => w.BranchApplicationCountryId == branchApplicationCountryId)
                    .GroupBy(p => new { p.SlotId, p.SlotTypeId })
                    .ToDictionary(
                        g => g.Key.SlotId,
                        g => new
                        {
                            TotalCount = g.Sum(p => p.PreApplicationApplicants.Count),
                            SlotTypeId = g.Key.SlotTypeId
                        }
                    );

                var appointmentPeriod = branchSlots
                    .SelectMany(s => s.BranchShiftHolidays)
                    .DefaultIfEmpty(30)
                    .FirstOrDefault();

                var walkinAppointmentTime = branchWalkinAppointments
                    .Select(s => branchSlots
                        .OrderBy(b => Math.Abs((b.SlotTime - s.SlotTime.Value).TotalMinutes)).First().SlotTime)
                    .ToList();

                var slotsResult = branchSlots
                    .GroupBy(s => s.SlotTime)
                    .Select(g => new SlotReportResponseDto.SlotResponse
                    {
                        SlotDate = g.Key.Date,
                        SlotTime = g.Key,
                        AppointmentPeriod = appointmentPeriod,
                        Quota = g.Sum(s => s.Quota + (slotApplicantCounts.TryGetValue(s.Id, out var count) ? count.TotalCount : 0)),
                        Individual = g.Where(w => w.SlotTypeId == (int)SlotType.Individual).Sum(s => s.Quota + (slotApplicantCounts.TryGetValue(s.Id, out var count) && count.SlotTypeId == (int)SlotType.Individual ? count.TotalCount : 0)),
                        Agent = g.Where(w => w.SlotTypeId == (int)SlotType.Agency)
                            .Sum(s => s.Quota + (slotApplicantCounts.TryGetValue(s.Id, out var count) && count.SlotTypeId == (int)SlotType.Agency ? count.TotalCount : 0)),
                        CallCenter = g.Where(w => w.SlotTypeId == (int)SlotType.CallCenter)
                            .Sum(s => s.Quota + (slotApplicantCounts.TryGetValue(s.Id, out var count) && count.SlotTypeId == (int)SlotType.CallCenter ? count.TotalCount : 0)),
                        Mobile = g.Where(w => w.SlotTypeId == (int)SlotType.Mobile)
                            .Sum(s => s.Quota + (slotApplicantCounts.TryGetValue(s.Id, out var count) && count.SlotTypeId == (int)SlotType.Mobile ? count.TotalCount : 0)),
                        WalkinAppointment = walkinAppointmentTime.Count(w => w == g.Key),
                        RemainingAppointment = g.Sum(s => s.Quota)
                    })
                    .ToList();

                result.Add(new SlotReportResponseDto.Branch
                {
                    BranchName = branchName,
                    Slots = slotsResult
                });
            }

            return new SlotReportResponseDto { Branches = result };
        }

        #endregion

        #region UserShortcut

        public async Task<UserShortcutResponseDto> UpdateUserShortcutsAsync(UpdateUserShortcutRequestDto request)
        {
            var entityUser = await _unitOfWorkPortalDb.GetRepository<User>().Entities
                                   .Where(p => p.Id == request.UserId && !p.IsDeleted && p.IsActive)
                                   .FirstOrDefaultAsync();

            if (entityUser == null)
                throw new NoFoundDataPortalException($"{nameof(SiteResources.Exception_NoRecordsFound).ToSiteResourcesValue(request.LanguageId)} ({nameof(SiteResources.User).ToSiteResourcesValue(request.LanguageId)})");

            var entityUserShortcuts = _unitOfWorkPortalDb.GetRepository<UserShortcut>().Entities
                .Where(p => !p.IsDeleted && p.UserId == request.UserId)
                .ToList();

            var entityUserShortcutDeactivated = entityUserShortcuts.Where(p => !request.SelectedActions.Any(x => x.ActionId == p.ActionId) && p.IsActive).AsEnumerable();

            if (entityUserShortcutDeactivated.Count() > 0)
            {
                entityUserShortcutDeactivated.ToList().ForEach(p => p.IsActive = false);

                _unitOfWorkPortalDb.GetRepository<UserShortcut>().UpdateRange(entityUserShortcutDeactivated);

                await _unitOfWorkPortalDb.SaveChangesAsync();
            }

            var entityUserShortcutUpdate = entityUserShortcuts.Where(p => request.SelectedActions.Any(x => x.ActionId == p.ActionId) && !p.IsActive).AsEnumerable();

            if (entityUserShortcutUpdate.Count() > 0)
            {
                entityUserShortcutUpdate.Where(p => !p.IsActive).ToList().ForEach(p => p.IsActive = true);

                _unitOfWorkPortalDb.GetRepository<UserShortcut>().UpdateRange(entityUserShortcutUpdate);

                await _unitOfWorkPortalDb.SaveChangesAsync();
            }

            var newUserShortcuts = request.SelectedActions
                                        .Where(p => !entityUserShortcuts.Exists(m => m.ActionId == p.ActionId))
                                        .Select(p => new UserShortcut
                                        {
                                            UserId = request.UserId,
                                            ActionId = p.ActionId
                                        }).AsEnumerable();

            if (newUserShortcuts.Count() > 0)
            {
                await _unitOfWorkPortalDb.GetRepository<UserShortcut>().AddRangeAsync(newUserShortcuts);
                await _unitOfWorkPortalDb.SaveChangesAsync();
            }

            var updatedUserShortcuts = _unitOfWorkPortalDb.GetRepository<UserShortcut>().Entities
                .Include(p => p.Action)
                .ThenInclude(p => p.ActionTranslations)
                .Where(p => !p.IsDeleted && p.IsActive && p.UserId == request.UserId)
                .ToList();

            return new UserShortcutResponseDto()
            {
                ActionList = updatedUserShortcuts.Select(p => new UserShortcutResponseDto.ActionResponseDto()
                {
                    Id = p.ActionId,
                    Controller = p.Action.Controller,
                    Area = p.Action.Area,
                    Method = p.Action.Method,
                    Names = p.Action.ActionTranslations.Select(q => new UserShortcutResponseDto.ActionResponseDto.ActionTranslationsResponseDto()
                    {
                        LanguageId = q.LanguageId,
                        Name = q.Name
                    }).ToList()
                }).ToList()
            };
        }

        #endregion

        #region CustomerCard

        public async Task<AddResponseDto> AddCustomerCardAsync(AddCustomerCardRequestDto request)
        {
            var anyCustomerCard = await _unitOfWorkPortalDb.GetRepository<CustomerCard>().Entities
                                            .Where(p => !p.IsDeleted && p.PassportNumber == request.PassportNumber)
                                            .AnyAsync();

            if (anyCustomerCard)
                throw new AlreadyExistsDataPortalException($"{nameof(SiteResources.Exception_ExistingRecord).ToSiteResourcesValue(request.LanguageId)} ({nameof(SiteResources.CustomerCard).ToSiteResourcesValue(request.LanguageId)})");

            var newCustomerCard = new CustomerCard
            {
                BirthDate = request.BirthDate,
                PassportNumber = request.PassportNumber,
                GenderId = request.GenderId,
                NationalityId = request.NationalityId,
                PassportExpireDate = request.PassportExpireDate,
                Name = request.Name,
                Surname = request.Surname,
                Email = request.Email,
                PhoneNumber = request.PhoneNumber,
                CreatedBy = request.UserAuditId,
                CreatedAt = DateTime.Now
            };

            await _unitOfWorkPortalDb.GetRepository<CustomerCard>().AddAsync(newCustomerCard);
            await _unitOfWorkPortalDb.SaveChangesAsync();

            return new AddResponseDto { Id = newCustomerCard.Id };
        }

        public async Task<UpdateResponseDto> UpdateCustomerCardAsync(UpdateCustomerCardRequestDto request)
        {
            var existingCustomerCard = await _unitOfWorkPortalDb.GetRepository<CustomerCard>().Entities
                                        .Where(p => !p.IsDeleted && p.Id == request.Id)
                                        .FirstOrDefaultAsync();

            if (existingCustomerCard == null)
                throw new NoFoundDataPortalException($"{nameof(SiteResources.Exception_NoRecordsFound).ToSiteResourcesValue(request.LanguageId)} ({nameof(SiteResources.CustomerCard).ToSiteResourcesValue(request.LanguageId)})");

            existingCustomerCard.PassportNumber = request.PassportNumber;
            existingCustomerCard.PassportExpireDate = request.PassportExpireDate;
            existingCustomerCard.NationalityId = request.NationalityId;
            existingCustomerCard.BirthDate = request.BirthDate;
            existingCustomerCard.GenderId = request.GenderId;
            existingCustomerCard.Name = request.Name;
            existingCustomerCard.Surname = request.Surname;
            existingCustomerCard.Email = request.Email;
            existingCustomerCard.PhoneNumber = request.PhoneNumber;
            existingCustomerCard.IsActive = request.IsActive;
            existingCustomerCard.UpdatedAt = DateTime.Now;
            existingCustomerCard.UpdatedBy = request.UserAuditId;

            _unitOfWorkPortalDb.GetRepository<CustomerCard>().Update(existingCustomerCard);
            await _unitOfWorkPortalDb.SaveChangesAsync();

            return new UpdateResponseDto { Result = true };
        }

        public async Task<DeleteResponseDto> DeleteCustomerCardAsync(DeleteRequestDto request)
        {
            var existingCustomerCard = await _unitOfWorkPortalDb.GetRepository<CustomerCard>().Entities
                                            .Where(p => !p.IsDeleted && p.Id == request.Id)
                                            .FirstOrDefaultAsync();

            if (existingCustomerCard == null)
                throw new NoFoundDataPortalException($"{nameof(SiteResources.Exception_NoRecordsFound).ToSiteResourcesValue()} ({nameof(SiteResources.CustomerCard).ToSiteResourcesValue()})");

            existingCustomerCard.DeletedBy = request.UserAuditId;

            _unitOfWorkPortalDb.GetRepository<CustomerCard>().MarkAsDeleted(existingCustomerCard.Id);
            await _unitOfWorkPortalDb.SaveChangesAsync();

            return new DeleteResponseDto() { Result = true };
        }

        public async Task<CustomerCardDto> GetCustomerCardAsync(GetCustomerCardRequestDto request)
        {
            var existingCustomerCard = new CustomerCard();

            if (request.CustomerCardId > 0)
            {
                existingCustomerCard = await _unitOfWorkPortalDb.GetRepository<CustomerCard>().Entities
                                                .Include(q => q.CustomerCardNotes)
                                                    .ThenInclude(q => q.User)
                                                .Where(p => !p.IsDeleted && p.Id == request.CustomerCardId)
                                                .FirstOrDefaultAsync();


            }
            else if (request.ApplicationId > 0)
            {
                var existingApplication = await _unitOfWorkPortalDb.GetRepository<Application>().Entities
                                        .Where(p => p.IsActive && !p.IsDeleted && p.Id == request.ApplicationId)
                                        .FirstOrDefaultAsync();

                if (existingApplication == null)
                    throw new NoFoundDataPortalException($"{nameof(SiteResources.Exception_NoRecordsFound).ToSiteResourcesValue()} ({nameof(SiteResources.Application).ToSiteResourcesValue()})");

                existingCustomerCard = await _unitOfWorkPortalDb.GetRepository<CustomerCard>().Entities
                                                    .Where(p => !p.IsDeleted && p.Email == existingApplication.Email && p.PassportNumber == existingApplication.PassportNumber)
                                                    .FirstOrDefaultAsync();
            }

            if (existingCustomerCard == null)
                throw new NoFoundDataPortalException($"{nameof(SiteResources.Exception_NoRecordsFound).ToSiteResourcesValue()} ({nameof(SiteResources.CustomerCard).ToSiteResourcesValue()})");

            var existingApplications = await _unitOfWorkPortalDb.GetRepository<Application>().Entities
                                        .Include(p => p.ApplicationStatusHistories)
                                        .Include(p => p.BranchApplicationCountry)
                                        .Where(p => p.IsActive && !p.IsDeleted && p.PassportNumber == existingCustomerCard.PassportNumber)
                                        .ToListAsync();

            var existingPreApplications = await _unitOfWorkPortalDb.GetRepository<PreApplication>().Entities
                                        .Include(i => i.PreApplicationApplicants)
                                        .Include(p => p.BranchApplicationCountry)
                                            .ThenInclude(p => p.Branch)
                                                .ThenInclude(p => p.BranchTranslations)
                                        .Include(i => i.Slot)
                                        .Where(p => p.IsActive && !p.IsDeleted && p.PreApplicationApplicants.Any(x => x.PassportNumber == existingCustomerCard.PassportNumber))
                                        .ToListAsync();

            var result = new CustomerCardDto
            {
                Id = existingCustomerCard.Id,
                Name = existingCustomerCard.Name,
                Surname = existingCustomerCard.Surname,
                Email = existingCustomerCard.Email,
                PhoneNumber = existingCustomerCard.PhoneNumber,
                IsActive = existingCustomerCard.IsActive,
                BirthDate = existingCustomerCard.BirthDate,
                GenderId = existingCustomerCard.GenderId,
                NationalityId = existingCustomerCard.NationalityId,
                PassportExpireDate = existingCustomerCard.PassportExpireDate,
                PassportNumber = existingCustomerCard.PassportNumber,
                Notes = existingCustomerCard.CustomerCardNotes?.Where(q => q.IsActive && !q.IsDeleted)?.Select(q => new CustomerCardDto.CustomerCardNote()
                {
                    Id = q.Id,
                    Note = q.Note,
                    CreatedBy = $"{q.User.Name} {q.User.Surname}",
                    CreatedAt = q.CreatedAt
                }).ToList(),
                CustomerPreApplications = existingPreApplications.Select(q => new CustomerCardDto.CustomerPreApplicationDto()
                {
                    ApplicationTime = q.Slot.SlotTime,
                    BranchName = q.BranchApplicationCountry.Branch.BranchTranslations.Any(p => p.LanguageId == request.LanguageId) ?
                                q.BranchApplicationCountry.Branch.BranchTranslations.First(p => p.LanguageId == request.LanguageId).Name :
                                q.BranchApplicationCountry.Branch.BranchTranslations.First().Name,
                    Id = q.Id,
                    VisaCategoryId = q.VisaCategoryId
                }).AsEnumerable(),
                CustomerApplications = existingApplications.Select(q => new CustomerCardDto.CustomerApplicationDto()
                {
                    ApplicationTime = q.ApplicationTime,
                    ApplicantTypeId = q.ApplicantTypeId,
                    BranchId = q.BranchApplicationCountry.BranchId,
                    StatusId = q.StatusId,
                    ApplicationTypeId = q.ApplicationTypeId,
                    Id = q.Id,
                    ApplicationLastStatusId = q.ApplicationStatusHistories.OrderByDescending(p => p.Id).FirstOrDefault().ApplicationStatusId
                }).ToArray()
            };

            var queryBranchApplicationStatus = _unitOfWorkPortalDb.GetRepository<BranchApplicationStatus>().Entities
                                                    .Include(i => i.ApplicationStatus)
                                                    .ThenInclude(i => i.ApplicationStatusTranslations)
                                                    .Where(p => p.IsActive && !p.IsDeleted);

            var branchApplicationStatusList = await queryBranchApplicationStatus.ToListAsync();

            foreach (var item in result.CustomerApplications)
            {
                var branchApplicationStatus = branchApplicationStatusList.FirstOrDefault(p => p.BranchId == item.BranchId && p.ApplicationStatusId == item.ApplicationLastStatusId);

                if (branchApplicationStatus != null)
                {
                    item.ApplicationLastStatus = branchApplicationStatus.ApplicationStatus.ApplicationStatusTranslations.Any(p2 => p2.LanguageId == request.LanguageId) ?
                    branchApplicationStatus.ApplicationStatus.ApplicationStatusTranslations.FirstOrDefault(p2 => p2.LanguageId == request.LanguageId).Name :
                    branchApplicationStatus.ApplicationStatus.ApplicationStatusTranslations.FirstOrDefault().Name;
                }
            }

            return result;
        }

        public async Task<CustomerCardDto> GetCustomerCardWithoutApplicationAsync(GetCustomerCardRequestDto request)
        {
            var existingCustomerCard = await _unitOfWorkPortalDb.GetRepository<CustomerCard>().Entities
                                              .Include(q => q.CustomerCardNotes)
                                                  .ThenInclude(q => q.User)
                                              .Where(p => !p.IsDeleted && p.Id == request.CustomerCardId)
                                              .FirstOrDefaultAsync();

            if (existingCustomerCard == null)
                throw new NoFoundDataPortalException($"{nameof(SiteResources.Exception_NoRecordsFound).ToSiteResourcesValue()} ({nameof(SiteResources.CustomerCard).ToSiteResourcesValue()})");

            var result = new CustomerCardDto
            {
                Id = existingCustomerCard.Id,
                Name = existingCustomerCard.Name,
                Surname = existingCustomerCard.Surname,
                Email = existingCustomerCard.Email,
                PhoneNumber = existingCustomerCard.PhoneNumber,
                IsActive = existingCustomerCard.IsActive,
                BirthDate = existingCustomerCard.BirthDate,
                GenderId = existingCustomerCard.GenderId,
                NationalityId = existingCustomerCard.NationalityId,
                PassportExpireDate = existingCustomerCard.PassportExpireDate,
                PassportNumber = existingCustomerCard.PassportNumber,
                Notes = existingCustomerCard.CustomerCardNotes?.Where(q => q.IsActive && !q.IsDeleted)?.Select(q => new CustomerCardDto.CustomerCardNote()
                {
                    Id = q.Id,
                    Note = q.Note,
                    CreatedBy = $"{q.User.Name} {q.User.Surname}",
                    CreatedAt = q.CreatedAt
                }).ToList(),
            };

            return result;
        }

        public async Task<Pagination<CustomerCardsDto>> GetPaginatedCustomerCardsAsync(PaginatedCustomerCardsRequestDto request)
        {
            var paginationResult = new Pagination<CustomerCardsDto> { Page = request.Pagination.Page, PageSize = request.Pagination.PageSize };

            var queryCustomerCard = _unitOfWorkPortalDb.GetRepository<CustomerCard>().Entities
                                    .Where(p => !p.IsDeleted);

            if (!string.IsNullOrEmpty(request.PassportNo))
                queryCustomerCard = queryCustomerCard.Where(p => p.PassportNumber.Contains(request.PassportNo));

            if (!string.IsNullOrEmpty(request.Name))
                queryCustomerCard = queryCustomerCard.Where(p => p.Name.Contains(request.Name));

            if (!string.IsNullOrEmpty(request.Surname))
                queryCustomerCard = queryCustomerCard.Where(p => p.Surname.Contains(request.Surname));

            if (!string.IsNullOrEmpty(request.Email))
                queryCustomerCard = queryCustomerCard.Where(p => p.Email.Contains(request.Email));

            if (!string.IsNullOrEmpty(request.PhoneNumber))
                queryCustomerCard = queryCustomerCard.Where(p => p.PhoneNumber.Contains(request.PhoneNumber));

            paginationResult.TotalItemCount = queryCustomerCard.Count();

            var customerCardList = new List<CustomerCard>();

            if (request.Pagination != null && request.Pagination.Page > 0 && request.Pagination.PageSize > 0)
            {
                customerCardList = await queryCustomerCard
                    .OrderByDescending(o => o.Id)
                    .Skip((request.Pagination.Page - 1) * request.Pagination.PageSize)
                    .Take(request.Pagination.PageSize)
                    .ToListAsync();
            }
            else
            {
                customerCardList = await queryCustomerCard
                    .OrderByDescending(o => o.Id)
                    .ToListAsync();
            }

            var customerCards = new CustomerCardsDto() { CustomerCards = new List<CustomerCardDto>() };

            customerCards.CustomerCards = customerCardList.Select(p => new CustomerCardDto
            {
                Id = p.Id,
                Name = p.Name,
                Surname = p.Surname,
                Email = p.Email,
                PhoneNumber = p.PhoneNumber,
                IsActive = p.IsActive,
                BirthDate = p.BirthDate,
                GenderId = p.GenderId,
                NationalityId = p.NationalityId,
                PassportExpireDate = p.PassportExpireDate,
                PassportNumber = p.PassportNumber
            }).ToList();

            paginationResult.Items.Add(customerCards);
            return paginationResult;
        }

        public async Task<AddResponseDto> AddCustomerCardFromApplicationAsync(AddCustomerCardFromApplicationRequestDto request)
        {
            var existingApplication = await _unitOfWorkPortalDb.GetRepository<Application>().Entities
                                                    .Where(p => p.IsActive && !p.IsDeleted && p.Id == request.ApplicationId)
                                                    .FirstOrDefaultAsync();

            if (existingApplication == null)
                throw new NoFoundDataPortalException($"{nameof(SiteResources.Exception_NoRecordsFound).ToSiteResourcesValue(request.LanguageId)} ({nameof(SiteResources.Application).ToSiteResourcesValue(request.LanguageId)})");

            var anyCustomerCard = await _unitOfWorkPortalDb.GetRepository<CustomerCard>().Entities
                                            .Where(p => !p.IsDeleted && (p.Email == existingApplication.Email || p.PassportNumber == existingApplication.PassportNumber))
                                            .AnyAsync();

            if (anyCustomerCard)
                throw new AlreadyExistsDataPortalException($"{nameof(SiteResources.Exception_ExistingRecord).ToSiteResourcesValue(request.LanguageId)} ({nameof(SiteResources.CustomerCard).ToSiteResourcesValue(request.LanguageId)})");

            var newCustomerCard = new CustomerCard
            {
                BirthDate = existingApplication.BirthDate,
                PassportNumber = existingApplication.PassportNumber,
                GenderId = existingApplication.GenderId,
                NationalityId = existingApplication.NationalityId,
                PassportExpireDate = existingApplication.PassportExpireDate,
                Name = existingApplication.Name,
                Surname = existingApplication.Surname,
                Email = existingApplication.Email,
                PhoneNumber = existingApplication.PhoneNumber1,
                CreatedAt = DateTime.Now,
                CreatedBy = request.UserAuditId
            };

            await _unitOfWorkPortalDb.GetRepository<CustomerCard>().AddAsync(newCustomerCard);
            await _unitOfWorkPortalDb.SaveChangesAsync();

            return new AddResponseDto { Id = newCustomerCard.Id };
        }

        #endregion

        #region Department

        public async Task<AddResponseDto> AddDepartmentAsync(AddDepartmentRequestDto request)
        {
            var anyDepartment = await _unitOfWorkPortalDb.GetRepository<Department>().Entities
                                            .Where(p => !p.IsDeleted && p.DepartmentTranslations.Any(q => q.Name == request.NameTranslations.First().Name))
                                            .AnyAsync();

            if (anyDepartment)
                throw new AlreadyExistsDataPortalException($"{nameof(SiteResources.Exception_ExistingRecord).ToSiteResourcesValue(request.LanguageId)} ({nameof(SiteResources.Department).ToSiteResourcesValue(request.LanguageId)})");

            var newDepartment = new Department
            {
                IsActive = true,
                Status = request.Status,
                DepartmentTranslations = request.NameTranslations.Select(q => new DepartmentTranslation()
                {
                    Name = q.Name,
                    LanguageId = q.LanguageId
                }).ToList()
            };

            await _unitOfWorkPortalDb.GetRepository<Department>().AddAsync(newDepartment);
            await _unitOfWorkPortalDb.SaveChangesAsync();

            return new AddResponseDto { Id = newDepartment.Id };
        }

        public async Task<UpdateResponseDto> UpdateDepartmentAsync(UpdateDepartmentRequestDto request)
        {
            var existingDepartment = await _unitOfWorkPortalDb.GetRepository<Department>().Entities
                                        .Include(q => q.DepartmentTranslations)
                                        .Where(p => !p.IsDeleted && p.Id == request.Id)
                                        .FirstOrDefaultAsync();

            if (existingDepartment == null)
                throw new NoFoundDataPortalException($"{nameof(SiteResources.Exception_NoRecordsFound).ToSiteResourcesValue(request.LanguageId)} ({nameof(SiteResources.Department).ToSiteResourcesValue(request.LanguageId)})");

            existingDepartment.IsActive = request.IsActive;
            existingDepartment.Status = request.Status;

            foreach (var item in request.NameTranslations)
            {
                var entityDepartmentTranslation = existingDepartment.DepartmentTranslations.FirstOrDefault(p => p.LanguageId == item.LanguageId);

                if (entityDepartmentTranslation == null)
                    continue;

                entityDepartmentTranslation.Name = item.Name;

                _unitOfWorkPortalDb.GetRepository<DepartmentTranslation>().Update(entityDepartmentTranslation);
            }

            _unitOfWorkPortalDb.GetRepository<Department>().Update(existingDepartment);
            await _unitOfWorkPortalDb.SaveChangesAsync();

            return new UpdateResponseDto { Result = true };
        }

        public async Task<DeleteResponseDto> DeleteDepartmentAsync(int id)
        {
            var existingDepartment = await _unitOfWorkPortalDb.GetRepository<Department>().Entities
                                            .Where(p => !p.IsDeleted && p.Id == id)
                                            .FirstOrDefaultAsync();

            if (existingDepartment == null)
                throw new NoFoundDataPortalException($"{nameof(SiteResources.Exception_NoRecordsFound).ToSiteResourcesValue()} ({nameof(SiteResources.Department).ToSiteResourcesValue()})");

            _unitOfWorkPortalDb.GetRepository<Department>().MarkAsDeleted(existingDepartment.Id);
            await _unitOfWorkPortalDb.SaveChangesAsync();

            return new DeleteResponseDto() { Result = true };
        }

        public async Task<DepartmentResponseDto> GetDepartmentAsync(GetDepartmentRequestDto request)
        {
            var existingDepartment = await _unitOfWorkPortalDb.GetRepository<Department>().Entities
                                                .Include(q => q.DepartmentTranslations)
                                                .Where(p => !p.IsDeleted && p.Id == request.Id)
                                                .FirstOrDefaultAsync();

            if (existingDepartment == null)
                throw new NoFoundDataPortalException($"{nameof(SiteResources.Exception_NoRecordsFound).ToSiteResourcesValue(request.LanguageId)} ({nameof(SiteResources.Department).ToSiteResourcesValue(request.LanguageId)})");

            var result = new DepartmentResponseDto
            {
                Id = existingDepartment.Id,
                IsActive = existingDepartment.IsActive,
                Status = existingDepartment.Status,
                NameTranslations = existingDepartment.DepartmentTranslations.Select(q => new DepartmentResponseDto.DepartmentTranslationResponseDto()
                {
                    Name = q.Name,
                    LanguageId = q.LanguageId
                }).ToList()
            };

            return result;
        }

        public async Task<Pagination<PaginatedDepartmentResponseDto>> GetPaginatedDepartmentAsync(PaginatedDepartmentRequestDto request)
        {
            var paginationResult = new Pagination<PaginatedDepartmentResponseDto> { Page = request.Pagination.Page, PageSize = request.Pagination.PageSize };

            var queryDepartment = _unitOfWorkPortalDb.GetRepository<Department>().Entities
                                    .Include(q => q.DepartmentTranslations)
                                    .Where(p => !p.IsDeleted);

            paginationResult.TotalItemCount = queryDepartment.Count();

            var departmentList = new List<Department>();

            if (request.Pagination != null && request.Pagination.Page > 0 && request.Pagination.PageSize > 0)
            {
                departmentList = await queryDepartment
                    .OrderByDescending(o => o.Id)
                    .Skip((request.Pagination.Page - 1) * request.Pagination.PageSize)
                    .Take(request.Pagination.PageSize)
                    .ToListAsync();
            }
            else
            {
                departmentList = await queryDepartment
                    .OrderByDescending(o => o.Id)
                    .ToListAsync();
            }

            var list = new PaginatedDepartmentResponseDto() { Departments = new List<PaginatedDepartmentResponseDto.DepartmentResponseDto>() };

            list.Departments = departmentList.Select(p => new PaginatedDepartmentResponseDto.DepartmentResponseDto
            {
                Id = p.Id,
                IsActive = p.IsActive,
                Status = p.Status,
                Name = p.DepartmentTranslations.Any(q => q.LanguageId == request.LanguageId) ?
                      p.DepartmentTranslations.First(q => q.LanguageId == request.LanguageId).Name :
                      p.DepartmentTranslations.FirstOrDefault().Name
            }).ToList();

            paginationResult.Items.Add(list);
            return paginationResult;
        }

        #endregion

        #region BranchDepartment

        public async Task<AddResponseDto> AddBranchDepartmentAsync(AddBranchDepartmentRequestDto request)
        {
            var existingBranchDepartment = await _unitOfWorkPortalDb.GetRepository<BranchDepartment>().Entities
                .Include(p => p.BranchDepartmentOrder)
                .Where(p => !p.IsDeleted && p.BranchId == request.BranchId)
                .Select(q => new
                {
                    Flow = string.Join('-', q.BranchDepartmentOrder.Where(p => p.IsActive && !p.IsDeleted).OrderBy(p => p.Order).Select(q => q.DepartmentId.ToString())),
                    FirstDepartment = q.BranchDepartmentOrder.Where(p => p.IsActive && !p.IsDeleted).OrderBy(p => p.Order).FirstOrDefault().DepartmentId
                })
                .ToListAsync();

            var requestFlow = string.Join('-', request.Departments.OrderBy(q => q.Order).Select(q => q.DepartmentId.ToString()));

            if (existingBranchDepartment.Any(q => q.Flow == requestFlow))
                throw new AlreadyExistsDataPortalException($"{nameof(SiteResources.Exception_ExistingRecord).ToSiteResourcesValue(request.LanguageId)} ({nameof(SiteResources.BranchDepartment).ToSiteResourcesValue(request.LanguageId)})");

            if (existingBranchDepartment.Any(q => q.FirstDepartment == request.Departments.OrderBy(q => q.Order).FirstOrDefault().DepartmentId))
                throw new AlreadyExistsDataPortalException($"{nameof(SiteResources.Exception_ExistingRecordWithSameOrder).ToSiteResourcesValue(request.LanguageId)} ({nameof(SiteResources.BranchDepartment).ToSiteResourcesValue(request.LanguageId)})");

            var newBranchDepartment = new BranchDepartment
            {
                BranchId = request.BranchId,
                Name = request.Name,
                BranchDepartmentOrder = request.Departments.Select(q => new BranchDepartmentOrder
                {
                    DepartmentId = q.DepartmentId,
                    Order = q.Order,

                }).ToList()
            };

            await _unitOfWorkPortalDb.GetRepository<BranchDepartment>().AddAsync(newBranchDepartment);
            await _unitOfWorkPortalDb.SaveChangesAsync();

            return new AddResponseDto { Id = newBranchDepartment.Id };
        }

        public async Task<UpdateResponseDto> UpdateBranchDepartmentAsync(UpdateBranchDepartmentRequestDto request)
        {
            var branchDepartment = await _unitOfWorkPortalDb.GetRepository<BranchDepartment>().Entities
                .Include(p => p.BranchDepartmentOrder)
                .Where(p => !p.IsDeleted && p.BranchId == request.BranchId && p.Id != request.BranchDepartmentId)
                .Select(q => new
                {
                    Flow = string.Join('-', q.BranchDepartmentOrder.Where(p => p.IsActive && !p.IsDeleted).OrderBy(p => p.Order).Select(q => q.DepartmentId.ToString())),
                    FirstDepartment = q.BranchDepartmentOrder.Where(p => p.IsActive && !p.IsDeleted).OrderBy(p => p.Order).FirstOrDefault().DepartmentId
                })
                .ToListAsync();

            var requestFlow = string.Join('-', request.Departments.OrderBy(q => q.Order).Select(q => q.DepartmentId.ToString()));

            if (branchDepartment.Any(q => q.Flow == requestFlow))
                throw new AlreadyExistsDataPortalException($"{nameof(SiteResources.Exception_ExistingRecord).ToSiteResourcesValue(request.LanguageId)} ({nameof(SiteResources.BranchDepartment).ToSiteResourcesValue(request.LanguageId)})");

            if (branchDepartment.Any(q => q.FirstDepartment == request.Departments.OrderBy(q => q.Order).FirstOrDefault().DepartmentId))
                throw new AlreadyExistsDataPortalException($"{nameof(SiteResources.Exception_ExistingRecordWithSameOrder).ToSiteResourcesValue(request.LanguageId)} ({nameof(SiteResources.BranchDepartment).ToSiteResourcesValue(request.LanguageId)})");

            var existingBranchDepartment = await _unitOfWorkPortalDb.GetRepository<BranchDepartment>().Entities
                .Where(p => !p.IsDeleted && p.Id == request.BranchDepartmentId)
                .FirstOrDefaultAsync();

            if (existingBranchDepartment == null)
                throw new NoFoundDataPortalException($"{nameof(SiteResources.Exception_NoRecordsFound).ToSiteResourcesValue(request.LanguageId)} ({nameof(SiteResources.BranchDepartment).ToSiteResourcesValue(request.LanguageId)})");

            var existingBranchDepartmentOrder = _unitOfWorkPortalDb.GetRepository<BranchDepartmentOrder>().Entities
                .Where(p => !p.IsDeleted && p.BranchDepartmentId == request.BranchDepartmentId)
                .ToList();

            existingBranchDepartment.IsActive = request.IsActive;
            existingBranchDepartment.Name = request.Name;

            _unitOfWorkPortalDb.GetRepository<BranchDepartment>().Update(existingBranchDepartment);

            var entityDepartmentOrderDeactivated = existingBranchDepartmentOrder.Where(p => !request.Departments.Any(q => q.DepartmentId == p.DepartmentId)).AsEnumerable();

            if (entityDepartmentOrderDeactivated.Count() > 0)
            {
                entityDepartmentOrderDeactivated.ToList().ForEach(p => p.IsActive = false);

                _unitOfWorkPortalDb.GetRepository<BranchDepartmentOrder>().UpdateRange(entityDepartmentOrderDeactivated);
            }

            var entityDepartmentOrderUpdated = existingBranchDepartmentOrder.Where(p => request.Departments.Any(q => q.DepartmentId == p.DepartmentId)).AsEnumerable();

            if (entityDepartmentOrderUpdated.Count() > 0)
            {
                foreach (var order in entityDepartmentOrderUpdated)
                {
                    var updateValue = request.Departments.First(q => q.DepartmentId == order.DepartmentId);

                    order.Order = updateValue.Order;
                    order.IsActive = true;

                    _unitOfWorkPortalDb.GetRepository<BranchDepartmentOrder>().Update(order);
                }
            }

            var newDepartmentOrders = request.Departments
                            .Where(p => !existingBranchDepartmentOrder.Exists(m => m.DepartmentId == p.DepartmentId))
                            .Select(p => new BranchDepartmentOrder
                            {
                                BranchDepartmentId = request.BranchDepartmentId,
                                DepartmentId = p.DepartmentId,
                                Order = p.Order,
                            }).AsEnumerable();

            if (newDepartmentOrders.Count() > 0)
            {
                await _unitOfWorkPortalDb.GetRepository<BranchDepartmentOrder>().AddRangeAsync(newDepartmentOrders);
            }

            await _unitOfWorkPortalDb.SaveChangesAsync();

            return new UpdateResponseDto()
            {
                Result = true
            };
        }

        public async Task<DeleteResponseDto> DeleteBranchDepartmentAsync(int id)
        {
            var existingBranchDepartment = await _unitOfWorkPortalDb.GetRepository<BranchDepartment>().Entities
                                        .Where(p => !p.IsDeleted && p.Id == id)
                                        .FirstOrDefaultAsync();

            if (existingBranchDepartment == null)
                throw new NoFoundDataPortalException($"{nameof(SiteResources.Exception_NoRecordsFound).ToSiteResourcesValue()} ({nameof(SiteResources.BranchDepartment).ToSiteResourcesValue()})");

            _unitOfWorkPortalDb.GetRepository<BranchDepartment>().MarkAsDeleted(existingBranchDepartment.Id);
            await _unitOfWorkPortalDb.SaveChangesAsync();

            return new DeleteResponseDto()
            {
                Result = true
            };
        }

        public async Task<BranchDepartmentResponseDto> GetBranchDepartmentAsync(GetBranchDepartmentRequestDto request)
        {
            var existingBranchDepartment = await _unitOfWorkPortalDb.GetRepository<BranchDepartment>().Entities
                .Include(p => p.BranchDepartmentOrder)
                    .ThenInclude(p => p.Department)
                        .ThenInclude(p => p.DepartmentTranslations)
                .Where(p => !p.IsDeleted && p.Id == request.BranchDepartmentId)
                .FirstOrDefaultAsync();

            if (existingBranchDepartment == null)
                throw new NoFoundDataPortalException($"{nameof(SiteResources.Exception_NoRecordsFound).ToSiteResourcesValue(request.LanguageId)} ({nameof(SiteResources.BranchDepartment).ToSiteResourcesValue(request.LanguageId)})");

            var result = new BranchDepartmentResponseDto
            {
                BranchId = existingBranchDepartment.BranchId,
                BranchDepartmentId = existingBranchDepartment.Id,
                IsActive = existingBranchDepartment.IsActive,
                Name = existingBranchDepartment.Name,
                Departments = existingBranchDepartment.BranchDepartmentOrder.Where(q => q.IsActive && !q.IsDeleted).Select(q => new BranchDepartmentResponseDto.OrderResponseDto
                {
                    DepartmentId = q.DepartmentId,
                    DepartmentName = q.Department.DepartmentTranslations.Any(p => p.LanguageId == request.LanguageId) ?
                                        q.Department.DepartmentTranslations.First(p => p.LanguageId == request.LanguageId).Name :
                                        q.Department.DepartmentTranslations.FirstOrDefault().Name,
                    Order = q.Order
                }).ToList()
            };

            return result;
        }

        public async Task<BranchDepartmentsResponseDto> GetBranchDepartmentsAsync(GetBranchDepartmentsRequestDto request)
        {
            var result = new BranchDepartmentsResponseDto();

            result.BranchDepartments = await _unitOfWorkPortalDb.GetRepository<BranchDepartment>().Entities
                .Include(p => p.BranchDepartmentOrder)
                    .ThenInclude(p => p.Department)
                        .ThenInclude(p => p.DepartmentTranslations)
                .Where(p => !p.IsDeleted && p.BranchId == request.BranchId)
                .Select(p => new BranchDepartmentsResponseDto.BranchDepartmentResponseDto
                {
                    BranchDepartmentId = p.Id,
                    Name = p.Name,
                    IsActive = p.IsActive,
                    Departments = p.BranchDepartmentOrder.Where(q => q.IsActive && !q.IsDeleted).Select(q => new BranchDepartmentsResponseDto.BranchDepartmentResponseDto.OrderResponseDto
                    {
                        Order = q.Order,
                        DepartmentId = q.DepartmentId,
                        DepartmentName = q.Department.DepartmentTranslations.Any(w => w.LanguageId == request.LanguageId) ?
                                           q.Department.DepartmentTranslations.First(w => w.LanguageId == request.LanguageId).Name :
                                           q.Department.DepartmentTranslations.FirstOrDefault().Name
                    })
                })
                .ToListAsync();

            return result;
        }

        #endregion

        #region Menu

        public async Task<MenuResponseDto> GetMenuAsync(EmptyRequestDto request)
        {
            var existingMenuItems = await _unitOfWorkPortalDb.GetRepository<Action>().Entities
                                                .Include(q => q.ActionTranslations)
                                                .Where(p => !p.IsDeleted && p.IsActive && p.IsMenuItem)
                                                .ToListAsync();

            if (existingMenuItems.Count() == 0)
                throw new NoFoundDataPortalException($"{nameof(SiteResources.Exception_NoRecordsFound).ToSiteResourcesValue(request.LanguageId)} ({nameof(SiteResources.Action).ToSiteResourcesValue(request.LanguageId)})");

            var result = new MenuResponseDto()
            {
                MainMenuList = existingMenuItems.Where(q => q.ParentId == null).Select(q => new MenuResponseDto.MainMenuResponseDto()
                {
                    Id = q.Id,
                    Order = q.Order.GetValueOrDefault(),
                    Name = q.ActionTranslations.Any(p => p.LanguageId == request.LanguageId) ?
                            q.ActionTranslations.First(p => p.LanguageId == request.LanguageId).Name :
                            q.ActionTranslations.FirstOrDefault().Name,
                    IsAction = !string.IsNullOrEmpty(q.Controller.Trim()),
                    SubMenu = existingMenuItems.Where(p => p.ParentId == q.Id).Select(p => new MenuResponseDto.SubMenuResponseDto()
                    {
                        Id = p.Id,
                        Order = p.Order.GetValueOrDefault(),
                        Name = p.ActionTranslations.Any(r => r.LanguageId == request.LanguageId) ?
                                p.ActionTranslations.First(r => r.LanguageId == request.LanguageId).Name :
                                p.ActionTranslations.FirstOrDefault().Name,
                        IsAction = !string.IsNullOrEmpty(p.Controller.Trim()),
                        Node = existingMenuItems.Where(r => r.ParentId == p.Id).Select(r => new MenuResponseDto.NodeResponseDto()
                        {
                            Id = r.Id,
                            Order = r.Order.GetValueOrDefault(),
                            Name = r.ActionTranslations.Any(t => t.LanguageId == request.LanguageId) ?
                                    r.ActionTranslations.First(t => t.LanguageId == request.LanguageId).Name :
                                    r.ActionTranslations.FirstOrDefault().Name,
                            IsAction = !string.IsNullOrEmpty(r.Controller.Trim()),
                        }).OrderBy(z => z.Order).ToList()
                    }).OrderBy(z => z.Order).ToList()
                }).OrderBy(z => z.Order).ToList()
            };

            return result;
        }

        public async Task<UpdateResponseDto> UpdateMenuAsync(UpdateMenuRequestDto request)
        {
            var menuItems = await _unitOfWorkPortalDb.GetRepository<Action>().Entities
                                            .Where(p => !p.IsDeleted && p.IsActive && p.IsMenuItem)
                                            .ToListAsync();

            foreach (var mainMenuItem in request.MainMenuList)
            {
                var existingMainMenuItem = menuItems.FirstOrDefault(q => q.Id == mainMenuItem.Id);
                existingMainMenuItem.Order = mainMenuItem.Order;
                existingMainMenuItem.ParentId = null;

                _unitOfWorkPortalDb.GetRepository<Action>().Update(existingMainMenuItem);

                foreach (var subMenuItem in mainMenuItem.SubMenuList)
                {
                    var existingSubMenuItem = menuItems.FirstOrDefault(q => q.Id == subMenuItem.Id);
                    existingSubMenuItem.Order = subMenuItem.Order;
                    existingSubMenuItem.ParentId = mainMenuItem.Id;

                    _unitOfWorkPortalDb.GetRepository<Action>().Update(existingSubMenuItem);

                    foreach (var node in subMenuItem.NodeList)
                    {
                        var existingNodeItem = menuItems.FirstOrDefault(q => q.Id == node.Id);
                        existingNodeItem.Order = node.Order;
                        existingNodeItem.ParentId = subMenuItem.Id;

                        _unitOfWorkPortalDb.GetRepository<Action>().Update(existingNodeItem);
                    }
                }
            }

            await _unitOfWorkPortalDb.SaveChangesAsync();

            return new UpdateResponseDto { Result = true };
        }

        #endregion

        #region BranchIcrNote

        public async Task<BranchIcrResponseDto> GetBranchIcrAsync(GetBranchIcrRequestDto request)
        {
            var queryBranchIcr = await _unitOfWorkPortalDb.GetRepository<BranchIcrNote>().Entities
                                    .Include(i => i.Branch).ThenInclude(i => i.SapBranch)
                                    .Where(p => !p.IsDeleted && p.BranchId == request.BranchId)
                                    .FirstOrDefaultAsync();

            if (queryBranchIcr == null)
                return new BranchIcrResponseDto
                {
                    BranchId = request.BranchId,
                    Type = 0
                };

            return new BranchIcrResponseDto
            {
                Id = queryBranchIcr.Id,
                BranchId = queryBranchIcr.BranchId,
                Type = queryBranchIcr.IcrType,
                Note = queryBranchIcr.Note,
                Name = queryBranchIcr.Name,
                NameAr = queryBranchIcr.NameAr,
                NoteAr = queryBranchIcr.NoteAr,
                NameTm = queryBranchIcr.NameTm,
                NoteTm = queryBranchIcr.NoteTm,
                NameRu = queryBranchIcr.NameRu,
                NoteRu = queryBranchIcr.NoteRu,
                NameFr = queryBranchIcr.NameFr,
                NoteFr = queryBranchIcr.NoteFr,
                SapBranch = queryBranchIcr.Branch.SapBranch.SapBranchId
            };
        }

        public async Task<UpdateResponseDto> UpdateBranchIcrAsync(UpdateBranchIcrRequestDto request)
        {
            if (request.Id.HasValue)
            {
                var existingBranchIcr = await _unitOfWorkPortalDb.GetRepository<BranchIcrNote>().Entities
                                    .Where(p => !p.IsDeleted && p.BranchId == request.BranchId)
                                    .FirstOrDefaultAsync();

                existingBranchIcr.IcrType = request.Type;
                existingBranchIcr.Note = request.Note;
                existingBranchIcr.Name = request.Name;
                existingBranchIcr.NameAr = request.NameAr;
                existingBranchIcr.NoteAr = request.NoteAr;
                existingBranchIcr.NameTm = request.NameTm;
                existingBranchIcr.NoteTm = request.NoteTm;
                existingBranchIcr.NameRu = request.NameRu;
                existingBranchIcr.NoteRu = request.NoteRu;
                existingBranchIcr.NameFr = request.NameFr;
                existingBranchIcr.NoteFr = request.NoteFr;

                _unitOfWorkPortalDb.GetRepository<BranchIcrNote>().Update(existingBranchIcr);
                await _unitOfWorkPortalDb.SaveChangesAsync();

                return new UpdateResponseDto { Result = true };
            }

            var entity = new BranchIcrNote()
            {
                BranchId = request.BranchId,
                Note = request.Note,
                IcrType = request.Type,
                Name = request.Name,
                NameAr = request.NameAr,
                NoteAr = request.NoteAr,
                NameTm = request.NameTm,
                NoteTm = request.NoteTm,
                NameRu = request.NameRu,
                NoteRu = request.NoteRu,
                NameFr = request.NameFr,
                NoteFr = request.NoteFr
            };

            await _unitOfWorkPortalDb.GetRepository<BranchIcrNote>().AddAsync(entity);
            await _unitOfWorkPortalDb.SaveChangesAsync();

            return new UpdateResponseDto { Result = true };
        }

        #endregion

        #region BranchApplicationCountryFile

        public async Task<AddResponseDto> AddBranchApplicationCountryFileAsync(AddBranchApplicationCountryFileRequestDto request)
        {
            var existingApplication = await _unitOfWorkPortalDb.GetRepository<BranchApplicationCountry>().Entities
                                                            .Where(p => p.IsActive && !p.IsDeleted && p.Id == request.BranchApplicationCountryId)
                                                            .FirstOrDefaultAsync();

            if (existingApplication == null)
                throw new NoFoundDataPortalException($"{nameof(SiteResources.Exception_NoRecordsFound).ToSiteResourcesValue(request.LanguageId)} ({nameof(SiteResources.BranchApplicationCountry).ToSiteResourcesValue(request.LanguageId)})");

            var newBranchApplicationCountryFile = new BranchApplicationCountryFile
            {
                BranchApplicationCountryId = request.BranchApplicationCountryId,
                FileTypeId = request.FileTypeId,
                DocumentId = request.DocumentId
            };

            await _unitOfWorkPortalDb.GetRepository<BranchApplicationCountryFile>().AddAsync(newBranchApplicationCountryFile);
            await _unitOfWorkPortalDb.SaveChangesAsync();

            return new AddResponseDto() { Id = newBranchApplicationCountryFile.Id };
        }

        public async Task<BranchApplicationCountryFileWithNoteResponseDto> GetAllBranchApplicationCountryFilesAsync(int id)
        {
            var existingBranchApplicationCountry = await _unitOfWorkPortalDb.GetRepository<BranchApplicationCountry>().Entities
                                                    .Include(q => q.BranchApplicationCountryFiles)
                                                    .Where(p => p.IsActive && !p.IsDeleted && p.Id == id).FirstOrDefaultAsync();

            if (existingBranchApplicationCountry == null)
                throw new NoFoundDataPortalException($"{nameof(SiteResources.Exception_NoRecordsFound).ToSiteResourcesValue()} ({nameof(SiteResources.BranchApplicationCountryFile).ToSiteResourcesValue()})");

            var branchApplicationCountryFiles = new BranchApplicationCountryFileWithNoteResponseDto()
            {
                Note = existingBranchApplicationCountry.Note,
                BranchApplicationCountryFiles = new List<BranchApplicationCountryFileWithNoteResponseDto.BranchApplicationCountryFileDto>()
            };

            branchApplicationCountryFiles.BranchApplicationCountryFiles = existingBranchApplicationCountry.BranchApplicationCountryFiles.Select(p => new BranchApplicationCountryFileWithNoteResponseDto.BranchApplicationCountryFileDto
            {
                Id = p.Id,
                BranchApplicationCountryId = p.BranchApplicationCountryId,
                FileTypeId = p.FileTypeId,
                DocumentId = p.DocumentId
            }).ToList();

            return branchApplicationCountryFiles;
        }

        public async Task<DeleteResponseDto> DeleteBranchApplicationCountryFileAsync(int id)
        {
            var existingBranchApplicationCountryFile = await _unitOfWorkPortalDb.GetRepository<BranchApplicationCountryFile>().Entities
                                        .Where(p => !p.IsDeleted && p.Id == id)
                                        .FirstOrDefaultAsync();

            if (existingBranchApplicationCountryFile == null)
                throw new NoFoundDataPortalException($"{nameof(SiteResources.Exception_NoRecordsFound).ToSiteResourcesValue()} ({nameof(SiteResources.BranchApplicationCountryFile).ToSiteResourcesValue()})");

            var existingDocument = await _unitOfWorkPortalDb.GetRepository<Document>().Entities
                                            .Where(p => p.Id == existingBranchApplicationCountryFile.DocumentId)
                                            .FirstOrDefaultAsync();

            if (existingDocument == null)
                throw new NoFoundDataPortalException($"{nameof(SiteResources.Exception_NoRecordsFound).ToSiteResourcesValue()} ({nameof(SiteResources.Document).ToSiteResourcesValue()})");

            _unitOfWorkPortalDb.GetRepository<BranchApplicationCountryFile>().MarkAsDeleted(existingBranchApplicationCountryFile.Id);
            _unitOfWorkPortalDb.GetRepository<Document>().MarkAsDeleted(existingDocument.Id);
            await _unitOfWorkPortalDb.SaveChangesAsync();

            return new DeleteResponseDto { Result = true };
        }

        public async Task<Pagination<BranchApplicationCountryFilesResponseDto>> GetPaginatedBranchApplicationCountryFilesAsync(PaginatedBranchApplicationCountryFilesRequestDto request)
        {
            var paginationResult = new Pagination<BranchApplicationCountryFilesResponseDto> { Page = request.Pagination.Page, PageSize = request.Pagination.PageSize };

            var queryBranchApplicationCountryFile = _unitOfWorkPortalDb.GetRepository<BranchApplicationCountryFile>().Entities
                                                    .Where(p => p.IsActive && !p.IsDeleted && p.BranchApplicationCountryId == request.BranchApplicationCountryId);

            paginationResult.TotalItemCount = queryBranchApplicationCountryFile.Count();

            var branchApplicationCountryFileFileList = new List<BranchApplicationCountryFile>();

            if (request.Pagination != null && request.Pagination.Page > 0 && request.Pagination.PageSize > 0)
            {
                branchApplicationCountryFileFileList = await queryBranchApplicationCountryFile
                    .OrderByDescending(o => o.Id)
                    .Skip((request.Pagination.Page - 1) * request.Pagination.PageSize)
                    .Take(request.Pagination.PageSize)
                    .ToListAsync();
            }
            else
            {
                branchApplicationCountryFileFileList = await queryBranchApplicationCountryFile
                    .OrderByDescending(o => o.Id)
                    .ToListAsync();
            }

            var branchApplicationCountryFiles = new BranchApplicationCountryFilesResponseDto() { BranchApplicationCountryFiles = new List<BranchApplicationCountryFilesResponseDto.BranchApplicationCountryFileDto>() };

            branchApplicationCountryFiles.BranchApplicationCountryFiles = branchApplicationCountryFileFileList.Select(p => new BranchApplicationCountryFilesResponseDto.BranchApplicationCountryFileDto
            {
                Id = p.Id,
                BranchApplicationCountryId = p.BranchApplicationCountryId,
                FileTypeId = p.FileTypeId,
                DocumentId = p.DocumentId
            }).ToList();

            paginationResult.Items.Add(branchApplicationCountryFiles);
            return paginationResult;
        }

        #endregion

        #region CustomerCardNote

        public async Task<AddResponseDto> AddCustomerCardNoteAsync(AddCustomerCardNoteRequestDto request)
        {
            var anyCustomerCard = await _unitOfWorkPortalDb.GetRepository<CustomerCard>().Entities
                                            .Where(p => !p.IsDeleted && p.Id == request.CustomerCardId)
                                            .AnyAsync();

            if (!anyCustomerCard)
                throw new AlreadyExistsDataPortalException($"{nameof(SiteResources.Exception_ExistingRecord).ToSiteResourcesValue(request.LanguageId)} ({nameof(SiteResources.CustomerCard).ToSiteResourcesValue(request.LanguageId)})");

            var newCustomerCardNote = new CustomerCardNote
            {
                CustomerCardId = request.CustomerCardId,
                Note = request.Note,
                CreatedAt = DateTimeOffset.UtcNow,
                CreatedBy = request.UserId
            };

            await _unitOfWorkPortalDb.GetRepository<CustomerCardNote>().AddAsync(newCustomerCardNote);
            await _unitOfWorkPortalDb.SaveChangesAsync();

            return new AddResponseDto { Id = newCustomerCardNote.Id };
        }

        public async Task<CustomerCardNotesResponseDto> GetCustomerCardNotesAsync(int customerCardId)
        {
            var existingCustomerCardNotes = await _unitOfWorkPortalDb.GetRepository<CustomerCardNote>().Entities
                                                    .Include(p => p.CustomerCard)
                                                    .Include(p => p.User)
                                                    .Where(p => p.IsActive && !p.IsDeleted && p.CustomerCardId == customerCardId).ToListAsync();

            if (existingCustomerCardNotes.Count() == 0)
                throw new NoFoundDataPortalException($"{nameof(SiteResources.Exception_NoRecordsFound).ToSiteResourcesValue()} ({nameof(SiteResources.CustomerCardNotes).ToSiteResourcesValue()})");

            var customerCardNotes = new CustomerCardNotesResponseDto()
            {
                CustomerCardId = existingCustomerCardNotes.FirstOrDefault().CustomerCard.Id,
                Name = existingCustomerCardNotes.FirstOrDefault().CustomerCard.Name,
                Surname = existingCustomerCardNotes.FirstOrDefault().CustomerCard.Surname,
                Email = existingCustomerCardNotes.FirstOrDefault().CustomerCard.Email,
                PhoneNumber = existingCustomerCardNotes.FirstOrDefault().CustomerCard.PhoneNumber,
                CustomerCardNotes = existingCustomerCardNotes.Select(q => new CustomerCardNotesResponseDto.CustomerCardNote()
                {
                    Id = q.Id,
                    Note = q.Note,
                    CreatedAt = q.CreatedAt,
                    CreatedBy = $"{q.User.Name} {q.User.Surname}"
                })
            };

            return customerCardNotes;
        }

        public async Task<DeleteResponseDto> DeleteCustomerCardNoteAsync(int id)
        {
            var existingCustomerCardNote = await _unitOfWorkPortalDb.GetRepository<CustomerCardNote>().Entities
                                        .Where(p => !p.IsDeleted && p.Id == id)
                                        .FirstOrDefaultAsync();

            if (existingCustomerCardNote == null)
                throw new NoFoundDataPortalException($"{nameof(SiteResources.Exception_NoRecordsFound).ToSiteResourcesValue()} ({nameof(SiteResources.CustomerCardNote).ToSiteResourcesValue()})");

            _unitOfWorkPortalDb.GetRepository<CustomerCardNote>().MarkAsDeleted(existingCustomerCardNote.Id);
            await _unitOfWorkPortalDb.SaveChangesAsync();

            return new DeleteResponseDto { Result = true };
        }

        #endregion

        #region Announcement

        public async Task<AddResponseDto> AddAnnouncementAsync(AddAnnouncementRequestDto request)
        {
            var stringBranchIds = request.BranchIds.ConvertAll<string>(delegate (int i) { return $"*{i}/"; });
            var stringAnnouncementPersonIds = request.AnnouncementPersonIds.ConvertAll<string>(delegate (int i) { return $"*{i}/"; });
            var newAnnouncement = new Announcement
            {
                BranchIds = string.Join(",", stringBranchIds),
                AnnouncementPersonIds = string.Join(",", stringAnnouncementPersonIds),
                DueDate = request.DueDate,
                Subject = request.Subject,
                Message = request.Message,
                CreatedAt = DateTimeOffset.UtcNow,
                CreatedBy = request.CreatedBy
            };

            await _unitOfWorkPortalDb.GetRepository<Announcement>().AddAsync(newAnnouncement);
            await _unitOfWorkPortalDb.SaveChangesAsync();

            return new AddResponseDto { Id = newAnnouncement.Id };
        }

        public async Task<UpdateResponseDto> UpdateAnnouncementAsync(UpdateAnnouncementRequestDto request)
        {
            var existingAnnouncement = await _unitOfWorkPortalDb.GetRepository<Announcement>().Entities
                                        .Where(p => !p.IsDeleted && p.IsActive && p.Id == request.Id)
                                        .FirstOrDefaultAsync();

            if (existingAnnouncement == null)
                throw new NoFoundDataPortalException($"{nameof(SiteResources.Exception_NoRecordsFound).ToSiteResourcesValue(request.LanguageId)} ({nameof(SiteResources.Announcement).ToSiteResourcesValue(request.LanguageId)})");

            var stringBranchIds = request.BranchIds.ConvertAll<string>(delegate (int i) { return $"*{i}/"; });
            var stringAnnouncementPersonIds = request.AnnouncementPersonIds.ConvertAll<string>(delegate (int i) { return $"*{i}/"; });

            existingAnnouncement.BranchIds = string.Join(",", stringBranchIds);
            existingAnnouncement.AnnouncementPersonIds = string.Join(",", stringAnnouncementPersonIds);
            existingAnnouncement.DueDate = request.DueDate;
            existingAnnouncement.Subject = request.Subject;
            existingAnnouncement.Message = request.Message;

            _unitOfWorkPortalDb.GetRepository<Announcement>().Update(existingAnnouncement);
            await _unitOfWorkPortalDb.SaveChangesAsync();

            return new UpdateResponseDto { Result = true };
        }

        public async Task<DeleteResponseDto> DeleteAnnouncementAsync(int id)
        {
            var existingAnnouncement = await _unitOfWorkPortalDb.GetRepository<Announcement>().Entities
                                        .Where(p => !p.IsDeleted && p.IsActive && p.Id == id)
                                        .FirstOrDefaultAsync();

            if (existingAnnouncement == null)
                throw new NoFoundDataPortalException($"{nameof(SiteResources.Exception_NoRecordsFound).ToSiteResourcesValue()} ({nameof(SiteResources.Announcement).ToSiteResourcesValue()})");

            _unitOfWorkPortalDb.GetRepository<Announcement>().MarkAsDeleted(existingAnnouncement.Id);
            await _unitOfWorkPortalDb.SaveChangesAsync();

            return new DeleteResponseDto { Result = true };
        }

        public async Task<AnnouncementDto> GetAnnouncementAsync(AnnouncementRequestDto request)
        {
            var existingAnnouncement = await _unitOfWorkPortalDb.GetRepository<Announcement>().Entities
                                        .Include(i => i.User)
                                        .Include(i => i.AnnouncementHistories)
                                        .Where(p => !p.IsDeleted && p.IsActive && p.Id == request.AnnouncementId)
                                        .FirstOrDefaultAsync();

            if (existingAnnouncement == null)
                throw new NoFoundDataPortalException($"{nameof(SiteResources.Exception_NoRecordsFound).ToSiteResourcesValue()} ({nameof(SiteResources.Announcement).ToSiteResourcesValue()})");

            var branches = await _unitOfWorkPortalDb.GetRepository<Branch>().Entities
                                    .Include(i => i.BranchTranslations)
                                    .Where(p => p.IsActive && !p.IsDeleted).ToListAsync();

            var persons = await _unitOfWorkPortalDb.GetRepository<User>().Entities
                                  .Where(p => p.IsActive && !p.IsDeleted).ToListAsync();
            var result = new AnnouncementDto
            {
                Id = existingAnnouncement.Id,
                BranchIds = existingAnnouncement.BranchIds?.Split(",").Select(p => p.Replace("*", "").Replace("/", "").ToInt()).ToList(),
                BranchNames = new List<string>(),
                AnnouncementPersonIds = existingAnnouncement.AnnouncementPersonIds?.Split(",").Select(p => p.Replace("*", "").Replace("/", "").ToInt()).ToList(),
                AnnouncementPersonNames = new List<string>(),
                DueDate = existingAnnouncement.DueDate,
                Subject = existingAnnouncement.Subject,
                Message = existingAnnouncement.Message,
                CreatedAt = existingAnnouncement.CreatedAt,
                CreatedBy = existingAnnouncement.CreatedBy,
                CreatedByNameSurname = $"{existingAnnouncement.User?.Name} {existingAnnouncement.User?.Surname}",
                IsReadByUser = existingAnnouncement.AnnouncementHistories == null ? false : existingAnnouncement.AnnouncementHistories.Any(p => !p.IsDeleted && p.IsActive && p.UserId == request.UserId)
            };

            if (result.BranchIds != null)
            {
                foreach (var branchId in result.BranchIds)
                {
                    var branch = branches.FirstOrDefault(p => p.Id == branchId);

                    if (branch != null)
                    {
                        var languageId = (int)Contracts.Entities.Enums.Language.English;

                        var branchName = branch.BranchTranslations.Any(p => p.LanguageId == languageId) ?
                                            branch.BranchTranslations.FirstOrDefault(p => p.LanguageId == languageId).Name :
                                            branch.BranchTranslations.FirstOrDefault().Name;

                        if (!string.IsNullOrWhiteSpace(branchName))
                            result.BranchNames.Add(branchName);
                    }
                }
            }
            if (result.AnnouncementPersonIds != null)
            {
                foreach (var personId in result.AnnouncementPersonIds)
                {
                    var person = persons.FirstOrDefault(p => p.Id == personId);

                    if (person != null)
                    {
                        var personName = person.Name + " " + person.Surname;

                        if (!string.IsNullOrWhiteSpace(personName))
                            result.AnnouncementPersonNames.Add(personName);
                    }
                }
            }
            return result;
        }

        public async Task<Pagination<AnnouncementsDto>> GetPaginatedAnnouncementsAsync(PaginatedAnnouncementsRequestDto request)
        {
            var paginationResult = new Pagination<AnnouncementsDto> { Page = request.Pagination.Page, PageSize = request.Pagination.PageSize };

            var stringBranchId = $"*{request.BranchId}/";
            var stringAnnouncementPersonId = $"*{request.UserAuditId}/";

            var queryAnnouncement = _unitOfWorkPortalDb.GetRepository<Announcement>().Entities
                                    .Include(i => i.AnnouncementHistories)
                                    .Where(p => !p.IsDeleted && p.IsActive && p.BranchIds.Contains(stringBranchId) && p.AnnouncementPersonIds.Contains(stringAnnouncementPersonId) || p.AnnouncementPersonIds == "");

            if (request.StartDate.HasValue)
                queryAnnouncement = queryAnnouncement.Where(p => p.DueDate == null || p.DueDate.Value >= request.StartDate.Value);

            paginationResult.TotalItemCount = queryAnnouncement.Count();

            var countries = await _unitOfWorkPortalDb.GetRepository<Country>().Entities.ToListAsync();

            var branches = await _unitOfWorkPortalDb.GetRepository<Branch>().Entities
                                    .Include(i => i.BranchTranslations)
                                    .Where(p => p.IsActive && !p.IsDeleted).ToListAsync();

            var persons = await _unitOfWorkPortalDb.GetRepository<User>().Entities
                                   .Where(p => p.IsActive && !p.IsDeleted).ToListAsync();

            var announcementList = new List<Announcement>();

            if (request.Pagination != null && request.Pagination.Page > 0 && request.Pagination.PageSize > 0)
            {
                announcementList = await queryAnnouncement
                    .OrderByDescending(o => o.Id)
                    .Skip((request.Pagination.Page - 1) * request.Pagination.PageSize)
                    .Take(request.Pagination.PageSize)
                    .ToListAsync();
            }
            else
            {
                announcementList = await queryAnnouncement
                    .OrderByDescending(o => o.Id)
                    .ToListAsync();
            }

            var newAnnouncements = new AnnouncementsDto() { Announcements = new List<AnnouncementDto>() };

            foreach (var announcement in announcementList)
            {
                var newAnnouncement = new AnnouncementDto
                {
                    Id = announcement.Id,
                    BranchIds = announcement.BranchIds?.Split(",").Select(p => p.Replace("*", "").Replace("/", "").ToInt()).ToList(),
                    BranchNames = new List<string>(),
                    AnnouncementPersonIds = announcement.AnnouncementPersonIds?.Split(",").Select(p => p.Replace("*", "").Replace("/", "").ToInt()).ToList(),
                    AnnouncementPersonNames = new List<string>(),
                    DueDate = announcement.DueDate,
                    Subject = announcement.Subject,
                    Message = announcement.Message,
                    IsReadByUser = announcement.AnnouncementHistories == null ? false : announcement.AnnouncementHistories.Any(p => !p.IsDeleted && p.IsActive && p.UserId == request.UserId)
                };

                if (newAnnouncement.BranchIds != null)
                {
                    foreach (var branchId in newAnnouncement.BranchIds)
                    {
                        var branch = branches.FirstOrDefault(p => p.Id == branchId);

                        if (branch != null)
                        {
                            var branchName = branch.BranchTranslations.Any(p => p.LanguageId == request.LanguageId) ?
                                                            branch.BranchTranslations.FirstOrDefault(p => p.LanguageId == request.LanguageId).Name :
                                                            branch.BranchTranslations.FirstOrDefault().Name;

                            if (!string.IsNullOrWhiteSpace(branchName))
                                newAnnouncement.BranchNames.Add(branchName);
                        }
                    }
                }
                if (newAnnouncement.AnnouncementPersonIds != null)
                {
                    foreach (var personId in newAnnouncement.AnnouncementPersonIds)
                    {
                        var person = persons.FirstOrDefault(p => p.Id == personId);

                        if (person != null)
                        {
                            var personName = person.Name + " " + person.Surname;

                            if (!string.IsNullOrWhiteSpace(personName))
                                newAnnouncement.AnnouncementPersonNames.Add(personName);
                        }
                    }
                }
                newAnnouncements.Announcements.Add(newAnnouncement);
            }

            paginationResult.Items.Add(newAnnouncements);
            return paginationResult;
        }

        public async Task<UpdateResponseDto> MarkAnnouncementAsReadAsync(MarkAnnouncementAsReadRequestDto request)
        {
            var existingAnnouncement = await _unitOfWorkPortalDb.GetRepository<Announcement>().Entities
                                        .Include(i => i.AnnouncementHistories)
                                        .Where(p => !p.IsDeleted && p.IsActive && p.Id == request.Id)
                                        .FirstOrDefaultAsync();

            if (existingAnnouncement == null)
                throw new NoFoundDataPortalException($"{nameof(SiteResources.Exception_NoRecordsFound).ToSiteResourcesValue(request.LanguageId)} ({nameof(SiteResources.Announcement).ToSiteResourcesValue(request.LanguageId)})");

            if (existingAnnouncement.AnnouncementHistories != null && existingAnnouncement.AnnouncementHistories.Any(p => !p.IsDeleted && p.IsActive && p.UserId == request.UserId))
                throw new AlreadyExistsDataPortalException($"{nameof(SiteResources.Exception_ExistingRecord).ToSiteResourcesValue(request.LanguageId)} ({nameof(SiteResources.AnnouncementHistory).ToSiteResourcesValue(request.LanguageId)})");

            var announcementHistory = new AnnouncementHistory
            {
                AnnouncementId = request.Id,
                UserId = request.UserId,
                CreatedAt = DateTime.UtcNow
            };

            existingAnnouncement.AnnouncementHistories.Add(announcementHistory);

            _unitOfWorkPortalDb.GetRepository<Announcement>().Update(existingAnnouncement);
            await _unitOfWorkPortalDb.SaveChangesAsync();

            return new UpdateResponseDto { Result = true };
        }

        public async Task<Pagination<AnnouncementHistoriesDto>> GetPaginatedAnnouncementHistoriesAsync(PaginatedAnnouncementHistoriesRequestDto request)
        {
            var paginationResult = new Pagination<AnnouncementHistoriesDto> { Page = request.Pagination.Page, PageSize = request.Pagination.PageSize };

            var queryAnnouncementHistory = _unitOfWorkPortalDb.GetRepository<AnnouncementHistory>().Entities
                                            .Include(i => i.User)
                                            .Where(p => !p.IsDeleted && p.IsActive && p.AnnouncementId == request.AnnouncementId);

            paginationResult.TotalItemCount = queryAnnouncementHistory.Count();

            var announcementHistoryList = new List<AnnouncementHistory>();

            if (request.Pagination != null && request.Pagination.Page > 0 && request.Pagination.PageSize > 0)
            {
                announcementHistoryList = await queryAnnouncementHistory
                    .OrderByDescending(o => o.Id)
                    .Skip((request.Pagination.Page - 1) * request.Pagination.PageSize)
                    .Take(request.Pagination.PageSize)
                    .ToListAsync();
            }
            else
            {
                announcementHistoryList = await queryAnnouncementHistory
                    .OrderByDescending(o => o.Id)
                    .ToListAsync();
            }

            var newAnnouncementHistories = new AnnouncementHistoriesDto() { AnnouncementHistories = new List<AnnouncementHistoryDto>() };

            foreach (var announcementHistory in announcementHistoryList)
            {
                var newAnnouncementHistory = new AnnouncementHistoryDto
                {
                    Name = announcementHistory.User.Name,
                    Surname = announcementHistory.User.Surname,
                    CreatedAt = announcementHistory.CreatedAt
                };

                newAnnouncementHistories.AnnouncementHistories.Add(newAnnouncementHistory);
            }

            paginationResult.Items.Add(newAnnouncementHistories);
            return paginationResult;
        }

        #endregion

        #region Bank

        public async Task<AddResponseDto> AddBankPosAsync(AddBankPosRequestDto request)
        {
            var newBankPos = new BankPos()
            {
                BankId = request.BankId,
                EncryptionKey = request.EncryptionKey,
                Is3dEnabled = request.Is3dEnabled,
                MerchantCode = request.MerchantCode,
                Password = request.Password,
                Payment3dConfirmUrl = request.Payment3dConfirmUrl,
                Payment3dUrl = request.Payment3dUrl,
                PaymentUrl = request.PaymentUrl,
                StoreKey = request.StoreKey,
                TerminalCode = request.TerminalCode,
                Username = request.Username,
                CreatedBy = request.UserAuditId,
                CreatedAt = DateTime.Now
            };

            await _unitOfWorkPortalDb.GetRepository<BankPos>().AddAsync(newBankPos);
            await _unitOfWorkPortalDb.SaveChangesAsync();

            return new AddResponseDto { Id = newBankPos.Id };
        }

        public async Task<UpdateResponseDto> UpdateBankPosAsync(UpdateBankPosRequestDto request)
        {
            var existingBankPos = await _unitOfWorkPortalDb.GetRepository<BankPos>().Entities
                                        .Where(p => !p.IsDeleted && p.Id == request.Id)
                                        .FirstOrDefaultAsync();

            if (existingBankPos == null)
                throw new NoFoundDataPortalException($"{nameof(SiteResources.Exception_NoRecordsFound).ToSiteResourcesValue(request.LanguageId)} ({nameof(SiteResources.BankPos).ToSiteResourcesValue(request.LanguageId)})");

            existingBankPos.BankId = request.BankId;
            existingBankPos.TerminalCode = request.TerminalCode;
            existingBankPos.MerchantCode = request.MerchantCode;
            existingBankPos.Username = request.Username;
            existingBankPos.Password = request.Password;
            existingBankPos.StoreKey = request.StoreKey;
            existingBankPos.EncryptionKey = request.EncryptionKey;
            existingBankPos.PaymentUrl = request.PaymentUrl;
            existingBankPos.Payment3dUrl = request.Payment3dUrl;
            existingBankPos.Payment3dConfirmUrl = request.Payment3dConfirmUrl;
            existingBankPos.Is3dEnabled = request.Is3dEnabled;
            existingBankPos.IsActive = request.IsActive;
            existingBankPos.UpdatedBy = request.UserAuditId;
            existingBankPos.UpdatedAt = DateTime.Now;

            _unitOfWorkPortalDb.GetRepository<BankPos>().Update(existingBankPos);
            await _unitOfWorkPortalDb.SaveChangesAsync();

            return new UpdateResponseDto { Result = true };
        }

        public async Task<DeleteResponseDto> DeleteBankPosAsync(int id)
        {
            var existingBankPos = await _unitOfWorkPortalDb.GetRepository<BankPos>().Entities
                                        .Where(p => !p.IsDeleted && p.Id == id)
                                        .FirstOrDefaultAsync();

            if (existingBankPos == null)
                throw new NoFoundDataPortalException($"{nameof(SiteResources.Exception_NoRecordsFound).ToSiteResourcesValue()} ({nameof(SiteResources.BankPos).ToSiteResourcesValue()})");

            _unitOfWorkPortalDb.GetRepository<BankPos>().MarkAsDeleted(existingBankPos.Id);
            await _unitOfWorkPortalDb.SaveChangesAsync();

            return new DeleteResponseDto { Result = true };
        }

        public async Task<BankPosDto> GetBankPosAsync(int id)
        {
            var existingBankPos = await _unitOfWorkPortalDb.GetRepository<BankPos>().Entities
                                        .Where(p => !p.IsDeleted && p.Id == id)
                                        .FirstOrDefaultAsync();

            if (existingBankPos == null)
                throw new NoFoundDataPortalException($"{nameof(SiteResources.Exception_NoRecordsFound).ToSiteResourcesValue()} ({nameof(SiteResources.BankPos).ToSiteResourcesValue()})");

            return new BankPosDto()
            {
                BankId = existingBankPos.BankId,
                EncryptionKey = existingBankPos.EncryptionKey,
                Id = existingBankPos.Id,
                Is3dEnabled = existingBankPos.Is3dEnabled,
                IsActive = existingBankPos.IsActive,
                MerchantCode = existingBankPos.MerchantCode,
                Password = existingBankPos.Password,
                Payment3dConfirmUrl = existingBankPos.Payment3dConfirmUrl,
                Payment3dUrl = existingBankPos.Payment3dUrl,
                PaymentUrl = existingBankPos.PaymentUrl,
                StoreKey = existingBankPos.StoreKey,
                TerminalCode = existingBankPos.TerminalCode,
                Username = existingBankPos.Username

            };
        }

        public async Task<Pagination<BankPosesDto>> GetPaginatedBankPosesAsync(PaginatedBankPosRequestDto request)
        {
            var paginationResult = new Pagination<BankPosesDto> { Page = request.Pagination.Page, PageSize = request.Pagination.PageSize };

            var queryBankPos = _unitOfWorkPortalDb.GetRepository<BankPos>().Entities
                                    .Where(p => !p.IsDeleted);

            paginationResult.TotalItemCount = queryBankPos.Count();

            var bankPosList = new List<BankPos>();

            if (request.Pagination != null && request.Pagination.Page > 0 && request.Pagination.PageSize > 0)
            {
                bankPosList = await queryBankPos
                    .OrderByDescending(o => o.Id)
                    .Skip((request.Pagination.Page - 1) * request.Pagination.PageSize)
                    .Take(request.Pagination.PageSize)
                    .ToListAsync();
            }
            else
            {
                bankPosList = await queryBankPos
                    .OrderByDescending(o => o.Id)
                    .ToListAsync();
            }

            var newBankPoses = new BankPosesDto() { BankPoses = new List<BankPosDto>() };

            foreach (var bankPos in bankPosList)
            {
                var newbankPos = new BankPosDto
                {
                    Id = bankPos.Id,
                    BankId = bankPos.BankId,
                    EncryptionKey = bankPos.EncryptionKey,
                    Is3dEnabled = bankPos.Is3dEnabled,
                    IsActive = bankPos.IsActive,
                    MerchantCode = bankPos.MerchantCode,
                    Password = bankPos.Password,
                    Payment3dConfirmUrl = bankPos.Payment3dConfirmUrl,
                    Payment3dUrl = bankPos.Payment3dUrl,
                    PaymentUrl = bankPos.PaymentUrl,
                    StoreKey = bankPos.StoreKey,
                    TerminalCode = bankPos.TerminalCode,
                    Username = bankPos.Username
                };

                newBankPoses.BankPoses.Add(newbankPos);
            }

            paginationResult.Items.Add(newBankPoses);
            return paginationResult;
        }

        public async Task<UpdateResponseDto> AddUpdateBankPosInstallmentAsync(AddUpdateBankPosInstallmentRequestDto request)
        {
            foreach (var itemBankPosInstallment in request.BankPosInstallments)
            {
                if (itemBankPosInstallment.Id.HasValue)
                {
                    var existingBankInstallment = await _unitOfWorkPortalDb.GetRepository<BankPosInstallment>().Entities
                                                    .Where(p => !p.IsDeleted && p.Id == itemBankPosInstallment.Id.Value)
                                                    .FirstOrDefaultAsync();

                    if (existingBankInstallment != null)
                    {
                        existingBankInstallment.BankPosId = itemBankPosInstallment.BankPosId;
                        existingBankInstallment.ChannelId = request.ChannelId;
                        existingBankInstallment.CommissionRate = itemBankPosInstallment.CommissionRate;
                        existingBankInstallment.InstallmentCount = itemBankPosInstallment.InstallmentCount;
                        existingBankInstallment.IsActive = itemBankPosInstallment.IsActive;
                        existingBankInstallment.UpdatedBy = request.UserAuditId;
                        existingBankInstallment.UpdatedAt = DateTime.Now;

                        _unitOfWorkPortalDb.GetRepository<BankPosInstallment>().Update(existingBankInstallment);
                    }
                }
                else
                {
                    var newBankPosInstallment = new BankPosInstallment()
                    {
                        BankPosId = itemBankPosInstallment.BankPosId,
                        ChannelId = request.ChannelId,
                        CommissionRate = itemBankPosInstallment.CommissionRate,
                        InstallmentCount = itemBankPosInstallment.InstallmentCount,
                        IsActive = itemBankPosInstallment.IsActive,
                        CreatedBy = request.UserAuditId,
                        CreatedAt = DateTime.Now
                    };

                    _unitOfWorkPortalDb.GetRepository<BankPosInstallment>().Add(newBankPosInstallment);
                }
            }

            await _unitOfWorkPortalDb.SaveChangesAsync();

            return new UpdateResponseDto { Result = true };
        }

        public async Task<BankPosInstallmentDto> GetBankPosInstallmentAsync(BankPosInstallmentRequestDto request)
        {
            var existingBankInstallments = await _unitOfWorkPortalDb.GetRepository<BankPosInstallment>().Entities
                                                    .Where(p => !p.IsDeleted && p.ChannelId == request.ChannelId)
                                                    .ToListAsync();

            return new BankPosInstallmentDto()
            {
                ChannelId = request.ChannelId,
                BankPosInstallments = existingBankInstallments.Select(p => new BankPosInstallmentDto.BankPosInstallment()
                {
                    BankPosId = p.BankPosId,
                    CommissionRate = p.CommissionRate,
                    Id = p.Id,
                    InstallmentCount = p.InstallmentCount,
                    IsActive = p.IsActive
                }).AsEnumerable()
            };
        }

        #endregion

        #region Inventory

        public async Task<AddResponseDto> AddInventoryAsync(AddInventoryRequestDto request)
        {
            var newInventory = new Inventory()
            {
                InventoryTypeId = request.InventoryTypeId,
                Name = request.Name,
                Brand = request.Brand,
                Model = request.Model,
                SerialNumber = request.SerialNumber,
                SDKAddress = request.SDKAddress,
                SDKVersion = request.SDKVersion,
                StatusId = request.StatusId,
                UpdatedAt = request.UpdatedAt,
                Definition = request.Definition
            };

            await _unitOfWorkPortalDb.GetRepository<Inventory>().AddAsync(newInventory);
            await _unitOfWorkPortalDb.SaveChangesAsync();

            return new AddResponseDto { Id = newInventory.Id };
        }

        public async Task<UpdateResponseDto> UpdateInventoryAsync(UpdateInventoryRequestDto request)
        {
            var existingInventory = await _unitOfWorkPortalDb.GetRepository<Inventory>().Entities
                                        .Where(p => !p.IsDeleted && p.Id == request.Id)
                                        .FirstOrDefaultAsync();
            if (existingInventory == null)
                throw new NoFoundDataPortalException($"{nameof(SiteResources.Exception_NoRecordsFound).ToSiteResourcesValue(request.LanguageId)} ({nameof(SiteResources.Inventory).ToSiteResourcesValue(request.LanguageId)})");

            existingInventory.InventoryTypeId = request.InventoryTypeId;
            existingInventory.Name = request.Name;
            existingInventory.Brand = request.Brand;
            existingInventory.Model = request.Model;
            existingInventory.SerialNumber = request.SerialNumber;
            existingInventory.SDKAddress = request.SDKAddress;
            existingInventory.SDKVersion = request.SDKVersion;
            existingInventory.StatusId = request.StatusId;
            existingInventory.UpdatedAt = request.UpdatedAt;
            existingInventory.Definition = request.Definition;
            existingInventory.IsActive = request.IsActive;

            _unitOfWorkPortalDb.GetRepository<Inventory>().Update(existingInventory);
            await _unitOfWorkPortalDb.SaveChangesAsync();

            return new UpdateResponseDto { Result = true };
        }

        public async Task<DeleteResponseDto> DeleteInventoryAsync(int id)
        {
            var existingInventory = await _unitOfWorkPortalDb.GetRepository<Inventory>().Entities
                                        .Where(p => !p.IsDeleted && p.Id == id)
                                        .FirstOrDefaultAsync();

            if (existingInventory == null)
                throw new NoFoundDataPortalException($"{nameof(SiteResources.Exception_NoRecordsFound).ToSiteResourcesValue()} ({nameof(SiteResources.Inventory).ToSiteResourcesValue()})");

            _unitOfWorkPortalDb.GetRepository<Inventory>().MarkAsDeleted(existingInventory.Id);
            await _unitOfWorkPortalDb.SaveChangesAsync();

            return new DeleteResponseDto { Result = true };
        }

        public async Task<InventoryResponseDto> GetInventoryAsync(int id)
        {
            var existingInventory = await _unitOfWorkPortalDb.GetRepository<Inventory>().Entities
                                        .Where(p => !p.IsDeleted && p.Id == id)
                                        .FirstOrDefaultAsync();

            if (existingInventory == null)
                throw new NoFoundDataPortalException($"{nameof(SiteResources.Exception_NoRecordsFound).ToSiteResourcesValue()} ({nameof(SiteResources.Inventory).ToSiteResourcesValue()})");

            return new InventoryResponseDto()
            {
                Id = existingInventory.Id,
                InventoryTypeId = existingInventory.InventoryTypeId,
                Name = existingInventory.Name,
                Brand = existingInventory.Brand,
                Model = existingInventory.Model,
                SerialNumber = existingInventory.SerialNumber,
                SDKAddress = existingInventory.SDKAddress,
                SDKVersion = existingInventory.SDKVersion,
                StatusId = existingInventory.StatusId,
                UpdatedAt = existingInventory.UpdatedAt,
                Definition = existingInventory.Definition,
                IsActive = existingInventory.IsActive
            };
        }

        public async Task<Pagination<PaginatedInventoriesResponseDto>> GetPaginatedInventoriesAsync(GetPaginatedInventoriesRequestDto request)
        {
            var paginationResult = new Pagination<PaginatedInventoriesResponseDto> { Page = request.Pagination.Page, PageSize = request.Pagination.PageSize };

            var queryInventory = _unitOfWorkPortalDb.GetRepository<Inventory>().Entities
                                    .Where(p => !p.IsDeleted);

            if (!string.IsNullOrEmpty(request.Name))
                queryInventory = queryInventory.Where(p => p.Name.Contains(request.Name));

            if (!string.IsNullOrEmpty(request.Brand))
                queryInventory = queryInventory.Where(p => p.Brand.Contains(request.Brand));

            if (!string.IsNullOrEmpty(request.Model))
                queryInventory = queryInventory.Where(p => p.Model.Contains(request.Model));

            if (!string.IsNullOrEmpty(request.SerialNumber))
                queryInventory = queryInventory.Where(p => p.SerialNumber.Contains(request.SerialNumber));

            paginationResult.TotalItemCount = queryInventory.Count();

            var inventoryList = new List<Inventory>();

            if (request.Pagination != null && request.Pagination.Page > 0 && request.Pagination.PageSize > 0)
            {
                inventoryList = await queryInventory
                    .OrderByDescending(o => o.Id)
                    .Skip((request.Pagination.Page - 1) * request.Pagination.PageSize)
                    .Take(request.Pagination.PageSize)
                    .ToListAsync();
            }
            else
            {
                inventoryList = await queryInventory
                    .OrderByDescending(o => o.Id)
                    .ToListAsync();
            }

            var newInventories = new PaginatedInventoriesResponseDto() { Inventories = new List<InventoryResponseDto>() };

            foreach (var inventory in inventoryList)
            {
                var newInventory = new InventoryResponseDto
                {
                    Id = inventory.Id,
                    InventoryTypeId = inventory.InventoryTypeId,
                    Name = inventory.Name,
                    Brand = inventory.Brand,
                    Model = inventory.Model,
                    SerialNumber = inventory.SerialNumber,
                    SDKAddress = inventory.SDKAddress,
                    SDKVersion = inventory.SDKVersion,
                    StatusId = inventory.StatusId,
                    UpdatedAt = inventory.UpdatedAt,
                    Definition = inventory.Definition,
                    IsActive = inventory.IsActive,
                };

                newInventories.Inventories.Add(newInventory);
            }

            paginationResult.Items.Add(newInventories);
            return paginationResult;
        }

        #endregion

        #region ClientDevice

        public async Task<AddResponseDto> AddClientDeviceAsync(AddClientDeviceRequestDto request)
        {
            var newClientDevice = new ClientDevice()
            {
                BranchId = request.BranchId,
                IsActive = request.IsActive,
                Name = request.Name,
                Description = request.Description,
                CreatedBy = request.UserAuditId
            };

            await _unitOfWorkPortalDb.GetRepository<ClientDevice>().AddAsync(newClientDevice);
            await _unitOfWorkPortalDb.SaveChangesAsync();

            return new AddResponseDto { Id = newClientDevice.Id };
        }

        public async Task<UpdateResponseDto> UpdateClientDeviceAsync(UpdateClientDeviceRequestDto request)
        {
            var existingClientDevice = await _unitOfWorkPortalDb.GetRepository<ClientDevice>().Entities
                                        .Where(p => !p.IsDeleted && p.Id == request.Id)
                                        .FirstOrDefaultAsync();

            if (existingClientDevice == null)
                throw new NoFoundDataPortalException($"{nameof(SiteResources.Exception_NoRecordsFound).ToSiteResourcesValue(request.LanguageId)} ({nameof(SiteResources.ClientDevice).ToSiteResourcesValue(request.LanguageId)})");

            existingClientDevice.Description = request.Description;
            existingClientDevice.IsActive = request.IsActive;
            existingClientDevice.BranchId = request.BranchId;
            existingClientDevice.Name = request.Name;
            existingClientDevice.UpdatedBy = request.UserAuditId;

            _unitOfWorkPortalDb.GetRepository<ClientDevice>().Update(existingClientDevice);
            await _unitOfWorkPortalDb.SaveChangesAsync();

            return new UpdateResponseDto { Result = true };
        }

        public async Task<DeleteResponseDto> DeleteClientDeviceAsync(int id)
        {
            var existingClientDevice = await _unitOfWorkPortalDb.GetRepository<ClientDevice>().Entities
                                        .Where(p => !p.IsDeleted && p.Id == id)
                                        .FirstOrDefaultAsync();

            if (existingClientDevice == null)
                throw new NoFoundDataPortalException($"{nameof(SiteResources.Exception_NoRecordsFound).ToSiteResourcesValue()} ({nameof(SiteResources.ClientDevice).ToSiteResourcesValue()})");

            _unitOfWorkPortalDb.GetRepository<ClientDevice>().MarkAsDeleted(existingClientDevice.Id);
            await _unitOfWorkPortalDb.SaveChangesAsync();

            return new DeleteResponseDto { Result = true };
        }

        public async Task<ClientDeviceResponseDto> GetClientDeviceAsync(int id)
        {
            var existingClientDevice = await _unitOfWorkPortalDb.GetRepository<ClientDevice>().Entities
                                        .Where(p => !p.IsDeleted && p.Id == id)
                                        .FirstOrDefaultAsync();

            if (existingClientDevice == null)
                throw new NoFoundDataPortalException($"{nameof(SiteResources.Exception_NoRecordsFound).ToSiteResourcesValue()} ({nameof(SiteResources.ClientDevice).ToSiteResourcesValue()})");

            return new ClientDeviceResponseDto()
            {
                Id = existingClientDevice.Id,
                BranchId = existingClientDevice.BranchId,
                Description = existingClientDevice.Description,
                Name = existingClientDevice.Name,
                IsActive = existingClientDevice.IsActive
            };
        }

        public async Task<Pagination<PaginatedClientDevicesResponseDto>> GetPaginatedClientDevicesAsync(GetPaginatedClientDevicesRequestDto request)
        {
            var paginationResult = new Pagination<PaginatedClientDevicesResponseDto> { Page = request.Pagination.Page, PageSize = request.Pagination.PageSize };

            var queryClientDevices = _unitOfWorkPortalDb.GetRepository<ClientDevice>().Entities
                                    .Where(p => !p.IsDeleted);

            if (!string.IsNullOrEmpty(request.Name))
                queryClientDevices = queryClientDevices.Where(p => p.Name.Contains(request.Name));

            if (request.BranchId.HasValue)
                queryClientDevices = queryClientDevices.Where(p => p.BranchId == request.BranchId);

            paginationResult.TotalItemCount = queryClientDevices.Count();

            var clientDeviceList = new List<ClientDevice>();

            if (request.Pagination != null && request.Pagination.Page > 0 && request.Pagination.PageSize > 0)
            {
                clientDeviceList = await queryClientDevices
                    .OrderByDescending(o => o.Id)
                    .Skip((request.Pagination.Page - 1) * request.Pagination.PageSize)
                    .Take(request.Pagination.PageSize)
                    .ToListAsync();
            }
            else
            {
                clientDeviceList = await queryClientDevices
                    .OrderByDescending(o => o.Id)
                    .ToListAsync();
            }

            var newClientDevices = new PaginatedClientDevicesResponseDto() { ClientDevices = new List<ClientDeviceResponseDto>() };

            foreach (var clientDevice in clientDeviceList)
            {
                var newClientDevice = new ClientDeviceResponseDto
                {
                    Id = clientDevice.Id,
                    BranchId = clientDevice.BranchId,
                    Description = clientDevice.Description,
                    Name = clientDevice.Name,
                    IsActive = clientDevice.IsActive
                };

                newClientDevices.ClientDevices.Add(newClientDevice);
            }

            paginationResult.Items.Add(newClientDevices);
            return paginationResult;
        }

        public async Task<ClientDeviceInventoryResponseDto> GetClientDeviceInventoryAsync(int clientDeviceId)
        {
            var existingInventories = await _unitOfWorkPortalDb.GetRepository<Inventory>().Entities
                                        .Where(p => !p.IsDeleted && p.IsActive)
                                        .ToListAsync();

            if (existingInventories.Count == 0)
                throw new NoFoundDataPortalException($"{nameof(SiteResources.Exception_NoRecordsFound).ToSiteResourcesValue()} ({nameof(SiteResources.Inventory).ToSiteResourcesValue()})");

            var existingClientDeviceInventories = await _unitOfWorkPortalDb.GetRepository<ClientDeviceInventory>().Entities
                                                    .Where(p => !p.IsDeleted && p.ClientDeviceId == clientDeviceId)
                                                    .ToListAsync();

            return new ClientDeviceInventoryResponseDto()
            {
                ClientDeviceInventories = existingInventories.Select(p => new ClientDeviceInventoryResponseDto.ClientDeviceInventory()
                {
                    Id = p.Id,
                    Name = p.Name,
                    IsActive = existingClientDeviceInventories.Any(x => x.InventoryId == p.Id && x.IsActive)
                }).AsEnumerable()
            };
        }

        public async Task<UpdateResponseDto> AddUpdateClientDeviceInventoryAsync(AddUpdateClientDeviceInventoryRequestDto request)
        {
            var entityClientDevice = await _unitOfWorkPortalDb.GetRepository<ClientDevice>().Entities
                                   .Where(p => p.Id == request.ClientDeviceId && !p.IsDeleted)
                                   .FirstOrDefaultAsync();

            if (entityClientDevice == null)
                throw new NoFoundDataPortalException($"{nameof(SiteResources.Exception_NoRecordsFound).ToSiteResourcesValue(request.LanguageId)} ({nameof(SiteResources.ClientDevice).ToSiteResourcesValue(request.LanguageId)})");

            var entityClientDeviceInventories = await _unitOfWorkPortalDb.GetRepository<ClientDeviceInventory>().Entities
                                                .Where(p => p.ClientDeviceId == request.ClientDeviceId
                                                            && !p.IsDeleted
                                                ).ToListAsync();

            var entityClientDeviceInventoriesDeactivated = entityClientDeviceInventories.Where(p => request.ClientDeviceInventories
                                                                .Where(x => !x.IsActive)
                                                                .Any(x => x.InventoryId == p.InventoryId)).AsEnumerable();

            // Deactivate existing client device inventories which request active = false
            if (entityClientDeviceInventoriesDeactivated.Count() > 0)
            {
                entityClientDeviceInventoriesDeactivated.ToList().ForEach(p => p.IsActive = false);

                _unitOfWorkPortalDb.GetRepository<ClientDeviceInventory>().UpdateRange(entityClientDeviceInventoriesDeactivated);

                await _unitOfWorkPortalDb.SaveChangesAsync();
            }

            var entityClientDeviceInventoriesActivated = entityClientDeviceInventories.Where(p => request.ClientDeviceInventories
                                                        .Where(x => x.IsActive)
                                                        .Any(x => x.InventoryId == p.InventoryId)).AsEnumerable();

            // Activate existing client device inventories which request active = true
            if (entityClientDeviceInventoriesActivated.Count() > 0)
            {
                entityClientDeviceInventoriesActivated.ToList().ForEach(p => p.IsActive = true);

                _unitOfWorkPortalDb.GetRepository<ClientDeviceInventory>().UpdateRange(entityClientDeviceInventoriesActivated);

                await _unitOfWorkPortalDb.SaveChangesAsync();
            }

            var newClientDeviceInventories = request.ClientDeviceInventories
                                                .Where(p => p.IsActive && !entityClientDeviceInventories.Exists(m => m.InventoryId == p.InventoryId))
                                                .Select(p => new ClientDeviceInventory
                                                {
                                                    InventoryId = p.InventoryId,
                                                    ClientDeviceId = request.ClientDeviceId,
                                                    IsActive = p.IsActive,
                                                    CreatedBy = request.UserAuditId,
                                                    CreatedAt = DateTime.Now
                                                }).AsEnumerable();

            // Add new client device inventories which do not exist
            if (newClientDeviceInventories.Count() > 0)
            {
                await _unitOfWorkPortalDb.GetRepository<ClientDeviceInventory>().AddRangeAsync(newClientDeviceInventories);
                await _unitOfWorkPortalDb.SaveChangesAsync();
            }

            return new UpdateResponseDto()
            {
                Result = true
            };
        }

        #endregion

        #region ApplicationSale

        public async Task<ApplicationSalesResponseDto> GetApplicationSalesAsync(GetApplicationSalesRequestDto request)
        {
            var applicationSales = await _unitOfWorkPortalDb.GetRepository<ApplicationSale>().Entities
                    .Where(p => p.IsActive)
                .Select(p => new
                {
                    p.ExtraFeeId,
                    p.Status
                }).AsNoTracking().ToListAsync();

            if (applicationSales == null)
                throw new NoFoundDataPortalException($"{nameof(SiteResources.Exception_NoRecordsFound).ToSiteResourcesValue(request.LanguageId)}");

            var extraFeeIds = applicationSales.Select(p => p.ExtraFeeId).Distinct().ToList();

            var extraFees = await _unitOfWorkPortalDb.GetRepository<ExtraFeeTranslation>().Entities
                    .Include(i => i.ExtraFee)
                    .Where(p => p.IsActive && !p.IsDeleted && extraFeeIds.Contains(p.ExtraFeeId) && p.LanguageId == request.LanguageId)
                .Select(p => new
                {
                    p.ExtraFeeId,
                    p.ExtraFee.FlagId,
                    p.Name
                }).ToListAsync();

            var applicationSaleListDto = new ApplicationSalesResponseDto();

            foreach (var extraFeeId in extraFeeIds)
            {
                applicationSaleListDto.ApplicationSales.Add(new ApplicationSaleResponseDto
                {
                    Id = GetExtraFeeTypeByFlag(extraFees.Find(p => p.ExtraFeeId == extraFeeId)?.FlagId.ToString()),
                    Name = extraFees.Find(p => p.ExtraFeeId == extraFeeId)?.Name,
                    AvailableCount = applicationSales.Count(p => p.ExtraFeeId == extraFeeId && p.Status == (byte)ApplicationSaleStatus.Available),
                    ReservedCount = applicationSales.Count(p => p.ExtraFeeId == extraFeeId && p.Status == (byte)ApplicationSaleStatus.Reserved),
                    UsedCount = applicationSales.Count(p => p.ExtraFeeId == extraFeeId && p.Status == (byte)ApplicationSaleStatus.Used)
                });
            }

            return applicationSaleListDto;
        }


        public async Task<UploadEsimResponseDto> UploadEsimAsync(UploadEsimRequestDto request)
        {
            var folder = GetEsimFolder(request.TypeId);
            var bucket = _appSettings.EsimBucket;

            if (folder == "")
                throw new NoFoundDataPortalException($"{nameof(SiteResources.Exception_TypeNotFound).ToSiteResourcesValue(request.LanguageId)}");

            var feeFlagId = GetEsimFee(request.TypeId);

            if (feeFlagId == "")
                throw new NoFoundDataPortalException($"{nameof(SiteResources.Exception_FeeNotFoundFromFlag).ToSiteResourcesValue(request.LanguageId)}");

            var fee = await _unitOfWorkPortalDb.GetRepository<ExtraFee>().Entities.Where(w => w.FlagId == Guid.Parse(feeFlagId)).FirstOrDefaultAsync();

            if(fee == null)
                throw new NoFoundDataPortalException($"{nameof(SiteResources.Exception_FeeNotFound).ToSiteResourcesValue(request.LanguageId)}");

            var feeId = fee.Id;

            var stream = new MemoryStream(request.FileContent);

            var unuploadList = new List<AddDigitalSimResponseDto>();
            var filesToAdd = new List<ApplicationSale>();

            if (request.FileExtension == ".zip")
            {
                var zip = new ZipArchive(stream, ZipArchiveMode.Read);
                var files = zip.Entries.Where(w => w.FullName.ToLower().EndsWith(".png"));

                if (!files.Any())
                    throw new NoFoundDataPortalException($"{nameof(SiteResources.Exception_FileNotFoundInCollection).ToSiteResourcesValue(request.LanguageId)}");

                var existingFiles = await _unitOfWorkPortalDb.GetRepository<ApplicationSale>().Entities.Where(w => w.ExtraFeeId == feeId).Select(s => s.SaleId.ToString()).ToListAsync();

                foreach (var file in files)
                {
                    var uniqueId = file.Name[..file.Name.LastIndexOf('.')].ToLower();

                    if (existingFiles.Any(a => a == uniqueId))
                        unuploadList.Add(new AddDigitalSimResponseDto { FileName = file.Name, Description = $"{nameof(SiteResources.Exception_AlreadyExist).ToSiteResourcesValue(request.LanguageId)}" });
                    else
                    {
                        var isFileExist = await _fileStorage.ExistsAsync(bucket, $"{folder}/{file.Name.ToLower()}");
                        if (isFileExist)
                            unuploadList.Add(new AddDigitalSimResponseDto { FileName = file.Name, Description = $"{nameof(SiteResources.Exception_AlreadyExist).ToSiteResourcesValue(request.LanguageId)}" });

                        else
                        {
                            var fileStream = new MemoryStream();
                            using (var stream1 = file.Open())
                            {
                                stream1.CopyTo(fileStream);
                            }

                            var uploadResult = await _fileStorage.SaveFileAsync(bucket, $"{folder}/{file.Name.ToLower()}", fileStream, "");
                            if (!uploadResult)
                                unuploadList.Add(new AddDigitalSimResponseDto { FileName = file.Name, Description = $"{nameof(SiteResources.Exception_UploadError).ToSiteResourcesValue(request.LanguageId)}" });

                            else
                                filesToAdd.Add(new ApplicationSale
                                {
                                    ExtraFeeId = feeId,
                                    Type = (byte)request.TypeId,
                                    SaleId = Convert.ToInt64(uniqueId),
                                    Status = 1,
                                    IsActive = true,
                                    IsDeleted = false,
                                    CreatedAt = DateTime.Now
                                });
                        }
                    }
                }
            }
            else
            {
                using (var archive = RarArchive.Open(stream))
                {
                    var entries = archive.Entries.Where(s => s.Key.ToLower().EndsWith(".png"));

                    if (!entries.Any())
                        throw new NoFoundDataPortalException($"{nameof(SiteResources.Exception_FileNotFoundInCollection).ToSiteResourcesValue(request.LanguageId)}");

                    var existingFiles = await _unitOfWorkPortalDb.GetRepository<ApplicationSale>().Entities.Where(w => w.ExtraFeeId == feeId).Select(s => s.SaleId.ToString()).ToListAsync();

                    foreach (var file in entries)
                    {
                        string fileName = System.IO.Path.GetFileName(file.Key);
                        var uniqueId = fileName[..fileName.LastIndexOf('.')].ToLower();

                        if (existingFiles.Any(a => a == uniqueId))
                            unuploadList.Add(new AddDigitalSimResponseDto { FileName = fileName, Description = $"{nameof(SiteResources.Exception_AlreadyExist).ToSiteResourcesValue(request.LanguageId)}" });
                        else
                        {
                            var isFileExist = await _fileStorage.ExistsAsync(bucket, $"{folder}/{fileName.ToLower()}");
                            if (isFileExist)
                                unuploadList.Add(new AddDigitalSimResponseDto { FileName = fileName, Description = $"{nameof(SiteResources.Exception_AlreadyExist).ToSiteResourcesValue(request.LanguageId)}" });

                            else
                            {
                                var fileStream = new MemoryStream();
                                using (var stream1 = file.OpenEntryStream())
                                {
                                    stream1.CopyTo(fileStream);
                                }

                                var uploadResult = await _fileStorage.SaveFileAsync(bucket, $"{folder}/{fileName.ToLower()}", fileStream, "");
                                if (!uploadResult)
                                    unuploadList.Add(new AddDigitalSimResponseDto { FileName = fileName, Description = $"{nameof(SiteResources.Exception_UploadError).ToSiteResourcesValue(request.LanguageId)}" });

                                else
                                    filesToAdd.Add(new ApplicationSale
                                    {
                                        ExtraFeeId = feeId,
                                        Type = (byte)request.TypeId,
                                        SaleId = Convert.ToInt64(uniqueId),
                                        Status = 1,
                                        IsActive = true,
                                        IsDeleted = false,
                                        CreatedAt = DateTime.Now
                                    });
                            }
                        }
                    }
                }
            }

            if (filesToAdd.Any())
            {
                await _unitOfWorkPortalDb.GetRepository<ApplicationSale>().AddRangeAsync(filesToAdd);
                await _unitOfWorkPortalDb.SaveChangesAsync();
            }

            return new UploadEsimResponseDto
            {
                Result = unuploadList
            };
        }

        private int GetExtraFeeTypeByFlag(string flagId)
        {
            return flagId switch
            {
                "46335b7f-3029-599e-87f7-9802d7d21469" => (int)EsimType.EightGB,
                "cbc41be4-c807-5d37-b0f0-b2dc926c6ad4" => (int)EsimType.TwentyGB,
                "4ee1446a-f4e6-55c8-bdeb-073772f6a4e5" => (int)EsimType.FiftyGB,
                _ => 0,
            };
        }

        private string GetEsimFee(int itemTypeId)
        {
            var extraFeeId = "";

            switch (itemTypeId)
            {
                case 1:
                    extraFeeId = "46335b7f-3029-599e-87f7-9802d7d21469";
                    break;
                case 2:
                    extraFeeId = "cbc41be4-c807-5d37-b0f0-b2dc926c6ad4";
                    break;
                case 3:
                    extraFeeId = "4ee1446a-f4e6-55c8-bdeb-073772f6a4e5";
                    break;
                default:
                    break;
            }
            return extraFeeId;
        }

        private string GetEsimFolder(int itemTypeId)
        {
            var folder = "";
            switch (itemTypeId)
            {
                case 1:
                    folder = "8GB";
                    break;
                case 2:
                    folder = "20GB";
                    break;
                case 3:
                    folder = "50GB";
                    break;
                default:
                    break;
            }
            return folder;
        }

        #endregion

        #region BranchDigitalSignatureDocument

        public async Task<GetDigitalSignatureDocumentsByBranchResponseDto> GetDigitalSignatureDocumentsByBranch(GetDigitalSignatureDocumentsByBranchRequestDto request)
        {
            var entityBranchApplicationCountry = await _unitOfWorkPortalDb.GetRepository<BranchApplicationCountry>().Entities
                .Include(p => p.Branch)
                .ThenInclude(p => p.BranchTranslations)
                .Include(p => p.Country)
                .Where(p => !p.IsDeleted && p.Id == request.BranchApplicationCountryId)
                .FirstOrDefaultAsync();

            if (entityBranchApplicationCountry == null)
                throw new NoFoundDataPortalException($"{nameof(SiteResources.Exception_NoRecordsFound).ToSiteResourcesValue(request.LanguageId)} ({nameof(SiteResources.BranchApplicationCountry).ToSiteResourcesValue(request.LanguageId)})");


            var digitalSignatureDocuments = await _unitOfWorkPortalDb.GetRepository<BranchDigitalSignatureDocument>().Entities
                .Where(w => w.BranchApplicationCountryId == request.BranchApplicationCountryId && !w.IsDeleted)
                .AsNoTracking()
                .ToListAsync();

            var documents = new GetDigitalSignatureDocumentsByBranchResponseDto
            {
                BranchApplicationCountryId = entityBranchApplicationCountry.Id,
                BranchName = entityBranchApplicationCountry.Branch.BranchTranslations.Any(p => p.LanguageId == request.LanguageId) ?
                    entityBranchApplicationCountry.Branch.BranchTranslations.First(p => p.LanguageId == request.LanguageId).Name :
                    entityBranchApplicationCountry.Branch.BranchTranslations.First().Name,
                CountryName = entityBranchApplicationCountry.Country.Name,
                DigitalSignatureDocuments = digitalSignatureDocuments
                    .GroupBy(s => s.DigitalSignatureDocumentId)
                    .Select(g => new GetDigitalSignatureDocumentsByBranchResponseDto.BranchDigitalSignatureDocumentDto
                    {
                        Id = g.First().Id,
                        BranchApplicationCountryId = g.First().BranchApplicationCountryId,
                        DigitalSignatureDocumentId = g.Key,
                        UsesDigitalSignatureDocument = g.First().UsesDigitalSignatureDocument,
                        Languages = g.Select(s => s.LanguageId).ToList()
                    })
                    .ToList()
            };

            return documents;
        }

        public async Task<AddOrUpdateBranchDigitalSignatureDocumentsResponseDto> AddOrUpdateBranchDigitalSignatureDocuments(AddOrUpdateBranchDigitalSignatureDocumentsRequestDto request)
        {
            #region queries

            var isBranchApplicationCountryExist = await CheckIfBranchApplicationCountryExist(request.BranchApplicationCountryId);

            if (!isBranchApplicationCountryExist)
                throw new NoFoundDataPortalException($"{nameof(SiteResources.Exception_NoRecordsFound).ToSiteResourcesValue(request.LanguageId)} ({nameof(SiteResources.BranchApplicationCountry).ToSiteResourcesValue(request.LanguageId)})");

            var existingDocuments = await GetExistingDocuments(request.BranchApplicationCountryId);

            #endregion

            var existingDocumentsDict = existingDocuments.ToDictionary(doc => (doc.DigitalSignatureDocumentId, doc.LanguageId));

            foreach (var digitalSignatureDocument in request.DigitalSignatureDocuments)
            {
                foreach (var languageId in digitalSignatureDocument.Languages)
                {
                    var key = (digitalSignatureDocument.DigitalSignatureDocumentId, languageId);
                    if (existingDocumentsDict.TryGetValue(key, out var existingDocument))
                    {
                        UpdateBranchDigitalSignatureDocument(existingDocument, digitalSignatureDocument);
                    }
                    else
                    {
                        await AddBranchDigitalSignatureDocument(digitalSignatureDocument, request.BranchApplicationCountryId, languageId, request.UserAuditId);
                    }
                }
            }

            DeleteDocumentsForBranchNotInRequest(existingDocuments, request.DigitalSignatureDocuments);

            await _unitOfWorkPortalDb.SaveChangesAsync();

            return new AddOrUpdateBranchDigitalSignatureDocumentsResponseDto
            {
                Result = true
            };
        }

        #region privateMethods

        private async Task<bool> CheckIfBranchApplicationCountryExist(int branchApplicationCountryId) =>
            await _unitOfWorkPortalDb.GetRepository<BranchApplicationCountry>().Entities
                .AnyAsync(f => !f.IsDeleted && f.IsActive && f.Id == branchApplicationCountryId);

        private async Task<List<BranchDigitalSignatureDocument>> GetExistingDocuments(int branchApplicationCountryId) =>
            await _unitOfWorkPortalDb.GetRepository<BranchDigitalSignatureDocument>().Entities
                .Where(w => w.BranchApplicationCountryId == branchApplicationCountryId && !w.IsDeleted && w.IsActive)
                .AsNoTracking()
                .ToListAsync();

        private void UpdateBranchDigitalSignatureDocument(BranchDigitalSignatureDocument existingDocument, AddOrUpdateBranchDigitalSignatureDocumentsRequestDto.AddBranchDigitalSignatureDocumentDto digitalSignatureDocument)
        {

            existingDocument.UsesDigitalSignatureDocument = digitalSignatureDocument.UsesDigitalSignatureDocument;
            _unitOfWorkPortalDb.GetRepository<BranchDigitalSignatureDocument>().Update(existingDocument);
        }

        private async Task AddBranchDigitalSignatureDocument(AddOrUpdateBranchDigitalSignatureDocumentsRequestDto.AddBranchDigitalSignatureDocumentDto digitalSignatureDocument, int branchApplicationCountryId, int languageId, int userAuditId)
        {
            var newDocument = new BranchDigitalSignatureDocument
            {
                BranchApplicationCountryId = branchApplicationCountryId,
                DigitalSignatureDocumentId = digitalSignatureDocument.DigitalSignatureDocumentId,
                UsesDigitalSignatureDocument = digitalSignatureDocument.UsesDigitalSignatureDocument,
                LanguageId = languageId,
                IsActive = true,
                CreatedAt = DateTime.UtcNow,
                CreatedBy = userAuditId
            };
            await _unitOfWorkPortalDb.GetRepository<BranchDigitalSignatureDocument>().AddAsync(newDocument);
        }

        private void DeleteDocumentsForBranchNotInRequest(List<BranchDigitalSignatureDocument> existingDocuments, List<AddOrUpdateBranchDigitalSignatureDocumentsRequestDto.AddBranchDigitalSignatureDocumentDto> requestedDocuments)
        {
            foreach (var existingDocument in existingDocuments)
            {
                if (requestedDocuments.All(a =>
                    a.DigitalSignatureDocumentId != existingDocument.DigitalSignatureDocumentId ||
                    !a.Languages.Contains(existingDocument.LanguageId)))
                {
                    existingDocument.IsDeleted = true;
                    existingDocument.IsActive = false;
                    _unitOfWorkPortalDb.GetRepository<BranchDigitalSignatureDocument>().Update(existingDocument);
                }
            }
        }

        #endregion

        #region BranchShiftHoliday

        public async Task<AddResponseDto> AddBranchShiftHolidayAsync(AddBranchShiftHolidayRequestDto request)
        {

            if (request.StartOfShift > request.EndOfShift || request.StartOfShift > request.StartOfLunch || request.StartOfShift > request.EndOfLunch)
                throw new NoFoundDataPortalException($"{nameof(SiteResources.Exception_StartOfShiftDate).ToSiteResourcesValue()} ({nameof(SiteResources.BranchShift).ToSiteResourcesValue()})");
            if (request.EndOfShift < request.StartOfShift || request.EndOfShift < request.StartOfLunch || request.EndOfShift < request.EndOfLunch)
                throw new NoFoundDataPortalException($"{nameof(SiteResources.Exception_EndOfShiftDate).ToSiteResourcesValue()} ({nameof(SiteResources.BranchShift).ToSiteResourcesValue()})");
            if (request.EndOfLunch < request.StartOfLunch)
                throw new NoFoundDataPortalException($"{nameof(SiteResources.Exception_EndOfLunchDate).ToSiteResourcesValue()} ({nameof(SiteResources.BranchShift).ToSiteResourcesValue()})");


            var entityBranchShiftHoliday = await _unitOfWorkPortalDb.GetRepository<BranchShiftHoliday>().Entities
                .Where(p => !p.IsDeleted && p.BranchApplicationCountryId == request.BranchApplicationCountryId)
                .FirstOrDefaultAsync();

            if (entityBranchShiftHoliday == null)
            {

                var newBranchShiftHoliday = new BranchShiftHoliday()
                {
                    BranchApplicationCountryId = request.BranchApplicationCountryId,
                    StartOfShift = request.StartOfShift,
                    EndOfShift = request.EndOfShift,
                    StartOfLunch = request.StartOfLunch,
                    EndOfLunch = request.EndOfLunch,
                    AppointmentPeriod = request.AppointmentPeriod
                };

                await _unitOfWorkPortalDb.GetRepository<BranchShiftHoliday>().AddAsync(newBranchShiftHoliday);

            }
            else
            {
                entityBranchShiftHoliday.StartOfShift = request.StartOfShift;
                entityBranchShiftHoliday.EndOfShift = request.EndOfShift;
                entityBranchShiftHoliday.StartOfLunch = request.StartOfLunch;
                entityBranchShiftHoliday.EndOfLunch = request.EndOfLunch;
                entityBranchShiftHoliday.AppointmentPeriod = request.AppointmentPeriod;

                _unitOfWorkPortalDb.GetRepository<BranchShiftHoliday>().Update(entityBranchShiftHoliday);

            }

            var entityDayOfWeekBranchShiftHoliday = await _unitOfWorkPortalDb.GetRepository<DayOfWeekBranchShiftHoliday>().Entities
                .Where(p => !p.IsDeleted && p.BranchApplicationCountryId == request.BranchApplicationCountryId)
                .ToListAsync();


            if (entityDayOfWeekBranchShiftHoliday != null)
            {

                foreach (var itemDayOfWeeks in entityDayOfWeekBranchShiftHoliday)
                {
                    itemDayOfWeeks.IsActive = false;
                    itemDayOfWeeks.IsDeleted = true;
                    itemDayOfWeeks.UpdatedAt = DateTime.Now;
                    itemDayOfWeeks.UpdatedBy = request.UserAuditId;
                    itemDayOfWeeks.DeletedAt = DateTime.Now;
                    itemDayOfWeeks.DeletedBy = request.UserAuditId;
                }

                _unitOfWorkPortalDb.GetRepository<DayOfWeekBranchShiftHoliday>().UpdateRange(entityDayOfWeekBranchShiftHoliday);

            }

            var insertDayOfWeekBranchShiftHolidays = new List<DayOfWeekBranchShiftHoliday>();

            foreach (var itemDayOfWeeks in request.DayOfWeeks)
            {
                var insertDayOfWeekBranchShiftHoliday = new DayOfWeekBranchShiftHoliday()
                {
                    BranchApplicationCountryId = request.BranchApplicationCountryId,
                    DayOfWeekKey = itemDayOfWeeks.Id
                };

                insertDayOfWeekBranchShiftHolidays.Add(insertDayOfWeekBranchShiftHoliday);
            }

            await _unitOfWorkPortalDb.GetRepository<DayOfWeekBranchShiftHoliday>().AddRangeAsync(insertDayOfWeekBranchShiftHolidays).ConfigureAwait(false);



            var entityBranchHolidaysBranchShiftHoliday = await _unitOfWorkPortalDb.GetRepository<BranchHolidaysBranchShiftHoliday>().Entities
                .Where(p => !p.IsDeleted && p.BranchApplicationCountryId == request.BranchApplicationCountryId)
                .ToListAsync();

            if (entityBranchHolidaysBranchShiftHoliday != null)
            {

                foreach (var itemDayOfWeeks in entityBranchHolidaysBranchShiftHoliday)
                {
                    itemDayOfWeeks.IsActive = false;
                    itemDayOfWeeks.IsDeleted = true;
                    itemDayOfWeeks.UpdatedAt = DateTime.Now;
                    itemDayOfWeeks.UpdatedBy = request.UserAuditId;
                    itemDayOfWeeks.DeletedAt = DateTime.Now;
                    itemDayOfWeeks.DeletedBy = request.UserAuditId;
                }

                _unitOfWorkPortalDb.GetRepository<BranchHolidaysBranchShiftHoliday>().UpdateRange(entityBranchHolidaysBranchShiftHoliday);

            }


            var insertBranchHolidaysBranchShiftHolidays = new List<BranchHolidaysBranchShiftHoliday>();

            foreach (var itemHolidays in request.Holidays)
            {
                var insertBranchHolidaysBranchShiftHoliday = new BranchHolidaysBranchShiftHoliday()
                {
                    BranchApplicationCountryId = request.BranchApplicationCountryId,
                    HolidayDate = itemHolidays.HolidayDate,
                    Description = itemHolidays.Description
                };
                insertBranchHolidaysBranchShiftHolidays.Add(insertBranchHolidaysBranchShiftHoliday);
            }

            await _unitOfWorkPortalDb.GetRepository<BranchHolidaysBranchShiftHoliday>().AddRangeAsync(insertBranchHolidaysBranchShiftHolidays).ConfigureAwait(false);


            await _unitOfWorkPortalDb.SaveChangesAsync();

            return new AddResponseDto();
        }

        public async Task<BranchShiftHolidayDaysDto> GetShiftHolidayByBranchApplicationCountryAsync(GetShiftHolidayByBranchApplicationCountryApiRequestDto request)
        {
            var firstDayOfMonth = new DateTime(DateTime.Now.Year, DateTime.Now.Month, 1);

            var branchApplicationCountry = await _unitOfWorkPortalDb.GetRepository<BranchApplicationCountry>().Entities
                .Include(i => i.BranchShiftHolidays)
                .Include(i => i.BranchHolidaysBranchShiftHolidays.Where(w => w.HolidayDate >= firstDayOfMonth))
                .Include(i => i.DayOfWeekBranchShiftHolidays)
                .Where(s => s.IsActive && !s.IsDeleted && s.Id == request.BranchApplicationCountryId).FirstOrDefaultAsync();



            var daysOfWeek = EnumHelper.GetEnumAsDictionary(typeof(Contracts.Entities.Enums.DayOfWeek));

            var result = new BranchShiftHolidayDaysDto() { };

            if (branchApplicationCountry != null)
            {
                result = new BranchShiftHolidayDaysDto()
                {
                    StartOfShift = branchApplicationCountry.BranchShiftHolidays.Where(w => w.IsActive && !w.IsDeleted).Select(s => s.StartOfShift).FirstOrDefault(),
                    EndOfShift = branchApplicationCountry.BranchShiftHolidays.Where(w => w.IsActive && !w.IsDeleted).Select(s => s.EndOfShift).FirstOrDefault(),
                    StartOfLunch = branchApplicationCountry.BranchShiftHolidays.Where(w => w.IsActive && !w.IsDeleted).Select(s => s.StartOfLunch).FirstOrDefault(),
                    EndOfLunch = branchApplicationCountry.BranchShiftHolidays.Where(w => w.IsActive && !w.IsDeleted).Select(s => s.EndOfLunch).FirstOrDefault(),
                    AppointmentPeriod = branchApplicationCountry.BranchShiftHolidays.Where(w => w.IsActive && !w.IsDeleted).Select(s => s.AppointmentPeriod).FirstOrDefault(),
                    Holidays = branchApplicationCountry.BranchHolidaysBranchShiftHolidays
                             .Where(w => w.IsActive && !w.IsDeleted).Select(s => new BranchShiftHolidayDto()
                             {
                                 HolidayDate = s.HolidayDate,
                                 Description = s.Description
                             }).ToList(),
                    Shifts = branchApplicationCountry.DayOfWeekBranchShiftHolidays
                             .Where(w => w.IsActive && !w.IsDeleted).Select(s => new DaysOfWeekBranchShiftHolidayDto()
                             {
                                 Id = s.DayOfWeekKey.ToString(),
                                 DisplayValue = daysOfWeek.First(f => f.Key == s.DayOfWeekKey).Value
                             }).ToList()

                };
            }

            return result;
        }
        #endregion
        #endregion

        #region BranchApplicationCountryCompanyExtraFee
        public async Task<GetBranchApplicationCountryCompanyExtraFeeResponseDto> GetBranchApplicationCountryCompanyExtraFee(GetBranchApplicationCountryCompanyExtraFeeRequestDto request)
        {
            var entityBranchApplicationCountryExtraFee = await _unitOfWorkPortalDb.GetRepository<BranchApplicationCountryExtraFee>().Entities
                .Include(p => p.ExtraFee)
                    .ThenInclude(p => p.ExtraFeeTranslations)
                .Where(p => !p.IsDeleted && p.Id == request.BranchApplicationCountryExtraFeeId)
                .FirstOrDefaultAsync();

            if (entityBranchApplicationCountryExtraFee == null)
                throw new NoFoundDataPortalException($"{nameof(SiteResources.Exception_NoRecordsFound).ToSiteResourcesValue(request.LanguageId)} ({nameof(SiteResources.BranchApplicationCountryExtraFee).ToSiteResourcesValue(request.LanguageId)})");


            var branchApplicationCountryCompanyExtraFee = _unitOfWorkPortalDb.GetRepository<BranchApplicationCountryCompanyExtraFee>().Entities
                .Where(w => w.BranchApplicationCountryExtraFeeId == request.BranchApplicationCountryExtraFeeId && !w.IsDeleted)
                .AsNoTracking()
                .GroupBy(g => g.BranchApplicationCountryExtraFeeId)
                .Select(s => new GetBranchApplicationCountryCompanyExtraFeeResponseDto.BranchApplicationCountryCompanyExtraFeeDto()
                {
                    Id = s.First().Id,
                    CompanyPrice = s.First().CompanyPrice,
                    CurrencyId = s.First().CurrencyId,
                    ShowInReport = s.First().ShowInReport,
                    BranchApplicationCountryExtraFeeId = s.Key,
                    ProviderCompanyId = s.First().ProviderCompanyId,
                    ReportTypeIds = s.Select(s => s.ReportTypeId).Where(w => w.HasValue).ToList(),
                    CompanyPrice2 = s.First().CompanyPrice2,
                    CurrencyId2 = s.First().CurrencyId2,
                    ShowInReport2 = s.First().ShowInReport2,
                    ProviderCompanyId2 = s.First().ProviderCompanyId2,
                    ReportTypeIds2 = s.Select(s => s.ReportTypeId2).Where(w => w.HasValue).ToList()
                })
                .SingleOrDefault();

            var companyExtraFee = new GetBranchApplicationCountryCompanyExtraFeeResponseDto
            {
                BranchApplicationCountryExtraFee = entityBranchApplicationCountryExtraFee.ExtraFee.ExtraFeeTranslations.Any(p => p.LanguageId == request.LanguageId) ?
                    entityBranchApplicationCountryExtraFee.ExtraFee.ExtraFeeTranslations.First(p => p.LanguageId == request.LanguageId).Name :
                    entityBranchApplicationCountryExtraFee.ExtraFee.ExtraFeeTranslations.First().Name,
                BranchApplicationCountryCompanyExtraFee = branchApplicationCountryCompanyExtraFee
            };

            return companyExtraFee;
        }

        public async Task<AddOrUpdateBranchCountryCompanyExtraFeeResponseDto> AddUpdateBranchApplicationCountryCompanyExtraFee(AddUpdateBranchApplicationCountryCompanyExtraFeeRequestDto request)
        {
            #region queries

            var isBranchApplicationCountryExtraFeeExist = await CheckIfBranchApplicationCountryExtraFeeExist(request.BranchApplicationCountryExtraFeeId);

            if (!isBranchApplicationCountryExtraFeeExist)
                throw new NoFoundDataPortalException($"{nameof(SiteResources.Exception_NoRecordsFound).ToSiteResourcesValue(request.LanguageId)} ({nameof(SiteResources.BranchApplicationCountryExtraFee).ToSiteResourcesValue(request.LanguageId)})");

            var existingCompanyExtraFees = await GetExistingCompanyExtraFees(request.BranchApplicationCountryExtraFeeId);

            #endregion
            
            
            var existingCompanyExtraFeesDict = existingCompanyExtraFees
                .Where(w => w.ReportTypeId.HasValue || w.ReportTypeId2.HasValue)
                .ToDictionary(
                    fee => $"{fee.ReportTypeId ?? 0}_{fee.ReportTypeId2 ?? 0}",
                    fee => fee);


            if (request.ReportTypeIds == null || !request.ReportTypeIds.Any())
            {
                // If no ReportTypeIds, set existing ReportTypeIds to null
                foreach (var existingCompanyExtraFee in existingCompanyExtraFees)
                {
                    UpdateBranchApplicationCountryCompanyExtraFee(existingCompanyExtraFee, request);
                }
            }
            else
            {
                int maxCount = Math.Max(request.ReportTypeIds.Count, request.ReportTypeIds2.Count);

                for (int i = 0; i < maxCount; i++)
                {
                    int? reportTypeId = i < request.ReportTypeIds.Count ? request.ReportTypeIds[i] : (int?)null;
                    int? reportTypeId2 = i < request.ReportTypeIds2.Count ? request.ReportTypeIds2[i] : (int?)null;

                    string key = $"{reportTypeId ?? 0}_{reportTypeId2 ?? 0}";

                    if (existingCompanyExtraFeesDict.TryGetValue(key, out var companyExtraFee))
                    {
                        // Update existing record
                        UpdateBranchApplicationCountryCompanyExtraFee(companyExtraFee, request);
                    }
                    else
                    {
                        // Add new
                        await AddBranchApplicationCountryCompanyExtraFee(request, reportTypeId, reportTypeId2, request.UserAuditId);
                    }
                }

                // Delete records that are not in the request
                DeleteCompanyExtraFeesNotInRequest(existingCompanyExtraFees, request);
            }
            
            await _unitOfWorkPortalDb.SaveChangesAsync();

            return new AddOrUpdateBranchCountryCompanyExtraFeeResponseDto
            {
                Result = true
            };
        }

        #region privateMethods

        private async Task<bool> CheckIfBranchApplicationCountryExtraFeeExist(int branchApplicationCountryExtraFeeId) =>
            await _unitOfWorkPortalDb.GetRepository<BranchApplicationCountryExtraFee>().Entities
                .AnyAsync(f => !f.IsDeleted && f.IsActive && f.Id == branchApplicationCountryExtraFeeId);

        private async Task<List<BranchApplicationCountryCompanyExtraFee>> GetExistingCompanyExtraFees(int branchApplicationCountryExtraFeeId) =>
            await _unitOfWorkPortalDb.GetRepository<BranchApplicationCountryCompanyExtraFee>().Entities
                .Where(w => w.BranchApplicationCountryExtraFeeId == branchApplicationCountryExtraFeeId && !w.IsDeleted && w.IsActive)
                .AsNoTracking()
                .ToListAsync();

        private void UpdateBranchApplicationCountryCompanyExtraFee(BranchApplicationCountryCompanyExtraFee existingCompanyExtraFee, AddUpdateBranchApplicationCountryCompanyExtraFeeRequestDto companyExtraFeeRequest)
        {
            existingCompanyExtraFee.CurrencyId = companyExtraFeeRequest.CurrencyId;
            existingCompanyExtraFee.CompanyPrice = companyExtraFeeRequest.CompanyPrice;
            existingCompanyExtraFee.ShowInReport = companyExtraFeeRequest.ShowInReport;
            existingCompanyExtraFee.ProviderCompanyId = companyExtraFeeRequest.ProviderCompanyId;
            if (companyExtraFeeRequest.ReportTypeIds == null || !companyExtraFeeRequest.ReportTypeIds.Any()) existingCompanyExtraFee.ReportTypeId = null;
            
            existingCompanyExtraFee.CurrencyId2 = companyExtraFeeRequest.CurrencyId2;
            existingCompanyExtraFee.CompanyPrice2 = companyExtraFeeRequest.CompanyPrice2;
            existingCompanyExtraFee.ShowInReport2 = companyExtraFeeRequest.ShowInReport2;
            existingCompanyExtraFee.ProviderCompanyId2 = companyExtraFeeRequest.ProviderCompanyId2;
            if (companyExtraFeeRequest.ReportTypeIds2 == null || !companyExtraFeeRequest.ReportTypeIds2.Any()) existingCompanyExtraFee.ReportTypeId2 = null;

            _unitOfWorkPortalDb.GetRepository<BranchApplicationCountryCompanyExtraFee>().Update(existingCompanyExtraFee);
        }
        
        private async Task AddBranchApplicationCountryCompanyExtraFee(AddUpdateBranchApplicationCountryCompanyExtraFeeRequestDto companyExtraFeeRequest, int? reportTypeId,int? reportTypeId2, int userAuditId)
        {
            var newCompanyExtraFee = new BranchApplicationCountryCompanyExtraFee
            {
                BranchApplicationCountryExtraFeeId = companyExtraFeeRequest.BranchApplicationCountryExtraFeeId,
                CompanyPrice = companyExtraFeeRequest.CompanyPrice,
                CurrencyId = companyExtraFeeRequest.CurrencyId,
                ShowInReport = companyExtraFeeRequest.ShowInReport,
                ProviderCompanyId = companyExtraFeeRequest.ProviderCompanyId,
                ReportTypeId = reportTypeId,
                CompanyPrice2 = companyExtraFeeRequest.CompanyPrice2,
                CurrencyId2 = companyExtraFeeRequest.CurrencyId2,
                ShowInReport2 = companyExtraFeeRequest.ShowInReport2,
                ProviderCompanyId2 = companyExtraFeeRequest.ProviderCompanyId2,
                ReportTypeId2 = reportTypeId2,
                IsActive = true,
                CreatedAt = DateTime.UtcNow,
                CreatedBy = userAuditId
            };
            await _unitOfWorkPortalDb.GetRepository<BranchApplicationCountryCompanyExtraFee>().AddAsync(newCompanyExtraFee);
        }
        
        private void DeleteCompanyExtraFeesNotInRequest(
            List<BranchApplicationCountryCompanyExtraFee> existingBranchApplicationCountryCompanyExtraFees,
            AddUpdateBranchApplicationCountryCompanyExtraFeeRequestDto requestedCompanyExtraFeeRequest)
        {
            int maxCount = Math.Max(requestedCompanyExtraFeeRequest.ReportTypeIds.Count, requestedCompanyExtraFeeRequest.ReportTypeIds2.Count);

            // Oluşacak eşleşme seti
            var requestKeys = new HashSet<string>(
                Enumerable.Range(0, maxCount).Select(i =>
                {
                    int? id1 = i < requestedCompanyExtraFeeRequest.ReportTypeIds.Count ? requestedCompanyExtraFeeRequest.ReportTypeIds[i] : (int?)null;
                    int? id2 = i < requestedCompanyExtraFeeRequest.ReportTypeIds2.Count ? requestedCompanyExtraFeeRequest.ReportTypeIds2[i] : (int?)null;
                    return $"{id1 ?? 0}_{id2 ?? 0}";
                })
            );

            foreach (var existing in existingBranchApplicationCountryCompanyExtraFees)
            {
                string key = $"{existing.ReportTypeId ?? 0}_{existing.ReportTypeId2 ?? 0}";

                if (!requestKeys.Contains(key))
                {
                    existing.IsDeleted = true;
                    existing.IsActive = false;
                    _unitOfWorkPortalDb.GetRepository<BranchApplicationCountryCompanyExtraFee>().Update(existing);
                }
            }
        }

        
        #endregion

        #endregion

        #region Inquiry

        public async Task<Pagination<GetPaginatedInquiriesResponseDto>> GetPaginatedInquiriesAsync(GetPaginatedInquiriesRequestDto request)
        {
            var paginationResult = new Pagination<GetPaginatedInquiriesResponseDto> { Page = request.Pagination.Page, PageSize = request.Pagination.PageSize };

            var queryInquiry = _unitOfWorkPortalDb.GetRepository<Inquiry>().Entities
                .Include(i => i.Translations)
                .Where(p => !p.IsDeleted && p.BranchId == request.BranchId);

            paginationResult.TotalItemCount = queryInquiry.Count();

            List<Inquiry> inquiryList;

            if (request.Pagination is { Page: > 0, PageSize: > 0 })
            {
                inquiryList = await queryInquiry
                    .OrderByDescending(o => o.Id)
                    .Skip((request.Pagination.Page - 1) * request.Pagination.PageSize)
                    .Take(request.Pagination.PageSize)
                    .ToListAsync();
            }
            else
            {
                inquiryList = await queryInquiry
                    .OrderByDescending(o => o.Id)
                    .ToListAsync();
            }

            var response = new GetPaginatedInquiriesResponseDto() { Inquiries = new List<InquiryResponseDto>() };

            foreach (var inquiryData in inquiryList.Select(inquiry => new InquiryResponseDto
            {
                Id = inquiry.Id,
                Name = inquiry.Translations.FirstOrDefault(s => s.LanguageId == request.LanguageId) != null ?
                             inquiry.Translations.FirstOrDefault(s => s.LanguageId == request.LanguageId)?.Name :
                             inquiry.Translations.FirstOrDefault()?.Name,
                IsActive = inquiry.IsActive,
                CreatedAt = inquiry.CreatedAt.GetValueOrDefault()
            }))
            {
                response.Inquiries.Add(inquiryData);
            }

            paginationResult.Items.Add(response);
            return paginationResult;
        }

        public async Task<UpdateResponseDto> UpdateInquiryOrderAsync(UpdateInquiryOrderRequestDto request)
        {
            var questions = await _unitOfWorkPortalDb.GetRepository<InquiryQuestion>().Entities
                .Where(s => request.Questions.Select(r => r.Id).Contains(s.Id)).ToListAsync();

            foreach (var item in questions)
            {
                var orderChange = request.Questions.First(r => r.Id == item.Id).Order;

                item.Order = orderChange;
            }
            _unitOfWorkPortalDb.GetRepository<InquiryQuestion>().UpdateRange(questions);
            await _unitOfWorkPortalDb.SaveChangesAsync();

            return new UpdateResponseDto { Result = true };
        }

        public async Task<AddResponseDto> AddInquiryAsync(AddInquiryRequestDto request)
        {
            var newInquiryEntity = new Inquiry()
            {
                BranchId = request.BranchId,
                IsActive = request.IsActive,
                CreatedAt = DateTime.Now,
                CreatedBy = request.UserAuditId,
                IsDeleted = false,
                IsShowInDetailPrint = false,
                Translations = request.Translations.Select(s => new InquiryTranslation()
                {
                    CreatedAt = DateTime.Now,
                    CreatedBy = request.UserAuditId,
                    IsActive = true,
                    IsDeleted = false,
                    LanguageId = s.LanguageId,
                    Name = s.Name
                }).ToList()
            };

            await _unitOfWorkPortalDb.GetRepository<Inquiry>().AddAsync(newInquiryEntity);
            await _unitOfWorkPortalDb.SaveChangesAsync();

            return new AddResponseDto()
            {
                Id = newInquiryEntity.Id
            };
        }

        public async Task<GetInquiryResponseDto> GetInquiryAsync(GetInquiryRequestDto request)
        {
            var existingInquiry = await _unitOfWorkPortalDb.GetRepository<Inquiry>().Entities
                .Include(i => i.Translations)
                .Include(i => i.InquiryQuestions)
                .ThenInclude(i => i.Translations)
                .Include(i => i.InquiryQuestions)
                .ThenInclude(i => i.Choices)
                .ThenInclude(i => i.Translations).Where(s => s.Id == request.Id && !s.IsDeleted).FirstOrDefaultAsync();

            if (existingInquiry == null)
                throw new NoFoundDataPortalException($"{nameof(SiteResources.Exception_NoRecordsFound).ToSiteResourcesValue(request.LanguageId)} ({nameof(SiteResources.Inquiry).ToSiteResourcesValue(request.LanguageId)})");

            var response = new GetInquiryResponseDto
            {
                InquiryName = existingInquiry.Translations.FirstOrDefault(s => s.LanguageId == request.LanguageId) != null ?
                    existingInquiry.Translations.FirstOrDefault(s => s.LanguageId == request.LanguageId).Name
                    : existingInquiry.Translations.FirstOrDefault().Name,
                InquiryId = existingInquiry.Id,
                IsActive = existingInquiry.IsActive,
                Translation = existingInquiry.Translations.Select(s => new InquiryTranslationDto()
                {
                    Name = s.Name,
                    LanguageId = s.LanguageId,
                }).ToList(),
                Questions = existingInquiry.InquiryQuestions.Where(s => !s.IsDeleted).OrderBy(s => s.Order).Select(s => new InquiryManagementQuestionDto()
                {
                    IsActive = s.IsActive,
                    QuestionId = s.Id,
                    Colspan = s.ColSpan,
                    QuestionType = s.QuestionTypeId,
                    Translations = s.Translations.Select(t => new InquiryTranslationDto
                    {
                        Name = t.Name,
                        LanguageId = t.LanguageId,
                    }).ToList(),
                    Choices = s.Choices.Where(s => !s.IsDeleted).Select(c => new InquiryManagementChoiceDto
                    {
                        IsActive = c.IsActive,
                        ChoiceTypeId = c.ChoiceTypeId,
                        Colspan = c.ColSpan,
                        ChoiceId = c.Id,
                        Translations = c.Translations.Select(t => new InquiryTranslationDto
                        {
                            Name = t.Name,
                            LanguageId = t.LanguageId,
                        }).ToList()
                    }).ToList()
                }).ToList(),
            };

            return response;
        }

        public async Task<DeleteResponseDto> DeleteInquiryQuestionAsync(DeleteInquiryQuestionRequestDto request)
        {
            var existingInquiryQuestion = await _unitOfWorkPortalDb.GetRepository<InquiryQuestion>().Entities
                .Where(s => s.Id == request.InquiryQuestionId && !s.IsDeleted).FirstOrDefaultAsync();

            if (existingInquiryQuestion == null)
                throw new NoFoundDataPortalException($"{nameof(SiteResources.Exception_NoRecordsFound).ToSiteResourcesValue(request.LanguageId)} ({nameof(SiteResources.Inquiry).ToSiteResourcesValue(request.LanguageId)})");

            var isAnyElective = await _unitOfWorkPortalDb.GetRepository<ApplicationInquiryAnswer>().Entities.AnyAsync(s =>
                s.IsActive && !s.IsDeleted && s.InquiryQuestionId == existingInquiryQuestion.Id);

            var isAnyExplanation = await _unitOfWorkPortalDb.GetRepository<ApplicationInquiryExplanation>().Entities
                .AnyAsync(s => s.IsActive && !s.IsDeleted && s.InquiryQuestionId == existingInquiryQuestion.Id);

            if (isAnyElective || isAnyExplanation)
                return new DeleteResponseDto() { Result = false };

            _unitOfWorkPortalDb.GetRepository<InquiryQuestion>().MarkAsDeleted(existingInquiryQuestion.Id);
            await _unitOfWorkPortalDb.SaveChangesAsync();

            return new DeleteResponseDto { Result = true };
        }

        public async Task<AddResponseDto> AddUpdateInquiryQuestionAsync(AddUpdateInquiryQuestionRequestDto request)
        {
            if (request.IsUpdateOperation)
            {
                var questionDataList = await _unitOfWorkPortalDb.GetRepository<InquiryQuestion>().Entities.Include(i => i.Translations)
                    .Where(s => request.Questions.Select(q => q.QuestionId).Contains(s.Id)).ToListAsync();

                foreach (var entity in questionDataList)
                {
                    var selectedData = request.Questions.FirstOrDefault(q => q.QuestionId == entity.Id);

                    if (selectedData != null)
                    {
                        entity.IsActive = selectedData.IsActive;
                        entity.ColSpan = selectedData.Colspan;

                        foreach (var translation in selectedData.Translations)
                        {
                            var translationData =
                                entity.Translations.FirstOrDefault(s => s.LanguageId == translation.LanguageId);

                            if (translationData == null)
                            {
                                var newTranslation = new InquiryQuestionTranslation()
                                {
                                    InquiryQuestionId = entity.Id,
                                    LanguageId = translation.LanguageId,
                                    Name = translation.Name ?? string.Empty,
                                    IsActive = true,
                                    IsDeleted = false,
                                    CreatedAt = DateTime.Now,
                                    CreatedBy = request.UserAuditId
                                };

                                await _unitOfWorkPortalDb.GetRepository<InquiryQuestionTranslation>().AddAsync(newTranslation);
                                await _unitOfWorkPortalDb.SaveChangesAsync();
                            }
                            else
                            {
                                entity.Translations.FirstOrDefault(s => s.LanguageId == translation.LanguageId).Name =
                                    translation.Name ?? string.Empty;
                            }

                            _unitOfWorkPortalDb.GetRepository<InquiryQuestion>().Update(entity);
                            await _unitOfWorkPortalDb.SaveChangesAsync();
                        }
                    }
                }
            }
            else
            {
                var inquiryQuestionLastOrder = await _unitOfWorkPortalDb.GetRepository<InquiryQuestion>().Entities.Where(s => s.InquiryId == request.Questions.First().InquiryId).OrderByDescending(o => o.Order).FirstOrDefaultAsync();

                var initialOrder = inquiryQuestionLastOrder != null ? inquiryQuestionLastOrder.Order + 1 : 1;
                foreach (var question in request.Questions)
                {
                    var entity = new InquiryQuestion()
                    {
                        ColSpan = question.Colspan,
                        DescriptionColSpan = 2,
                        InquiryId = question.InquiryId,
                        QuestionTypeId = question.QuestionType,
                        IsDescriptionIncluded = false,
                        IsMultiSelectable = true,
                        IsRequired = false,
                        Order = initialOrder,
                        IsActive = question.IsActive,
                        IsDeleted = false,
                        CreatedAt = DateTime.Now,
                        CreatedBy = request.UserAuditId,
                        Translations = question.Translations.Select(s => new InquiryQuestionTranslation()
                        {
                            Name = s.Name ?? string.Empty,
                            LanguageId = s.LanguageId,
                            IsActive = true,
                            IsDeleted = false,
                            CreatedAt = DateTime.Now,
                            CreatedBy = request.UserAuditId,
                        }).ToList()
                    };

                    await _unitOfWorkPortalDb.GetRepository<InquiryQuestion>().AddAsync(entity);
                    await _unitOfWorkPortalDb.SaveChangesAsync();

                    initialOrder++;
                }
            }

            return new AddResponseDto();
        }

        public async Task<GetInquiryQuestionChoicesResponseDto> GetInquiryQuestionChoicesAsync(
            GetInquiryQuestionChoicesRequestDto request)
        {
            var entities = await _unitOfWorkPortalDb.GetRepository<InquiryQuestionChoice>().Entities
                .Include(i => i.Translations).Where(s => !s.IsDeleted && s.InquiryQuestionId == request.InquiryQuestionId)
                .ToListAsync();

            if (!entities.Any())
                throw new NoFoundDataPortalException($"{nameof(SiteResources.Exception_NoRecordsFound).ToSiteResourcesValue(request.LanguageId)} ({nameof(SiteResources.Inquiry).ToSiteResourcesValue(request.LanguageId)})");

            return new GetInquiryQuestionChoicesResponseDto()
            {
                Choices = entities.Select(s => new InquiryManagementChoiceDto()
                {
                    ChoiceId = s.Id,
                    ChoiceTypeId = s.ChoiceTypeId,
                    IsActive = s.IsActive,
                    Colspan = s.ColSpan,
                    Translations = s.Translations.Select(t => new InquiryTranslationDto
                    {
                        Name = t.Name,
                        LanguageId = t.LanguageId
                    }).ToList()
                }).ToList()
            };
        }

        public async Task<AddResponseDto> AddUpdateInquiryQuestionChoiceAsync(AddUpdateInquiryQuestionChoiceRequestDto request)
        {
            if (request.IsUpdateOperation)
            {
                var questionChoiceDataList = await _unitOfWorkPortalDb.GetRepository<InquiryQuestionChoice>().Entities.Include(i => i.Translations)
                    .Where(s => request.Choices.Select(q => q.ChoiceId).Contains(s.Id)).ToListAsync();

                foreach (var entity in questionChoiceDataList)
                {
                    var selectedData = request.Choices.FirstOrDefault(q => q.ChoiceId == entity.Id);

                    if (selectedData != null)
                    {
                        entity.ChoiceTypeId = selectedData.ChoiceType;
                        entity.IsActive = selectedData.IsActive;
                        entity.ColSpan = selectedData.Colspan;

                        foreach (var translation in selectedData.Translations)
                        {
                            var translationData =
                                entity.Translations.FirstOrDefault(s => s.LanguageId == translation.LanguageId);

                            if (translationData == null)
                            {
                                var newTranslation = new InquiryQuestionChoiceTranslation()
                                {
                                    InquiryQuestionChoiceId = entity.Id,
                                    LanguageId = translation.LanguageId,
                                    Name = translation.Name ?? string.Empty,
                                    IsActive = true,
                                    IsDeleted = false,
                                    CreatedAt = DateTime.Now,
                                    CreatedBy = request.UserAuditId
                                };

                                await _unitOfWorkPortalDb.GetRepository<InquiryQuestionChoiceTranslation>().AddAsync(newTranslation);
                                await _unitOfWorkPortalDb.SaveChangesAsync();
                            }
                            else
                            {
                                entity.Translations.FirstOrDefault(s => s.LanguageId == translation.LanguageId).Name =
                                    translation.Name ?? string.Empty;
                            }

                            _unitOfWorkPortalDb.GetRepository<InquiryQuestionChoice>().Update(entity);
                            await _unitOfWorkPortalDb.SaveChangesAsync();
                        }
                    }
                }
            }
            else
            {
                if (request.Choices.Any())
                {
                    var inquiryId = await _unitOfWorkPortalDb.GetRepository<InquiryQuestion>().Entities
                        .Where(s => s.Id == request.Choices.First().InquiryQuestionId).Select(s => s.InquiryId)
                        .FirstOrDefaultAsync();

                    if (inquiryId != 0)
                    {
                        foreach (var choice in request.Choices)
                        {
                            var entity = new InquiryQuestionChoice()
                            {
                                ColSpan = choice.Colspan,
                                InquiryId = inquiryId,
                                ChoiceTypeId = choice.ChoiceType,
                                InquiryQuestionId = choice.InquiryQuestionId,
                                IsActive = choice.IsActive,
                                IsDeleted = false,
                                CreatedAt = DateTime.Now,
                                CreatedBy = request.UserAuditId,
                                Translations = choice.Translations.Select(s => new InquiryQuestionChoiceTranslation()
                                {
                                    Name = s.Name ?? string.Empty,
                                    LanguageId = s.LanguageId,
                                    IsActive = true,
                                    IsDeleted = false,
                                    CreatedAt = DateTime.Now,
                                    CreatedBy = request.UserAuditId,
                                }).ToList(),

                            };

                            await _unitOfWorkPortalDb.GetRepository<InquiryQuestionChoice>().AddAsync(entity);
                            await _unitOfWorkPortalDb.SaveChangesAsync();
                        }
                    }
                }

            }

            return new AddResponseDto();
        }

        public async Task<DeleteResponseDto> DeleteInquiryQuestionChoiceAsync(DeleteInquiryQuestionChoiceRequestDto request)
        {
            var existingInquiryQuestionChoice = await _unitOfWorkPortalDb.GetRepository<InquiryQuestionChoice>().Entities
                .Where(s => s.Id == request.InquiryQuestionChoiceId && !s.IsDeleted).FirstOrDefaultAsync();

            if (existingInquiryQuestionChoice == null)
                throw new NoFoundDataPortalException($"{nameof(SiteResources.Exception_NoRecordsFound).ToSiteResourcesValue(request.LanguageId)} ({nameof(SiteResources.Inquiry).ToSiteResourcesValue(request.LanguageId)})");

            var isAnyElective = await _unitOfWorkPortalDb.GetRepository<ApplicationInquiryAnswer>().Entities.AnyAsync(s =>
                s.IsActive && !s.IsDeleted && s.InquiryQuestionChoiceIds.Contains(existingInquiryQuestionChoice.Id));

            var isAnyExplanation = await _unitOfWorkPortalDb.GetRepository<ApplicationInquiryExplanation>().Entities
                .AnyAsync(s => s.IsActive && !s.IsDeleted && s.InquiryQuestionChoiceId == existingInquiryQuestionChoice.Id);

            if (isAnyElective || isAnyExplanation)
                return new DeleteResponseDto() { Result = false };

            _unitOfWorkPortalDb.GetRepository<InquiryQuestionChoice>().MarkAsDeleted(existingInquiryQuestionChoice.Id);
            await _unitOfWorkPortalDb.SaveChangesAsync();

            return new DeleteResponseDto { Result = true };
        }

        public async Task<UpdateResponseDto> UpdateInquiryAsync(UpdateInquiryRequestDto request)
        {
            var existingInquiry = await _unitOfWorkPortalDb.GetRepository<Inquiry>().Entities
                .Include(i => i.Translations).Where(s => s.Id == request.InquiryId && !s.IsDeleted).FirstOrDefaultAsync();

            if (existingInquiry == null)
                throw new NoFoundDataPortalException($"{nameof(SiteResources.Exception_NoRecordsFound).ToSiteResourcesValue(request.LanguageId)} ({nameof(SiteResources.Inquiry).ToSiteResourcesValue(request.LanguageId)})");

            existingInquiry.IsActive = request.IsActive;

            _unitOfWorkPortalDb.GetRepository<Inquiry>().Update(existingInquiry);
            await _unitOfWorkPortalDb.SaveChangesAsync();

            foreach (var translation in request.Translations)
            {
                var entity = existingInquiry.Translations.FirstOrDefault(s => s.LanguageId == translation.LanguageId);

                if (entity == null)
                {
                    var data = new InquiryTranslation()
                    {
                        InquiryId = existingInquiry.Id,
                        Name = translation.Name,
                        LanguageId = translation.LanguageId,
                        CreatedAt = DateTime.Now,
                        CreatedBy = request.UserAuditId,
                        IsActive = true,
                        IsDeleted = false,
                    };

                    await _unitOfWorkPortalDb.GetRepository<InquiryTranslation>().AddAsync(data);
                    await _unitOfWorkPortalDb.SaveChangesAsync();
                }
                else
                {

                    entity.Name = translation.Name;

                    _unitOfWorkPortalDb.GetRepository<InquiryTranslation>().Update(entity);
                    await _unitOfWorkPortalDb.SaveChangesAsync();
                }
            }

            return new UpdateResponseDto();
        }

        public async Task<GetInquiryChoiceSelectionResponseDto> GetInquiryQuestionChoiceSelectionAsync(GetInquiryQuestionChoiceSelectionRequestDto request)
        {
            var entities = await _unitOfWorkPortalDb.GetRepository<InquiryQuestionChoiceSelection>().Entities
                .Include(i => i.Translations).Where(s => s.IsActive && !s.IsDeleted && s.InquiryQuestionChoiceId == request.InquiryQuestionChoiceId)
                .ToListAsync();

            if (!entities.Any())
                throw new NoFoundDataPortalException($"{nameof(SiteResources.Exception_NoRecordsFound).ToSiteResourcesValue(request.LanguageId)} ({nameof(SiteResources.Inquiry).ToSiteResourcesValue(request.LanguageId)})");

            return new GetInquiryChoiceSelectionResponseDto()
            {
                Selections = entities.Select(s => new InquiryManagementChoiceSelectionDto()
                {
                    SelectionId = s.Id,
                    IsActive = s.IsActive,
                    Translations = s.Translations.Select(t => new InquiryTranslationDto
                    {
                        Name = t.Name,
                        LanguageId = t.LanguageId
                    }).ToList()
                }).ToList()
            };
        }

        public async Task<AddResponseDto> AddUpdateInquiryQuestionChoiceSelectionAsync(AddUpdateInquiryQuestionChoiceSelectionRequestDto request)
        {
            if (request.IsUpdateOperation)
            {
                var questionChoiceSelectionDataList = await _unitOfWorkPortalDb.GetRepository<InquiryQuestionChoiceSelection>().Entities.Include(i => i.Translations)
                    .Where(s => request.Selections.Select(q => q.SelectionId).Contains(s.Id)).ToListAsync();

                foreach (var entity in questionChoiceSelectionDataList)
                {
                    var selectedData = request.Selections.FirstOrDefault(q => q.SelectionId == entity.Id);

                    if (selectedData != null)
                    {
                        entity.IsActive = selectedData.IsActive;

                        foreach (var translation in selectedData.Translations)
                        {
                            var translationData =
                                entity.Translations.FirstOrDefault(s => s.LanguageId == translation.LanguageId);

                            if (translationData == null)
                            {
                                var newTranslation = new InquiryQuestionChoiceSelectionTranslation()
                                {
                                    InquiryQuestionChoiceSelectionId = entity.Id,
                                    LanguageId = translation.LanguageId,
                                    Name = translation.Name ?? string.Empty,
                                    IsActive = true,
                                    IsDeleted = false,
                                    CreatedAt = DateTime.Now,
                                    CreatedBy = request.UserAuditId
                                };

                                await _unitOfWorkPortalDb.GetRepository<InquiryQuestionChoiceSelectionTranslation>().AddAsync(newTranslation);
                                await _unitOfWorkPortalDb.SaveChangesAsync();
                            }
                            else
                            {
                                entity.Translations.FirstOrDefault(s => s.LanguageId == translation.LanguageId).Name =
                                    translation.Name ?? string.Empty;
                            }

                            _unitOfWorkPortalDb.GetRepository<InquiryQuestionChoiceSelection>().Update(entity);
                            await _unitOfWorkPortalDb.SaveChangesAsync();
                        }
                    }
                }
            }
            else
            {
                foreach (var selection in request.Selections)
                {
                    var entity = new InquiryQuestionChoiceSelection()
                    {
                        InquiryQuestionChoiceId = selection.ChoiceId,
                        IsActive = selection.IsActive,
                        IsDeleted = false,
                        CreatedAt = DateTime.Now,
                        CreatedBy = request.UserAuditId,
                        Translations = selection.Translations.Select(s => new InquiryQuestionChoiceSelectionTranslation
                        {
                            Name = s.Name ?? string.Empty,
                            LanguageId = s.LanguageId,
                            IsActive = true,
                            IsDeleted = false,
                            CreatedAt = DateTime.Now,
                            CreatedBy = request.UserAuditId,
                        }).ToList(),
                    };

                    await _unitOfWorkPortalDb.GetRepository<InquiryQuestionChoiceSelection>().AddAsync(entity);
                    await _unitOfWorkPortalDb.SaveChangesAsync();
                }
            }

            return new AddResponseDto();
        }

        public async Task<DeleteResponseDto> DeleteInquiryQuestionChoiceSelectionAsync(DeleteInquiryQuestionChoiceSelectionRequestDto request)
        {
            var existingInquiryQuestionChoiceSelection = await _unitOfWorkPortalDb.GetRepository<InquiryQuestionChoiceSelection>().Entities
                .Where(s => s.Id == request.InquiryQuestionChoiceSelectionId && !s.IsDeleted).FirstOrDefaultAsync();

            if (existingInquiryQuestionChoiceSelection == null)
                throw new NoFoundDataPortalException($"{nameof(SiteResources.Exception_NoRecordsFound).ToSiteResourcesValue(request.LanguageId)} ({nameof(SiteResources.Inquiry).ToSiteResourcesValue(request.LanguageId)})");

            var isUsed = await _unitOfWorkPortalDb.GetRepository<ApplicationInquiryAnswerSelection>().Entities.AnyAsync(s =>
                s.IsActive && !s.IsDeleted && s.InquiryQuestionChoiceSelectionId == request.InquiryQuestionChoiceSelectionId);

            if (isUsed)
                return new DeleteResponseDto() { Result = false };

            _unitOfWorkPortalDb.GetRepository<InquiryQuestionChoiceSelection>().MarkAsDeleted(existingInquiryQuestionChoiceSelection.Id);
            await _unitOfWorkPortalDb.SaveChangesAsync();

            return new DeleteResponseDto { Result = true };
        }

        #region Vms

        public async Task<Pagination<GetPaginatedVmsVideoByBranchResponseDto>> GetPaginatedVmsVideoByBranchAsync(GetPaginatedVmsVideoByBranchRequestDto request)
        {
            var paginationResult = new Pagination<GetPaginatedVmsVideoByBranchResponseDto> { Page = request.Pagination.Page, PageSize = request.Pagination.PageSize };

            var query = _unitOfWorkPortalDb.GetRepository<VmsVideo>().Entities
                .Where(p => !p.IsDeleted && p.BranchId == request.BranchId);

            paginationResult.TotalItemCount = query.Count();

            List<VmsVideo> videoList;

            if (request.Pagination is { Page: > 0, PageSize: > 0 })
            {
                videoList = await query
                    .OrderBy(o => o.Order)
                    .Skip((request.Pagination.Page - 1) * request.Pagination.PageSize)
                    .Take(request.Pagination.PageSize)
                    .ToListAsync();
            }
            else
            {
                videoList = await query
                    .OrderBy(o => o.Order)
                    .ToListAsync();
            }

            var response = new GetPaginatedVmsVideoByBranchResponseDto() { Videos = new List<GetPaginatedVmsVideoByBranchResponseDto.VmsVideoDto>() };

            foreach (var videoData in videoList.Select(video => new GetPaginatedVmsVideoByBranchResponseDto.VmsVideoDto
            {
                Id = video.Id,
                Name = video.Name,
                Order = video.Order,
                StartDate = video.StartDate,
                EndDate = video.EndDate,
            }))
            {
                response.Videos.Add(videoData);
            }

            paginationResult.Items.Add(response);
            return paginationResult;
        }

        public async Task<Pagination<GetPaginatedVmsAnnouncementByBranchResponseDto>> GetPaginatedVmsAnnouncementByBranchAsync(GetPaginatedVmsAnnouncementByBranchRequestDto request)
        {
            var paginationResult = new Pagination<GetPaginatedVmsAnnouncementByBranchResponseDto> { Page = request.Pagination.Page, PageSize = request.Pagination.PageSize };

            var query = _unitOfWorkPortalDb.GetRepository<VmsAnnouncement>().Entities.Include(i => i.Translations)
                .Where(p => !p.IsDeleted && p.BranchId == request.BranchId);

            paginationResult.TotalItemCount = query.Count();

            List<VmsAnnouncement> announcementList;

            if (request.Pagination is { Page: > 0, PageSize: > 0 })
            {
                announcementList = await query
                    .OrderBy(o => o.Id)
                    .Skip((request.Pagination.Page - 1) * request.Pagination.PageSize)
                    .Take(request.Pagination.PageSize)
                    .ToListAsync();
            }
            else
            {
                announcementList = await query
                    .OrderBy(o => o.Id)
                    .ToListAsync();
            }

            var response = new GetPaginatedVmsAnnouncementByBranchResponseDto() { Announcements = new List<GetPaginatedVmsAnnouncementByBranchResponseDto.VmsAnnouncementDto>() };

            foreach (var announcementData in announcementList.Select(announcement => new GetPaginatedVmsAnnouncementByBranchResponseDto.VmsAnnouncementDto
            {
                Id = announcement.Id,
                Order = announcement.Order,
                Title = announcement.Title,
                StartDate = announcement.StartDate,
                EndDate = announcement.EndDate,
                IsActive = announcement.IsActive,
                Translation = announcement.Translations.Select(s => new VmsAnnouncementTranslationDto()
                {
                    Id = announcement.Id,
                    Name = s.Name,
                    LanguageId = s.LanguageId,
                    Language = EnumHelper.GetEnumDescription(typeof(VmsAnnouncementLanguage), s.LanguageId.ToString()),
                    IsActive = s.IsActive
                }).ToList()
            }))
            {
                response.Announcements.Add(announcementData);
            }

            paginationResult.Items.Add(response);
            return paginationResult;
        }

        public async Task<AddResponseDto> AddVmsVideoAsync(AddVmsVideoRequestDto request)
        {
            var existingVmsVideos = await _unitOfWorkPortalDb.GetRepository<VmsVideo>().Entities
                .Where(s => s.IsActive && !s.IsDeleted && s.BranchId == request.BranchId).OrderByDescending(s => s.Order).ToListAsync();

            var entity = new VmsVideo()
            {
                Name = request.Name,
                IsActive = true,
                IsDeleted = false,
                BranchId = request.BranchId,
                EndDate = existingVmsVideos.FirstOrDefault()?.EndDate,
                StartDate = existingVmsVideos.FirstOrDefault()?.StartDate,
                FileExtension = request.FileExtension,
                UploadPath = request.UploadPath,
                Order = existingVmsVideos.FirstOrDefault() == null ? 1 : existingVmsVideos.FirstOrDefault().Order + 1,
                FileName = request.FileName,
                CreatedAt = DateTime.Now,
                CreatedBy = request.UserAuditId,
            };

            await _unitOfWorkPortalDb.GetRepository<VmsVideo>().AddAsync(entity);
            await _unitOfWorkPortalDb.SaveChangesAsync();

            return new AddResponseDto { Id = entity.Id };
        }

        public async Task<DeleteResponseDto> DeleteVmsVideoAsync(DeleteVmsVideoRequestDto request)
        {
            var existingVmsVideo = await _unitOfWorkPortalDb.GetRepository<VmsVideo>().Entities
                .Where(s => s.IsActive && !s.IsDeleted && s.Id == request.VmsVideoId).FirstOrDefaultAsync();

            if (existingVmsVideo == null)
                throw new NoFoundDataPortalException($"{nameof(SiteResources.Exception_NoRecordsFound).ToSiteResourcesValue(request.LanguageId)} (Video)");

            _unitOfWorkPortalDb.GetRepository<VmsVideo>().MarkAsDeleted(existingVmsVideo.Id);
            await _unitOfWorkPortalDb.SaveChangesAsync();

            return new DeleteResponseDto { Result = true };
        }

        public async Task<UpdateResponseDto> UpdateVmsVideoOrderAsync(UpdateVmsVideoOrderRequestDto request)
        {
            var videos = await _unitOfWorkPortalDb.GetRepository<VmsVideo>().Entities
                .Where(s => request.Videos.Select(r => r.Id).Contains(s.Id)).ToListAsync();

            foreach (var item in videos)
            {
                var orderChange = request.Videos.First(r => r.Id == item.Id).Order;

                item.Order = orderChange;
            }

            _unitOfWorkPortalDb.GetRepository<VmsVideo>().UpdateRange(videos);
            await _unitOfWorkPortalDb.SaveChangesAsync();

            return new UpdateResponseDto { Result = true };
        }

        public async Task<AddResponseDto> AddVmsAnnouncementAsync(AddVmsAnnouncementRequestDto request)
        {
            var existingVmsAnnouncements = await _unitOfWorkPortalDb.GetRepository<VmsAnnouncement>().Entities
                .Where(s => s.IsActive && !s.IsDeleted && s.BranchId == request.BranchId).OrderByDescending(s => s.Order).ToListAsync();

            if (existingVmsAnnouncements.Exists(s => s.Title == request.Title))
                throw new AlreadyExistsDataPortalException($"{nameof(SiteResources.Exception_ExistingRecord).ToSiteResourcesValue(request.LanguageId)} ({nameof(SiteResources.Announcements).ToSiteResourcesValue(request.LanguageId)})");

            var entity = new VmsAnnouncement()
            {
                Title = request.Title,
                IsActive = true,
                IsDeleted = false,
                BranchId = request.BranchId,
                EndDate = existingVmsAnnouncements.FirstOrDefault()?.EndDate,
                StartDate = existingVmsAnnouncements.FirstOrDefault()?.StartDate,
                Order = existingVmsAnnouncements.FirstOrDefault() == null ? 1 : existingVmsAnnouncements.FirstOrDefault()!.Order + 1,
                CreatedAt = DateTime.Now,
                CreatedBy = request.UserAuditId
            };

            await _unitOfWorkPortalDb.GetRepository<VmsAnnouncement>().AddAsync(entity);
            await _unitOfWorkPortalDb.SaveChangesAsync();

            var translations = request.Translation.Select(s => new VmsAnnouncementTranslation()
            {
                Name = s.Name,
                IsActive = s.IsActive,
                IsDeleted = false,
                LanguageId = s.LanguageId,
                CreatedAt = DateTime.Now,
                CreatedBy = request.UserAuditId,
                VmsAnnouncementId = entity.Id
            });

            await _unitOfWorkPortalDb.GetRepository<VmsAnnouncementTranslation>().AddRangeAsync(translations);
            await _unitOfWorkPortalDb.SaveChangesAsync();

            return new AddResponseDto { Id = entity.Id };
        }

        public async Task<DeleteResponseDto> DeleteVmsAnnouncementAsync(DeleteVmsAnnouncementRequestDto request)
        {
            var existingVmsAnnouncement = await _unitOfWorkPortalDb.GetRepository<VmsAnnouncement>().Entities
                .Where(s => s.IsActive && !s.IsDeleted && s.Id == request.VmsAnnouncementId).FirstOrDefaultAsync();

            if (existingVmsAnnouncement == null)
                throw new NoFoundDataPortalException($"{nameof(SiteResources.Exception_NoRecordsFound).ToSiteResourcesValue(request.LanguageId)} ({nameof(SiteResources.Announcements).ToSiteResourcesValue(request.LanguageId)})");

            _unitOfWorkPortalDb.GetRepository<VmsAnnouncement>().MarkAsDeleted(existingVmsAnnouncement.Id);
            await _unitOfWorkPortalDb.SaveChangesAsync();

            return new DeleteResponseDto { Result = true };
        }

        public async Task<UpdateResponseDto> UpdateVmsAnnouncementAsync(UpdateVmsAnnouncementRequestDto request)
        {
            var existingDataToUpdate = await _unitOfWorkPortalDb.GetRepository<VmsAnnouncement>().Entities
                .Include(i => i.Translations).Where(s => !s.IsDeleted && s.Id == request.Id).FirstOrDefaultAsync();

            if (existingDataToUpdate == null)
                throw new NoFoundDataPortalException($"{nameof(SiteResources.Exception_NoRecordsFound).ToSiteResourcesValue(request.LanguageId)} ({nameof(SiteResources.Announcements).ToSiteResourcesValue(request.LanguageId)})");

            existingDataToUpdate.Title = request.Title;
            existingDataToUpdate.UpdatedAt = DateTime.Now;
            existingDataToUpdate.UpdatedBy = request.UserAuditId;
            existingDataToUpdate.IsActive = request.IsActive;

            _unitOfWorkPortalDb.GetRepository<VmsAnnouncement>().Update(existingDataToUpdate);
            await _unitOfWorkPortalDb.SaveChangesAsync();

            foreach (var translation in request.Translation)
            {
                var translatedData =
                    existingDataToUpdate.Translations.FirstOrDefault(s => s.LanguageId == translation.LanguageId);

                if (translatedData == null)
                {
                    var newData = new VmsAnnouncementTranslation()
                    {
                        Name = translation.Name,
                        VmsAnnouncementId = existingDataToUpdate.Id,
                        IsActive = true,
                        IsDeleted = false,
                        LanguageId = translation.LanguageId,
                        CreatedAt = DateTime.Now,
                        CreatedBy = request.UserAuditId
                    };

                    await _unitOfWorkPortalDb.GetRepository<VmsAnnouncementTranslation>().AddAsync(newData);
                    await _unitOfWorkPortalDb.SaveChangesAsync();
                }
                else
                {
                    translatedData.Name = translation.Name;
                    translatedData.IsActive = translation.IsActive;

                    _unitOfWorkPortalDb.GetRepository<VmsAnnouncementTranslation>().Update(translatedData);
                    await _unitOfWorkPortalDb.SaveChangesAsync();
                }
            }

            return new UpdateResponseDto() { Result = true };
        }

        public async Task<UpdateResponseDto> UpdateVmsAnnouncementTimelineAsync(UpdateVmsAnnouncementTimelineRequestDto request)
        {
            var existingDataToUpdate = await _unitOfWorkPortalDb.GetRepository<VmsAnnouncement>().Entities.Where(s => s.IsActive && !s.IsDeleted && s.BranchId == request.BranchId).ToListAsync();

            if (!existingDataToUpdate.Any())
                throw new NoFoundDataPortalException($"{nameof(SiteResources.Exception_NoRecordsFound).ToSiteResourcesValue(request.LanguageId)} ({nameof(SiteResources.Announcements).ToSiteResourcesValue(request.LanguageId)})");

            foreach (var data in existingDataToUpdate)
            {
                data.StartDate = request.StartDate;
                data.EndDate = request.EndDate;
            }

            _unitOfWorkPortalDb.GetRepository<VmsAnnouncement>().UpdateRange(existingDataToUpdate);
            await _unitOfWorkPortalDb.SaveChangesAsync();

            return new UpdateResponseDto() { Result = true };
        }

        public async Task<UpdateResponseDto> UpdateVmsVideoTimelineAsync(UpdateVmsVideoTimelineRequestDto request)
        {
            var existingDataToUpdate = await _unitOfWorkPortalDb.GetRepository<VmsVideo>().Entities.Where(s => s.IsActive && !s.IsDeleted && s.BranchId == request.BranchId).ToListAsync();

            if (!existingDataToUpdate.Any())
                throw new NoFoundDataPortalException($"{nameof(SiteResources.Exception_NoRecordsFound).ToSiteResourcesValue(request.LanguageId)} (video)");

            foreach (var data in existingDataToUpdate)
            {
                data.StartDate = request.StartDate;
                data.EndDate = request.EndDate;
            }

            _unitOfWorkPortalDb.GetRepository<VmsVideo>().UpdateRange(existingDataToUpdate);
            await _unitOfWorkPortalDb.SaveChangesAsync();

            return new UpdateResponseDto() { Result = true };
        }

        public async Task<GetPaginatedVmsAnnouncementByBranchResponseDto.VmsAnnouncementDto> GetVmsVideoTimelineAsync(GetVmsAnnouncementRequestDto request)
        {
            var existingData = await _unitOfWorkPortalDb.GetRepository<VmsAnnouncement>().Entities
                .Include(i => i.Translations).Where(s => !s.IsDeleted && s.Id == request.Id).FirstOrDefaultAsync();

            if (existingData == null)
                throw new NoFoundDataPortalException($"{nameof(SiteResources.Exception_NoRecordsFound).ToSiteResourcesValue(request.LanguageId)} ({nameof(SiteResources.Announcements).ToSiteResourcesValue(request.LanguageId)})");

            var result = new GetPaginatedVmsAnnouncementByBranchResponseDto.VmsAnnouncementDto
            {
                Id = existingData.Id,
                Title = existingData.Title,
                IsActive = existingData.IsActive
            };

            if (existingData.Translations.Any())
            {
                var translations = existingData.Translations;

                result.Translation = translations.Select(p => new VmsAnnouncementTranslationDto
                {
                    Id = p.Id,
                    LanguageId = p.LanguageId,
                    Name = p.Name,
                    IsActive = p.IsActive
                }).ToList();
            }

            return result;
        }

        public async Task<VmsTimeLineResponseDto> GetVmsTimeLinesByBranchAsync(VmsTimeLineRequestDto request)
        {
            var result = new VmsTimeLineResponseDto();
            var now = DateTime.Now;

            var existingVmsAnnouncement = await _unitOfWorkPortalDb.GetRepository<VmsAnnouncement>().Entities
                .Where(s => s.IsActive && !s.IsDeleted && s.BranchId == request.BranchId)
                .Select(s => new { s.StartDate, s.EndDate })
                .FirstOrDefaultAsync();

            var existingVmsVideo = await _unitOfWorkPortalDb.GetRepository<VmsVideo>().Entities
                .Where(s => s.IsActive && !s.IsDeleted && s.BranchId == request.BranchId)
                .Select(s => new { s.StartDate, s.EndDate })
                .FirstOrDefaultAsync();

            result.AnnouncementStartDateTime = existingVmsAnnouncement?.StartDate ?? now;
            result.AnnouncementEndDateTime = existingVmsAnnouncement?.EndDate ?? now;
            result.VideoStartDateTime = existingVmsVideo?.StartDate ?? now;
            result.VideoEndDateTime = existingVmsVideo?.EndDate ?? now;

            return result;
        }


        #endregion


        #endregion

        #region BranchInsuranceRefundSettings

        public async Task<BranchInsuranceRefundSettingRequestDto> GetBranchInsuranceRefundSetting(GetBranchInsuranceRefundSettingRequestDto request)
        {
            var branchData = await _unitOfWorkPortalDb.GetRepository<Branch>().Entities
                .Include(i => i.BranchNotificationTemplates)
                .Include(i => i.BranchInsuranceRefundSettings)
                .Where(s => s.IsActive && !s.IsDeleted && s.Id == request.BranchId).FirstOrDefaultAsync();

            if (branchData == null)
                throw new NoFoundDataPortalException($"{nameof(SiteResources.Exception_NoRecordsFound).ToSiteResourcesValue(request.LanguageId)} ({nameof(SiteResources.Branch).ToSiteResourcesValue(request.LanguageId)})");

            return new BranchInsuranceRefundSettingRequestDto
            {
                IsRefundBlockByAllPolicies = branchData.IsRefundBlockByAllPolicies,
                RefundBlockByAllPoliciesDate = branchData.RefundBlockByAllPoliciesDate,
                RefundBlockByPolicyMailContent = branchData.BranchNotificationTemplates.FirstOrDefault(s => s.IsActive && !s.IsDeleted && s.NotificationTypeId == (int)ServiceNotificationType.RejectionWithPolicyRefund)?.MailText,
                RefundBlockByPolicySmsContent = branchData.BranchNotificationTemplates.FirstOrDefault(s => s.IsActive && !s.IsDeleted && s.NotificationTypeId == (int)ServiceNotificationType.RejectionWithPolicyRefund)?.SmsText,
                BranchInsuranceRefundSettings = branchData.BranchInsuranceRefundSettings.Where(w => w.IsActive && !w.IsDeleted).Select(s => new BranchInsuranceRefundSettingRequestDto.BranchInsuranceRefundSettingTypeDto
                {
                    BlockDate = s.BlockDate,
                    TypeId = s.InsurancePolicyTypeId
                }).ToList()
            };

        }

        public async Task<AddResponseDto> AddUpdateBranchInsuranceRefundSettingAsync(AddUpdateBranchInsuranceRefundSettingRequestDto request)
        {
            var branchData = await _unitOfWorkPortalDb.GetRepository<Branch>().Entities
                    .Include(i => i.BranchNotificationTemplates)
                    .Include(i => i.BranchInsuranceRefundSettings)
                .Where(s => s.IsActive && !s.IsDeleted && s.Id == request.BranchId).FirstOrDefaultAsync();

            if (branchData == null)
                throw new NoFoundDataPortalException($"{nameof(SiteResources.Exception_NoRecordsFound).ToSiteResourcesValue(request.LanguageId)} ({nameof(SiteResources.Branch).ToSiteResourcesValue(request.LanguageId)})");

            var existingSettingDataSet = branchData.BranchInsuranceRefundSettings.Where(s => s.IsActive && !s.IsDeleted).ToList();

            if (existingSettingDataSet.Any()) //update
            {
                existingSettingDataSet.ForEach(s => { s.IsActive = false; s.IsDeleted = true; });

                _unitOfWorkPortalDb.GetRepository<BranchInsuranceRefundSetting>().UpdateRange(existingSettingDataSet);
                await _unitOfWorkPortalDb.SaveChangesAsync();
            }

            if (request.InsuranceList.Any())
            {

                var newDataList = request.InsuranceList.Select(s => new BranchInsuranceRefundSetting
                {
                    InsurancePolicyTypeId = s.TypeId,
                    BranchId = request.BranchId,
                    BlockDate = s.StartDate.GetValueOrDefault(),
                    IsActive = true,
                    IsDeleted = false,
                }).ToList();

                await _unitOfWorkPortalDb.GetRepository<BranchInsuranceRefundSetting>().AddRangeAsync(newDataList);
                await _unitOfWorkPortalDb.SaveChangesAsync();
            }

            branchData.IsRefundBlockByAllPolicies = request.IsRefundBlockByAllPolicies;
            branchData.RefundBlockByAllPoliciesDate = request.RefundBlockByAllPoliciesDate;

            _unitOfWorkPortalDb.GetRepository<Branch>().Update(branchData);
            await _unitOfWorkPortalDb.SaveChangesAsync();

            var existingNotification = branchData.BranchNotificationTemplates.FirstOrDefault(s => s.IsActive && !s.IsDeleted && s.NotificationTypeId == (int)ServiceNotificationType.RejectionWithPolicyRefund);

            if (existingNotification == null)
            {
                var newNotificationData = new BranchNotificationTemplate
                {
                    BranchId = request.BranchId,
                    LanguageId = 2,
                    MailText = request.MailContent,
                    SmsText = request.SmsContent,
                    NotificationName = "Refund Policy Rejection",
                    NotificationTypeId = (int)ServiceNotificationType.RejectionWithPolicyRefund,
                    Subject = "Rejection Notification",
                    IsActive = true,
                    IsDeleted = false,
                };

                await _unitOfWorkPortalDb.GetRepository<BranchNotificationTemplate>().AddAsync(newNotificationData);
                await _unitOfWorkPortalDb.SaveChangesAsync();
            }
            else
            {
                existingNotification.SmsText = request.SmsContent;
                existingNotification.MailText = request.MailContent;

                _unitOfWorkPortalDb.GetRepository<BranchNotificationTemplate>().Update(existingNotification);
                await _unitOfWorkPortalDb.SaveChangesAsync();
            }



            return new AddResponseDto
            {
                Id = branchData.Id,
            };
        }

        #endregion

    }
}
