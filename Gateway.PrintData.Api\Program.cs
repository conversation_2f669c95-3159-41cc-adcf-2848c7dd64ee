using Gateway.Logger.Core.Configuration;
using Gateway.Logger.Core.Models;
using Gateway.PrintData.Api.Extensions;
using Gateway.PrintData.Api.Hubs;
using Microsoft.AspNetCore.SignalR;
using Microsoft.Extensions.FileProviders;
using Serilog;

var builder = WebApplication.CreateBuilder(args);

var environment = Environment.GetEnvironmentVariable("ASPNETCORE_ENVIRONMENT");

var environmentFile = environment != null ? $"appsettings.{environment}.json" : "appsettings.json";

var configuration = new ConfigurationBuilder()
    .AddJsonFile(environmentFile, optional: true, reloadOnChange: true)
    .AddJsonFile("config/appsettings.json", optional: true, reloadOnChange: true)
    .AddEnvironmentVariables()
    .AddCommandLine(args)
    .Build();

Log.Logger = LoggerConfigurationBuilder.GetLoggerConfiguration(new LogConfiguration("Gateway.PrintData.Api", Environment.MachineName, configuration));

try
{
    Log.Warning("Gateway Print Data Api started...");
}
catch (Exception ex)
{
    Log.Fatal(ex, " Gateway Print Data Api terminated unexpectedly...");
}

builder.Services.RegisterDbContext(builder.Configuration);
builder.Services.RegisterMappers();
builder.Services.RegisterServices();
builder.Services.RegisterHttpContext();

builder.Services.AddControllers();

builder.Services.AddEndpointsApiExplorer();

builder.Services.AddSwaggerGen(options =>
{
    options.CustomSchemaIds(type => type.ToString());
});

builder.Services.AddResponseCompression();
builder.Services.AddHttpClient();

var connectionString = "{0},abortConnect=false,defaultDatabase={1},ssl=false,ConnectTimeout={2},allowAdmin=true,connectRetry={3}";

builder.Services.AddSignalR().AddStackExchangeRedis(string.Format(connectionString, configuration["Redis:Url"], 
   configuration["Redis:DefaultDatabase"], configuration["Redis:ConnectTimeout"], configuration["Redis:ConnectRetry"]), 
   options => { options.Configuration.ChannelPrefix = "DigitalSignature"; });

builder.Services.AddMvc();

builder.Services.AddCors(options =>
{
	options.AddPolicy("AllowSpecified",
		builder =>
		{
			if (environment is "Staging" or "Development")
				builder.WithOrigins("https://*.gateway.com.tr")
					.SetIsOriginAllowedToAllowWildcardSubdomains()
					.AllowAnyHeader()
					.AllowAnyMethod()
					.AllowCredentials();
            else if (environment == "Production" || environment == "Production-K8S")
                builder.WithOrigins("https://turkeyvisa.gateway.com.tr", "https://visaturkey.gateway.com.tr", "https://turkeyvisa-k8s.gateway.com.tr").AllowAnyHeader().AllowAnyMethod().AllowCredentials();
			else
				builder.AllowAnyHeader()
					.AllowAnyMethod()
					.SetIsOriginAllowed((host) => true)
					.AllowCredentials();
		});
});
builder.Host.UseSerilog()
    .ConfigureAppConfiguration((context, config) =>
    {
        config.AddJsonFile("config/appsettings.json", optional: true, reloadOnChange: true);
    });

var app = builder.Build();

app.UseSwagger();
app.UseSwaggerUI();

app.UseHttpsRedirection();

app.UseMiddleware<ExceptionMiddleware>();

app.UseRouting();

app.UseCors("AllowSpecified");

app.UseStaticFiles(new StaticFileOptions
{
    FileProvider = new PhysicalFileProvider(
        Path.Combine(builder.Environment.ContentRootPath, "documents")),
    RequestPath = "/StaticFiles"
});

app.UseHsts();

app.UseEndpoints(endpoints =>
{
    endpoints.MapControllers();
    endpoints.MapHub<DigitalSignatureHub>("/digitalsignaturehub");
});

app.UseSerilogRequestLogging(opts => opts.EnrichDiagnosticContext = LogEnricher.EnrichFromRequest);

app.Use(async (context, next) =>
{
    _ = context.RequestServices.GetRequiredService<IHubContext<DigitalSignatureHub>>();

    if (next == null) return;

    await next.Invoke();
});

_ = app.Services.GetService(typeof(IHubContext<DigitalSignatureHub>));

app.Run();

static class LogEnricher
{
    public static void EnrichFromRequest(IDiagnosticContext diagnosticContext, HttpContext httpContext)
    {
        diagnosticContext.Set("ClientIp", httpContext.Connection.RemoteIpAddress?.ToString());
        diagnosticContext.Set("HttpMethod", httpContext.Request.Method);
        diagnosticContext.Set("UserAgent", httpContext.Request.Headers["User-Agent"].FirstOrDefault());
    }
}
