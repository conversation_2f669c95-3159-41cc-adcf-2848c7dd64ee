﻿using Microsoft.EntityFrameworkCore;
using Newtonsoft.Json;
using Portal.Gateway.Contracts.Entities.Dto.Appointment.ApplicationStatus;
using Portal.Gateway.Entity.Entities.Portal;
using Portal.Gateway.Entity.EntityConfigurations.Portal;
using Portal.Gateway.Entity.Logger;
using System;
using System.Collections.Generic;
using System.IO;
using System.Linq;
using JsonSerializer = System.Text.Json.JsonSerializer;

namespace Portal.Gateway.Entity.Context
{
    public class PortalDbContext : DbContext
    {
        private const string seedScriptsPath = "_GatewayPortals-API-K8S";
        public PortalDbContext(DbContextOptions<PortalDbContext> options) : base(options)
        {
            AppContext.SetSwitch("Npgsql.EnableLegacyTimestampBehavior", true);
        }

        #region DbSets

        public virtual DbSet<Entities.Portal.Action> Action => base.Set<Entities.Portal.Action>();
        public virtual DbSet<ActionTranslation> ActionTranslation => base.Set<ActionTranslation>();
        public virtual DbSet<Agency> Agency => base.Set<Agency>();
        public virtual DbSet<AgencyFile> AgencyFile => base.Set<AgencyFile>();
        public virtual DbSet<AgencyNotificationTemplate> AgencyNotificationTemplate => base.Set<AgencyNotificationTemplate>();
        public virtual DbSet<AgencyType> AgencyType => base.Set<AgencyType>();
        public virtual DbSet<AgencyTypeFile> AgencyTypeFile => base.Set<AgencyTypeFile>();
        public virtual DbSet<AgencyTypeTranslation> AgencyTypeTranslation => base.Set<AgencyTypeTranslation>();
        public virtual DbSet<AgencyUser> AgencyUser => base.Set<AgencyUser>();
        public virtual DbSet<AgencyUserFile> AgencyUserFile => base.Set<AgencyUserFile>();
        public virtual DbSet<Announcement> Announcement => base.Set<Announcement>();
        public virtual DbSet<AnnouncementHistory> AnnouncementHistory => base.Set<AnnouncementHistory>();
        public virtual DbSet<Application> Application => base.Set<Application>();
        public virtual DbSet<ApplicationBrandModelData> ApplicationBrandModelData => base.Set<ApplicationBrandModelData>();
        public virtual DbSet<ApplicationCancellation> ApplicationCancellation => base.Set<ApplicationCancellation>();
        public virtual DbSet<ApplicationCancellationExtraFee> ApplicationCancellationExtraFee => base.Set<ApplicationCancellationExtraFee>();
        public virtual DbSet<ApplicationConfirmationCode> ApplicationConfirmationCode => base.Set<ApplicationConfirmationCode>();
        public virtual DbSet<ApplicationConfirmationCodeHistory> ApplicationConfirmationCodeHistory => base.Set<ApplicationConfirmationCodeHistory>();
        public virtual DbSet<ApplicationDataContact> ApplicationDataContact => base.Set<ApplicationDataContact>();
        public virtual DbSet<ApplicationDataDemographic> ApplicationDataDemographic => base.Set<ApplicationDataDemographic>();
        public virtual DbSet<ApplicationDataTravel> ApplicationDataTravel => base.Set<ApplicationDataTravel>();
        public virtual DbSet<ApplicationDataTravelDetail> ApplicationDataTravelDetail => base.Set<ApplicationDataTravelDetail>();
        public virtual DbSet<ApplicationDataTravelHistory> ApplicationDataTravelHistory => base.Set<ApplicationDataTravelHistory>();
        public virtual DbSet<ApplicationData> ApplicationData => base.Set<ApplicationData>();
        public virtual DbSet<ApplicationDocument> ApplicationDocument => base.Set<ApplicationDocument>();
        public virtual DbSet<ApplicationEmailHistory> ApplicationEmailHistory => base.Set<ApplicationEmailHistory>();
        public virtual DbSet<ApplicationExtraFee> ApplicationExtraFee => base.Set<ApplicationExtraFee>();
        public virtual DbSet<ApplicationExtraFeeHistory> ApplicationExtraFeeHistory => base.Set<ApplicationExtraFeeHistory>();
        public virtual DbSet<ApplicationFile> ApplicationFile => base.Set<ApplicationFile>();
        public virtual DbSet<ApplicationFormElement> ApplicationFormElement => base.Set<ApplicationFormElement>();
        public virtual DbSet<ApplicationHistory> ApplicationHistory => base.Set<ApplicationHistory>();
        public virtual DbSet<ApplicationInformationNote> ApplicationInformationNote => base.Set<ApplicationInformationNote>();
        public virtual DbSet<ApplicationInquiry> ApplicationInquiry => base.Set<ApplicationInquiry>();
        public virtual DbSet<ApplicationInquiryAnswer> ApplicationInquiryAnswer => base.Set<ApplicationInquiryAnswer>();
        public virtual DbSet<ApplicationInsurance> ApplicationInsurance => base.Set<ApplicationInsurance>();
        public virtual DbSet<ApplicationLastMileDataHistory> ApplicationLastMileDataHistory => base.Set<ApplicationLastMileDataHistory>();
        public virtual DbSet<ApplicationLastMileData> ApplicationLastMileData => base.Set<ApplicationLastMileData>();
        public virtual DbSet<ApplicationNote> ApplicationNote => base.Set<ApplicationNote>();
        public virtual DbSet<ApplicationOfficialNote> ApplicationOfficialNote => base.Set<ApplicationOfficialNote>();
        public virtual DbSet<ApplicationPnlFile> ApplicationPnlFile => base.Set<ApplicationPnlFile>();
        public virtual DbSet<ApplicationReferenceNumber> ApplicationReferenceNumber => base.Set<ApplicationReferenceNumber>();
        public virtual DbSet<ApplicationRejectionApprovalStatusHistory> ApplicationRejectionApprovalStatusHistory => base.Set<ApplicationRejectionApprovalStatusHistory>();
        public virtual DbSet<ApplicationSale> ApplicationSale => base.Set<ApplicationSale>();
        public virtual DbSet<ApplicationSmsHistory> ApplicationSmsHistory => base.Set<ApplicationSmsHistory>();
        public virtual DbSet<ApplicationStatus> ApplicationStatus => base.Set<ApplicationStatus>();
        public virtual DbSet<ApplicationStatusHistory> ApplicationStatusHistory => base.Set<ApplicationStatusHistory>();
        public virtual DbSet<ApplicationStatusOrder> ApplicationStatusOrder => base.Set<ApplicationStatusOrder>();
        public virtual DbSet<ApplicationStatusTranslation> ApplicationStatusTranslation => base.Set<ApplicationStatusTranslation>();
        public virtual DbSet<ApplicationSurvey> ApplicationSurvey => base.Set<ApplicationSurvey>();
        public virtual DbSet<ApplicationVisaDecision> ApplicationVisaDecision => base.Set<ApplicationVisaDecision>();
        public virtual DbSet<ApplicationVisaHistory> ApplicationVisaHistory => base.Set<ApplicationVisaHistory>();
        public virtual DbSet<ApplicationVisaHistoryHistory> ApplicationVisaHistoryHistory => base.Set<ApplicationVisaHistoryHistory>();
        public virtual DbSet<AppointmentApplicantDemand> AppointmentApplicantDemand => base.Set<AppointmentApplicantDemand>();
        public virtual DbSet<AppointmentDemand> AppointmentDemand => base.Set<AppointmentDemand>();
        public virtual DbSet<AppointmentDemandFile> AppointmentDemandFile => base.Set<AppointmentDemandFile>();
        public virtual DbSet<AppointmentDemandFileType> AppointmentDemandFileType => base.Set<AppointmentDemandFileType>();
        public virtual DbSet<AppointmentDemandFileTypeTranslation> AppointmentDemandFileTypeTranslation => base.Set<AppointmentDemandFileTypeTranslation>();
        public virtual DbSet<AppointmentDemandRejectionReason> AppointmentDemandRejectionReason => base.Set<AppointmentDemandRejectionReason>();
        public virtual DbSet<AppointmentDemandRejectionReasonTranslation> AppointmentDemandRejectionReasonTranslation => base.Set<AppointmentDemandRejectionReasonTranslation>();
        public virtual DbSet<BankPos> BankPos => base.Set<BankPos>();
        public virtual DbSet<BankPosInstallment> BankPosInstallment => base.Set<BankPosInstallment>();
        public virtual DbSet<BlackList> BlackList => base.Set<BlackList>();
        public virtual DbSet<Branch> Branch => base.Set<Branch>();
        public virtual DbSet<BranchApplicationCountry> BranchApplicationCountry => base.Set<BranchApplicationCountry>();
        public virtual DbSet<BranchApplicationCountryExtraFee> BranchApplicationCountryExtraFee => base.Set<BranchApplicationCountryExtraFee>();
        public virtual DbSet<BranchApplicationCountryExtraFeeCommission> BranchApplicationCountryExtraFeeCommission => base.Set<BranchApplicationCountryExtraFeeCommission>();
        public virtual DbSet<BranchApplicationCountryExtraFeeCost> BranchApplicationCountryExtraFeeCost => base.Set<BranchApplicationCountryExtraFeeCost>();
        public virtual DbSet<BranchApplicationCountryExtraFeeYedek> BranchApplicationCountryExtraFeeYedek => base.Set<BranchApplicationCountryExtraFeeYedek>();
        public virtual DbSet<BranchApplicationCountryFile> BranchApplicationCountryFile => base.Set<BranchApplicationCountryFile>();
        public virtual DbSet<BranchApplicationCountryVisaCategory> BranchApplicationCountryVisaCategory => base.Set<BranchApplicationCountryVisaCategory>();
        public virtual DbSet<BranchApplicationStatus> BranchApplicationStatus => base.Set<BranchApplicationStatus>();
        public virtual DbSet<BranchDataTranslation> BranchDataTranslation => base.Set<BranchDataTranslation>();
        public virtual DbSet<BranchDepartment> BranchDepartment => base.Set<BranchDepartment>();
        public virtual DbSet<BranchDepartmentOrder> BranchDepartmentOrder => base.Set<BranchDepartmentOrder>();
        public virtual DbSet<BranchIcrNote> BranchIcrNote => base.Set<BranchIcrNote>();
        public virtual DbSet<BranchNotificationTemplate> BranchNotificationTemplate => base.Set<BranchNotificationTemplate>();
        public virtual DbSet<BranchSmsProviderPrefix> BranchSmsProviderPrefix => base.Set<BranchSmsProviderPrefix>();
        public virtual DbSet<BranchSmsProviderPrefixLog> BranchSmsProviderPrefixLog => base.Set<BranchSmsProviderPrefixLog>();
        public virtual DbSet<BranchStatusEmail> BranchStatusEmail => base.Set<BranchStatusEmail>();
        public virtual DbSet<BranchStatusSms> BranchStatusSms => base.Set<BranchStatusSms>();
        public virtual DbSet<BranchSupervisorDefinition> BranchSupervisorDefinition => base.Set<BranchSupervisorDefinition>();
        public virtual DbSet<BranchTranslation> BranchTranslation => base.Set<BranchTranslation>();
        public virtual DbSet<CargoBranch> CargoBranch => base.Set<CargoBranch>();
        public virtual DbSet<CargoBranchOffice> CargoBranchOffice => base.Set<CargoBranchOffice>();
        public virtual DbSet<CargoDailyShipment> CargoDailyShipment => base.Set<CargoDailyShipment>();
        public virtual DbSet<CargoTrack> CargoTrack => base.Set<CargoTrack>();
        public virtual DbSet<City> City => base.Set<City>();
        public virtual DbSet<ClaimLossEntryLog> ClaimLossEntryLog => base.Set<ClaimLossEntryLog>();
        public virtual DbSet<ClientDevice> ClientDevice => base.Set<ClientDevice>();
        public virtual DbSet<ClientDeviceInventory> ClientDeviceInventory => base.Set<ClientDeviceInventory>();
        public virtual DbSet<Company> Company => base.Set<Company>();
        public virtual DbSet<CompanyAlert> CompanyAlert => base.Set<CompanyAlert>();
        public virtual DbSet<CompanyAlertMessage> CompanyAlertMessage => base.Set<CompanyAlertMessage>();
        public virtual DbSet<CompanyFile> CompanyFile => base.Set<CompanyFile>();
        public virtual DbSet<CompanyFileType> CompanyFileType => base.Set<CompanyFileType>();
        public virtual DbSet<CompanyFileTypeTranslation> CompanyFileTypeTranslation => base.Set<CompanyFileTypeTranslation>();
        public virtual DbSet<CompanyPayment> CompanyPayment => base.Set<CompanyPayment>();
        public virtual DbSet<CompanyProfileApproval> CompanyProfileApproval => base.Set<CompanyProfileApproval>();
        public virtual DbSet<CompanyProfileCompletion> CompanyProfileCompletion => base.Set<CompanyProfileCompletion>();
        public virtual DbSet<CompanyRoles> CompanyRoles => base.Set<CompanyRoles>();
        public virtual DbSet<CompanyTermsOfServices> CompanyTermsOfServices => base.Set<CompanyTermsOfServices>();
        public virtual DbSet<CompanyType> CompanyType => base.Set<CompanyType>();
        public virtual DbSet<CompanyTypeFile> CompanyTypeFile => base.Set<CompanyTypeFile>();
        public virtual DbSet<CompanyTypeTranslation> CompanyTypeTranslation => base.Set<CompanyTypeTranslation>();
        public virtual DbSet<CompanyUser> CompanyUser => base.Set<CompanyUser>();
        public virtual DbSet<CompanyUserBranch> CompanyUserBranch => base.Set<CompanyUserBranch>();
        public virtual DbSet<CompanyUserChat> CompanyUserChat => base.Set<CompanyUserChat>();
        public virtual DbSet<CompanyUserFile> CompanyUserFile => base.Set<CompanyUserFile>();
        public virtual DbSet<CompanyUserFileType> CompanyUserFileType => base.Set<CompanyUserFileType>();
        public virtual DbSet<CompanyUserFileTypeTranslation> CompanyUserFileTypeTranslation => base.Set<CompanyUserFileTypeTranslation>();
        public virtual DbSet<CompanyUserLogin> CompanyUserLogin => base.Set<CompanyUserLogin>();
        public virtual DbSet<CompanyUserPosition> CompanyUserPosition => base.Set<CompanyUserPosition>();
        public virtual DbSet<CompanyUserPositionTranslation> CompanyUserPositionTranslation => base.Set<CompanyUserPositionTranslation>();
        public virtual DbSet<ContactInformationVerificationHistory> ContactInformationVerificationHistory => base.Set<ContactInformationVerificationHistory>();
        public virtual DbSet<Counter> Counter => base.Set<Counter>();
        public virtual DbSet<Country> Country => base.Set<Country>();
        public virtual DbSet<CountryCity> CountryCity => base.Set<CountryCity>();
        public virtual DbSet<Customer> Customer => base.Set<Customer>();
        public virtual DbSet<CustomerCard> CustomerCard => base.Set<CustomerCard>();
        public virtual DbSet<CustomerCardNote> CustomerCardNote => base.Set<CustomerCardNote>();
        public virtual DbSet<Department> Department => base.Set<Department>();
        public virtual DbSet<DepartmentTranslation> DepartmentTranslation => base.Set<DepartmentTranslation>();
        public virtual DbSet<DigitalSignatureDevice> DigitalSignatureDevice => base.Set<DigitalSignatureDevice>();
        public virtual DbSet<Document> Document => base.Set<Document>();
        public virtual DbSet<EmaaBranch> EmaaBranch => base.Set<EmaaBranch>();
        public virtual DbSet<EmailProvider> EmailProvider => base.Set<EmailProvider>();
        public virtual DbSet<ExcelAppointmentData> ExcelAppointmentData => base.Set<ExcelAppointmentData>();
        public virtual DbSet<ExcelAppointmentUploadHistory> ExcelAppointmentUploadHistory => base.Set<ExcelAppointmentUploadHistory>();
        public virtual DbSet<ExtraFee> ExtraFee => base.Set<ExtraFee>();
        public virtual DbSet<ExtraFeeTranslation> ExtraFeeTranslation => base.Set<ExtraFeeTranslation>();
        public virtual DbSet<ForeignCity> ForeignCity => base.Set<ForeignCity>();
        public virtual DbSet<ForeignHealthInsurance> ForeignHealthInsurance => base.Set<ForeignHealthInsurance>();
        public virtual DbSet<GeneratedToken> GeneratedToken => base.Set<GeneratedToken>();
        public virtual DbSet<GeneratedTokenHistory> GeneratedTokenHistory => base.Set<GeneratedTokenHistory>();
        public virtual DbSet<GeneratedTokenNote> GeneratedTokenNote => base.Set<GeneratedTokenNote>();
        public virtual DbSet<HealthInstitution> HealthInstitution => base.Set<HealthInstitution>();
        public virtual DbSet<IncorrectApplicationStatusHistory> IncorrectApplicationStatusHistory => base.Set<IncorrectApplicationStatusHistory>();
        public virtual DbSet<Inquiry> Inquiry => base.Set<Inquiry>();
        public virtual DbSet<IhbOrder> IhbOrder => base.Set<IhbOrder>();
        public virtual DbSet<CancelInsuranceOrder> CancelInsuranceOrder => base.Set<CancelInsuranceOrder>();
        public virtual DbSet<InquiryQuestion> InquiryQuestion => base.Set<InquiryQuestion>();
        public virtual DbSet<InquiryQuestionChoice> InquiryQuestionChoice => base.Set<InquiryQuestionChoice>();
        public virtual DbSet<InquiryQuestionChoiceTranslation> InquiryQuestionChoiceTranslation => base.Set<InquiryQuestionChoiceTranslation>();
        public virtual DbSet<InquiryQuestionTranslation> InquiryQuestionTranslation => base.Set<InquiryQuestionTranslation>();
        public virtual DbSet<InquiryTranslation> InquiryTranslation => base.Set<InquiryTranslation>();
        public virtual DbSet<ApplicationInquiryAnswerSelection> ApplicationInquiryAnswerSelection => base.Set<ApplicationInquiryAnswerSelection>();
        public virtual DbSet<InquiryQuestionChoiceSelection> InquiryQuestionChoiceSelection => base.Set<InquiryQuestionChoiceSelection>();
        public virtual DbSet<InquiryQuestionChoiceSelectionTranslation> InquiryQuestionChoiceSelectionTranslation => base.Set<InquiryQuestionChoiceSelectionTranslation>();
        public virtual DbSet<Inventory> Inventory => base.Set<Inventory>();
        public virtual DbSet<KsaClearServiceParams> KsaClearServiceParams => base.Set<KsaClearServiceParams>();
        public virtual DbSet<KsaClearTaxServiceLog> KsaClearTaxServiceLog => base.Set<KsaClearTaxServiceLog>();
        public virtual DbSet<Line> Line => base.Set<Line>();
        public virtual DbSet<LineDepartment> LineDepartment => base.Set<LineDepartment>();
        public virtual DbSet<LineDepartmentConnection> LineDepartmentConnection => base.Set<LineDepartmentConnection>();
        public virtual DbSet<LookupRule> LookupRule => base.Set<LookupRule>();
        public virtual DbSet<MasterCompany> MasterCompany => base.Set<MasterCompany>();
        public virtual DbSet<MasterCompanyModule> MasterCompanyModule => base.Set<MasterCompanyModule>();
        public virtual DbSet<MissionRejection> MissionRejection => base.Set<MissionRejection>();
        public virtual DbSet<Multipledbtest> Multipledbtest => base.Set<Multipledbtest>();
        public virtual DbSet<NotCompletedReason> NotCompletedReason => base.Set<NotCompletedReason>();
        public virtual DbSet<NotCompletedReasonHistory> NotCompletedReasonHistory => base.Set<NotCompletedReasonHistory>();
        public virtual DbSet<NotCompletedReasonTranslation> NotCompletedReasonTranslation => base.Set<NotCompletedReasonTranslation>();
        public virtual DbSet<NotificationTemplate> NotificationTemplate => base.Set<NotificationTemplate>();
        public virtual DbSet<PhotoBooth> PhotoBooth => base.Set<PhotoBooth>();
        public virtual DbSet<PreApplication> PreApplication => base.Set<PreApplication>();
        public virtual DbSet<PreApplicationAdditional> PreApplicationAdditional => base.Set<PreApplicationAdditional>();
        public virtual DbSet<PreApplicationApplicant> PreApplicationApplicant => base.Set<PreApplicationApplicant>();
        public virtual DbSet<PreApplicationApplicantAdditional> PreApplicationApplicantAdditional => base.Set<PreApplicationApplicantAdditional>();
        public virtual DbSet<PreApplicationApplicantFile> PreApplicationApplicantFile => base.Set<PreApplicationApplicantFile>();
        public virtual DbSet<PreApplicationApplicantFileHistory> PreApplicationApplicantFileHistory => base.Set<PreApplicationApplicantFileHistory>();
        public virtual DbSet<PreApplicationApplicantHistory> PreApplicationApplicantHistory => base.Set<PreApplicationApplicantHistory>();
        public virtual DbSet<PreApplicationCallCenterHistory> PreApplicationCallCenterHistory => base.Set<PreApplicationCallCenterHistory>();
        public virtual DbSet<PreApplicationHistory> PreApplicationHistory => base.Set<PreApplicationHistory>();
        public virtual DbSet<PrinterAgent> PrinterAgent => base.Set<PrinterAgent>();
        public virtual DbSet<PrinterInfo> PrinterInfo => base.Set<PrinterInfo>();
        public virtual DbSet<PrinterType> PrinterType => base.Set<PrinterType>();
        public virtual DbSet<Provider> Provider => base.Set<Provider>();
        public virtual DbSet<QualityCheck> QualityCheck => base.Set<QualityCheck>();
        public virtual DbSet<QualityCheckHistory> QualityCheckHistory => base.Set<QualityCheckHistory>();
        public virtual DbSet<QueueMatic> QueueMatic => base.Set<QueueMatic>();
        public virtual DbSet<QueueMaticHistory> QueueMaticHistory => base.Set<QueueMaticHistory>();
        public virtual DbSet<QueueSequence> QueueSequence => base.Set<QueueSequence>();
        public virtual DbSet<Role> Role => base.Set<Role>();
        public virtual DbSet<RoleAction> RoleAction => base.Set<RoleAction>();
        public virtual DbSet<RoleTranslation> RoleTranslation => base.Set<RoleTranslation>();
        public virtual DbSet<SapApplicationExtraFee> SapApplicationExtraFee => base.Set<SapApplicationExtraFee>();
        public virtual DbSet<SapApplicationOrder> SapApplicationOrder => base.Set<SapApplicationOrder>();
        public virtual DbSet<SapBranch> SapBranch => base.Set<SapBranch>();
        public virtual DbSet<SapCashNumberForBranch> SapCashNumberForBranch => base.Set<SapCashNumberForBranch>();
        public virtual DbSet<SapExtraFee> SapExtraFee => base.Set<SapExtraFee>();
        public virtual DbSet<SapRateInformation> SapRateInformation => base.Set<SapRateInformation>();
        public virtual DbSet<SapUser> SapUser => base.Set<SapUser>();
        public virtual DbSet<Screen> Screen => base.Set<Screen>();
        public virtual DbSet<SendVisaRejectionSapLog> SendVisaRejectionSapLog => base.Set<SendVisaRejectionSapLog>();
        public virtual DbSet<Slot> Slot => base.Set<Slot>();
        public virtual DbSet<SlotDemand> SlotDemand => base.Set<SlotDemand>();
        public virtual DbSet<SlotDemandReason> SlotDemandReason => base.Set<SlotDemandReason>();
        public virtual DbSet<SlotDemandReasonTranslation> SlotDemandReasonTranslation => base.Set<SlotDemandReasonTranslation>();
        public virtual DbSet<SmsProvider> SmsProvider => base.Set<SmsProvider>();
        public virtual DbSet<TempEmaaHasarDosyaVerileri> TempEmaaHasarDosyaVerileri => base.Set<TempEmaaHasarDosyaVerileri>();
        public virtual DbSet<TempEmaaHasarDosyaVerileriYedek> TempEmaaHasarDosyaVerileriYedek => base.Set<TempEmaaHasarDosyaVerileriYedek>();
        public virtual DbSet<TempExtraFeeUpdateByBranch> TempExtraFeeUpdateByBranch => base.Set<TempExtraFeeUpdateByBranch>();
        public virtual DbSet<Token> Token => base.Set<Token>();
        public virtual DbSet<TokenNumberProcess> TokenNumberProcess => base.Set<TokenNumberProcess>();
        public virtual DbSet<UnicoBranchCountry> UnicoBranchCountry => base.Set<UnicoBranchCountry>();
        public virtual DbSet<UnicoCountry> UnicoCountry => base.Set<UnicoCountry>();
        public virtual DbSet<User> User => base.Set<User>();
        public virtual DbSet<UserBranch> UserBranch => base.Set<UserBranch>();
        public virtual DbSet<UserModule> UserModule => base.Set<UserModule>();
        public virtual DbSet<UserRole> UserRole => base.Set<UserRole>();
        public virtual DbSet<UserShortcut> UserShortcut => base.Set<UserShortcut>();
        public virtual DbSet<VasTypeCost> VasTypeCost => base.Set<VasTypeCost>();
        public virtual DbSet<VasTypeMessage> VasTypeMessage => base.Set<VasTypeMessage>();
        public virtual DbSet<VisaCategoryFile> VisaCategoryFile => base.Set<VisaCategoryFile>();
        public virtual DbSet<VisaInformation> VisaInformation => base.Set<VisaInformation>();
        public virtual DbSet<VisaInformationMessage> VisaInformationMessage => base.Set<VisaInformationMessage>();
        public virtual DbSet<WhiteList> WhiteList => base.Set<WhiteList>();
        public virtual DbSet<WhiteListHistory> WhiteListHistory => base.Set<WhiteListHistory>();
        public virtual DbSet<MinistrySignProcess> MinistrySignProcess => base.Set<MinistrySignProcess>();
        public virtual DbSet<MinistryProcess> MinistryProcess => base.Set<MinistryProcess>();
        public virtual DbSet<MinistryProcessHistory> MinistryProcessHistory => base.Set<MinistryProcessHistory>();
        public virtual DbSet<AllStatusUpdateModel> AllStatusUpdateModel => base.Set<AllStatusUpdateModel>();
        public virtual DbSet<BranchDigitalSignatureDocument> BranchDigitalSignatureDocument => base.Set<BranchDigitalSignatureDocument>();
        public virtual DbSet<BranchShiftHoliday> BranchShiftHoliday => base.Set<BranchShiftHoliday>();
        public virtual DbSet<BranchHolidaysBranchShiftHoliday> BranchHolidaysBranchShiftHoliday => base.Set<BranchHolidaysBranchShiftHoliday>();
        public virtual DbSet<DayOfWeekBranchShiftHoliday> DayOfWeekBranchShiftHoliday => base.Set<DayOfWeekBranchShiftHoliday>();
        public virtual DbSet<ApplicationInquiryExplanation> ApplicationInquiryExplanation => base.Set<ApplicationInquiryExplanation>();
        public virtual DbSet<VisaType> VisaType => base.Set<VisaType>();
        public virtual DbSet<VisaTypeTranslation> VisaTypeTranslation => base.Set<VisaTypeTranslation>();
        public virtual DbSet<BranchApplicationCountryCompanyExtraFee> BranchApplicationCountryCompanyExtraFee => base.Set<BranchApplicationCountryCompanyExtraFee>();
        public virtual DbSet<MainExtraFeeCategory> MainExtraFeeCategory => base.Set<MainExtraFeeCategory>();
        public virtual DbSet<MainExtraFeeCategoryTranslation> MainExtraFeeCategoryTranslation => base.Set<MainExtraFeeCategoryTranslation>();
        public virtual DbSet<PerformanceManagement> PerformanceManagement => base.Set<PerformanceManagement>();

        #endregion


        protected override void OnModelCreating(ModelBuilder modelBuilder)
        {
            modelBuilder.HasPostgresExtension("citext");
            modelBuilder.UseSerialColumns();

            #region EntityConfigurations

            modelBuilder.ApplyConfiguration(new ActionEntityConfiguration());
            modelBuilder.ApplyConfiguration(new ActionTranslationEntityConfiguration());
            modelBuilder.ApplyConfiguration(new AgencyEntityConfiguration());
            modelBuilder.ApplyConfiguration(new AgencyFileEntityConfiguration());
            modelBuilder.ApplyConfiguration(new AgencyNotificationTemplateEntityConfiguration());
            modelBuilder.ApplyConfiguration(new AgencyTypeEntityConfiguration());
            modelBuilder.ApplyConfiguration(new AgencyTypeFileEntityConfiguration());
            modelBuilder.ApplyConfiguration(new AgencyTypeTranslationEntityConfiguration());
            modelBuilder.ApplyConfiguration(new AgencyUserEntityConfiguration());
            modelBuilder.ApplyConfiguration(new AgencyUserFileEntityConfiguration());
            modelBuilder.ApplyConfiguration(new AnnouncementEntityConfiguration());
            modelBuilder.ApplyConfiguration(new AnnouncementHistoryEntityConfiguration());
            modelBuilder.ApplyConfiguration(new ApplicationEntityConfiguration());
            modelBuilder.ApplyConfiguration(new ApplicationBrandModelDataEntityConfiguration());
            modelBuilder.ApplyConfiguration(new ApplicationCancellationEntityConfiguration());
            modelBuilder.ApplyConfiguration(new ApplicationCancellationExtraFeeEntityConfiguration());
            modelBuilder.ApplyConfiguration(new ApplicationConfirmationCodeEntityConfiguration());
            modelBuilder.ApplyConfiguration(new ApplicationConfirmationCodeHistoryEntityConfiguration());
            modelBuilder.ApplyConfiguration(new ApplicationDataEntityConfiguration());
            modelBuilder.ApplyConfiguration(new ApplicationDataContactEntityConfiguration());
            modelBuilder.ApplyConfiguration(new ApplicationDataDemographicEntityConfiguration());
            modelBuilder.ApplyConfiguration(new ApplicationDataTravelEntityConfiguration());
            modelBuilder.ApplyConfiguration(new ApplicationDataTravelDetailEntityConfiguration());
            modelBuilder.ApplyConfiguration(new ApplicationDataTravelHistoryEntityConfiguration());
            modelBuilder.ApplyConfiguration(new ApplicationDocumentEntityConfiguration());
            modelBuilder.ApplyConfiguration(new ApplicationEmailHistoryEntityConfiguration());
            modelBuilder.ApplyConfiguration(new ApplicationExtraFeeEntityConfiguration());
            modelBuilder.ApplyConfiguration(new ApplicationExtraFeeHistoryEntityConfiguration());
            modelBuilder.ApplyConfiguration(new ApplicationFileEntityConfiguration());
            modelBuilder.ApplyConfiguration(new ApplicationFormElementEntityConfiguration());
            modelBuilder.ApplyConfiguration(new ApplicationHistoryEntityConfiguration());
            modelBuilder.ApplyConfiguration(new ApplicationInformationNoteEntityConfiguration());
            modelBuilder.ApplyConfiguration(new ApplicationInquiryEntityConfiguration());
            modelBuilder.ApplyConfiguration(new ApplicationInquiryAnswerEntityConfiguration());
            modelBuilder.ApplyConfiguration(new ApplicationInsuranceEntityConfiguration());
            modelBuilder.ApplyConfiguration(new ApplicationLastMileDataEntityConfiguration());
            modelBuilder.ApplyConfiguration(new ApplicationLastMileDataHistoryEntityConfiguration());
            modelBuilder.ApplyConfiguration(new ApplicationNoteEntityConfiguration());
            modelBuilder.ApplyConfiguration(new ApplicationOfficialNoteEntityConfiguration());
            modelBuilder.ApplyConfiguration(new ApplicationPnlFileEntityConfiguration());
            modelBuilder.ApplyConfiguration(new ApplicationReferenceNumberEntityConfiguration());
            modelBuilder.ApplyConfiguration(new ApplicationRejectionApprovalStatusHistoryEntityConfiguration());
            modelBuilder.ApplyConfiguration(new ApplicationSaleEntityConfiguration());
            modelBuilder.ApplyConfiguration(new ApplicationSmsHistoryEntityConfiguration());
            modelBuilder.ApplyConfiguration(new ApplicationStatusEntityConfiguration());
            modelBuilder.ApplyConfiguration(new ApplicationStatusHistoryEntityConfiguration());
            modelBuilder.ApplyConfiguration(new ApplicationStatusOrderEntityConfiguration());
            modelBuilder.ApplyConfiguration(new ApplicationStatusTranslationEntityConfiguration());
            modelBuilder.ApplyConfiguration(new ApplicationSurveyEntityConfiguration());
            modelBuilder.ApplyConfiguration(new ApplicationVisaDecisionEntityConfiguration());
            modelBuilder.ApplyConfiguration(new ApplicationVisaHistoryEntityConfiguration());
            modelBuilder.ApplyConfiguration(new ApplicationVisaHistoryHistoryEntityConfiguration());
            modelBuilder.ApplyConfiguration(new AppointmentApplicantDemandEntityConfiguration());
            modelBuilder.ApplyConfiguration(new AppointmentDemandEntityConfiguration());
            modelBuilder.ApplyConfiguration(new AppointmentDemandFileEntityConfiguration());
            modelBuilder.ApplyConfiguration(new AppointmentDemandFileTypeEntityConfiguration());
            modelBuilder.ApplyConfiguration(new AppointmentDemandFileTypeTranslationEntityConfiguration());
            modelBuilder.ApplyConfiguration(new AppointmentDemandRejectionReasonEntityConfiguration());
            modelBuilder.ApplyConfiguration(new AppointmentDemandRejectionReasonTranslationEntityConfiguration());
            modelBuilder.ApplyConfiguration(new BankPosEntityConfiguration());
            modelBuilder.ApplyConfiguration(new BankPosInstallmentEntityConfiguration());
            modelBuilder.ApplyConfiguration(new BlackListEntityConfiguration());
            modelBuilder.ApplyConfiguration(new BlackListNoteEntityConfiguration());
            modelBuilder.ApplyConfiguration(new BranchEntityConfiguration());
            modelBuilder.ApplyConfiguration(new BranchInsuranceRefundSettingEntityConfiguration());
            modelBuilder.ApplyConfiguration(new BranchApplicationCountryEntityConfiguration());
            modelBuilder.ApplyConfiguration(new BranchApplicationCountryExtraFeeEntityConfiguration());
            modelBuilder.ApplyConfiguration(new BranchApplicationCountryExtraFeeCommissionEntityConfiguration());
            modelBuilder.ApplyConfiguration(new BranchApplicationCountryExtraFeeCostEntityConfiguration());
            modelBuilder.ApplyConfiguration(new BranchApplicationCountryExtraFeeYedekEntityConfiguration());
            modelBuilder.ApplyConfiguration(new BranchApplicationCountryFileEntityConfiguration());
            modelBuilder.ApplyConfiguration(new BranchApplicationCountryVisaCategoryEntityConfiguration());
            modelBuilder.ApplyConfiguration(new BranchApplicationStatusEntityConfiguration());
            modelBuilder.ApplyConfiguration(new BranchDataTranslationEntityConfiguration());
            modelBuilder.ApplyConfiguration(new BranchDepartmentEntityConfiguration());
            modelBuilder.ApplyConfiguration(new BranchDepartmentOrderEntityConfiguration());
            modelBuilder.ApplyConfiguration(new BranchIcrNoteEntityConfiguration());
            modelBuilder.ApplyConfiguration(new BranchNotificationTemplateEntityConfiguration());
            modelBuilder.ApplyConfiguration(new BranchSmsProviderPrefixEntityConfiguration());
            modelBuilder.ApplyConfiguration(new BranchSmsProviderPrefixLogEntityConfiguration());
            modelBuilder.ApplyConfiguration(new BranchStatusEmailEntityConfiguration());
            modelBuilder.ApplyConfiguration(new BranchStatusSmsEntityConfiguration());
            modelBuilder.ApplyConfiguration(new BranchSupervisorDefinitionEntityConfiguration());
            modelBuilder.ApplyConfiguration(new BranchTranslationEntityConfiguration());
            modelBuilder.ApplyConfiguration(new CargoBranchEntityConfiguration());
            modelBuilder.ApplyConfiguration(new CargoBranchOfficeEntityConfiguration());
            modelBuilder.ApplyConfiguration(new CargoDailyShipmentEntityConfiguration());
            modelBuilder.ApplyConfiguration(new CargoTrackEntityConfiguration());
            modelBuilder.ApplyConfiguration(new CityEntityConfiguration());
            modelBuilder.ApplyConfiguration(new ClaimLossEntryLogEntityConfiguration());
            modelBuilder.ApplyConfiguration(new ClientDeviceEntityConfiguration());
            modelBuilder.ApplyConfiguration(new ClientDeviceInventoryEntityConfiguration());
            modelBuilder.ApplyConfiguration(new CompanyEntityConfiguration());
            modelBuilder.ApplyConfiguration(new CompanyAlertEntityConfiguration());
            modelBuilder.ApplyConfiguration(new CompanyAlertMessageEntityConfiguration());
            modelBuilder.ApplyConfiguration(new CompanyFileEntityConfiguration());
            modelBuilder.ApplyConfiguration(new CompanyFileTypeEntityConfiguration());
            modelBuilder.ApplyConfiguration(new CompanyFileTypeTranslationEntityConfiguration());
            modelBuilder.ApplyConfiguration(new CompanyPaymentEntityConfiguration());
            modelBuilder.ApplyConfiguration(new CompanyProfileApprovalEntityConfiguration());
            modelBuilder.ApplyConfiguration(new CompanyProfileCompletionEntityConfiguration());
            modelBuilder.ApplyConfiguration(new CompanyRolesEntityConfiguration());
            modelBuilder.ApplyConfiguration(new CompanyTermsOfServicesEntityConfiguration());
            modelBuilder.ApplyConfiguration(new CompanyTypeEntityConfiguration());
            modelBuilder.ApplyConfiguration(new CompanyTypeFileEntityConfiguration());
            modelBuilder.ApplyConfiguration(new CompanyTypeTranslationEntityConfiguration());
            modelBuilder.ApplyConfiguration(new CompanyUserEntityConfiguration());
            modelBuilder.ApplyConfiguration(new CompanyUserBranchEntityConfiguration());
            modelBuilder.ApplyConfiguration(new CompanyUserChatEntityConfiguration());
            modelBuilder.ApplyConfiguration(new CompanyUserFileEntityConfiguration());
            modelBuilder.ApplyConfiguration(new CompanyUserFileTypeEntityConfiguration());
            modelBuilder.ApplyConfiguration(new CompanyUserFileTypeTranslationEntityConfiguration());
            modelBuilder.ApplyConfiguration(new CompanyUserLoginEntityConfiguration());
            modelBuilder.ApplyConfiguration(new CompanyUserPositionEntityConfiguration());
            modelBuilder.ApplyConfiguration(new CompanyUserPositionTranslationEntityConfiguration());
            modelBuilder.ApplyConfiguration(new ContactInformationVerificationHistoryEntityConfiguration());
            modelBuilder.ApplyConfiguration(new CounterEntityConfiguration());
            modelBuilder.ApplyConfiguration(new CountryEntityConfiguration());
            modelBuilder.ApplyConfiguration(new CountryCityEntityConfiguration());
            modelBuilder.ApplyConfiguration(new CustomerEntityConfiguration());
            modelBuilder.ApplyConfiguration(new CustomerCardEntityConfiguration());
            modelBuilder.ApplyConfiguration(new CustomerCardNoteEntityConfiguration());
            modelBuilder.ApplyConfiguration(new DepartmentEntityConfiguration());
            modelBuilder.ApplyConfiguration(new DepartmentTranslationEntityConfiguration());
            modelBuilder.ApplyConfiguration(new DigitalSignatureDeviceEntityConfiguration());
            modelBuilder.ApplyConfiguration(new DocumentEntityConfiguration());
            modelBuilder.ApplyConfiguration(new EmaaBranchEntityConfiguration());
            modelBuilder.ApplyConfiguration(new EmailProviderEntityConfiguration());
            modelBuilder.ApplyConfiguration(new ExcelAppointmentDataEntityConfiguration());
            modelBuilder.ApplyConfiguration(new ExcelAppointmentUploadHistoryEntityConfiguration());
            modelBuilder.ApplyConfiguration(new ExtraFeeEntityConfiguration());
            modelBuilder.ApplyConfiguration(new ExtraFeeTranslationEntityConfiguration());
            modelBuilder.ApplyConfiguration(new ForeignCityEntityConfiguration());
            modelBuilder.ApplyConfiguration(new ForeignHealthInsuranceEntityConfiguration());
            modelBuilder.ApplyConfiguration(new GeneratedTokenEntityConfiguration());
            modelBuilder.ApplyConfiguration(new GeneratedTokenHistoryEntityConfiguration());
            modelBuilder.ApplyConfiguration(new GeneratedTokenNoteEntityConfiguration());
            modelBuilder.ApplyConfiguration(new HealthInstitutionEntityConfiguration());
            modelBuilder.ApplyConfiguration(new IncorrectApplicationStatusHistoryEntityConfiguration());
            modelBuilder.ApplyConfiguration(new IhbOrderEntityConfiguration());
            modelBuilder.ApplyConfiguration(new InquiryEntityConfiguration());
            modelBuilder.ApplyConfiguration(new InquiryQuestionEntityConfiguration());
            modelBuilder.ApplyConfiguration(new InquiryQuestionChoiceEntityConfiguration());
            modelBuilder.ApplyConfiguration(new InquiryQuestionChoiceTranslationEntityConfiguration());
            modelBuilder.ApplyConfiguration(new InquiryQuestionTranslationEntityConfiguration());
            modelBuilder.ApplyConfiguration(new InquiryTranslationEntityConfiguration());
            modelBuilder.ApplyConfiguration(new InventoryEntityConfiguration());
            modelBuilder.ApplyConfiguration(new KsaClearServiceParamsEntityConfiguration());
            modelBuilder.ApplyConfiguration(new KsaClearTaxServiceLogEntityConfiguration());
            modelBuilder.ApplyConfiguration(new LineEntityConfiguration());
            modelBuilder.ApplyConfiguration(new LineDepartmentEntityConfiguration());
            modelBuilder.ApplyConfiguration(new LineDepartmentConnectionEntityConfiguration());
            modelBuilder.ApplyConfiguration(new LookupRuleEntityConfiguration());
            modelBuilder.ApplyConfiguration(new MissionRejectionEntityConfiguration());
            modelBuilder.ApplyConfiguration(new MultipledbtestEntityConfiguration());
            modelBuilder.ApplyConfiguration(new NotCompletedReasonEntityConfiguration());
            modelBuilder.ApplyConfiguration(new NotCompletedReasonHistoryEntityConfiguration());
            modelBuilder.ApplyConfiguration(new NotCompletedReasonTranslationEntityConfiguration());
            modelBuilder.ApplyConfiguration(new NotificationTemplateEntityConfiguration());
            modelBuilder.ApplyConfiguration(new PhotoBoothEntityConfiguration());
            modelBuilder.ApplyConfiguration(new PreApplicationEntityConfiguration());
            modelBuilder.ApplyConfiguration(new PreApplicationAdditionalEntityConfiguration());
            modelBuilder.ApplyConfiguration(new PreApplicationApplicantEntityConfiguration());
            modelBuilder.ApplyConfiguration(new PreApplicationApplicantAdditionalEntityConfiguration());
            modelBuilder.ApplyConfiguration(new PreApplicationApplicantFileEntityConfiguration());
            modelBuilder.ApplyConfiguration(new PreApplicationApplicantFileHistoryEntityConfiguration());
            modelBuilder.ApplyConfiguration(new PreApplicationApplicantHistoryEntityConfiguration());
            modelBuilder.ApplyConfiguration(new PreApplicationCallCenterHistoryEntityConfiguration());
            modelBuilder.ApplyConfiguration(new PreApplicationHistoryEntityConfiguration());
            modelBuilder.ApplyConfiguration(new PrinterAgentEntityConfiguration());
            modelBuilder.ApplyConfiguration(new PrinterInfoEntityConfiguration());
            modelBuilder.ApplyConfiguration(new PrinterTypeEntityConfiguration());
            modelBuilder.ApplyConfiguration(new ProviderEntityConfiguration());
            modelBuilder.ApplyConfiguration(new QualityCheckEntityConfiguration());
            modelBuilder.ApplyConfiguration(new QualityCheckHistoryEntityConfiguration());
            modelBuilder.ApplyConfiguration(new QueueMaticEntityConfiguration());
            modelBuilder.ApplyConfiguration(new QueueMaticHistoryEntityConfiguration());
            modelBuilder.ApplyConfiguration(new QueueSequenceEntityConfiguration());
            modelBuilder.ApplyConfiguration(new RoleEntityConfiguration());
            modelBuilder.ApplyConfiguration(new RoleActionEntityConfiguration());
            modelBuilder.ApplyConfiguration(new RoleTranslationEntityConfiguration());
            modelBuilder.ApplyConfiguration(new SapApplicationExtraFeeEntityConfiguration());
            modelBuilder.ApplyConfiguration(new SapApplicationOrderEntityConfiguration());
            modelBuilder.ApplyConfiguration(new SapBranchEntityConfiguration());
            modelBuilder.ApplyConfiguration(new SapCashNumberForBranchEntityConfiguration());
            modelBuilder.ApplyConfiguration(new SapExtraFeeEntityConfiguration());
            modelBuilder.ApplyConfiguration(new SapRateInformationEntityConfiguration());
            modelBuilder.ApplyConfiguration(new SapUserEntityConfiguration());
            modelBuilder.ApplyConfiguration(new ScreenEntityConfiguration());
            modelBuilder.ApplyConfiguration(new SendVisaRejectionSapLogEntityConfiguration());
            modelBuilder.ApplyConfiguration(new SlotEntityConfiguration());
            modelBuilder.ApplyConfiguration(new SlotDemandEntityConfiguration());
            modelBuilder.ApplyConfiguration(new SlotDemandReasonEntityConfiguration());
            modelBuilder.ApplyConfiguration(new SlotDemandReasonTranslationEntityConfiguration());
            modelBuilder.ApplyConfiguration(new SmsProviderEntityConfiguration());
            modelBuilder.ApplyConfiguration(new TempEmaaHasarDosyaVerileriEntityConfiguration());
            modelBuilder.ApplyConfiguration(new TempEmaaHasarDosyaVerileriYedekEntityConfiguration());
            modelBuilder.ApplyConfiguration(new TempExtraFeeUpdateByBranchEntityConfiguration());
            modelBuilder.ApplyConfiguration(new TokenEntityConfiguration());
            modelBuilder.ApplyConfiguration(new TokenNumberProcessEntityConfiguration());
            modelBuilder.ApplyConfiguration(new UnicoBranchCountryEntityConfiguration());
            modelBuilder.ApplyConfiguration(new UnicoCountryEntityConfiguration());
            modelBuilder.ApplyConfiguration(new UserEntityConfiguration());
            modelBuilder.ApplyConfiguration(new UserBranchEntityConfiguration());
            modelBuilder.ApplyConfiguration(new UserModuleEntityConfiguration());
            modelBuilder.ApplyConfiguration(new UserRoleEntityConfiguration());
            modelBuilder.ApplyConfiguration(new UserShortcutEntityConfiguration());
            modelBuilder.ApplyConfiguration(new VasTypeCostEntityConfiguration());
            modelBuilder.ApplyConfiguration(new VasTypeMessageEntityConfiguration());
            modelBuilder.ApplyConfiguration(new VisaCategoryFileEntityConfiguration());
            modelBuilder.ApplyConfiguration(new VisaInformationEntityConfiguration());
            modelBuilder.ApplyConfiguration(new VisaInformationMessageEntityConfiguration());
            modelBuilder.ApplyConfiguration(new WhiteListEntityConfiguration());
            modelBuilder.ApplyConfiguration(new WhiteListHistoryEntityConfiguration());
            modelBuilder.ApplyConfiguration(new BranchDigitalSignatureDocumentEntityConfiguration());
            modelBuilder.ApplyConfiguration(new BranchShiftHolidayEntityConfiguration());
            modelBuilder.ApplyConfiguration(new BranchHolidaysBranchShiftHolidayEntityConfiguration());
            modelBuilder.ApplyConfiguration(new DayOfWeekBranchShiftHolidayEntityConfiguration());
            modelBuilder.ApplyConfiguration(new MinistrySignProcessEntityConfiguration());
            modelBuilder.ApplyConfiguration(new MinistryProcessEntityConfiguration());
            modelBuilder.ApplyConfiguration(new MinistryProcessHistoryEntityConfiguration());
            modelBuilder.ApplyConfiguration(new ApplicationInquiryExplanationEntityConfiguration());
            modelBuilder.ApplyConfiguration(new BranchApplicationCountryCompanyExtraFeeEntityConfiguration());

            modelBuilder.ApplyConfiguration(new VisaTypeEntityConfiguration());
            modelBuilder.ApplyConfiguration(new VisaTypeTranslationEntityConfiguration());
            modelBuilder.ApplyConfiguration(new MainExtraFeeCategoryEntityConfiguration());
            modelBuilder.ApplyConfiguration(new MainExtraFeeCategoryTranslationEntityConfiguration());
            modelBuilder.ApplyConfiguration(new MasterCompanyEntityConfiguration());
            modelBuilder.ApplyConfiguration(new MasterCompanyModuleEntityConfiguration());

            modelBuilder.ApplyConfiguration(new PerformanceManagementEntityConfiguration());
            modelBuilder.ApplyConfiguration(new VmsVideoEntityConfiguration());
            modelBuilder.ApplyConfiguration(new VmsAnnouncementEntityConfiguration());
            modelBuilder.ApplyConfiguration(new VmsAnnouncementTranslationEntityConfiguration());
            #endregion


            base.OnModelCreating(modelBuilder);
        }

        protected List<TEntity> SeedEntity<TEntity>(string path)
        {
            var result = new List<TEntity>();

            using (StreamReader r = new StreamReader(path))
            {
                string json = r.ReadToEnd();

                result = JsonConvert.DeserializeObject<List<TEntity>>(json);
            }
            return result;
        }
        public static string SeedCustomDbScripts(string seedName)
        {
            var path = @""+ seedScriptsPath + "/migrationScripts/Resources/SeedDbScripts/" + seedName + ".sql";
            string sqlQuery = string.Empty;
            sqlQuery += File.ReadAllText(path);
            return sqlQuery;
        }
        public override int SaveChanges()
        {
            _ = OnBeforeLogging();

            return base.SaveChanges();
        }
        protected string OnBeforeLogging()
        {
            try
            {
                var result = new AuditLog() { EntityChanges = new List<EntityChange>() };
                var modifiedEntities = ChangeTracker.Entries().Where(p => p.State == EntityState.Modified).ToList();

                foreach (var entity in modifiedEntities)
                {
                    var primaryKey = entity.OriginalValues.Properties.First(p => p.IsPrimaryKey()).Name;

                    var entityChanges = new EntityChange
                    {
                        EntityName = entity.Entity.GetType().Name,
                        Id = int.Parse(entity.OriginalValues[primaryKey]?.ToString()),
                        Changes = new List<Change>()
                    };

                    foreach (var item in entity.OriginalValues.Properties)
                    {
                        var originalValue = entity.OriginalValues[item.Name]?.ToString();
                        var currentValue = entity.CurrentValues[item.Name]?.ToString();

                        if (originalValue != currentValue)
                        {
                            var changes = new Change()
                            {
                                NewValue = currentValue,
                                OldValue = originalValue,
                                Field = item.Name
                            };
                            entityChanges.Changes.Add(changes);
                        }
                    }
                    result.EntityChanges.Add(entityChanges);
                    result.DateChanged = DateTime.Now;
                }
                return JsonSerializer.Serialize(result);
            }
            catch (Exception ex)
            {
                var error = ex.Message;

                return string.Empty;
            }
        }
    }
}
