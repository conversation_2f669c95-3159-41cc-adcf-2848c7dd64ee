﻿using System;
using System.Collections.Generic;
using Portal.Gateway.Contracts.Entities.Enums;
using Portal.Gateway.UI.Models;

namespace Portal.Gateway.UI.Areas.Appointment.ViewModels.Application
{
    public class FilterApplicationViewModel : PaginationFilterModel
    {
        public string FilterApplicationNumber { get; set; }

        public int? FilterCountryId { get; set; }

        public int? FilterResidingCountryId { get; set; }

        public int? FilterWaitingTimeForDocument { get; set; }

        public int? FilterBranchId { get; set; }
        
        public int? FilterAgencyId { get; set; }

        public int? FilterApplicantTypeId { get; set; }

        public List<int?> FilterApplicationStatusIds { get; set; }

        public int? FilterApplicationTypeId { get; set; }

        public int? FilterLocalAuthorityStatusId { get; set; }

        public string FilterName { get; set; }

        public string FilterSurname { get; set; }

        public string FilterPassportNumber { get; set; }

        public int? FilterNationalityId { get; set; }

        public string FilterEmail { get; set; }

        public string FilterPhoneNumber { get; set; }

        public int? FilterVisaCategoryId { get; set; }

        public bool FilterAllBranchs { get; set; }
        public bool FilterIsTurkmenistanPage { get; set; }

        public bool FilterAllowPassiveDeletedApplications { get; set; }
        public int? FilterResidenceApplication { get; set; }

        public DateTime? FilterStartDate { get; set; }

        public DateTime? FilterEndDate { get; set; }

        public List<int> FilterStatusHistoryStatusIds { get; set; }

        public DateTime? FilterApplicationDate { get; set; }

        public DateTime? FilterRejectionStartDate { get; set; }

        public DateTime? FilterRejectionEndDate { get; set; }
        public DateTime? FilterEvaluationDate { get; set; }
        public bool FilterAllList { get; set; }
        public bool FilterSuitable { get; set; }
        public bool FilterNotSuitable { get; set; }
        public bool FilterWaiting { get; set; }
        public string EncryptedIhbDocumentId { get; set; }
        public bool? IsAuthorizedInterviewChecbox { get; set; }
		public int? FilterVerificationTypeId { get; set; }
        public DateTime? FilterBirthDate { get; set; }
    }
}