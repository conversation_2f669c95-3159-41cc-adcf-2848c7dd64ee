﻿using System;
using System.ComponentModel.DataAnnotations;

namespace Portal.Gateway.Entity.Entities.Portal
{
    public class ApplicationSale : AuditableEntity
    {
        public ApplicationSale()
        {
            IsActive = true;
            IsDeleted = false;
        }
        public int Id { get; set; }
        public long SaleId { get; set; }
        public int? ApplicationId { get; set; }
        public byte Status { get; set; }
        public byte Type { get; set; }
        public int? ApplicationExtraFeeId { get; set; }
        public int ExtraFeeId { get; set; }
        public uint Xmin { get; set; }
        public virtual Application Application { get; set; }
        public virtual ApplicationExtraFee ApplicationExtraFee { get; set; }
        public virtual ExtraFee ExtraFee { get; set; }
    }
}
