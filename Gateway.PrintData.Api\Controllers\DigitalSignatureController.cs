﻿using AutoMapper;
using BarcodeStandard;
using Gateway.Barcode;
using Gateway.PrintData.Api.Helpers;
using Gateway.PrintData.Api.Hubs;
using Gateway.PrintData.Api.Models.RequestModel.DigitalSignature;
using Gateway.PrintData.Api.Models.Response.DigitalSignature;
using Gateway.PrintData.Api.Models.ViewModel.DigitalSignature;
using Gateway.PrintData.Application.DigitalSignature;
using Gateway.PrintData.Application.DigitalSignature.Dto.Request;
using Gateway.PrintData.Application.DigitalSignature.Dto.Response;
using Gateway.PrintData.Application.Print;
using Gateway.PrintData.Core.Context;
using Microsoft.AspNetCore.Mvc;
using Microsoft.AspNetCore.SignalR;
using Portal.Gateway.ApiModel;
using Portal.Gateway.ApiModel.Requests;
using Portal.Gateway.ApiModel.Requests.Appointment.Application;
using Portal.Gateway.ApiModel.Requests.Appointment.PhotoBooth;
using Portal.Gateway.ApiModel.Responses;
using Portal.Gateway.ApiModel.Responses.Appointment.Application;
using Portal.Gateway.ApiModel.Responses.Appointment.PhotoBooth;
using Portal.Gateway.ApiModel.Responses.DigitalSignature;
using Portal.Gateway.ApiModel.Responses.PrintData;
using Portal.Gateway.Common.Utility;
using Portal.Gateway.Common.Utility.Extensions;
using Portal.Gateway.Common.Utility.Helpers;
using Portal.Gateway.Contracts.Entities.Constants;
using Portal.Gateway.Contracts.Entities.Dto.Appointment.Application.Requests;
using Portal.Gateway.Contracts.Entities.Dto.PrintData;
using Portal.Gateway.Contracts.Entities.Enums;
using Portal.Gateway.Contracts.Extensions;
using Portal.Gateway.Resources;
using SkiaSharp;
using SkiaSharp.QrCode;
using Swashbuckle.AspNetCore.Annotations;
using System.Drawing;
using System.Drawing.Imaging;
using System.Globalization;
using System.Text;
using static Portal.Gateway.ApiModel.Responses.Appointment.Application.ApplicationPrintAllApiResponse;
using static Portal.Gateway.Contracts.Entities.Dto.Appointment.Application.Responses.ApplicationPrintAllResponseDto;

namespace Gateway.PrintData.Api.Controllers
{
    public class DigitalSignatureController : BaseController<DigitalSignatureController>
    {
        private readonly IPrintService _printService;
        private readonly IDigitalSignatureService _digitalSignatureService;
        private readonly IHubContext<DigitalSignatureHub> _hubContext;
        private readonly IConfiguration _configuration;
        private readonly IContext _context;

        public DigitalSignatureController(IContext context, IPrintService printService,
            IDigitalSignatureService digitalSignatureService, IMapper mapper,
            IHubContext<DigitalSignatureHub> hubContext, IConfiguration configuration) : base(mapper)
        {
            _context = context;
            _printService = printService;
            _digitalSignatureService = digitalSignatureService;
            _hubContext = hubContext;
            _configuration = configuration;
            Mapper = mapper;
        }

        [SwaggerOperation(
            Summary = "Send documents to connected tablet",
            Description = "Send documents to connected tablet")]
        [HttpPost(ApiMethodName.DigitalSignature.SendToTablet)]
        public async Task<ApiResponse<SendDigitalSignatureApiResponse>> SendToTablet(
            [FromBody] SendToTabletApiRequest request)
        {
            if (request.TabletId == 0)
                return new ApiResponse<SendDigitalSignatureApiResponse>
                {
                    IsSuccess = false,
                    Data = new SendDigitalSignatureApiResponse { Result = false }
                };

            request.CreatedBy = _context.Identity.UserId;

            var result = await _digitalSignatureService.GetDeviceAsync(request.TabletId);

            if (result?.Status != DigitalSignatureDeviceStatus.Successful)
                return new ApiResponse<SendDigitalSignatureApiResponse>
                {
                    IsSuccess = false,
                    Message = result?.Message,
                    Data = new SendDigitalSignatureApiResponse
                    {
                        Result = false
                    }
                };

            await _hubContext.Clients.Group(result.Device.DeviceId).SendAsync("ReceiveDocument", request);

            return new ApiResponse<SendDigitalSignatureApiResponse>
            {
                IsSuccess = true,
                Data = new SendDigitalSignatureApiResponse { Result = true }
            };
        }

        [SwaggerOperation(
            Summary = "Provides clients to get information used in entry form",
            Description = "Get information used in entry form by id and language option.")]
        [HttpPost(ApiMethodName.DigitalSignature.ApplicationEntryForm + "{applicationId}/{languageId}")]
        public async Task<ApiResponse<DigitalSignatureBaseApiResponse>> GetApplicationEntryForm(int applicationId,
            byte languageId, [FromBody] DocumentRequestModel requestModel)
        {
            if (requestModel == null || applicationId == 0 || languageId == 0)
                throw new InvalidRequestException(EnumResources.MissingOrInvalidData);

            var request = new ApplicationEntryFormApiRequest { ApplicationId = applicationId };

            var data = await Process<GetApplicationEntryFormApiResponse, GetApplicationEntryFormDto>(request,
                () => _printService.GetEntryInformationForm(Mapper.Map<GetApplicationPrintAllRequestDto>(request)));

            if (data?.Data == null)
                throw new NoFoundDataPortalException(
                    $"{nameof(SiteResources.Exception_NoRecordsFound).ToSiteResourcesValue()} {nameof(SiteResources.Application).ToSiteResourcesValue()}: {request.ApplicationId}");

            var viewModel = new EntryInformationFormForTurkeyEnViewModel
            {
                Name = data.Data.ApplicationEntryForm.Name,
                Surname = data.Data.ApplicationEntryForm.Surname,
                Nationality = data.Data.ApplicationEntryForm.Nationality,
                BirthDate = data.Data.ApplicationEntryForm.BirthDate,
                PassportNumber = data.Data.ApplicationEntryForm.PassportNumber,
                DateOfArrival = data.Data.ApplicationEntryForm.ArrivalDate,
                BranchId = data.Data.ApplicationEntryForm.BranchId,
                Signatures = Mapper.Map<List<DocumentSignatureViewModel>>(requestModel.Signatures)
            };

            var fileName = new StringBuilder();

            switch (languageId)
            {
                case (byte)DigitalSignatureDocumentLanguage.English:
                    fileName.Append("EntryInformationFormForTurkeyEn");
                    break;
                case (byte)DigitalSignatureDocumentLanguage.French:
                    fileName.Append("EntryInformationFormForTurkeyFr");
                    break;
                case (byte)DigitalSignatureDocumentLanguage.Arabic:
                    fileName.Append("EntryInformationFormForTurkeyAr");
                    break;
                case (byte)DigitalSignatureDocumentLanguage.Turkish:
                    fileName.Append("EntryInformationFormForTurkeyTr");
                    break;
                case (byte)DigitalSignatureDocumentLanguage.Russian:
                    fileName.Append("EntryInformationFormForTurkeyRu");
                    break;
                default:
                    return new ApiResponse<DigitalSignatureBaseApiResponse>
                    {
                        IsSuccess = false,
                        Message = SiteResources.WrongLanguageSelection
                    };
            }

            var html = new StringBuilder(
                await RenderToStringHelper.RenderViewToStringAsync(fileName.ToString(), viewModel, ControllerContext));

            if (html == null)
                throw new ConvertingDataPortalException(
                    $"{nameof(SiteResources.Exception_ConvertingToHtml).ToSiteResourcesValue()} {nameof(SiteResources.Application).ToSiteResourcesValue()}: {request.ApplicationId}");

            html.Replace("{{baseUrl}}", $"{_configuration["BaseImageUri"]}");
            html.Replace("{{image001}}", $"{_configuration["BaseImageUri"]}/StaticFiles/EntryForm/Images/image001.png");
            html.Replace("{{image003}}", $"{_configuration["BaseImageUri"]}/StaticFiles/EntryForm/Images/image003.jpg");
            html.Replace("{{image005}}", $"{_configuration["BaseImageUri"]}/StaticFiles/EntryForm/Images/image005.png");
            html.Replace("{{imageO}}",
                $"{_configuration["BaseImageUri"]}/StaticFiles/EntryForm/Images/Arabic/imageO.png");

            var bytes = Encoding.UTF8.GetBytes(html.ToString());
            var htmlString = Convert.ToBase64String(bytes);

            return new ApiResponse<DigitalSignatureBaseApiResponse>
            {
                IsSuccess = true,
                Data = new DigitalSignatureBaseApiResponse
                {
                    Html = htmlString
                }
            };
        }

        [SwaggerOperation(
            Summary = "Provides clients to get information used in cargo receipt form",
            Description = "Get information used in cargo receipt form by id and language option.")]
        [HttpPost(ApiMethodName.DigitalSignature.CargoReceiptForm + "{applicationId}/{languageId}")]
        public async Task<ApiResponse<DigitalSignatureBaseApiResponse>> GetCargoReceipt(int applicationId,
            byte languageId, [FromBody] DocumentRequestModel requestModel)
        {
            if (requestModel == null || applicationId == 0 || languageId == 0)
                throw new InvalidRequestException(EnumResources.MissingOrInvalidData);

            var relationalApplication = await _printService.GetRelationalApplication(applicationId);

            applicationId = relationalApplication != null && relationalApplication.RelationalApplicationId != 0
                ? relationalApplication.RelationalApplicationId
                : applicationId;

            var request = new ApplicationEntryFormApiRequest
                { ApplicationId = applicationId, RelationalApplicationId = applicationId };

            var data = await Process<GetCargoApiResponse, GetCargoResponseDto>(request,
                () => _printService.GetCargoReport(Mapper.Map<GetApplicationPrintAllRequestDto>(request)));

            var mainApp = data?.Data?.RelationalApplications.Applicants.Count == 1
                ? data?.Data?.RelationalApplications.Applicants.FirstOrDefault()
                : data?.Data?.RelationalApplications.Applicants.FirstOrDefault(q =>
                    q.Id == data?.Data?.RelationalApplications.RelationalApplicationId);

            if (data?.Data == null || mainApp == null)
                throw new NoFoundDataPortalException(
                    $"{nameof(SiteResources.Exception_NoRecordsFound).ToSiteResourcesValue()} {nameof(SiteResources.Application).ToSiteResourcesValue()}: {request.ApplicationId}");

            var viewModel = new CargoReceiptFormViewModel
            {
                ApplicationTime = data.Data.RelationalApplications.ApplicationTime
                    .AddHours(data.Data.RelationalApplications.BranchCountryTimeOffset).DateTime,
                CityName = data.Data.RelationalApplications.BranchCityName,
                BranchCountryIso3 = data.Data.RelationalApplications.BranchCountryIso3,
                IsCargoIntegrationActive = data.Data.RelationalApplications.IsCargoIntegrationActive,
                CargoProviderId = data.Data.RelationalApplications.CargoProviderId.GetValueOrDefault(),
                MainAppAddress = mainApp.Address,
                MainAppEmail = mainApp.Email,
                MainAppPhone1 = mainApp.PhoneNumber,
                MainAppPhone2 = mainApp.PhoneNumber2,
                MainApplicant = $"{mainApp.Name} {mainApp.Surname}",
                MainApplicantPassportNumber = mainApp.PassportNumber,
                MainAppSecondContactPerson = mainApp.NameOfSecondContactPerson,
                CreatedBy = data.Data.RelationalApplications.CreatedBy,
                Applicants = data.Data.RelationalApplications.Applicants
                .Where(q => q.StatusId != (int)ApplicationStatus.Cancelled && q.ExtraFees.Any(a => a.Category == (int)ExtraFeeCategoryType.Cargo))
                    .Select(p => new CargoReceiptFormViewModel.ApplicantViewModel
                    {
                        Name = p.Name,
                        Surname = p.Surname,
                        PassportNumber = p.PassportNumber
                    }).ToList(),
                Signatures = Mapper.Map<List<DocumentSignatureViewModel>>(requestModel.Signatures)
            };

            var fileName = new StringBuilder();

            switch (languageId)
            {
                case (byte)DigitalSignatureDocumentLanguage.English:
                    fileName.Append("CargoReceiptFormEn");
                    break;
                case (byte)DigitalSignatureDocumentLanguage.French:
                    fileName.Append("CargoReceiptFormFr");
                    break;
                case (byte)DigitalSignatureDocumentLanguage.Arabic:
                    fileName.Append(viewModel.BranchCountryIso3 == "DZA"
                        ? "CargoReceiptFormAlgerianAr"
                        : "CargoReceiptFormAr");
                    break;
                case (byte)DigitalSignatureDocumentLanguage.Russian:
                    fileName.Append("CargoReceiptFormRu");
                    break;
                default:
                    return new ApiResponse<DigitalSignatureBaseApiResponse>
                    {
                        IsSuccess = false,
                        Message = SiteResources.WrongLanguageSelection
                    };
            }

            var html = new StringBuilder(
                await RenderToStringHelper.RenderViewToStringAsync(fileName.ToString(), viewModel, ControllerContext));

            if (html == null)
                throw new ConvertingDataPortalException(
                    $"{nameof(SiteResources.Exception_ConvertingToHtml).ToSiteResourcesValue()} {nameof(SiteResources.Application).ToSiteResourcesValue()}: {request.ApplicationId}");

            html.Replace("{{baseUrl}}", $"{_configuration["BaseImageUri"]}");

            var bytes = Encoding.UTF8.GetBytes(html.ToString());
            var htmlString = Convert.ToBase64String(bytes);

            return new ApiResponse<DigitalSignatureBaseApiResponse>
            {
                IsSuccess = true,
                Data = new DigitalSignatureBaseApiResponse
                {
                    Html = htmlString
                }
            };
        }

        [SwaggerOperation(
            Summary = "Provides clients to get information used in application page",
            Description = "Get information used in application page by id and language option.")]
        [HttpPost(ApiMethodName.DigitalSignature.ApplicationPage + "{applicationId}/{languageId}")]
        public async Task<ApiResponse<DigitalSignatureBaseApiResponse>> GetApplicationPage(int applicationId,
            byte languageId, [FromBody] DocumentRequestModel requestModel)
        {
            if (requestModel == null || applicationId == 0 || languageId == 0)
                throw new InvalidRequestException(EnumResources.MissingOrInvalidData);

            var request = new ApplicationEntryFormApiRequest
                { ApplicationId = applicationId, RelationalApplicationId = applicationId };

            var data = await Process<GetApplicationPageApiResponse, GetApplicationPageResponseDto>(request,
                () => _printService.GetApplicationPage(Mapper.Map<GetApplicationPrintAllRequestDto>(request)));

            if (data?.Data == null)
                throw new NoFoundDataPortalException(
                    $"{nameof(SiteResources.Exception_NoRecordsFound).ToSiteResourcesValue()} {nameof(SiteResources.Application).ToSiteResourcesValue()}: {request.ApplicationId}");

            var visaCategories = await Process<GetVisaCategoriesApiResponse, GetVisaCategoriesResponseDto>(request,
                    () => _printService.GetVisaCategoriesAsync(new GetVisaCategoriesRequestDto()));

            if (visaCategories?.Data == null || visaCategories.Data.VisaCategories == null)
                throw new NoFoundDataPortalException(
                    $"{nameof(SiteResources.Exception_NoRecordsFound).ToSiteResourcesValue()} {nameof(SiteResources.VisaCategory).ToSiteResourcesValue()}");

            var viewModel = new ApplicationPageViewModel
            {
                EncryptedId = data.Data.ApplicationPage.Id.ToEncrypt(),
                BranchApplicationCountry = data.Data.ApplicationPage.BranchApplicationCountry,
                CountryName = data.Data.ApplicationPage.CountryName,
                ApplicantTypeId = data.Data.ApplicationPage.ApplicantTypeId,
                ApplicationTypeId = data.Data.ApplicationPage.ApplicationTypeId,
                PassportNumber = data.Data.ApplicationPage.PassportNumber,
                PassportExpireDate = data.Data.ApplicationPage.PassportExpireDate,
                ApplicationPassportStatusId = data.Data.ApplicationPage.ApplicationPassportStatusId,
                TitleId = data.Data.ApplicationPage.TitleId,
                Name = data.Data.ApplicationPage.Name,
                Surname = data.Data.ApplicationPage.Surname,
                BirthDate = data.Data.ApplicationPage.BirthDate,
                GenderId = data.Data.ApplicationPage.GenderId,
                MaritalStatusId = data.Data.ApplicationPage.MaritalStatusId,
                Nationality = data.Data.ApplicationPage.Nationality,
                ResidenceNumber = data.Data.ApplicationPage.ResidenceNumber,
                MaidenName = data.Data.ApplicationPage.MaidenName,
                FatherName = data.Data.ApplicationPage.FatherName,
                MotherName = data.Data.ApplicationPage.MotherName,
                Email = data.Data.ApplicationPage.Email,
                PhoneNumber1 = data.Data.ApplicationPage.PhoneNumber1,
                PhoneNumber2 = data.Data.ApplicationPage.PhoneNumber2,
                Address = data.Data.ApplicationPage.Address,
                Note = data.Data.ApplicationPage.Note,
                ApplicationTime =
                    data.Data.ApplicationPage.ApplicationTime.AddHours(data.Data.ApplicationPage.TimeZoneOffset),
                CreatedByNameSurname = data.Data.ApplicationPage.CreatedByNameSurname,
                StatusId = data.Data.ApplicationPage.StatusId,
                Document = new ApplicationPageViewModel.ApplicationDocument
                {
                    TotalYearInCountry = data.Data.ApplicationPage.Document.TotalYearInCountry,
                    ReimbursementTypeId = data.Data.ApplicationPage.Document.ReimbursementTypeId,
                    ReimbursementSponsorDetail = data.Data.ApplicationPage.Document.ReimbursementSponsorDetail,
                    Job = data.Data.ApplicationPage.Document.Job ?? EnumHelper.GetEnumDescription(
                        typeof(DataOccupationType), data.Data.ApplicationPage.Document.OccupationId.ToString()),
                    CompanyName = data.Data.ApplicationPage.Document.CompanyName,
                    TotalYearInCompany = data.Data.ApplicationPage.Document.TotalYearInCompany,
                    MonthlySalary = data.Data.ApplicationPage.Document.MonthlySalary,
                    MonthlySalaryCurrencyId = data.Data.ApplicationPage.Document.MonthlySalaryCurrencyId,
                    HasBankAccount = data.Data.ApplicationPage.Document.HasBankAccount,
                    BankBalance = data.Data.ApplicationPage.Document.BankBalance,
                    BankBalanceCurrencyId = data.Data.ApplicationPage.Document.BankBalanceCurrencyId,
                    HasDeed = data.Data.ApplicationPage.Document.HasDeed,
                    VisaCategoryId = data.Data.ApplicationPage.Document.VisaCategoryId,
                    VisaCategory = visaCategories.Data.VisaCategories.FirstOrDefault(s => s.Id == data.Data.ApplicationPage.Document.VisaCategoryId) == null ? string.Empty :
                    visaCategories.Data.VisaCategories.FirstOrDefault(s => s.Id == data.Data.ApplicationPage.Document.VisaCategoryId).Translations.FirstOrDefault(s => s.LanguageId == GetIcrLanguageFromPrintLanguage(languageId)) == null ?
                    visaCategories.Data.VisaCategories.FirstOrDefault(s => s.Id == data.Data.ApplicationPage.Document.VisaCategoryId).Translations.FirstOrDefault().Name :
                    visaCategories.Data.VisaCategories.FirstOrDefault(s => s.Id == data.Data.ApplicationPage.Document.VisaCategoryId).Translations.FirstOrDefault(s => s.LanguageId == GetIcrLanguageFromPrintLanguage(languageId)).Name,
                    NumberOfEntryId = EnumHelper.GetEnumDescription(typeof(NumberOfEntryType),
                        data.Data.ApplicationPage.Document.NumberOfEntryId.ToString()),
                    HasEntryBan = data.Data.ApplicationPage.Document.HasEntryBan,
                    EntryDate = data.Data.ApplicationPage.Document.EntryDate,
                    ExitDate = data.Data.ApplicationPage.Document.ExitDate,
                    CityName = data.Data.ApplicationPage.Document.CityName,
                    AccomodationDetail = data.Data.ApplicationPage.Document.AccomodationDetail,
                    HasRelativeAbroad = data.Data.ApplicationPage.Document.HasRelativeAbroad,
                    RelativeLocation = data.Data.ApplicationPage.Document.RelativeLocation,
                    PersonTravelWith = data.Data.ApplicationPage.Document.PersonTravelWith,
                    PersonTravelWithHasVisa = data.Data.ApplicationPage.Document.PersonTravelWithHasVisa,
                    ApplicationTogether = data.Data.ApplicationPage.Document.ApplicationTogether,
                    ApplicationTogetherFiftyYearCount =
                        data.Data.ApplicationPage.Document.ApplicationTogetherFiftyYearCount,
                    ApplicationTogetherFifteenYearCount =
                        data.Data.ApplicationPage.Document.ApplicationTogetherFifteenYearCount,
                    HasPersonVisitedTurkeyBefore = data.Data.ApplicationPage.Document.HasPersonVisitedTurkeyBefore,
                    ResidenceApplication = data.Data.ApplicationPage.Document.ResidenceApplicationToBeMade switch
                    {
                        true => (int)YesNoQuestion.Yes, false => (int)YesNoQuestion.No,
                        _ => (int)YesNoQuestion.Unspecified
                    }
                },
                VisaHistories = data.Data.ApplicationPage.VisaHistories.Select(p =>
                    new ApplicationPageViewModel.ApplicationVisaHistory
                    {
                        CountryName = p.CountryName,
                        FromDate = p.FromDate,
                        UntilDate = p.UntilDate,
                        IsUsed = p.IsUsed,
                        NumberOfEntry =
                            EnumHelper.GetEnumDescription(typeof(NumberOfEntryType), p.NumberOfEntryId.ToString()),
                        VisaIsUsedYearString =
                            EnumHelper.GetEnumDescription(typeof(VisaIsUsedYear), p.VisaIsUsedYear.ToString()),
                        OldVisaDecision = p.OldVisaDecisionId == null
                            ? ""
                            : EnumHelper.GetEnumDescription(typeof(OldVisaDecisionType), p.OldVisaDecisionId.ToString())
                    }).ToList(),
                ExtraFees = data.Data.ApplicationPage.ExtraFees.Where(q => q.ShowInSummary).Select(p =>
                    new ApplicationPageViewModel.ApplicationExtraFee
                    {
                        TypeId = p.TypeId,
                        ExtraFeeName = p.ExtraFeeName,
                        Price = p.Price,
                        Tax = p.Tax,
                        CurrencyId = p.CurrencyId,
                        Quantity = p.Quantity,
                        PaymentMethod = p.PaymentMethod
                    }).ToList(),
                StatusHistories = data.Data.ApplicationPage.StatusHistories.Select(p =>
                    new ApplicationPageViewModel.ApplicationStatusHistory()
                    {
                        Order = p.Order,
                        Status = p.Status,
                        NameSurname = p.NameSurname,
                        StatusDate = p.StatusDate.AddHours(data.Data.ApplicationPage.TimeZoneOffset)
                    }).ToList(),
                Signatures = Mapper.Map<List<DocumentSignatureViewModel>>(requestModel.Signatures),
                IsInquiryActive = data.Data.ApplicationPage.IsInquiryActive,
                Inquiries = data.Data.ApplicationPage.Inquiries.Select(s => new ApplicationPageViewModel.Inquiry()
                {
                    Id = s.Id,
                    BranchId = s.BranchId,
                    Name = s.Name,
                    Observation = s.Observation,
                    InquiryQuestions = s.InquiryQuestions.Select(q => new ApplicationPageViewModel.InquiryQuestion()
                    {
                        Id = q.Id,
                        Name = q.Name,
                        QuestionTypeId = q.QuestionTypeId,
                        IsMultiSelectable = q.IsMultiSelectable,
                        IsRequired = q.IsRequired,
                        IsDescriptionIncluded = q.IsDescriptionIncluded,
                        ElectiveAnswer = q.ElectiveAnswer == null
                            ? new ApplicationPageViewModel.InquiryElectiveAnswerDto()
                            : new ApplicationPageViewModel.InquiryElectiveAnswerDto()
                            {
                                Description = q.ElectiveAnswer?.Description,
                                Ids = q.ElectiveAnswer?.Ids
                            },
                        ExplanationAnswers = q.ExplanationAnswers == null
                            ? new List<ApplicationPageViewModel.InquiryExplanationAnswerDto>()
                            : q.ExplanationAnswers.Select(s =>
                                new ApplicationPageViewModel.InquiryExplanationAnswerDto()
                                {
                                    Explanation = s.Explanation,
                                    Id = s.Id
                                }).ToList(),
                        Choices = q.Choices.Select(c => new ApplicationPageViewModel.InquiryQuestionChoiceDto()
                        {
                            Id = c.Id,
                            ChoiceTypeId = c.ChoiceTypeId,
                            Name = c.Name
                        }).ToList()
                    }).ToList()
                }).ToList()

            };

            var currency = new List<int>();

            foreach (var item in viewModel.ExtraFees)
            {
                if (!currency.Contains(item.CurrencyId))
                {
                    currency.Add(item.CurrencyId);
                }
            }

            var allTotal = new List<ApplicationPageViewModel.Total>();

            foreach (var item in currency)
            {
                var totalCount = new ApplicationPageViewModel.Total()
                {
                    CurrencyId = item,
                    Price = viewModel.ExtraFees.Where(q => q.CurrencyId == item).Sum(q => q.Price * q.Quantity),
                    Tax = viewModel.ExtraFees.Where(q => q.CurrencyId == item).Sum(q => q.Tax),
                    Count = viewModel.ExtraFees.Where(q => q.CurrencyId == item).Sum(q => q.Quantity)
                };
                allTotal.Add(totalCount);
            }

            viewModel.TotalCount = allTotal;

            var fileName = new StringBuilder();

            switch (languageId)
            {
                case (byte)DigitalSignatureDocumentLanguage.English:
                    fileName.Append("PrintOutEn");
                    break;
                case (byte)DigitalSignatureDocumentLanguage.Turkish:
                    fileName.Append("PrintOutTr");
                    break;
            }

            var html = new StringBuilder(
                await RenderToStringHelper.RenderViewToStringAsync(fileName.ToString(), viewModel, ControllerContext));

            if (html == null)
                throw new ConvertingDataPortalException(
                    $"{nameof(SiteResources.Exception_ConvertingToHtml).ToSiteResourcesValue()} {nameof(SiteResources.Application).ToSiteResourcesValue()}: {request.ApplicationId}");

            html.Replace("{{baseUrl}}", $"{_configuration["BaseImageUri"]}");
            html.Replace("{{logo}}", $"{_configuration["BaseImageUri"]}/StaticFiles/images/logo.png");
            html.Replace("{{icrbarcode}}",
                $"{_configuration["BaseImageUri"]}/StaticFiles/ApplicationPage/icrbarcode.png");

            var bytes = Encoding.UTF8.GetBytes(html.ToString());
            var htmlString = Convert.ToBase64String(bytes);

            return new ApiResponse<DigitalSignatureBaseApiResponse>
            {
                IsSuccess = true,
                Data = new DigitalSignatureBaseApiResponse
                {
                    Html = htmlString
                }
            };
        }

        [SwaggerOperation(
            Summary = "Provides clients to get information used in explicit consent text",
            Description = "Get information used in explicit consent text by id and language option.")]
        [HttpPost(ApiMethodName.DigitalSignature.ExplicitConsentText + "{applicationId}/{languageId}")]
        public async Task<ApiResponse<DigitalSignatureBaseApiResponse>> GetExplicitConsentText(int applicationId,
            byte languageId, [FromBody] DocumentRequestModel requestModel)
        {
            if (requestModel == null || applicationId == 0 || languageId == 0)
                throw new InvalidRequestException(EnumResources.MissingOrInvalidData);

            var request = new ApplicationEntryFormApiRequest
                { ApplicationId = applicationId, RelationalApplicationId = applicationId };

            var data = await Process<GetExplicitConsentTextApiResponse, GetExplicitConsentTextResponseDto>(request,
                () => _printService.GetExplicitConsentText(Mapper.Map<GetApplicationPrintAllRequestDto>(request)));

            if (data?.Data == null)
                throw new NoFoundDataPortalException(
                    $"{nameof(SiteResources.Exception_NoRecordsFound).ToSiteResourcesValue()} {nameof(SiteResources.Application).ToSiteResourcesValue()}: {request.ApplicationId}");

            var viewModel = new ExplicitConsentTextViewModel
            {
                NameAndSurname = data.Data.NameAndSurname,
                Date = data.Data.Date,
                PassportNumber = data.Data.PassportNumber,
                ContactInformation = data.Data.ContactInformation,
                BranchCountryIso3 = data.Data.BranchCountryIso3,
                Signatures = Mapper.Map<List<DocumentSignatureViewModel>>(requestModel.Signatures)
            };

            var fileName = new StringBuilder();

            switch (data.Data.BranchCountryIso3)
            {
                case "RUS":
                    switch (languageId)
                    {
                        case (byte)DigitalSignatureDocumentLanguage.English:
                            fileName.Append("ExplicitConsentTextEn");
                            break;
                        case (byte)DigitalSignatureDocumentLanguage.Russian:
                            fileName.Append("ExplicitConsentTextRu");
                            break;
                    }

                    break;
                case "TKM":
                    switch (languageId)
                    {
                        case (byte)DigitalSignatureDocumentLanguage.English:
                            fileName.Append("ExplicitConsentTextEn");
                            break;
                        case (byte)DigitalSignatureDocumentLanguage.Russian:
                            fileName.Append("ExplicitConsentTextRu");
                            break;
                        case (byte)DigitalSignatureDocumentLanguage.Turkmen:
                            fileName.Append("ExplicitConsentTextTrkm");
                            break;
                        case (byte)DigitalSignatureDocumentLanguage.Turkish:
                            fileName.Append("ExplicitConsentTextTr");
                            break;
                    }

                    break;
                case "DZA":
                    switch (languageId)
                    {
                        case (byte)DigitalSignatureDocumentLanguage.English:
                            fileName.Append("ExplicitConsentTextEn");
                            break;
                        case (byte)DigitalSignatureDocumentLanguage.French:
                            fileName.Append("ExplicitConsentTextFr");
                            break;
                        case (byte)DigitalSignatureDocumentLanguage.Arabic:
                            fileName.Append("ExplicitConsentTextAr");
                            break;
                    }

                    break;
                case "ARE" or "SAU" or "KWT" or "LBY":
                    switch (languageId)
                    {
                        case (byte)DigitalSignatureDocumentLanguage.English:
                            fileName.Append("ExplicitConsentTextEn");
                            break;
                        case (byte)DigitalSignatureDocumentLanguage.Arabic:
                            fileName.Append("ExplicitConsentTextAr");
                            break;
                    }

                    break;
                case "IRQ":
                    switch (languageId)
                    {
                        case (byte)DigitalSignatureDocumentLanguage.English:
                            fileName.Append("ExplicitConsentTextEn");
                            break;
                        case (byte)DigitalSignatureDocumentLanguage.Arabic:
                            fileName.Append("ExplicitConsentTextAr");
                            break;
                    }

                    break;
                default:
                    return new ApiResponse<DigitalSignatureBaseApiResponse>
                    {
                        IsSuccess = false,
                        Message = SiteResources.WrongLanguageSelection
                    };
            }

            var html = new StringBuilder(
                await RenderToStringHelper.RenderViewToStringAsync(fileName.ToString(), viewModel, ControllerContext));

            if (html == null)
                throw new ConvertingDataPortalException(
                    $"{nameof(SiteResources.Exception_ConvertingToHtml).ToSiteResourcesValue()} {nameof(SiteResources.Application).ToSiteResourcesValue()}: {request.ApplicationId}");

            html.Replace("{{baseUrl}}", $"{_configuration["BaseImageUri"]}");
            html.Replace("{{gateway_logo}}",
                $"{_configuration["BaseImageUri"]}/StaticFiles/ExplicitConsentText/GW/gateway_logo.jpg");
            html.Replace("{{english_qr_kod}}",
                $"{_configuration["BaseImageUri"]}/StaticFiles/ExplicitConsentText/GW/english_qr_kod.jpg");
            html.Replace("{{arabic_qr_kod}}",
                $"{_configuration["BaseImageUri"]}/StaticFiles/ExplicitConsentText/GW/arabic_qr_kod.png");
            html.Replace("{{french_qr_kod}}",
                $"{_configuration["BaseImageUri"]}/StaticFiles/ExplicitConsentText/GW/french_qr_kod.jpg");
            html.Replace("{{russian_qr_kod}}",
                $"{_configuration["BaseImageUri"]}/StaticFiles/ExplicitConsentText/GW/russian_qr_kod.jpg");
            html.Replace("{{turkish_qr_kod}}",
                $"{_configuration["BaseImageUri"]}/StaticFiles/ExplicitConsentText/GW/turkish_qr_kod.jpg");
            html.Replace("{{turkmen_qr_kod}}",
                $"{_configuration["BaseImageUri"]}/StaticFiles/ExplicitConsentText/GW/turkmen_qr_kod.jpg");

            var bytes = Encoding.UTF8.GetBytes(html.ToString());
            var htmlString = Convert.ToBase64String(bytes);

            return new ApiResponse<DigitalSignatureBaseApiResponse>
            {
                IsSuccess = true,
                Data = new DigitalSignatureBaseApiResponse
                {
                    Html = htmlString
                }
            };
        }

        [SwaggerOperation(
            Summary = "Provides clients to get information used in explicit consent text",
            Description = "Get information used in emaa explicit consent text by id and language option.")]
        [HttpPost(ApiMethodName.DigitalSignature.EmaaExplicitConsentText + "{applicationId}/{languageId}")]
        public async Task<ApiResponse<DigitalSignatureBaseApiResponse>> GetEmaaExplicitConsentText(int applicationId,
            byte languageId, [FromBody] DocumentRequestModel requestModel)
        {
            if (requestModel == null || applicationId == 0 || languageId == 0)
                throw new InvalidRequestException(EnumResources.MissingOrInvalidData);

            var request = new ApplicationEntryFormApiRequest
                { ApplicationId = applicationId, RelationalApplicationId = applicationId };

            var data = await Process<GetExplicitConsentTextApiResponse, GetExplicitConsentTextResponseDto>(request,
                () => _printService.GetExplicitConsentText(Mapper.Map<GetApplicationPrintAllRequestDto>(request)));

            if (data?.Data == null)
                throw new NoFoundDataPortalException(
                    $"{nameof(SiteResources.Exception_NoRecordsFound).ToSiteResourcesValue()} {nameof(SiteResources.Application).ToSiteResourcesValue()}: {request.ApplicationId}");

            var viewModel = new ExplicitConsentTextViewModel
            {
                NameAndSurname = data.Data.NameAndSurname,
                Date = data.Data.Date,
                PassportNumber = data.Data.PassportNumber,
                ContactInformation = data.Data.ContactInformation,
                BranchCountryIso3 = data.Data.BranchCountryIso3,
                IsInsuranceFeeSelected = data.Data.IsInsuranceFeeSelected,
                Signatures = Mapper.Map<List<DocumentSignatureViewModel>>(requestModel.Signatures)
            };

            if (!data.Data.IsInsuranceFeeSelected)
                throw new NoFoundDataPortalException(
                    $"{nameof(SiteResources.Exception_NoRecordsFound).ToSiteResourcesValue()} {nameof(SiteResources.Application).ToSiteResourcesValue()}: {request.ApplicationId}");

            var fileName = new StringBuilder();

            if (data.Data.BranchCountryIso3 == "IND" || data.Data.BranchCountryIso3 == "NPL")
            {
                if (languageId == (byte)DigitalSignatureDocumentLanguage.English)
                    fileName.Append("EmaaExplicitConsentTextEn");
            }
            else if (data.Data.BranchCountryIso3 == "KWT" || data.Data.BranchCountryIso3 == "LBY" ||
                     data.Data.BranchCountryIso3 == "SAU" || data.Data.BranchCountryIso3 == "ARE")
            {
                if (languageId == (byte)DigitalSignatureDocumentLanguage.English)
                {
                    if (data.Data.BranchId == 2)
                        fileName.Append("ExplicitConsentTextEn");
                    else
                        fileName.Append("EmaaExplicitConsentTextEn");
                }
                else if (languageId == (byte)DigitalSignatureDocumentLanguage.Arabic)
                {
                    if (data.Data.BranchId == 2)
                        fileName.Append("ExplicitConsentTextAr");
                    else
                        fileName.Append("EmaaExplicitConsentTextAr");
                }
            }
            else if (data.Data.BranchCountryIso3 == "TKM")
            {
                if (languageId == (byte)DigitalSignatureDocumentLanguage.English)
                    fileName.Append("EmaaExplicitConsentTextEn");
                else if (languageId == (byte)DigitalSignatureDocumentLanguage.Russian)
                    fileName.Append("EmaaExplicitConsentTextRu");
                else if (languageId == (byte)DigitalSignatureDocumentLanguage.Turkmen)
                    fileName.Append("EmaaExplicitConsentTextTrkm");
                else if (languageId == (byte)DigitalSignatureDocumentLanguage.Turkish)
                    fileName.Append("EmaaExplicitConsentTextTr");
            }
            else
                return new ApiResponse<DigitalSignatureBaseApiResponse>
                {
                    IsSuccess = false,
                    Message = SiteResources.WrongLanguageSelection
                };

            var html = new StringBuilder(
                await RenderToStringHelper.RenderViewToStringAsync(fileName.ToString(), viewModel, ControllerContext));

            if (html == null)
                throw new ConvertingDataPortalException(
                    $"{nameof(SiteResources.Exception_ConvertingToHtml).ToSiteResourcesValue()} {nameof(SiteResources.Application).ToSiteResourcesValue()}: {request.ApplicationId}");

            html.Replace("{{baseUrl}}", $"{_configuration["BaseImageUri"]}");
            html.Replace("{{emaasigorta_logo}}",
                $"{_configuration["BaseImageUri"]}/StaticFiles/ExplicitConsentText/Emaa/emaasigorta_logo.gif");
            html.Replace("{{emaasigorta_background}}",
                $"{_configuration["BaseImageUri"]}/StaticFiles/ExplicitConsentText/Emaa/emaasigorta_background.png");
            html.Replace("{{emaasigorta_ar}}",
                $"{_configuration["BaseImageUri"]}/StaticFiles/ExplicitConsentText/Emaa/emaasigorta_ar.jpg");
            html.Replace("{{emaasigorta_en}}",
                $"{_configuration["BaseImageUri"]}/StaticFiles/ExplicitConsentText/Emaa/emaasigorta_en.jpg");
            html.Replace("{{emaasigorta_fr}}",
                $"{_configuration["BaseImageUri"]}/StaticFiles/ExplicitConsentText/Emaa/emaasigorta_fr.jpg");
            html.Replace("{{emaasigorta_ru}}",
                $"{_configuration["BaseImageUri"]}/StaticFiles/ExplicitConsentText/Emaa/emaasigorta_ru.jpg");
            html.Replace("{{emaasigorta_trkmn}}",
                $"{_configuration["BaseImageUri"]}/StaticFiles/ExplicitConsentText/Emaa/emaasigorta_trkmn.jpg");
            html.Replace("{{emaasigorta_turkish}}",
                $"{_configuration["BaseImageUri"]}/StaticFiles/ExplicitConsentText/Emaa/emaasigorta_turkish.jpg");

            var bytes = Encoding.UTF8.GetBytes(html.ToString());
            var htmlString = Convert.ToBase64String(bytes);

            return new ApiResponse<DigitalSignatureBaseApiResponse>
            {
                IsSuccess = true,
                Data = new DigitalSignatureBaseApiResponse
                {
                    Html = htmlString
                }
            };
        }

        [SwaggerOperation(
            Summary = "Provides clients to get all taxed icr document",
            Description = "Get documentation of icr (bill) with all taxed included type.")]
        [HttpPost(ApiMethodName.DigitalSignature.AllTaxedICR + "{applicationId}/{languageId}")]
        public async Task<ApiResponse<DigitalSignatureBaseApiResponse>> AllTaxedICR(int applicationId, byte languageId,
            [FromBody] DocumentRequestModel requestModel)
        {
            if (requestModel == null || applicationId == 0 || languageId == 0)
                throw new InvalidRequestException(EnumResources.MissingOrInvalidData);

            var request = new GetApplicationPrintAllApiRequest
            {
                ApplicationId = applicationId,
                RelationalApplicationId = applicationId,
                IcrLanguage = GetIcrLanguageFromPrintLanguage(languageId),
            };

            try
            {
                var apiResponse = await Process<GetIcrApiResponse, GetIcrResponseDto>(request,
                    () => _printService.GetApplicationIcr(Mapper.Map<GetApplicationPrintAllRequestDto>(request)));

                if (apiResponse?.Data == null)
                    throw new NoFoundDataPortalException(
                        $"{nameof(SiteResources.Exception_NoRecordsFound).ToSiteResourcesValue()} {nameof(SiteResources.Application).ToSiteResourcesValue()}: {request.ApplicationId}");

                var viewmodel =await GetICRViewModel(apiResponse, languageId, true,true, requestModel);

                var html = await IcrRendererByLanguage(languageId, viewmodel);

                if (html == null)
                    throw new ConvertingDataPortalException(
                        $"{nameof(SiteResources.Exception_ConvertingToHtml).ToSiteResourcesValue()} {nameof(SiteResources.Application).ToSiteResourcesValue()}: {request.ApplicationId}");

                html = html.Replace("{{baseUrl}}", $"{_configuration["BaseImageUri"]}");

                html = html.Replace("{{gateway_logo}}", $"{_configuration["BaseImageUri"]}/StaticFiles/ICR/logo.png");

                var bytes = Encoding.UTF8.GetBytes(html.ToString());
                var htmlString = Convert.ToBase64String(bytes);

                return new ApiResponse<DigitalSignatureBaseApiResponse>
                {
                    IsSuccess = true,
                    Data = new DigitalSignatureBaseApiResponse
                    {
                        Html = htmlString
                    }
                };

            }
            catch (Exception)
            {
                throw new InvalidRequestException(EnumResources.MissingOrInvalidData);
            }
        }

        [SwaggerOperation(
            Summary = "Provides clients to get icr document",
            Description = "Get documentation of icr (bill).")]
        [HttpPost(ApiMethodName.DigitalSignature.ICR + "{applicationId}/{languageId}")]
        public async Task<ApiResponse<DigitalSignatureBaseApiResponse>> ICR(int applicationId, byte languageId,
            [FromBody] DocumentRequestModel requestModel)
        {
            if (requestModel == null || applicationId == 0 || languageId == 0)
                throw new InvalidRequestException(EnumResources.MissingOrInvalidData);

            var request = new GetApplicationPrintAllApiRequest
            {
                ApplicationId = applicationId,
                RelationalApplicationId = applicationId,
                IcrLanguage = GetIcrLanguageFromPrintLanguage(languageId),
			};

            try
            {
                var apiResponse = await Process<GetIcrApiResponse, GetIcrResponseDto>(request,
                    () => _printService.GetApplicationIcr(Mapper.Map<GetApplicationPrintAllRequestDto>(request)));

                if (apiResponse?.Data == null)
                    throw new NoFoundDataPortalException(
                        $"{nameof(SiteResources.Exception_NoRecordsFound).ToSiteResourcesValue()} {nameof(SiteResources.Application).ToSiteResourcesValue()}: {request.ApplicationId}");

                var viewmodel = await GetICRViewModel(apiResponse, languageId, false,false, requestModel);

                var html = await IcrRendererByLanguage(languageId, viewmodel);

                if (html == null)
                    throw new ConvertingDataPortalException(
                        $"{nameof(SiteResources.Exception_ConvertingToHtml).ToSiteResourcesValue()} {nameof(SiteResources.Application).ToSiteResourcesValue()}: {request.ApplicationId}");

                html = html.Replace("{{baseUrl}}", $"{_configuration["BaseImageUri"]}");

                html = html.Replace("{{gateway_logo}}", $"{_configuration["BaseImageUri"]}/StaticFiles/ICR/logo.png");

                var bytes = Encoding.UTF8.GetBytes(html.ToString());
                var htmlString = Convert.ToBase64String(bytes);

                return new ApiResponse<DigitalSignatureBaseApiResponse>
                {
                    IsSuccess = true,
                    Data = new DigitalSignatureBaseApiResponse
                    {
                        Html = htmlString
                    }
                };
            }
            catch (Exception)
            {
                throw new InvalidRequestException(EnumResources.MissingOrInvalidData);
            }
        }

        [SwaggerOperation(
            Summary = "Provides clients to get taxed icr document",
            Description = "Get documentation of icr (bill) with taxed type.")]
        [HttpPost(ApiMethodName.DigitalSignature.TaxedICR + "{applicationId}/{languageId}")]
        public async Task<ApiResponse<DigitalSignatureBaseApiResponse>> TaxedICR(int applicationId, byte languageId,
            [FromBody] DocumentRequestModel requestModel)
        {
            if (requestModel == null || applicationId == 0 || languageId == 0)
                throw new InvalidRequestException(EnumResources.MissingOrInvalidData);

            var request = new GetApplicationPrintAllApiRequest
            {
                ApplicationId = applicationId,
                RelationalApplicationId = applicationId,
                IcrLanguage = GetIcrLanguageFromPrintLanguage(languageId),
			};

            try
            {
                var apiResponse = await Process<GetIcrApiResponse, GetIcrResponseDto>(request,
                    () => _printService.GetApplicationIcr(Mapper.Map<GetApplicationPrintAllRequestDto>(request)));

                if (apiResponse?.Data == null)
                    throw new NoFoundDataPortalException(
                        $"{nameof(SiteResources.Exception_NoRecordsFound).ToSiteResourcesValue()} {nameof(SiteResources.Application).ToSiteResourcesValue()}: {request.ApplicationId}");

                var viewmodel = await GetICRViewModel(apiResponse, languageId, true,false, requestModel);

                var html =await IcrRendererByLanguage(languageId, viewmodel);

                if (html == null)
                    throw new ConvertingDataPortalException(
                        $"{nameof(SiteResources.Exception_ConvertingToHtml).ToSiteResourcesValue()} {nameof(SiteResources.Application).ToSiteResourcesValue()}: {request.ApplicationId}");

                html = html.Replace("{{baseUrl}}", $"{_configuration["BaseImageUri"]}");

                html = html.Replace("{{gateway_logo}}", $"{_configuration["BaseImageUri"]}/StaticFiles/ICR/logo.png");

                var bytes = Encoding.UTF8.GetBytes(html.ToString());
                var htmlString = Convert.ToBase64String(bytes);

                return new ApiResponse<DigitalSignatureBaseApiResponse>
                {
                    IsSuccess = true,
                    Data = new DigitalSignatureBaseApiResponse
                    {
                        Html = htmlString
                    }
                };
            }
            catch (Exception)
            {
                throw new InvalidRequestException(EnumResources.MissingOrInvalidData);
            }
        }

        [SwaggerOperation(
            Summary = "Provides clients to get devices by branch",
            Description = "Get devices by branch.")]
        [HttpGet(ApiMethodName.DigitalSignature.GetDevicesByBranchSelectList + "{branchId}")]
        public async Task<ApiResponse<GetDevicesByBranchApiResponse>> GetDevicesByBranchSelectList(int branchId)
        {
            return await Process<GetDevicesByBranchApiResponse, GetDevicesByBranchResponseDto>(new EmptyApiRequest(),
                () => _digitalSignatureService.GetDevicesByBranchAsync(branchId));
        }

        [SwaggerOperation(
            Summary = "Save documents",
            Description = "Send documents")]
        [HttpPost(ApiMethodName.DigitalSignature.SaveDocuments)]
        public Task<ApiResponse<SaveDocumentsApiResponse>> SaveDocuments([FromBody] SaveDocumentsRequestModel request)
        {
            return Process<SaveDocumentsApiResponse, SaveDocumentsResponseDto>(request,
                () => _digitalSignatureService.SaveDocumentsAsync(Mapper.Map<SaveDocumentsRequestDto>(request)));
        }

        #region Private Methods

        private async Task<ICRViewModel> GetICRViewModel(ApiResponse<GetIcrApiResponse> apiResponse, int languageId, bool taxIncluded,
            bool includeAll, DocumentRequestModel request)
        {
            var tlvFormatCountries = EnumHelper.GetEnumAsDictionary(typeof(TLVFormatQRCodeCountries)).Select(p => new
            {
                Value = p.Key
            }).ToList();

            var visaCategories = await Process<GetVisaCategoriesApiResponse, GetVisaCategoriesResponseDto>(request,
                    () => _printService.GetVisaCategoriesAsync(new GetVisaCategoriesRequestDto()));

            if (visaCategories?.Data == null || visaCategories.Data.VisaCategories == null)
                throw new NoFoundDataPortalException(
                    $"{nameof(SiteResources.Exception_NoRecordsFound).ToSiteResourcesValue()} {nameof(SiteResources.VisaCategory).ToSiteResourcesValue()}");

            var viewModel = new ICRViewModel
            {
                Signatures = Mapper.Map<List<DocumentSignatureViewModel>>(request.Signatures),
                TaxInclude = taxIncluded,
                EncryptedId = apiResponse.Data.RelationalApplicationsForIcr.RelationalApplicationId.ToEncrypt(),
                IsTLVFormat = tlvFormatCountries.Any(q =>
                    q.Value == apiResponse.Data.RelationalApplicationsForIcr.BranchCountryId),
                BranchName = apiResponse.Data.RelationalApplicationsForIcr.BranchCorporateName,
                BranchCountryId = apiResponse.Data.RelationalApplicationsForIcr.BranchCountryId,
                InvoiceNumber = apiResponse.Data.RelationalApplicationsForIcr.InvoiceNumber,
                Phone = apiResponse.Data.RelationalApplicationsForIcr.Phone,
                Email = apiResponse.Data.RelationalApplicationsForIcr.Email,
                Address = apiResponse.Data.RelationalApplicationsForIcr.BranchAddress,
                AppointmentTime = apiResponse.Data.RelationalApplicationsForIcr.ApplicationTime.DateTime,
                DateTimeNow = DateTime.Now.ToShortDateString() + ", " + DateTime.Now.ToShortTimeString(),
                IcrType = apiResponse.Data.BranchIcr.Type,
                InvoiceNo = apiResponse.Data.BranchIcr.SapBranch.TrimStart('0') + "-" +
                            apiResponse.Data.RelationalApplicationsForIcr.ApplicationTime.DateTime
                                .ToString("yyyyMMdd") + "-" + apiResponse.Data.RelationalApplicationsForIcr
                                .RelationalApplicationId,
                CreatedBy = apiResponse.Data.RelationalApplicationsForIcr.CreatedBy,
                ApplicationExtraFeesTotal = new List<TotalExtraFeeViewModel>(),
                QrBranchName = apiResponse.Data.RelationalApplicationsForIcr.QrCorporateName, //used for qr decodinBg
                QrInvoiceNumber = apiResponse.Data.RelationalApplicationsForIcr.QrInvoiceNumber,
                PrintAll = false,
                BranchCityName = apiResponse.Data.RelationalApplicationsForIcr.BranchCityName,
                KSAICR = apiResponse.Data.RelationalApplicationsForIcr.KSAICR,
                Applicants = apiResponse.Data.RelationalApplicationsForIcr.Applicants.Where(q =>
                    (q.ApplicantTypeId == (int)ApplicantType.Family || q.ApplicantTypeId == (int)ApplicantType.Group)
                        ? q.StatusId != (int)ApplicationStatus.Cancelled
                        : 1 == 1).Select(p => new ICRViewModel.ApplicantViewModel
                {
                    IcrLanguage = GetIcrLanguageFromPrintLanguage(languageId),
                    PhotoBoothCheck =
                        p.ExtraFees.Any(q => q.ExtraFeeNameTr.Contains("Fotoğraf")) && p.PhotoBoothId != 0,
                    ApplicationNumber = p.Id.ToApplicationNumber(),
                    VisaCategoryId = p.VisaCategoryId,
                            VisaCategory = visaCategories.Data.VisaCategories.FirstOrDefault(s => s.Id == p.VisaCategoryId) == null ? string.Empty :
                    visaCategories.Data.VisaCategories.FirstOrDefault(s => s.Id == p.VisaCategoryId).Translations.FirstOrDefault(s => s.LanguageId == GetIcrLanguageFromPrintLanguage(languageId)) == null ?
                    visaCategories.Data.VisaCategories.FirstOrDefault(s => s.Id == p.VisaCategoryId).Translations.FirstOrDefault().Name :
                    visaCategories.Data.VisaCategories.FirstOrDefault(s => s.Id == p.VisaCategoryId).Translations.FirstOrDefault(s => s.LanguageId == GetIcrLanguageFromPrintLanguage(languageId)).Name,
                            VisaCategoryEn = visaCategories.Data.VisaCategories.FirstOrDefault(s => s.Id == p.VisaCategoryId) == null ? string.Empty :
                    visaCategories.Data.VisaCategories.FirstOrDefault(s => s.Id == p.VisaCategoryId).Translations.FirstOrDefault(s => s.LanguageId == (int)Language.English) == null ?
                    visaCategories.Data.VisaCategories.FirstOrDefault(s => s.Id == p.VisaCategoryId).Translations.FirstOrDefault().Name :
                    visaCategories.Data.VisaCategories.FirstOrDefault(s => s.Id == p.VisaCategoryId).Translations.FirstOrDefault(s => s.LanguageId == (int)Language.English).Name,
                            VisaCategoryAr = visaCategories.Data.VisaCategories.FirstOrDefault(s => s.Id == p.VisaCategoryId) == null ? string.Empty :
                    visaCategories.Data.VisaCategories.FirstOrDefault(s => s.Id == p.VisaCategoryId).Translations.FirstOrDefault(s => s.LanguageId == (int)ICRLanguage.Arabic) == null ?
                    visaCategories.Data.VisaCategories.FirstOrDefault(s => s.Id == p.VisaCategoryId).Translations.FirstOrDefault().Name :
                    visaCategories.Data.VisaCategories.FirstOrDefault(s => s.Id == p.VisaCategoryId).Translations.FirstOrDefault(s => s.LanguageId == (int)ICRLanguage.Arabic).Name,Name = p.Name,
                    VisaNo = p.VisaNo,
                    BarcodeICR = !string.IsNullOrEmpty(p.VisaNo) ? CreateBarcodeICR_(p.VisaNo) : "",
                    PhotoBoothBarcodeICR =
                        (p.ExtraFees.Any(q => q.ExtraFeeNameTr.Contains("Fotoğraf")) && p.PhotoBoothId != 0)
                            ? CreateBarcodePhotobooth(p.PhotoBoothId.ToEncrypt(), apiResponse.Data.RelationalApplicationsForIcr.BranchId, request.GetUserAuditId(), request.GetCorporateId()).Result
                            : "",
                    Surname = p.Surname,
                    PassportNumber = p.PassportNumber,
                    PhoneNumber = p.PhoneNumber,
                    Nationality = p.Nationality,
                    PhotoBoothEncryptedId = p.PhotoBoothId.ToEncrypt(),
                    ApplicationAdress = p.Address,
                    ApplicantExtraFeesTotal = p.ExtraFees.Where(q => includeAll || q.ShowInICR)
                        .GroupBy(e => new { e.CurrencyId, e.TaxRatio }).Select(e => new TotalExtraFeeViewModel()
                        {
                            Currency = GetCurrencyConverterByLanguageId(languageId, e.Key.CurrencyId),
                            Price = e.Sum(q => q.Price * q.Quantity),
                            BasePrice = e.Sum(q => q.BasePrice * q.Quantity),
                            Tax = e.Sum(q => q.Tax * q.Quantity),
                            TaxRatio = e.Key.TaxRatio,
                            ServiceTax = e.Sum(q => q.ServiceTax * q.Quantity),
                        }).GroupBy(e => new { e.Currency, e.TaxRatio }).Select(e => new TotalExtraFeeViewModel()
                        {
                            Currency = e.Key.Currency,
                            Price = e.Sum(q => q.Price),
                            BasePrice = e.Sum(q => q.BasePrice),
                            Tax = e.Sum(q => q.Tax),
                            TaxRatio = e.Key.TaxRatio,
                            ServiceTax = e.Sum(q => q.ServiceTax),
                        }).OrderBy(z => z.Currency).ThenBy(z => z.TaxRatio).ToList(),
                    ExtraFees = p.ExtraFees.Where(q => includeAll || q.ShowInICR)
                        .GroupBy(e => new
                            { e.CurrencyId, e.TaxRatio, e.ExtraFeeName, e.ExtraFeeNameAr, e.IsGroupInIcr }).Select(e =>
                            new ICRViewModel.ApplicantViewModel.ApplicantExtraFeeViewModel
                            {
                                Name = e.Key.IsGroupInIcr
                                    ? GetExtraFeeNameConverterByLanguageId(languageId,
                                        apiResponse.Data.RelationalApplicationsForIcr.BranchCountryId)
                                    : e.Key.ExtraFeeName,
                                NameAr = e.Key.ExtraFeeNameAr,
                                IsGroupInIcr = e.Key.IsGroupInIcr,
                                Currency = GetCurrencyConverterByLanguageId(languageId, e.Key.CurrencyId),
                                Price = e.Sum(q => q.Price * q.Quantity),
                                BasePrice = e.Sum(q => q.BasePrice * q.Quantity),
                                Tax = e.Sum(q => q.Tax * q.Quantity),
                                TaxRatio = e.Key.TaxRatio,
                                PriceZero = e.Where(q => q.TaxRatio == 0 && q.Category == 4)
                                    .Select(q => q.Price * q.Quantity).Sum(),
                                ServiceTax = e.Sum(q => q.ServiceTax * q.Quantity)
                            }).GroupBy(e => new
                        {
                            e.Name, e.NameAr, e.Currency, e.IsGroupInIcr,
                            Value = e.IsGroupInIcr ? new decimal(1) : e.TaxRatio
                        }).Select(e => new ICRViewModel.ApplicantViewModel.ApplicantExtraFeeViewModel
                        {
                            Name = e.Key.Name,
                            NameAr = e.Key.NameAr,
                            IsGroupInIcr = e.Key.IsGroupInIcr,
                            Currency = e.Key.Currency,
                            Price = e.Sum(q => q.Price),
                            BasePrice = e.Sum(q => q.BasePrice),
                            Tax = e.Sum(q => q.Tax),
                            TaxRatio = e.Key.IsGroupInIcr
                                ? e.Where(q => q.TaxRatio != 0).Select(q => q.TaxRatio).FirstOrDefault()
                                : e.Key.Value,
                            PriceZero = e.Sum(q => q.PriceZero),
                            ServiceTax = e.Sum(q => q.ServiceTax)
                        }).OrderBy(z => z.Currency).ThenBy(z => z.TaxRatio).ToList()
                }).ToList()
            };
            String branchPreviousNotes = "";
            if (viewModel.Applicants.Find(q => q.PassportNumber != null).ExtraFees.Find(q => q.PriceZero != 0) != null)
            {
                if (languageId == (int)DigitalSignatureDocumentLanguage.Russian)
                {
                    branchPreviousNotes =
                        viewModel.Applicants.Find(q => q.PassportNumber != null).ExtraFees.Find(q => q.PriceZero != 0)
                            .PriceZero.ToString("0.00") + " " +
                        viewModel.Applicants.Find(q => q.PassportNumber != null).ExtraFees.Find(q => q.PriceZero != 0)
                            .Currency + " это визовый сбор, который не облагается налогом.";
                }
                else if (languageId == (int)DigitalSignatureDocumentLanguage.Arabic)
                {
                    branchPreviousNotes =
                        viewModel.Applicants.Find(q => q.PassportNumber != null).ExtraFees.Find(q => q.PriceZero != 0)
                            .PriceZero.ToString("0.00") + " " +
                        viewModel.Applicants.Find(q => q.PassportNumber != null).ExtraFees.Find(q => q.PriceZero != 0)
                            .Currency + " درهم إماراتي هي رسوم التأشيرة معفات من الضريبة. ";
                }
                else if (languageId == (int)DigitalSignatureDocumentLanguage.English)
                {
                    branchPreviousNotes =
                        viewModel.Applicants.Find(q => q.PassportNumber != null).ExtraFees.Find(q => q.PriceZero != 0)
                            .Currency + " " +
                        viewModel.Applicants.Find(q => q.PassportNumber != null).ExtraFees.Find(q => q.PriceZero != 0)
                            .PriceZero.ToString("0.00") + " is the Visa fee and its exempted from tax.";
                }
                else if (languageId == (int)DigitalSignatureDocumentLanguage.French)
                {
                    branchPreviousNotes =
                        viewModel.Applicants.Find(q => q.PassportNumber != null).ExtraFees.Find(q => q.PriceZero != 0)
                            .Currency + " " +
                        viewModel.Applicants.Find(q => q.PassportNumber != null).ExtraFees.Find(q => q.PriceZero != 0)
                            .PriceZero.ToString("0.00") + " est le frais de visa et son exonération d'impôt.";
                }
                else if (languageId == (int)DigitalSignatureDocumentLanguage.Turkmen)
                {
                    branchPreviousNotes =
                        viewModel.Applicants.Find(q => q.PassportNumber != null).ExtraFees.Find(q => q.PriceZero != 0)
                            .Currency + " " +
                        viewModel.Applicants.Find(q => q.PassportNumber != null).ExtraFees.Find(q => q.PriceZero != 0)
                            .PriceZero.ToString("0.00") + " wiza tölegidir we salgytdan boşadylýar.";
                }
            }

            viewModel.BranchIcrNotes = GetPrintAllIcrNoteByLanguageId(languageId, apiResponse.Data, branchPreviousNotes,
                viewModel.BranchCountryId).Item2;
            viewModel.IcrName = GetPrintAllIcrNoteByLanguageId(languageId, apiResponse.Data, branchPreviousNotes,
                viewModel.BranchCountryId).Item1;

            foreach (var applicant in viewModel.Applicants)
            {
                foreach (var extraFeesPerCurrency in applicant.ApplicantExtraFeesTotal)
                {
                    if (viewModel.ApplicationExtraFeesTotal.Any(p =>
                            p.Currency == extraFeesPerCurrency.Currency && p.TaxRatio == extraFeesPerCurrency.TaxRatio))
                    {
                        viewModel.ApplicationExtraFeesTotal.FirstOrDefault(p =>
                                p.Currency == extraFeesPerCurrency.Currency &&
                                p.TaxRatio == extraFeesPerCurrency.TaxRatio)
                            .Price += extraFeesPerCurrency.Price;
                        viewModel.ApplicationExtraFeesTotal.FirstOrDefault(p =>
                                p.Currency == extraFeesPerCurrency.Currency &&
                                p.TaxRatio == extraFeesPerCurrency.TaxRatio)
                            .Tax += extraFeesPerCurrency.Tax;
                        viewModel.ApplicationExtraFeesTotal.FirstOrDefault(p =>
                                p.Currency == extraFeesPerCurrency.Currency &&
                                p.TaxRatio == extraFeesPerCurrency.TaxRatio)
                            .ServiceTax += extraFeesPerCurrency.ServiceTax;
                        viewModel.ApplicationExtraFeesTotal.FirstOrDefault(p =>
                                p.Currency == extraFeesPerCurrency.Currency &&
                                p.TaxRatio == extraFeesPerCurrency.TaxRatio)
                            .BasePrice += extraFeesPerCurrency.BasePrice;
                    }
                    else
                    {
                        viewModel.ApplicationExtraFeesTotal.Add(new TotalExtraFeeViewModel()
                        {
                            Currency = extraFeesPerCurrency.Currency, Price = extraFeesPerCurrency.Price,
                            Tax = extraFeesPerCurrency.Tax, TaxRatio = extraFeesPerCurrency.TaxRatio,
                            ServiceTax = extraFeesPerCurrency.ServiceTax, BasePrice = extraFeesPerCurrency.BasePrice
                        });
                    }
                }
            }

            viewModel.ApplicationExtraFeesTotal.OrderBy(z => z.Currency).ThenBy(z => z.TaxRatio);

            var vatAmount = string.Empty;
            var totalAmount = string.Empty;
            if (viewModel.IsTLVFormat)
            {
                vatAmount = String.Format("{0:0.00}", viewModel.ApplicationExtraFeesTotal.Sum(q => q.Tax));
                totalAmount = String.Format("{0:0.00}", viewModel.ApplicationExtraFeesTotal.Sum(q => q.Price));
            }

            viewModel.CreateICR = CreateICR_(viewModel.BranchName, viewModel.InvoiceNumber, vatAmount, totalAmount,
                viewModel.TaxInclude, viewModel.AppointmentTime, viewModel.IsTLVFormat);
            viewModel.ApplicationExtraFeesGrandTotal = viewModel.ApplicationExtraFeesTotal
                .GroupBy(q => new { q.Currency }).Select(q => new GrandTotalExtraFeeViewModel()
                {
                    Currency = q.Key.Currency,
                    Price = q.Sum(p => p.Price),
                }).ToList();

            if (viewModel.BranchCountryId == 77) //India
                viewModel.Title = "Tax Invoice";
            else
                viewModel.Title = viewModel.BranchName + " ICR";

            return viewModel;
        }

        public string CreateBarcodeICR_(string visaNo)
        {
            return Convert.ToBase64String(BarcodeClient.CreateBarcodeByteArray(visaNo, new SKColor(33, 37, 41), BarcodeClient.DefaultWidth, BarcodeClient.ICRHeight));
        }

        private string GetCurrencyConverterByLanguageId(int languageId, int currencyId)
        {
            switch (languageId)
            {
                case (int)DigitalSignatureDocumentLanguage.English:
                    return EnumHelper.GetEnumDescription(typeof(CurrencyType), currencyId.ToString());
                case (int)DigitalSignatureDocumentLanguage.Arabic:
                    return CurrencyNameHelper.CurrencyConverterArabic(currencyId);
                case (int)DigitalSignatureDocumentLanguage.Turkmen:
                    return CurrencyNameHelper.CurrencyConverterTurkmen(currencyId);
                case (int)DigitalSignatureDocumentLanguage.Russian:
                    return CurrencyNameHelper.CurrencyConverterRussian(currencyId);
                case (int)DigitalSignatureDocumentLanguage.French:
                    return CurrencyNameHelper.CurrencyConverterFrench(currencyId);
                default:
                    return EnumHelper.GetEnumDescription(typeof(CurrencyType), currencyId.ToString());
            }
        }

        private string GetExtraFeeNameConverterByLanguageId(int languageId, int branchCountryId)
        {
            if (branchCountryId == 77)
            {
                switch (languageId)
                {
                    case (int)DigitalSignatureDocumentLanguage.Turkish:
                        return "Türkiye Vize Başvuru İşlem Bedeli";
                    case (int)DigitalSignatureDocumentLanguage.English:
                        return "Türkiye Visa Application Processing Fee";
                    case (int)DigitalSignatureDocumentLanguage.Arabic:
                        return "رسوم الحصول على تأشيرة تركيا"; //bunları sonra değiştir
                    case (int)DigitalSignatureDocumentLanguage.Turkmen:
                        return "Türkiye Visa Application Processing Fee"; //bunları sonra değiştir
                    case (int)DigitalSignatureDocumentLanguage.Russian:
                        return "Türkiye Visa Application Processing Fee"; //bunları sonra değiştir
                    default:
                        return "Türkiye Vize Başvuru İşlem Bedeli";
                }
            }
            else
            {
                switch (languageId)
                {
                    case (int)DigitalSignatureDocumentLanguage.Turkish:
                        return "Türkiye Vize Başvuru Bedeli";
                    case (int)DigitalSignatureDocumentLanguage.English:
                        return "Turkiye Visa Application Fee";
                    case (int)DigitalSignatureDocumentLanguage.Arabic:
                        return "تكلفة طلب التأشيرة إلى تركيا";
                    case (int)DigitalSignatureDocumentLanguage.Turkmen:
                        return "Turkiye Wizasynyn Arza Bahasy";
                    case (int)DigitalSignatureDocumentLanguage.Russian:
                        return "СТОИМОСТЬ ПРИМЕНЕНИЕ ВИЗЫ В ТУРЦИЮ";
                    case (int)DigitalSignatureDocumentLanguage.French:
                        return "Frais de Demande de Visa Turquie";
                    default:
                        return "Türkiye Vize Bedeli";
                }
            }
        }

        private Tuple<string, string> GetPrintAllIcrNoteByLanguageId(int languageId,
            ApplicationPrintAllApiResponse.GetIcrApiResponse apiResponse, String previousNote, int branchCountryId)
        {
            if (branchCountryId is 80 or 181 or 100 or 93)
            {
                return languageId switch
                {
                    (int)DigitalSignatureDocumentLanguage.English => Tuple.Create(apiResponse.BranchIcr.Name, apiResponse.BranchIcr.Note),
                    (int)DigitalSignatureDocumentLanguage.Arabic => Tuple.Create(apiResponse.BranchIcr.NameAr, apiResponse.BranchIcr.NoteAr),
                    (int)DigitalSignatureDocumentLanguage.Turkmen => Tuple.Create(apiResponse.BranchIcr.NameTm,
                        apiResponse.BranchIcr.NoteTm),
                    (int)DigitalSignatureDocumentLanguage.Russian => Tuple.Create(apiResponse.BranchIcr.NameRu,
                        apiResponse.BranchIcr.NoteRu),
                    _ => Tuple.Create(apiResponse.BranchIcr.Name, apiResponse.BranchIcr.Note)
                };
            }

            return languageId switch
            {
                (int)DigitalSignatureDocumentLanguage.English => Tuple.Create(apiResponse.BranchIcr.Name,
                    previousNote + apiResponse.BranchIcr.Note),
                (int)DigitalSignatureDocumentLanguage.Arabic => Tuple.Create(apiResponse.BranchIcr.NameAr,
                    previousNote.Replace(" ", "&nbsp;") + apiResponse.BranchIcr.NoteAr),
                (int)DigitalSignatureDocumentLanguage.Turkmen => Tuple.Create(apiResponse.BranchIcr.NameTm,
                    previousNote + apiResponse.BranchIcr.NoteTm),
                (int)DigitalSignatureDocumentLanguage.Russian => Tuple.Create(apiResponse.BranchIcr.NameRu,
                    previousNote + apiResponse.BranchIcr.NoteRu),
                (int)DigitalSignatureDocumentLanguage.French => Tuple.Create(apiResponse.BranchIcr.NameFr,
                    previousNote + apiResponse.BranchIcr.NoteFr),
                _ => Tuple.Create(apiResponse.BranchIcr.Name, previousNote + apiResponse.BranchIcr.Note)
            };

            #endregion
        }

        private string CreateICR_(string createdBy, string invoiceNumber, string vatAmount, string totalAmount, bool taxInclude, DateTime invoiceTime, bool isTLV)
        {
            var qrNote = string.Empty;
            if (taxInclude && isTLV)
            {
                var formattedDateTime = invoiceTime.ToString("yyyy'-'MM'-'dd' 'HH':'mm':'ss' 'tt", CultureInfo.CreateSpecificCulture("en-US"));
                var seller = $"01-{createdBy.Length.ToString("X").PadLeft(2, '0')}-{BitConverter.ToString(Encoding.Default.GetBytes(createdBy))}";
                var varNumber = $"02-{invoiceNumber.Length.ToString("X").PadLeft(2, '0')}-{BitConverter.ToString(Encoding.Default.GetBytes(invoiceNumber))}";
                var timeStamp = $"03-{formattedDateTime.Length.ToString("X").PadLeft(2, '0')}-{BitConverter.ToString(Encoding.Default.GetBytes(formattedDateTime))}";
                var invoiceTotal = $"04-{totalAmount.Length.ToString("X").PadLeft(2, '0')}-{BitConverter.ToString(Encoding.Default.GetBytes(totalAmount.Replace(",", ".")))}";
                var vatTotal = $"05-{vatAmount.Length.ToString("X").PadLeft(2, '0')}-{BitConverter.ToString(Encoding.Default.GetBytes(vatAmount.Replace(",", ".")))}";
                var hex = $"{seller}-{varNumber}-{timeStamp}-{invoiceTotal}-{vatTotal}";
                var hexString = ConverterHelper.FromHex(hex);
                qrNote = Convert.ToBase64String(hexString);
            }

            var qrReturnUrl = "https://gatewayinternational.com.tr";

            var content = taxInclude && isTLV ? qrNote : qrReturnUrl;
            using var generator = new QRCodeGenerator();

            // Generate QrCode
            var qr = generator.CreateQrCode(content, ECCLevel.Q);

            // Render to canvas
            var info = new SKImageInfo(512, 512);
            using var surface = SKSurface.Create(info);
            var canvas = surface.Canvas;
            canvas.Render(qr, info.Width, info.Height);

            // Output to Stream -> File
            using var image = surface.Snapshot();
            using var data = image.Encode(SKEncodedImageFormat.Png, 100);

            string str = Convert.ToBase64String(data.ToArray());
            return str;
        }

        private async Task<string> CreateBarcodePhotobooth(string encryptedId, int branchId, int userId, string corporateId)
        {
            int photoBoothId = encryptedId.ToDecryptInt();
            string newPhotoBoothId = "";

            var portalApiUrl = _configuration["PortalApiUrl"];
            var portalApiKey = _configuration["PortalApiKey"];

            var headers = new Dictionary<string, string>
            {
                { "apiKey", portalApiKey },
                { "languageId", "2" },
                { "corporateId", corporateId },
                { "UserId", userId.ToString() },
            };

            var apiResponseGetPhotoBooth = await PortalHttpClientHelper
                .GetAsync<ApiResponse<PhotoBoothApiResponse>>
                    (ApiMethodName.Appointment.GetPhotoBooth + photoBoothId, portalApiUrl, headers)
                .ConfigureAwait(false);

            newPhotoBoothId = photoBoothId.ToString();
            DateTime today = DateTime.Today.Date;
            if (today == apiResponseGetPhotoBooth.Data.ExpireDateTime.Value.Date && apiResponseGetPhotoBooth.Data.Status == (int)Portal.Gateway.Contracts.Entities.Enums.PhotoBoothStatus.Expired)
            {

                var apiAddPhotoBoothRequest = new AddPhotoBoothApiRequest
                {
                    BranchId = branchId,
                    ApplicationId = apiResponseGetPhotoBooth.Data.ApplicationId,
                    Name = apiResponseGetPhotoBooth.Data.Name,
                    Surname = apiResponseGetPhotoBooth.Data.Surname,
                    PassportNumber = apiResponseGetPhotoBooth.Data.PassportNumber,
                    Price = (decimal)apiResponseGetPhotoBooth.Data.Price,
                    CurrencyId = (int)apiResponseGetPhotoBooth.Data.CurrencyId,
                    Status = (int)PhotoBoothStatus.Expired,
                    RequestedBy = userId,
                };

                var apiAddPhotoBoothResponse = await PortalHttpClientHelper
                    .PostAsJsonAsync<ApiResponse<AddApiResponse>>
                    (apiAddPhotoBoothRequest, ApiMethodName.Appointment.AddPhotoBooth, portalApiUrl, headers)
                    .ConfigureAwait(false);

                _ = await PortalHttpClientHelper
                           .GetAsync<ApiResponse<UpdateApiResponse>>
                           ($"{ApiMethodName.Appointment.UpdatePhotoBoothApplicationStatus + photoBoothId}/{(int)PhotoBoothStatus.Expired}", portalApiUrl, headers)
                           .ConfigureAwait(false);

                newPhotoBoothId = apiAddPhotoBoothResponse.Data.Id.ToString();
            }

            return Convert.ToBase64String(BarcodeClient.CreateBarcodeByteArray(Int32.Parse(newPhotoBoothId).ToString("9000000000000"), new SKColor(33, 37, 41), BarcodeClient.DefaultWidth, BarcodeClient.PhotoboothHeight));
        }

        private async Task<string> IcrRendererByLanguage(int languageId, ICRViewModel viewModel)
        {
            string viewStr;
            string viewApplicant;
            int applicantCount;
            var viewApplicantStr = new List<string>();

            switch (languageId)
            {
                case (int)DigitalSignatureDocumentLanguage.English:
                        if (viewModel.BranchCountryId == 3)
                        {
                            applicantCount = viewModel.Applicants.Count;

                            for (int i = 0; i < applicantCount; i = i + 3)
                            {
                                viewModel.Order = i;
                                
                                viewApplicantStr.Add(await RenderToStringHelper.RenderViewToStringAsync("_ICRApplicantsAlgeriaEnglish", viewModel, ControllerContext));
                            }

                            viewApplicant = string.Join(" ", viewApplicantStr.Select(q => q));
                            return viewApplicant;
                        }
                        else
                        {
                            viewStr = await RenderToStringHelper.RenderViewToStringAsync("_ICR", viewModel, ControllerContext);
                            applicantCount = viewModel.Applicants.Count;

                            for (int i = 0; i < applicantCount; i = i + 3)
                            {
                                viewModel.Order = i;
                                viewApplicantStr.Add(await RenderToStringHelper.RenderViewToStringAsync("_ICRApplicants", viewModel, ControllerContext));
                            }

                            viewApplicant = string.Join(" ", viewApplicantStr.Select(q => q));
                            return viewStr.Replace("<progress></progress>", viewApplicant);
                        }
                case (int)DigitalSignatureDocumentLanguage.Arabic:
                    if (viewModel.BranchCountryId == 3)
                    {
                        applicantCount = viewModel.Applicants.Count;

                        for (int i = 0; i < applicantCount; i = i + 3)
                        {
                            viewModel.Order = i;
                            viewApplicantStr.Add(await RenderToStringHelper.RenderViewToStringAsync("_ICRApplicantsAlgeriaArabic", viewModel, ControllerContext));
                        }

                        viewApplicant = string.Join(" ", viewApplicantStr.Select(q => q));
                        return viewApplicant;
                    }
                    else
                    {
                        viewStr = await RenderToStringHelper.RenderViewToStringAsync("_ICRArabic", viewModel, ControllerContext);
                        applicantCount = viewModel.Applicants.Count;

                        for (int i = 0; i < applicantCount; i = i + 3)
                        {
                            viewModel.Order = i;
                            viewApplicantStr.Add(await RenderToStringHelper.RenderViewToStringAsync("_ICRApplicantsArabic", viewModel, ControllerContext));
                        }

                        var viewApplicantsRTL = string.Join(" ", viewApplicantStr.Select(q => q));
                        return viewStr.Replace("<progress></progress>", viewApplicantsRTL);
                    }
                case (int)DigitalSignatureDocumentLanguage.French:

                    applicantCount = viewModel.Applicants.Count;

                    for (var i = 0; i < applicantCount; i += 3)
                    {
                        viewModel.Order = i;

                        viewApplicantStr.Add(await RenderToStringHelper.RenderViewToStringAsync("_ICRApplicantsAlgeriaFrench", viewModel, ControllerContext));
                    }

                    viewApplicant = string.Join(" ", viewApplicantStr.Select(q => q));
                    return viewApplicant;

                case (int)DigitalSignatureDocumentLanguage.Turkmen:

                    viewStr = await RenderToStringHelper.RenderViewToStringAsync("_ICRTurkmen", viewModel, ControllerContext);
                    applicantCount = viewModel.Applicants.Count;

                    for (int i = 0; i < applicantCount; i = i + 3)
                    {
                        viewModel.Order = i;
                        viewApplicantStr.Add(await RenderToStringHelper.RenderViewToStringAsync("_ICRApplicantsTurkmen", viewModel, ControllerContext));
                    }

                    viewApplicant = string.Join(" ", viewApplicantStr.Select(q => q));
                    return viewStr.Replace("<progress></progress>", viewApplicant);

                case (int)DigitalSignatureDocumentLanguage.Russian:

                    viewStr = await RenderToStringHelper.RenderViewToStringAsync("_ICRRussian", viewModel, ControllerContext);
                    applicantCount = viewModel.Applicants.Count;

                    for (int i = 0; i < applicantCount; i = i + 3)
                    {
                        viewModel.Order = i;
                        viewApplicantStr.Add(await RenderToStringHelper.RenderViewToStringAsync("_ICRApplicantsRussian", viewModel, ControllerContext));
                    }

                    viewApplicant = string.Join(" ", viewApplicantStr.Select(q => q));
                    return viewStr.Replace("<progress></progress>", viewApplicant);

                default:
                    return null;
            }
        }

        private static int GetIcrLanguageFromPrintLanguage(int printLanguage)
        {
	        return printLanguage switch
	        {
		        (int)DigitalSignatureDocumentLanguage.English => (int)ICRLanguage.English,
		        (int)DigitalSignatureDocumentLanguage.French => (int)ICRLanguage.French,
		        (int)DigitalSignatureDocumentLanguage.Arabic => (int)ICRLanguage.Arabic,
		        (int)DigitalSignatureDocumentLanguage.Russian => (int)ICRLanguage.Russian,
		        (int)DigitalSignatureDocumentLanguage.Turkmen => (int)ICRLanguage.Turkmen,
		        _ => (int)ICRLanguage.English
	        };
        }


	}
}
