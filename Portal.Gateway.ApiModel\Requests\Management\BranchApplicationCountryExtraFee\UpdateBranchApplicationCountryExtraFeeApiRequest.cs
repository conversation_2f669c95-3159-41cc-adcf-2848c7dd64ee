﻿
namespace Portal.Gateway.ApiModel.Requests.Management.BranchApplicationCountryExtraFee
{
    public class UpdateBranchApplicationCountryExtraFeeApiRequest : BaseRequest
    {
        [RequiredValidator]
        public int Id { get; set; }

        [RequiredValidator]
        public int BranchApplicationCountryId { get; set; }

        [RequiredValidator]
        public int ExtraFeeId { get; set; }

        [RequiredValidator]
        public bool IsAutoChecked { get; set; }

        [RequiredValidator]
        public decimal Price { get; set; }

        [RequiredValidator]
        public decimal Tax { get; set; }

        [RequiredValidator]
        public decimal TaxRatio { get; set; }

        [RequiredValidator]
        public decimal ServiceTax { get; set; }

        [RequiredValidator]
        public decimal BasePrice { get; set; }

        [RequiredValidator]
        public int CurrencyId { get; set; }

        public string SapExtraFeeId { get; set; }

        [RequiredValidator]
        public bool IsActive { get; set; }

        [RequiredValidator]
        public bool ShowInICR { get; set; }

        [RequiredValidator]
        public bool ShowInSummary { get; set; }

        [RequiredValidator]
        public bool IsShowInReport { get; set; }
        
        [RequiredValidator]
        public bool IsGroupInIcr { get; set; }

        [RequiredValidator]
        public bool IsShowInRejectionList { get; set; }

        [RequiredValidator]
        public bool IsShowInAllApplicationsReport { get; set; }

        [RequiredValidator]
        public bool IsShowInApplicationAfterRejection { get; set; }


    }
}
