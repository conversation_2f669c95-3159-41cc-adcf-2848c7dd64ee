﻿<?xml version="1.0" encoding="utf-8"?>
<root>
  <!-- 
    Microsoft ResX Schema 
    
    Version 2.0
    
    The primary goals of this format is to allow a simple XML format 
    that is mostly human readable. The generation and parsing of the 
    various data types are done through the TypeConverter classes 
    associated with the data types.
    
    Example:
    
    ... ado.net/XML headers & schema ...
    <resheader name="resmimetype">text/microsoft-resx</resheader>
    <resheader name="version">2.0</resheader>
    <resheader name="reader">System.Resources.ResXResourceReader, System.Windows.Forms, ...</resheader>
    <resheader name="writer">System.Resources.ResXResourceWriter, System.Windows.Forms, ...</resheader>
    <data name="Name1"><value>this is my long string</value><comment>this is a comment</comment></data>
    <data name="Color1" type="System.Drawing.Color, System.Drawing">Blue</data>
    <data name="Bitmap1" mimetype="application/x-microsoft.net.object.binary.base64">
        <value>[base64 mime encoded serialized .NET Framework object]</value>
    </data>
    <data name="Icon1" type="System.Drawing.Icon, System.Drawing" mimetype="application/x-microsoft.net.object.bytearray.base64">
        <value>[base64 mime encoded string representing a byte array form of the .NET Framework object]</value>
        <comment>This is a comment</comment>
    </data>
                
    There are any number of "resheader" rows that contain simple 
    name/value pairs.
    
    Each data row contains a name, and value. The row also contains a 
    type or mimetype. Type corresponds to a .NET class that support 
    text/value conversion through the TypeConverter architecture. 
    Classes that don't support this are serialized and stored with the 
    mimetype set.
    
    The mimetype is used for serialized objects, and tells the 
    ResXResourceReader how to depersist the object. This is currently not 
    extensible. For a given mimetype the value must be set accordingly:
    
    Note - application/x-microsoft.net.object.binary.base64 is the format 
    that the ResXResourceWriter will generate, however the reader can 
    read any of the formats listed below.
    
    mimetype: application/x-microsoft.net.object.binary.base64
    value   : The object must be serialized with 
            : System.Runtime.Serialization.Formatters.Binary.BinaryFormatter
            : and then encoded with base64 encoding.
    
    mimetype: application/x-microsoft.net.object.soap.base64
    value   : The object must be serialized with 
            : System.Runtime.Serialization.Formatters.Soap.SoapFormatter
            : and then encoded with base64 encoding.

    mimetype: application/x-microsoft.net.object.bytearray.base64
    value   : The object must be serialized into a byte array 
            : using a System.ComponentModel.TypeConverter
            : and then encoded with base64 encoding.
    -->
  <xsd:schema id="root" xmlns="" xmlns:xsd="http://www.w3.org/2001/XMLSchema" xmlns:msdata="urn:schemas-microsoft-com:xml-msdata">
    <xsd:import namespace="http://www.w3.org/XML/1998/namespace" />
    <xsd:element name="root" msdata:IsDataSet="true">
      <xsd:complexType>
        <xsd:choice maxOccurs="unbounded">
          <xsd:element name="metadata">
            <xsd:complexType>
              <xsd:sequence>
                <xsd:element name="value" type="xsd:string" minOccurs="0" />
              </xsd:sequence>
              <xsd:attribute name="name" use="required" type="xsd:string" />
              <xsd:attribute name="type" type="xsd:string" />
              <xsd:attribute name="mimetype" type="xsd:string" />
              <xsd:attribute ref="xml:space" />
            </xsd:complexType>
          </xsd:element>
          <xsd:element name="assembly">
            <xsd:complexType>
              <xsd:attribute name="alias" type="xsd:string" />
              <xsd:attribute name="name" type="xsd:string" />
            </xsd:complexType>
          </xsd:element>
          <xsd:element name="data">
            <xsd:complexType>
              <xsd:sequence>
                <xsd:element name="value" type="xsd:string" minOccurs="0" msdata:Ordinal="1" />
                <xsd:element name="comment" type="xsd:string" minOccurs="0" msdata:Ordinal="2" />
              </xsd:sequence>
              <xsd:attribute name="name" type="xsd:string" use="required" msdata:Ordinal="1" />
              <xsd:attribute name="type" type="xsd:string" msdata:Ordinal="3" />
              <xsd:attribute name="mimetype" type="xsd:string" msdata:Ordinal="4" />
              <xsd:attribute ref="xml:space" />
            </xsd:complexType>
          </xsd:element>
          <xsd:element name="resheader">
            <xsd:complexType>
              <xsd:sequence>
                <xsd:element name="value" type="xsd:string" minOccurs="0" msdata:Ordinal="1" />
              </xsd:sequence>
              <xsd:attribute name="name" type="xsd:string" use="required" />
            </xsd:complexType>
          </xsd:element>
        </xsd:choice>
      </xsd:complexType>
    </xsd:element>
  </xsd:schema>
  <resheader name="resmimetype">
    <value>text/microsoft-resx</value>
  </resheader>
  <resheader name="version">
    <value>2.0</value>
  </resheader>
  <resheader name="reader">
    <value>System.Resources.ResXResourceReader, System.Windows.Forms, Version=4.0.0.0, Culture=neutral, PublicKeyToken=b77a5c561934e089</value>
  </resheader>
  <resheader name="writer">
    <value>System.Resources.ResXResourceWriter, System.Windows.Forms, Version=4.0.0.0, Culture=neutral, PublicKeyToken=b77a5c561934e089</value>
  </resheader>
  <data name="BRANCH_DEPARTMENT_NOT_FOUND" xml:space="preserve">
    <value>Отделение не найдено</value>
  </data>
  <data name="APPLICANT_NOT_FOUND" xml:space="preserve">
    <value>Заявитель не найден</value>
  </data>
  <data name="APPOINTMENT_NOT_FOUND" xml:space="preserve">
    <value>Назначенная встреча не найдена</value>
  </data>
  <data name="BRANCH_NOT_FOUND" xml:space="preserve">
    <value>Филиал не найден</value>
  </data>
  <data name="FAILED" xml:space="preserve">
    <value>НЕ УДАЛОСЬ</value>
  </data>
  <data name="FAMILY_APPLICANT_COUNT_ERROR" xml:space="preserve">
    <value>Количество семейных заявок может быть больше 1</value>
  </data>
  <data name="GROUP_APPLICANT_COUNT_ERROR" xml:space="preserve">
    <value>Количество групповых заявок может быть больше 1</value>
  </data>
  <data name="INDIVIDUAL_APPLICANT_COUNT_ERROR" xml:space="preserve">
    <value>Количество индивидуальных заявок не может быть более 1.</value>
  </data>
  <data name="INPUT_ERROR" xml:space="preserve">
    <value>ОШИБКА_ВВОДА</value>
  </data>
  <data name="INTERNAL_SERVER_ERROR" xml:space="preserve">
    <value>ВНУТРЕННЯЯ_ОШИБКА_СЕРВЕРА</value>
  </data>
  <data name="INVALID_INPUT_ERROR" xml:space="preserve">
    <value>Ошибка недопустимого ввода</value>
  </data>
  <data name="INVALID_REQUEST" xml:space="preserve">
    <value>Неверный запрос</value>
  </data>
  <data name="PROPERTY_MAX_LENGTH_ERROR" xml:space="preserve">
    <value>Свойство {0} не может быть больше, чем {1} символов</value>
  </data>
  <data name="PROPERTY_REQUIRED" xml:space="preserve">
    <value>{0} требуется</value>
  </data>
  <data name="RESOURCE_ALREADY_REGISTERED" xml:space="preserve">
    <value>Ресурс уже зарегистрирован</value>
  </data>
  <data name="RESOURCE_CREATED" xml:space="preserve">
    <value>Созданный ресурс</value>
  </data>
  <data name="RESOURCE_DELETED" xml:space="preserve">
    <value>Ресурс удален</value>
  </data>
  <data name="RESOURCE_FOUND" xml:space="preserve">
    <value>Ресурс найден</value>
  </data>
  <data name="RESOURCE_NOT_FOUND" xml:space="preserve">
    <value>Ресурс не найден</value>
  </data>
  <data name="RESOURCE_RETRIEVED" xml:space="preserve">
    <value>Извлеченный ресурс</value>
  </data>
  <data name="RESOURCE_UPDATED" xml:space="preserve">
    <value>Ресурс обновлен</value>
  </data>
  <data name="SUCCESS" xml:space="preserve">
    <value>УСПЕШНО</value>
  </data>
  <data name="SLOT_NOT_FOUND" xml:space="preserve">
    <value>Слот не найден</value>
  </data>
  <data name="NO_AVAILABLE_SLOTS_FOUND" xml:space="preserve">
    <value>Свободные слоты не найдены</value>
  </data>
  <data name="FIRST_AVAILABLE_SLOT_FOUND" xml:space="preserve">
    <value>Найден первый свободный слот</value>
  </data>
  <data name="COUNTRY_NOT_FOUND" xml:space="preserve">
    <value>Страна не найдена</value>
  </data>
  <data name="INVALID_EMAIL_ADDRESS" xml:space="preserve">
    <value>Неверный адрес электронной почты</value>
  </data>
  <data name="REPEATED_APPOINTMENT_FOUND" xml:space="preserve">
    <value>С данным паспортом назначена встреча</value>
  </data>
  <data name="SLOT_QUOTA_NOT_FOUND" xml:space="preserve">
    <value>Слотовая квота не найдена</value>
  </data>
  <data name="APPLICATION_STATUS_NOT_FOUND" xml:space="preserve">
    <value>Статус приложения не найден</value>
  </data>
  <data name="APPLICANT_COUNT_VALIDATION_ERROR" xml:space="preserve">
    <value>В индивидуальном типе заявок количество заявок не может превышать больше 1.</value>
  </data>
  <data name="APPLICATION_NOT_FOUND" xml:space="preserve">
    <value>Заявление не найдено</value>
  </data>
  <data name="PROPERTY_FORMAT_ERROR" xml:space="preserve">
    <value>Свойство {0} формата недействительно</value>
  </data>
  <data name="APPLICANT_ONLY_HAS_ONE_WİFEORHUSBAND" xml:space="preserve">
    <value>Заявитель должен иметь только одного супруга</value>
  </data>
  <data name="ONLY_ONE_RECORD_CANBE_SET_AS_WIFEORHUSBAND_RECORD_FOR_APPOINTMENT" xml:space="preserve">
    <value>В качестве партнера на дату можно выбрать только одну запись.</value>
  </data>
  <data name="METHOD_REQUIREMENT_ERROR" xml:space="preserve">
    <value>Этот метод не действует для данной комбинации параметров</value>
  </data>
  <data name="APPOINTMENT_NOT_CONVERTED_APPLICATION" xml:space="preserve">
    <value>Запись не преобразована в заявку.</value>
  </data>
  <data name="APPOINTMENT_HAS_CONVERTED_APPLICATION_NOT_FOUND" xml:space="preserve">
    <value>Поскольку заявки не было, ее перевели в запись на прием.</value>
  </data>
  <data name="FILE_OPERATION_FAILED" xml:space="preserve">
    <value>Сбой файловой операции</value>
  </data>
  <data name="MAIL_FOOTER" xml:space="preserve">
    <value>&lt;string name="EmailFooter_Russian"&gt;&amp;lt;div style=&amp;quot;font-family: Arial, sans-serif; font-size: 12px; color: #333;&amp;quot;&amp;gt;&amp;lt;p&amp;gt;Для получения более подробной информации, пожалуйста, посетите наш веб-сайт: &amp;lt;a href=&amp;quot;https://gatewayinternational.com.tr&amp;quot; target=&amp;quot;_blank&amp;quot;&amp;gt;https://gatewayinternational.com.tr&amp;lt;/a&amp;gt;&amp;lt;/p&amp;gt;&amp;lt;p&amp;gt;Вы можете связаться с нами по электронной почте или номеру колл-центра указанному ниже.&amp;lt;/p&amp;gt;&amp;lt;p&amp;gt;электронная почта: &amp;lt;a href=&amp;quot;mailto:<EMAIL>&amp;quot;&amp;gt;<EMAIL>&amp;lt;/a&amp;gt;&amp;lt;/p&amp;gt;&amp;lt;p&amp;gt;Колл-центр: &amp;lt;a href=&amp;quot;tel:+964662111919&amp;quot;&amp;gt;00964 ************&amp;lt;/a&amp;gt;&amp;lt;/p&amp;gt;&amp;lt;p&amp;gt;Спасибо.&amp;lt;/p&amp;gt;&amp;lt;p&amp;gt;С Уважением,&amp;lt;/p&amp;gt;&amp;lt;p&amp;gt;Gateway International&amp;lt;/p&amp;gt;&amp;lt;p&amp;gt;Это автоматическое письмо. Пожалуйста, не отвечайте на это письмо.&amp;lt;/p&amp;gt;&amp;lt;/div&amp;gt;&lt;/string&gt;</value>
  </data>
  <data name="NOTIFICATION_MAIL_NEWAPPOINTMENT" xml:space="preserve">
    <value>Уважаемый заявитель &lt;br/&gt;&lt;br/&gt; Ваш предварительный прием назначен в &lt;span style=\"color: black;\"&gt; филиале [BRANCH] &lt;/span&gt; на &lt;span style=\"color: black;\"&gt; [DATE] дату, [TIME] час. &lt;/span&gt; Ваш регистрационный номер - &lt;span style=\"color: black;\"&gt; [APPNUMBER] &lt;/span&gt; &lt;br/&gt;&lt;br/&gt; Пожалуйста, найдите письмо о предварительной записи во вложении.</value>
  </data>
  <data name="NOTIFICATION_MAIL_UPDATEAPPOINTMENT" xml:space="preserve">
    <value>Уважаемый заявитель &lt;br/&gt;&lt;br/&gt; Ваша предварительная запись на прием в &lt;span style=\"color: black;\"&gt; филиал [BRANCH] &lt;/span&gt; на &lt;span style=\"color: black;\"&gt; [DATE] дату, [TIME] час. &lt;/span&gt;  Пожалуйста, ознакомьтесь с письмом о назначении во вложении</value>
  </data>
  <data name="APPLICANT_NOT_MATCH_WITH_APPOINTMENT" xml:space="preserve">
    <value>Кандидат не соответствует назначению</value>
  </data>
  <data name="BRANCH_CHANGE_NOT_ALLOWED" xml:space="preserve">
    <value>Изменить центр можно только один раз.</value>
  </data>
  <data name="NEW_APPLICANT_NOT_ALLOWED" xml:space="preserve">
    <value>Новый заявитель не допускается</value>
  </data>
  <data name="SLOT_NOT_IN_THIS_BRANCH" xml:space="preserve">
    <value>Слот для данного центра не найден</value>
  </data>
  <data name="PASSPORT_VALIDITY_PERIOD_ERROR" xml:space="preserve">
    <value>Срок действия паспорта не может быть менее 180 дней</value>
  </data>
  <data name="BIRTHDATE_MUST_BE_PAST_TENSE" xml:space="preserve">
    <value>Дата рождения должна быть в прошедшем времени</value>
  </data>
  <data name="APPOINTMENT_SLOT_LIMIT" xml:space="preserve">
    <value>Обновление даты и времени может быть произведено не более чем 2 раз</value>
  </data>
  <data name="APPOINTMENT_UPDATE_NOT_ALLOWED" xml:space="preserve">
    <value>Обновление назначений встреч не может быть произведено в течение последних 48 часов</value>
  </data>
  <data name="DELETE_OPERATION_NOT_ALLOWED" xml:space="preserve">
    <value>Назначения, до которых осталось менее 24 часов, не могут быть удалены</value>
  </data>
  <data name="REPEATED_MAIL_NOT_ALLOWED" xml:space="preserve">
    <value>На один и тот же адрес электронной почты можно назначить не более 2 встреч.</value>
  </data>
  <data name="REPEATED_PHONE_NUMBER_NOT_ALLOWED" xml:space="preserve">
    <value>На один и тот же номер телефона можно назначить не более 3 встреч.</value>
  </data>
  <data name="VAST_TYPE_PRICE_MESSAGE" xml:space="preserve">
    <value>Помимо платы за обслуживание, за эту услугу взимается дополнительная плата {price}.</value>
  </data>
  <data name="VAS_TYPE_NOT_ACTIVE" xml:space="preserve">
    <value>Тип заявления не активен</value>
  </data>
  <data name="VISA_INFORMATION_NOT_ACTIVE" xml:space="preserve">
    <value>Информация о визе не активна</value>
  </data>
  <data name="USER_ALREADY_REGISTERED" xml:space="preserve">
    <value>Пользователь уже зарегистрирован</value>
  </data>
  <data name="PASSWORD_MISMATCH" xml:space="preserve">
    <value>Несоответствие паролей</value>
  </data>
  <data name="ALREADY_HAS_UPDATE_REQUEST" xml:space="preserve">
    <value>Уже есть запрос на обновление</value>
  </data>
  <data name="USER_NOT_FOUND" xml:space="preserve">
    <value>Пользователь не найден</value>
  </data>
  <data name="AUTHORIZED_USER_NOT_FOUND" xml:space="preserve">
    <value>Авторизованный пользователь не найден</value>
  </data>
  <data name="INVALID_PHONE_NUMBER" xml:space="preserve">
    <value>Неверный номер телефона</value>
  </data>
  <data name="PROPERTY_MUST_NOT_HAVE_NUMERIC_CHARACTER" xml:space="preserve">
    <value>Не должно содержать числовых символов</value>
  </data>
  <data name="PASSWORD_MUST_NOT_SAME" xml:space="preserve">
    <value>Пароль не должен совпадать</value>
  </data>
  <data name="TRANSLATION_NOT_FOUND" xml:space="preserve">
    <value>Перевод не найден</value>
  </data>
  <data name="COMPANY_NOT_FOUND" xml:space="preserve">
    <value>учреждение не найдено</value>
  </data>
  <data name="EXTERNAL_REGISTER_NOTIFICATION" xml:space="preserve">
    <value>Ваша регистрация завершена. Ваш временный пароль - [PASSWORD] Вы можете изменить его в системе после входа в систему с временным паролем.</value>
  </data>
  <data name="NO_DATA_FOUND_TO_REPORT" xml:space="preserve">
    <value>Данные не найдены</value>
  </data>
  <data name="CREATED_BY" xml:space="preserve">
    <value>Создано</value>
  </data>
  <data name="ORDER" xml:space="preserve">
    <value>Заказать</value>
  </data>
  <data name="REPORT_DATE" xml:space="preserve">
    <value>Дата отчета</value>
  </data>
  <data name="SAME_PASSPORT_USED_BETWEEN_APPLICANTS" xml:space="preserve">
    <value>Один и тот же паспорт используется для разных заявителей</value>
  </data>
  <data name="COMPANY_USER_REPORT" xml:space="preserve">
    <value>Отчет пользователя учреждения</value>
  </data>
  <data name="COMPANY_SLOT_DEMAND_REPORT" xml:space="preserve">
    <value>Отчет о запросе слота</value>
  </data>
  <data name="COMPANY_APPOINTMENT_DEMAND_REPORT" xml:space="preserve">
    <value>Отчет о запросе на назначение в учреждение</value>
  </data>
  <data name="APPOINTMENT_DEMAND_NOT_FOUND" xml:space="preserve">
    <value>Запрос на назначение не найден</value>
  </data>
  <data name="COMPANY_APPLICATION_REPORT" xml:space="preserve">
    <value>Отчет о заявки учреждения</value>
  </data>
  <data name="PNL_REPORT" xml:space="preserve">
    <value>Отчет PNL</value>
  </data>
  <data name="PNL_ALREADY_REGISTERED" xml:space="preserve">
    <value>PNL уже зарегистрирован</value>
  </data>
  <data name="INVALID_PARAMETER" xml:space="preserve">
    <value>Недопустимый параметр {0}</value>
  </data>
  <data name="INVALID_PASSPORT_NUMBER" xml:space="preserve">
    <value>Неверный номер паспорта</value>
  </data>
  <data name="DATE_PARAMETERS_MISMATCH" xml:space="preserve">
    <value>Несоответствие параметров даты</value>
  </data>
  <data name="FILE_SIZE_LIMIT_EXCEEDED" xml:space="preserve">
    <value>Превышен лимит размера файла</value>
  </data>
  <data name="INVALID_FILE_EXTENSION" xml:space="preserve">
    <value>Недопустимое расширение файла</value>
  </data>
  <data name="EMAIL_MISMATCH_WITH_TOKEN" xml:space="preserve">
    <value>Электронная почта должна совпадать с электронной почтой, используемой при входе в систему</value>
  </data>
  <data name="APPOINTMENT_FOUND_WITH_SAME_PASSPORT" xml:space="preserve">
    <value>Найдено назначение с тем же номером паспорта</value>
  </data>
  <data name="APPOINTMENT_CONFIRMATION_FILE_NAME" xml:space="preserve">
    <value>Подтверждение встречи</value>
  </data>
  <data name="APPOINTMENT_UPDATE_CONFIRMATION_FILE_NAME" xml:space="preserve">
    <value>Подтверждение обновления назначений</value>
  </data>
  <data name="BAD_REQUEST" xml:space="preserve">
    <value>BAD_REQUEST</value>
  </data>
  <data name="CANNOT_CONTINUE_APPOINTMENT" xml:space="preserve">
    <value>Вы не можете продолжить прием с этим номером паспорта.</value>
  </data>
  <data name="PRE_CONDITION_FAILED" xml:space="preserve">
    <value>Предварительное условие не выполнено</value>
  </data>
  <data name="INVALID_PARAMETER_LOWER" xml:space="preserve">
    <value>Неверный параметр: {0} должен быть меньше {1}</value>
  </data>
  <data name="CANNOT_UPDATE_WALKIN" xml:space="preserve">
    <value>Невозможно обновить записи на прием</value>
  </data>
  <data name="MOBILE_SLOT_NOT_FOUND" xml:space="preserve">
    <value>Нет доступных назначений на выбранную дату. Попробуйте другую дату.</value>
  </data>
</root>