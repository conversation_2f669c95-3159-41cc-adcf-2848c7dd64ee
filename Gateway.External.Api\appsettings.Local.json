{"Logging": {"LogLevel": {"Default": "Information", "Microsoft": "Warning", "Microsoft.Hosting.Lifetime": "Information"}}, "ConnectionStrings": {"GatewayExternalApiDbConnection": "Server=**********;Username=gw_portal_stg_app_usr;Password=*****************;Database=MigrationTestPortal;"}, "Authority": {"ServiceUrl": "http://***********:8000"}, "AppSettings": {"MinioConfiguration": {"EndPoint": "visacdn.gateway.com.tr:443", "AccessKey": "ZzwzxvFNvs0Yf6eK", "SecretKey": "ZOKfrxMYSkB12Fst6BBprVDJvdFoCETI", "IsSecure": "false", "B2BBucketName": "visaportal-b2b-development"}}, "RabbitMq": {"Host": "GWMQ01.gateway.com.tr", "User": "test_user", "Password": "okmenn", "Port": 5672, "Exchange": "log.exchange", "LoggerQueue": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "EmailQueue": "EmailQueue", "SMSQueue": "SMSQueue"}, "B2BIdentityServer": {"BaseAddress": "http://***********:8000/", "ApplicationId": 9, "ApplicationResourceId": "gwAms2!", "ClientId": "gw-ams-b2b", "ClientSecret": "09e47380-90cd-b44a-8a45-3848efb5cd58"}, "Payment": {"Url": "https://localhost:7270", "X_PGW_API_KEY": "4XGkFQX07IVQe6ehUrtwzy3GG30BXbpeMJFYowe9AoGjQ1c2A95tboJA1TFxNkcR", "X_PGW_SECRET_KEY": "yAsAb7ynVENcKk85EjAyNGDD7Hpx38AlWsXQsTxDjGjbhXok9uOb4drdzXaoSxwA"}, "Redis": {"Url": "***********", "Port": "6379", "ConnectTimeout": 10000, "ConnectRetry": 3, "DefaultDatabase": 8}, "IpRateLimiting": {"EnableEndpointRateLimiting": false, "StackBlockedRequests": false, "RealIpHeader": "X-Real-IP", "ClientIdHeader": "X-ClientId", "HttpStatusCode": 429, "EndpointWhitelist": ["*:/api/lookup", "*:/api/branches/*", "*:/api/countries/*"], "GeneralRules": [{"Endpoint": "*", "Period": "1s", "Limit": 10}, {"Endpoint": "*", "Period": "1m", "Limit": 100}]}, "ExternalClients": {"ClientInfo": {"gw-ams-b2b": {"Code": "2"}, "gw-etiket-visa-b2b": {"Code": "2"}, "mobile-client": {"Code": "3"}, "gw-international": {"Code": "4"}, "gw-ams": {"Code": "4"}, "gw-etiket-visa-b2c": {"Code": "4"}, "gw-ams-b2c": {"Code": "4"}, "gw-pmo": {"Code": "5"}}}, "SenCard": {"Url": "https://www.cgmturkiye.com/ilacECP/NetworkRestWS/"}}