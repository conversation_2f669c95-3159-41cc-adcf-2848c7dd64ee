﻿using AutoMapper;
using Gateway.External.Api.Mapping;
using Gateway.External.Application.Application;
using Gateway.External.Application.Appointment;
using Gateway.External.Application.Appointment.EventHandler;
using Gateway.External.Application.AppointmentDemand;
using Gateway.External.Application.Biometrics;
using Gateway.External.Application.Company;
using Gateway.External.Application.Company.EventHandler;
using Gateway.External.Application.Country;
using Gateway.External.Application.Customer;
using Gateway.External.Application.Dashboard;
using Gateway.External.Application.Document;
using Gateway.External.Application.File;
using Gateway.External.Application.Lookup;
using Gateway.External.Application.Management;
using Gateway.External.Application.Notification;
using Gateway.External.Application.Notification.Events;
using Gateway.External.Application.Payment;
using Gateway.External.Application.PhotoBooth;
using Gateway.External.Application.Report;
using Gateway.External.Application.SenCard;
using Gateway.External.Application.Slot;
using Gateway.External.Application.SlotDemand;
using Gateway.External.Application.User;
using Gateway.External.Core.Context;
using Gateway.External.Persistence;
using Gateway.ObjectStoring;
using Gateway.ObjectStoring.Minio;
using Gateway.Validation;
using Microsoft.EntityFrameworkCore;
using Microsoft.Extensions.Configuration;
using Microsoft.Extensions.DependencyInjection;

namespace Gateway.External.Api.Extensions
{
    public static class ServiceCollectionExtensions
    {
        public static IServiceCollection RegisterMappers(this IServiceCollection services)
        {
            var mapperConfiguration = new MapperConfiguration(cfg =>
            {
                cfg.AddProfile(new ApiMappingProfile());
            });

            var mapper = mapperConfiguration.CreateMapper();

            services.AddSingleton(typeof(IMapper), _ => mapper);

            return services;
        }

        public static IServiceCollection RegisterServices(this IServiceCollection services)
        {
            services.AddScoped<IValidationService, ValidationService>();
            services.AddScoped<IFileStorage, MinioFileStorage>();
            services.AddScoped<IBucketNamingNormalizer, MinioBucketNamingNormalizer>();
            services.AddScoped<IAppointmentService, AppointmentService>();
            services.AddScoped<IManagementService, ManagementService>();
            services.AddScoped<ISlotService, SlotService>();
            services.AddScoped<ILookupService, LookupService>();
            services.AddScoped<ICountryService, CountryService>();
            services.AddScoped<IApplicationService, ApplicationService>();
            services.AddScoped<IBiometricsService, BiometricsService>();
            services.AddScoped<IDocumentService, DocumentService>();
            services.AddScoped<ICustomerService, CustomerService>();
            services.AddScoped<ICompanyService, CompanyService>();
			services.AddScoped<ISlotDemandService, SlotDemandService>();
			services.AddScoped<IReportService, ReportService>();
			services.AddScoped<IPaymentService, PaymentService>();
			services.AddScoped<IAppointmentDemandService, AppointmentDemandService>();
			services.AddScoped<IDashboardService, DashboardService>();
			services.AddScoped<IUserService, UserService>();
            services.AddScoped<IFileService, FileService>();
			services.AddScoped<IPhotoBoothService, PhotoBoothService>();
			services.AddScoped<ISenCardService, SenCardService>();

			return services;
        }

        public static IServiceCollection RegisterDbContext(this IServiceCollection services, IConfiguration configuration)
        {
            services.AddDbContext<ApiDbContext>(options => options.UseNpgsql(configuration["ConnectionStrings:GatewayExternalApiDbConnection"]));

            return services;
        }

        public static IServiceCollection RegisterHttpContext(this IServiceCollection services)
        {
            services.AddSingleton<Microsoft.AspNetCore.Http.IHttpContextAccessor, Microsoft.AspNetCore.Http.HttpContextAccessor>();
            services.AddScoped<IContextFactory, ContextFactory>();
            services.AddTransient(p => p.GetRequiredService<IContextFactory>().Create());

            return services;
        }

        public static void RegisterEvents(this IServiceCollection services)
        {
            services.AddScoped<INotificationEventService, NotificationEventService>();

            services.AddScoped<INotificationEventHandler<CreateAppointmentSendNotificationEvent>, CreateAppointmentSendNotificationEventHandler>();
            services.AddScoped<INotificationEventHandler<UpdateAppointmentSendNotificationEvent>, UpdateAppointmentSendNotificationEventHandler>();
            services.AddScoped<INotificationEventHandler<ExternalRegisterSendNotificationEvent>, ExternalRegisterNotificationEventHandler>();
        }
    }
}
