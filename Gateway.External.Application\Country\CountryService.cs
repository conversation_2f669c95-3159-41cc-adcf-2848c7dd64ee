﻿using Gateway.External.Application.Country.Dto;
using Gateway.External.Application.Country.Validator;
using Gateway.External.Application.Lookup;
using Gateway.External.Application.Lookup.Dto;
using Gateway.External.Persistence;
using Gateway.External.Resources;
using Gateway.Validation;
using Microsoft.EntityFrameworkCore;
using Portal.Gateway.Common.Utility.Extensions;
using Serilog;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Threading.Tasks;
using StringExtensions = Portal.Gateway.Common.Utility.Extensions.StringExtensions;

namespace Gateway.External.Application.Country
{
    public class CountryService : ICountryService
	{
        private static readonly ILogger Logger = Log.ForContext<CountryService>();
        private readonly ApiDbContext _dbContext;
		private readonly IValidationService _validationService;

		public CountryService(IValidationService validationService, ApiDbContext dbContext)
		{
			_validationService = validationService;
			_dbContext = dbContext;
		}

		public async Task<CountriesResult> GetCountries(GetCountriesRequest request)
		{
			var validationResult = _validationService.Validate(typeof(GetCountriesValidator), request);

			if (!validationResult.IsValid)
				return new CountriesResult
				{
					Status = GetCountriesStatus.InvalidInput,
					Message = ServiceResources.INVALID_INPUT_ERROR,
					ValidationMessages = validationResult.ErrorMessages
				};

            try
            {
                var queryCountries = await _dbContext.Country.ToListAsync();

                if (request.To)
                {
                    var queryBranchApplicationCountries = await _dbContext.BranchApplicationCountry
                        .Where(r => r.IsActive && !r.IsDeleted && r.CountryId == 180).ToListAsync(); //türkiye statik yapıldı sonradan

                    if (!queryBranchApplicationCountries.Any())
                        return new CountriesResult
                        {
                            Status = GetCountriesStatus.BranchNotFound,
                            Message = ServiceResources.BRANCH_NOT_FOUND,
                        };

                    queryCountries = queryCountries
                        .Where(r => queryBranchApplicationCountries.Select(p => p.CountryId).Contains(r.Id)).ToList();
                }
                else if (request.From)
                {
                    var queryBranches = await _dbContext.Branch
                        .If(request.Context.ChannelTypeId == (int)Enums.Enums.ChannelType.GwInternational, s => s.Where(w => w.ShowInB2c))
						.If(request.Context.ChannelTypeId == (int)Enums.Enums.ChannelType.Mobile, s => s.Where(w => w.ShowBranchInMobile))
						.Where(r => r.IsActive && !r.IsDeleted).ToListAsync();

                    if (!queryBranches.Any())
                        return new CountriesResult
                        {
                            Status = GetCountriesStatus.BranchNotFound,
                            Message = ServiceResources.BRANCH_NOT_FOUND,
                        };

                    queryCountries = queryCountries
                        .Where(r => queryBranches.Select(p => p.CountryId).Contains(r.Id)).ToList();
                }
                else if (request.Residence)
                {
                    var queryBranches = await _dbContext.Branch
                        .Where(r => r.IsActive && !r.IsDeleted).ToListAsync();

                    if (!queryBranches.Any())
                        return new CountriesResult
                        {
                            Status = GetCountriesStatus.BranchNotFound,
                            Message = ServiceResources.BRANCH_NOT_FOUND,
                        };

                    queryCountries = queryCountries
                        .Where(r => queryBranches.Select(p => p.CountryId).Contains(r.Id)).ToList();
                }

                if (!queryCountries.Any())
                    return new CountriesResult
                    {
                        Status = GetCountriesStatus.ResourceNotFound,
                        Message = ServiceResources.RESOURCE_NOT_FOUND,
                    };

                var countries = queryCountries.Select(c => new CountryDto
                {
                    Id = c.Id,
                    Name = GetCountryName(c, request.Context.LanguageId),
                    ISO2 = c.ISO2,
                    ISO3 = c.ISO3,
                    CallingCode = c.CallingCode
                }).ToList();

                return new CountriesResult
                {
                    Countries = countries,
                    Status = countries.Any() ? GetCountriesStatus.Successful : GetCountriesStatus.ResourceNotFound,
                    Message = countries.Any() ? ServiceResources.RESOURCE_RETRIEVED : ServiceResources.RESOURCE_NOT_FOUND
                };
            }
            catch (Exception exc)
            {
                Logger.Error($@"Exception:{exc.Message}:{DateTime.Now}");

                return new CountriesResult
                {
                    Status = GetCountriesStatus.InternalServerError,
                    Message = ServiceResources.INTERNAL_SERVER_ERROR,
                };
            }
        }

		public async Task<CountryResult> GetCountry(GetCountryRequest request)
		{
            try
            {
                var queryCountry = await _dbContext.Country.Where(r => r.Id == request.ResourceId).FirstOrDefaultAsync();

                if (queryCountry == null)
                    return new CountryResult
                    {
                        Status = GetCountryStatus.ResourceNotFound,
                        Message = ServiceResources.RESOURCE_NOT_FOUND,
                    };

                var country = new CountryDto
                {
                    Id = queryCountry.Id,
                    Name = GetCountryName(queryCountry, request.Context.LanguageId),
                    ISO2 = queryCountry.ISO2,
                    ISO3 = queryCountry.ISO3,
                    CallingCode = queryCountry.CallingCode
                };

                return new CountryResult
                {
                    Country = country,
                    Status = GetCountryStatus.Successful,
                    Message = ServiceResources.RESOURCE_RETRIEVED
                };
            }
            catch (Exception exc)
            {
                Logger.Error($@"Exception:{exc.Message}:{DateTime.Now}");

                return new CountryResult
                {
                    Status = GetCountryStatus.InternalServerError,
                    Message = ServiceResources.INTERNAL_SERVER_ERROR,
                };
            }
        }

		public async Task<BranchesByCountryResult> GetBranchesByCountry(BranchesByCountryRequest request)
		{
			var validationResult = _validationService.Validate(typeof(BranchesByCountryValidator), request);

			if (!validationResult.IsValid)
				return new BranchesByCountryResult
				{
					Status = GetBranchesByCountryStatus.InvalidInput,
					Message = ServiceResources.INVALID_INPUT_ERROR,
					ValidationMessages = validationResult.ErrorMessages
				};

            try
            {
                var branches = await _dbContext.Branch
                    .Include(p => p.BranchTranslations)
                    .Include(p => p.BranchDataTranslation)
                    .If(request.Context.ChannelTypeId == (int)Enums.Enums.ChannelType.GwInternational, s => s.Where(w => w.ShowInB2c))
					.If(request.Context.ChannelTypeId == (int)Enums.Enums.ChannelType.Mobile, s => s.Where(w => w.ShowBranchInMobile))
					.Where(p => p.IsActive && !p.IsDeleted && p.CountryId == request.CountryId).ToListAsync();

                if (!branches.Any())
                    return new BranchesByCountryResult
                    {
                        Status = GetBranchesByCountryStatus.BranchNotFound,
                        Message = ServiceResources.BRANCH_NOT_FOUND
                    };

                var result = branches.Select(p => new BranchesByCountryResult.BranchDto()
                {
                    BranchId = p.Id,
                    BranchName = p.BranchTranslations.FirstOrDefault(t => t.LanguageId == request.Context.LanguageId)?.Name
                                 ?? p.BranchTranslations.FirstOrDefault(r => r.LanguageId == (int)Enums.Enums.Language.English)?.Name,
                    Address = request.Context.LanguageId == (int)Enums.Enums.Language.Arabic ? p.BranchDataTranslation.FirstOrDefault(r => r.LanguageId == request.Context.LanguageId)?.Address : p.Address,
                    CityName = request.Context.LanguageId == (int)Enums.Enums.Language.Arabic ? p.BranchDataTranslation.FirstOrDefault(r => r.LanguageId == request.Context.LanguageId)?.CityName : p.CityName,
                    Telephone = p.Telephone,
                }).ToList();

                return new BranchesByCountryResult
                {
                    Branches = result,
                    Status = result.Any() ? GetBranchesByCountryStatus.Successful : GetBranchesByCountryStatus.ResourceNotFound,
                    Message = result.Any() ? ServiceResources.RESOURCE_RETRIEVED : ServiceResources.RESOURCE_NOT_FOUND
                };
            }
            catch (Exception exc)
            {
                Logger.Error($@"Exception:{exc.Message}:{DateTime.Now}");

                return new BranchesByCountryResult
                {
                    Status = GetBranchesByCountryStatus.InternalServerError,
                    Message = ServiceResources.INTERNAL_SERVER_ERROR,
                };
            }
        }

		public async Task<GetLookupResult> GetLookupValueByCountry(GetLookupRequest request)
		{
			var result = new List<LookupDto>();

            try
            {
                var lookUpRules = await _dbContext.LookupRule.Where(r => r.CountryId == request.CountryId).ToListAsync();

                foreach (var item in request.LookupTypeIds)
                {
                    if (!LookupTypeFactory.IsTypeExist(item)) continue;

                    var lookupValues = Gateway.Extensions.EnumExtensions.GetEnumAsDictionary(LookupTypeFactory.GetInstance(item))
                        .Select(x => new LookupValue { Id = x.Key.ToString(), DisplayValue = x.Value }).ToList();

                    if (lookUpRules.Any())
                    {
                        var lookupRule = lookUpRules.Find(r => r.LookupId == item);

                        if (lookupRule != null) //apply rule
                        {
                            var lookupValueList = lookupRule.Value.Split(',').Select(int.Parse).ToList();

                            lookupValues = lookupValues.Where(r => lookupValueList.Contains(StringExtensions.ToInt(r.Id)))
                                .ToList();
                        }
                    }

                    var items = lookupValues.Select(lookupValue => new LookupValue
                    {
                        Id = lookupValue.Id,
                        DisplayValue = lookupValue.DisplayValue,
                    }).ToList();

                    result.Add(new LookupDto
                    {
                        Items = items,
                        LookupType = new LookupValue
                        {
                            Id = item.ToString(),
                            DisplayValue = LookupTypeFactory.GetInstance(item).Name
                        }
                    });

                }

                return await Task.FromResult(new GetLookupResult
                {
                    Status = result.Any() ? GetLookupStatus.Successful : GetLookupStatus.ResourceNotFound,
                    Message = result.Any() ? ServiceResources.RESOURCE_RETRIEVED : ServiceResources.RESOURCE_NOT_FOUND,
                    Lookups = result
                });
            }
            catch (Exception exc)
            {
                Logger.Error($@"Exception:{exc.Message}:{DateTime.Now}");

                return await Task.FromResult(new GetLookupResult
                {
                    Status = GetLookupStatus.InternalServerError,
                    Message = ServiceResources.INTERNAL_SERVER_ERROR,
                });
            }
        }

		public async Task<GetCountryVisaInformationResult> GetCountryVisaInformation(GetCountryVisaInformationRequest request)
		{
            try
            {
                var visaInformationEntity = await _dbContext.VisaInformation.Where(r => r.CountryId == request.ResourceId).FirstOrDefaultAsync();

                if (visaInformationEntity == null)
                    return new GetCountryVisaInformationResult
                    {
                        Status = GetCountryVisaInformationStatus.ResourceNotFound,
                        Message = ServiceResources.RESOURCE_NOT_FOUND,
                    };

                if (!visaInformationEntity.IsActive)
                    return new GetCountryVisaInformationResult
                    {
                        Status = GetCountryVisaInformationStatus.VısaInformationNotActive,
                        Message = ServiceResources.VISA_INFORMATION_NOT_ACTIVE,
                    };

                var visaInformationMessageEntity = await _dbContext.VisaInformationMessage.Where(r =>
                    r.VisaInformationMessageId == visaInformationEntity.VisaInformationMessageId &&
                    r.LanguageId == request.Context.LanguageId).FirstOrDefaultAsync();

                if (visaInformationMessageEntity == null)
                    return new GetCountryVisaInformationResult
                    {
                        Status = GetCountryVisaInformationStatus.VısaInformationNotActive,
                        Message = ServiceResources.VISA_INFORMATION_NOT_ACTIVE,
                    };

                if (visaInformationMessageEntity.MessageText.Contains("/r"))
                {
                    var messageList = visaInformationMessageEntity.MessageText.Split("/r").ToList();

                    return new GetCountryVisaInformationResult
                    {
                        Message = ServiceResources.SUCCESS,
                        Status = GetCountryVisaInformationStatus.Successful,
                        ResultMessages = messageList.Select(r => new CountryVisaInformationMessage()
                        {
                            IsActive = visaInformationEntity.IsActive,
                            ResultMessage = r.Trim()
                        })
                    };
                }

                return new GetCountryVisaInformationResult
                {
                    Message = ServiceResources.SUCCESS,
                    Status = GetCountryVisaInformationStatus.Successful,
                    ResultMessages = new List<CountryVisaInformationMessage>() {new CountryVisaInformationMessage()
                    {
                        IsActive = visaInformationEntity.IsActive,
                        ResultMessage = visaInformationMessageEntity.MessageText.Trim()
                    }}
                };
            }
            catch (Exception exc)
            {
                Logger.Error($@"Exception:{exc.Message}:{DateTime.Now}");

                return new GetCountryVisaInformationResult
                {
                    Message = ServiceResources.INTERNAL_SERVER_ERROR,
                    Status = GetCountryVisaInformationStatus.InternalServerError,
                };
            }
        }

		public async Task<GetCityByCountryResult> GetCityByCountry(GetCityByCountryRequest request)
		{
			var validationResult = _validationService.Validate(typeof(GetCityByCountryValidator), request);

			if (!validationResult.IsValid)
				return new GetCityByCountryResult
				{
					Status = GetCityByCountryStatus.InvalidInput,
					Message = ServiceResources.INVALID_INPUT_ERROR,
					ValidationMessages = validationResult.ErrorMessages
				};

            try
            {
                var queryCountry = await _dbContext.Country.Where(r => r.Id == request.ResourceId).FirstOrDefaultAsync();

                if (queryCountry == null)
                    return new GetCityByCountryResult
                    {
                        Status = GetCityByCountryStatus.NotFound,
                        Message = ServiceResources.COUNTRY_NOT_FOUND,
                    };

                var queryCities = await _dbContext.CountryCity.Where(r => r.Iso3.Equals(queryCountry.ISO3)).ToListAsync();

                if (!queryCities.Any())
                    return new GetCityByCountryResult
                    {
                        Status = GetCityByCountryStatus.NotFound,
                        Message = ServiceResources.RESOURCE_NOT_FOUND,
                    };

                var citiesDto = queryCities.Select(r => new CityDto()
                {
                    Id = r.Id,
                    Name = r.City,
                });

                return new GetCityByCountryResult
                {
                    Cities = citiesDto,
                    Status = GetCityByCountryStatus.Successful,
                    Message = ServiceResources.RESOURCE_RETRIEVED
                };
            }
            catch (Exception exc)
            {
                Logger.Error($@"Exception:{exc.Message}:{DateTime.Now}");

                return new GetCityByCountryResult
                {
                    Message = ServiceResources.INTERNAL_SERVER_ERROR,
                    Status = GetCityByCountryStatus.InternalServerError,
                };
            }
        }

		public async Task<GetVasTypeMessageByCountryResult> GetVasTypeMessageByCountry(GetVasTypeMessageByCountryRequest request)
		{
			var validationResult = _validationService.Validate(typeof(GetVasTypeMessageByCountryValidator), request);

			if (!validationResult.IsValid)
				return new GetVasTypeMessageByCountryResult
				{
					Status = GetVasTypeMessageByCountryStatus.InvalidInput,
					Message = ServiceResources.INVALID_INPUT_ERROR,
					ValidationMessages = validationResult.ErrorMessages
				};

            try
            {
                var queryCountry = await _dbContext.Country.Where(r => r.Id == request.CountryId).FirstOrDefaultAsync();

                if (queryCountry == null)
                    return new GetVasTypeMessageByCountryResult
                    {
                        Status = GetVasTypeMessageByCountryStatus.NotFound,
                        Message = ServiceResources.COUNTRY_NOT_FOUND,
                    };

                var queryVasTypeMessages = await _dbContext.VasTypeMessage.Where(r => r.IsActive && !r.IsDeleted &&
                    r.CountryId == request.CountryId && r.VasTypeId == request.VasTypeId && r.LanguageId == request.Context.LanguageId).OrderBy(o => o.Id).ToListAsync();

                if (!queryVasTypeMessages.Any())
                    return new GetVasTypeMessageByCountryResult
                    {
                        Status = GetVasTypeMessageByCountryStatus.NotFound,
                        Message = ServiceResources.RESOURCE_NOT_FOUND,
                    };

                var messagesDto = queryVasTypeMessages.Select(r => new VasTypeMessageDto
                {
                    IsActive = r.IsActive,
                    ResultMessage = r.MessageText
                });

                return new GetVasTypeMessageByCountryResult()
                {
                    Status = GetVasTypeMessageByCountryStatus.Successful,
                    Message = ServiceResources.RESOURCE_RETRIEVED,
                    Messages = messagesDto
                };
            }
            catch (Exception exc)
            {
                Logger.Error($@"Exception:{exc.Message}:{DateTime.Now}");

                return new GetVasTypeMessageByCountryResult
                {
                    Message = ServiceResources.INTERNAL_SERVER_ERROR,
                    Status = GetVasTypeMessageByCountryStatus.InternalServerError,
                };
            }
        }

        public async Task<GetChecklistByCountryResult> GetChecklistByCountry(GetChecklistByCountryRequest request)
        {
            var validationResult = _validationService.Validate(typeof(GetChecklistByCountryValidator), request);

            if (!validationResult.IsValid)
                return new GetChecklistByCountryResult
                {
                    Status = GetChecklistByCountryStatus.InvalidInput,
                    Message = ServiceResources.INVALID_INPUT_ERROR,
                    ValidationMessages = validationResult.ErrorMessages
                };

            try
            {
                var isCountryExist = await _dbContext.Country.AnyAsync(r => r.Id == request.ResourceId);

                if (!isCountryExist)
                    return new GetChecklistByCountryResult
                    {
                        Status = GetChecklistByCountryStatus.NotFound,
                        Message = ServiceResources.COUNTRY_NOT_FOUND,
                    };

                var queryCountryChecklists = await _dbContext.CountryChecklist
                    .Include(i => i.CountryChecklistTranslations)
                    .Where(r => r.IsActive && !r.IsDeleted && r.CountryId == request.ResourceId)
                    .OrderBy(o => o.Id)
                    .ToListAsync();

                var checklistDto = queryCountryChecklists.Select(r => new ChecklistDto()
                {
                    Id = r.Id,
                    CountryId = r.CountryId,
                    MustCheck = r.MustCheck,
                    Name = r.CountryChecklistTranslations.FirstOrDefault(s => s.IsActive && !s.IsDeleted && s.LanguageId == request.Context.LanguageId) != null ?
                        r.CountryChecklistTranslations.FirstOrDefault(s => s.IsActive && !s.IsDeleted && s.LanguageId == request.Context.LanguageId)?.Name :
                        r.CountryChecklistTranslations.FirstOrDefault(s => s.IsActive && !s.IsDeleted)?.Name
                });

                return new GetChecklistByCountryResult()
                {
                    Status = GetChecklistByCountryStatus.Successful,
                    Message = ServiceResources.RESOURCE_RETRIEVED,
                    Checklists = checklistDto
                };
            }
            catch (Exception exc)
            {
                Logger.Error($@"Exception:{exc.Message}:{DateTime.Now}");

                return new GetChecklistByCountryResult
                {
                    Message = ServiceResources.INTERNAL_SERVER_ERROR,
                    Status = GetChecklistByCountryStatus.InternalServerError,
                };
            }
        }

        #region Private Methods

		private static string GetCountryName(Entity.Entities.Country.Country country, int languageId)
		{
			return languageId switch
			{
				1 => country.NameTr,
				3 => country.NameAr,
                5 => country.NameRu,
                _ => country.Name
			};
		}

		#endregion
	}
}
