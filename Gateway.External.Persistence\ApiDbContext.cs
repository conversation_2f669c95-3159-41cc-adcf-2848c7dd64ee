﻿using Gateway.External.Entity.Entities.Application;
using Gateway.External.Entity.Entities.Branch;
using Gateway.External.Entity.Entities.Country;
using Gateway.External.Entity.Entities.Customer;
using Gateway.External.Entity.Entities.PreApplications;
using Gateway.External.Entity.Entities.Slot;
using Microsoft.EntityFrameworkCore;
using System.Threading.Tasks;
using System.Threading;
using System;
using System.Collections.Generic;
using System.Linq;
using Gateway.External.Entity.Entities.B2B;
using Gateway.External.Entity.Entities.B2B.Company;
using Gateway.External.Entity.Entities.B2B.Company.CompanyFile;
using Gateway.External.Entity.Entities.Rules;
using Gateway.External.Entity.Entities.VisaInformation;
using Gateway.External.Entity.Entities.B2B.CompanyUser;
using Gateway.External.Entity.Entities.B2B.CompanyUser.CompanyUserFile;
using Gateway.External.Entity.Entities.B2B.City;
using Gateway.External.Entity.Entities.B2B.AppointmentDemand;
using Gateway.External.Entity.Entities.B2B.AppointmentDemand.AppointmentDemandFile;
using Gateway.External.Entity.Entities.B2B.AppointmentDemand.AppointmentDemandRejectionReason;
using Gateway.External.Entity.Entities.B2B.Application;
using Gateway.External.Entity.Entities.B2B.SlotDemand;
using Gateway.External.Entity.Entities.PhotoBooth;

namespace Gateway.External.Persistence
{
    public class ApiDbContext : DbContext
    {
        public ApiDbContext(DbContextOptions<ApiDbContext> options) : base(options)
        {
            AppContext.SetSwitch("Npgsql.EnableLegacyTimestampBehavior", true);
            // AppContext.SetSwitch("Npgsql.DisableDateTimeInfinityConversions", true);
        }
        
        public DbSet<PreApplication> PreApplication { get; set; }
        
        public DbSet<PreApplicationApplicant> PreApplicationApplicant { get; set; }

        public DbSet<Slot> Slot { get; set; }
        
        public DbSet<BranchApplicationCountry> BranchApplicationCountry { get; set; }
        
        public DbSet<Application> Application { get; set; }
        public DbSet<ForeignHealthInsurance> ForeignHealthInsurance { get; set; }
        public DbSet<ApplicationReferenceNumber> ApplicationReferenceNumber { get; set; }

        public DbSet<ApplicationPnlFile> ApplicationPnlFile { get; set; }

        public DbSet<ApplicationDocument> ApplicationDocument { get; set; }

        public DbSet<Branch> Branch { get; set; }
        public DbSet<BranchApplicationStatus> BranchApplicationStatus { get; set; }
        public DbSet<BranchHolidaysBranchShiftHoliday> BranchHolidaysBranchShiftHoliday { get; set; }
        public DbSet<DayOfWeekBranchShiftHoliday> DayOfWeekBranchShiftHoliday { get; set; }

        public DbSet<Country> Country { get; set; }
        public DbSet<ForeignCity> ForeignCity { get; set; }
        public DbSet<CountryCity> CountryCity { get; set; }
        public DbSet<CountryChecklist> CountryChecklist { get; set; }
        public DbSet<CountryChecklistTranslation> CountryChecklistTranslation { get; set; }
        public DbSet<CustomerCard> CustomerCard { get; set; }

        public DbSet<ApplicationStatus> ApplicationStatus { get; set; }
        public DbSet<ApplicationStatusHistory> ApplicationStatusHistory { get; set; }

        public DbSet<ApplicationStatusTranslation> ApplicationStatusTranslation { get; set; }
        public DbSet<LookupRule> LookupRule { get; set; }
        public DbSet<VasTypeCost> VasTypeCost { get; set; }
        public DbSet<VisaInformation> VisaInformation { get; set; }
        public DbSet<VisaInformationMessage> VisaInformationMessage { get; set; }
		public DbSet<PhotoBooth> PhotoBooth { get; set; }

		#region B2B

		#region Company

		public DbSet<Company> Company { get; set; }
        public DbSet<CompanyPayment> CompanyPayment { get; set; }
        public DbSet<CompanyTermsOfServices> CompanyTermsOfServices { get; set; }
        public DbSet<CompanyRoles> CompanyRoles { get; set; }
        public DbSet<CompanyProfileApproval> CompanyProfileApproval { get; set; }
        public DbSet<CompanyProfileCompletion> CompanyProfileCompletion { get; set; }
        public DbSet<CompanyType> CompanyType { get; set; }
        public DbSet<CompanyTypeTranslation> CompanyTypeTranslation { get; set; }
        public DbSet<CompanyTypeFile> CompanyTypeFile { get; set; }
        public DbSet<CompanyFile> CompanyFile { get; set; }
        public DbSet<CompanyFileType> CompanyFileType { get; set; }
        public DbSet<CompanyFileTypeTranslation> CompanyFileTypeTranslation { get; set; }
        public DbSet<CompanyAlert> CompanyAlert { get; set; }
        public DbSet<CompanyAlertMessage> CompanyAlertMessage { get; set; }

        #endregion

		#region CompanyUser

		public DbSet<CompanyUser> CompanyUser { get; set; }
        public DbSet<CompanyUserFile> CompanyUserFile { get; set; }
        public DbSet<CompanyUserBranch> CompanyUserBranch { get; set; }
        public DbSet<CompanyUserChat> CompanyUserChat { get; set; }
        public DbSet<CompanyUserPosition> CompanyUserPosition { get; set; }
        public DbSet<CompanyUserPositionTranslation> CompanyUserPositionTranslation { get; set; }
        public DbSet<CompanyUserLogin> CompanyUserLogin { get; set; }

		#endregion

		#region SlotDemand

		public DbSet<SlotDemand> SlotDemand { get; set; }
		public DbSet<SlotDemandReason> SlotDemandReason { get; set; }
		public DbSet<SlotDemandReasonTranslation> SlotDemandReasonTranslation { get; set; }

		#endregion

		#region AppointmentDemand

		public DbSet<AppointmentDemand> AppointmentDemand { get; set; }
		public DbSet<AppointmentApplicantDemand> AppointmentApplicantDemand { get; set; }
		public DbSet<AppointmentDemandFile> AppointmentDemandFile { get; set; }
		public DbSet<AppointmentDemandFileType> AppointmentDemandFileType { get; set; }
		public DbSet<AppointmentDemandFileTypeTranslation> AppointmentDemandFileTypeTranslation { get; set; }
		public DbSet<AppointmentDemandRejectionReason> AppointmentDemandRejectionReason { get; set; }
		public DbSet<AppointmentDemandRejectionReasonTranslation> AppointmentDemandRejectionReasonTranslation { get; set; }
		public DbSet<VisaCategoryFile> VisaCategoryFile { get; set; }
		public DbSet<VasTypeMessage> VasTypeMessage { get; set; }

		#endregion

		#endregion

		protected override void OnModelCreating(ModelBuilder modelBuilder)
		{
			#region B2B

			modelBuilder.ApplyConfiguration(new CompanyEntityConfiguration());
			modelBuilder.ApplyConfiguration(new CompanyUserEntityConfiguration());
			modelBuilder.ApplyConfiguration(new CompanyFileEntityConfiguration());
			modelBuilder.ApplyConfiguration(new CompanyUserFileEntityConfiguration());
			modelBuilder.ApplyConfiguration(new CompanyUserBranchEntityConfiguration());
			modelBuilder.ApplyConfiguration(new SlotDemandEntityConfiguration());
			modelBuilder.ApplyConfiguration(new AppointmentDemandEntityConfiguration());
			modelBuilder.ApplyConfiguration(new AppointmentApplicantDemandEntityConfiguration());
			modelBuilder.ApplyConfiguration(new AppointmentDemandFileEntityConfiguration());
			modelBuilder.ApplyConfiguration(new CompanyAlertEntityConfiguration());
			modelBuilder.ApplyConfiguration(new CompanyAlertMessageEntityConfiguration());

			modelBuilder.Entity<Company>().HasQueryFilter(e => e.IsActive && !e.IsDeleted);
			modelBuilder.Entity<CompanyFile>().HasQueryFilter(e => e.IsActive && !e.IsDeleted);
			modelBuilder.Entity<CompanyFileType>().HasQueryFilter(e => e.IsActive && !e.IsDeleted);
			modelBuilder.Entity<CompanyFileTypeTranslation>().HasQueryFilter(e => e.IsActive && !e.IsDeleted);
			modelBuilder.Entity<CompanyTypeFile>().HasQueryFilter(e => e.IsActive && !e.IsDeleted);
			modelBuilder.Entity<CompanyProfileCompletion>().HasQueryFilter(e => e.IsActive && !e.IsDeleted);
			modelBuilder.Entity<CompanyProfileApproval>().HasQueryFilter(e => e.IsActive && !e.IsDeleted);
			modelBuilder.Entity<CompanyRoles>().HasQueryFilter(e => e.IsActive && !e.IsDeleted);
			modelBuilder.Entity<CompanyTermsOfServices>().HasQueryFilter(e => e.IsActive && !e.IsDeleted);
			modelBuilder.Entity<CompanyType>().HasQueryFilter(e => e.IsActive && !e.IsDeleted);
			modelBuilder.Entity<CompanyTypeTranslation>().HasQueryFilter(e => e.IsActive && !e.IsDeleted);
			modelBuilder.Entity<CompanyUserPosition>().HasQueryFilter(e => e.IsActive && !e.IsDeleted);
			modelBuilder.Entity<CompanyUserPositionTranslation>().HasQueryFilter(e => e.IsActive && !e.IsDeleted);
			modelBuilder.Entity<CompanyUser>().HasQueryFilter(e => e.IsActive && !e.IsDeleted);
			modelBuilder.Entity<CompanyUserFile>().HasQueryFilter(e => e.IsActive && !e.IsDeleted);
			modelBuilder.Entity<CompanyUserBranch>().HasQueryFilter(e => e.IsActive && !e.IsDeleted);
			modelBuilder.Entity<CompanyPayment>().HasQueryFilter(e => e.IsActive && !e.IsDeleted);
			modelBuilder.Entity<SlotDemand>().HasQueryFilter(e => e.IsActive && !e.IsDeleted);
			modelBuilder.Entity<AppointmentDemand>().HasQueryFilter(e => e.IsActive && !e.IsDeleted);
			modelBuilder.Entity<AppointmentApplicantDemand>().HasQueryFilter(e => e.IsActive && !e.IsDeleted);
			modelBuilder.Entity<AppointmentDemandFile>().HasQueryFilter(e => e.IsActive && !e.IsDeleted);
			modelBuilder.Entity<AppointmentDemandFileType>().HasQueryFilter(e => e.IsActive && !e.IsDeleted);
			modelBuilder.Entity<AppointmentDemandFileTypeTranslation>().HasQueryFilter(e => e.IsActive && !e.IsDeleted);
			modelBuilder.Entity<AppointmentDemandRejectionReason>().HasQueryFilter(e => e.IsActive && !e.IsDeleted);
			modelBuilder.Entity<AppointmentDemandRejectionReasonTranslation>().HasQueryFilter(e => e.IsActive && !e.IsDeleted);
			modelBuilder.Entity<ApplicationPnlFile>().HasQueryFilter(e => e.IsActive && !e.IsDeleted);
			modelBuilder.Entity<CompanyAlert>().HasQueryFilter(e => e.IsActive && !e.IsDeleted);
			modelBuilder.Entity<CompanyAlertMessage>().HasQueryFilter(e => e.IsActive && !e.IsDeleted);

            #endregion

			base.OnModelCreating(modelBuilder);
		}

		public override Task<int> SaveChangesAsync(bool acceptAllChangesOnSuccess,
            CancellationToken cancellationToken = new())
        {
            OnBeforeSaving();
            return base.SaveChangesAsync(acceptAllChangesOnSuccess, cancellationToken);
        }

        protected virtual void OnBeforeSaving()
        {
            #region PreApplicationHistory

            var newPreApplicationHistories = GetPreApplicationHistories();
            if (newPreApplicationHistories != null && newPreApplicationHistories.Any())
                AddRange(newPreApplicationHistories);

            #endregion

            #region PreApplicationApplicantHistory

            var newPreApplicationApplicantHistories = GetPreApplicationApplicantHistories();
            if (newPreApplicationApplicantHistories != null && newPreApplicationApplicantHistories.Any())
                AddRange(newPreApplicationApplicantHistories);

            #endregion
        }

        private List<PreApplicationHistory> GetPreApplicationHistories()
        {
            var newPreApplicationHistories = new List<PreApplicationHistory>();

            try
            {
                var entitiesToCheck = new List<string>
                {
                    nameof(PreApplication),
                };

                var modifiedEntities = ChangeTracker.Entries().Where(p => p.State == EntityState.Modified && entitiesToCheck.Contains(p.Entity.GetType().Name)).ToList();

                foreach (var modifiedEntity in modifiedEntities)
                {
                    var preApplicationId = 0;
                    var createdBy = 0;
                    string externalUserId = null;
                    byte? channelType = null;

                    if (modifiedEntity.Entity.GetType().Name == nameof(PreApplication))
                    {
                        var preApplication = modifiedEntity.Entity as PreApplication;
                        preApplicationId = preApplication.Id;
                        createdBy = preApplication.UpdatedBy.GetValueOrDefault();
                        externalUserId = preApplication.ExternalUserId;
                        channelType = preApplication.ChannelType;
                    }

                    if (preApplicationId != 0 && !string.IsNullOrEmpty(externalUserId))
                    {
                        foreach (var prop in modifiedEntity.Properties)
                        {
                            var propertyName = prop.Metadata.Name;
                            var originalValue = prop.OriginalValue;
                            var currentValue = prop.CurrentValue;

                            if (propertyName != "UpdatedAt" && propertyName != "UpdatedBy" && propertyName != "DeletedAt" && propertyName != "DeletedBy" && $"{originalValue}" != $"{currentValue}")
                            {
                                if (ShouldSkipVasTypeIdChange(propertyName, originalValue, currentValue))
                                    continue;

                                var newPreApplicationHistory = new PreApplicationHistory
                                {
                                    PreApplicationId = preApplicationId,
                                    PropertyName = propertyName,
                                    PreviousValue = $"{originalValue}",
                                    CurrentValue = $"{currentValue}",
                                    CreatedBy = createdBy,
                                    CreatedAt = DateTimeOffset.UtcNow,
                                    ExternalUserId = externalUserId,
                                    ChannelType = channelType
                                };

                                newPreApplicationHistories.Add(newPreApplicationHistory);
                            }
                        }
                    }
                }
            }
            catch
            {
                // ignored
            }

            return newPreApplicationHistories;
        }

        private List<PreApplicationApplicantHistory> GetPreApplicationApplicantHistories()
        {
            var newPreApplicationApplicantHistories = new List<PreApplicationApplicantHistory>();

            try
            {
                var entitiesToCheck = new List<string>
                {
                    nameof(PreApplicationApplicant),
                };

                var modifiedEntities = ChangeTracker.Entries().Where(p =>
                    p.State == EntityState.Modified && entitiesToCheck.Contains(p.Entity.GetType().Name)).ToList();

                foreach (var modifiedEntity in modifiedEntities)
                {
                    var preApplicationApplicantId = 0;
                    var createdBy = 0;
                    string externalUserId = null;
                    byte? channelType = null;

                    if (modifiedEntity.Entity.GetType().Name == nameof(PreApplicationApplicant))
                    {
                        var preApplicationApplicant =
                            modifiedEntity.Entity as PreApplicationApplicant;
                        preApplicationApplicantId = preApplicationApplicant.Id;
                        createdBy = preApplicationApplicant.PreApplication.UpdatedBy.GetValueOrDefault();
                        externalUserId = preApplicationApplicant.PreApplication.ExternalUserId;
                        channelType = preApplicationApplicant.PreApplication.ChannelType;
                    }

                    if (preApplicationApplicantId != 0 && !string.IsNullOrEmpty(externalUserId))
                    {
                        foreach (var prop in modifiedEntity.Properties)
                        {
                            var propertyName = prop.Metadata.Name;
                            var originalValue = prop.OriginalValue;
                            var currentValue = prop.CurrentValue;

                            if (propertyName != "UpdatedAt" && propertyName != "UpdatedBy" && propertyName != "DeletedAt" && propertyName != "DeletedBy" && originalValue != null && currentValue != null &&
                                $"{originalValue}" != $"{currentValue}")
                            {

                                var newPreApplicationApplicantHistory =
                                    new PreApplicationApplicantHistory
                                    {
                                        PreApplicationApplicantId = preApplicationApplicantId,
                                        PropertyName = propertyName,
                                        PreviousValue = $"{originalValue}",
                                        CurrentValue = $"{currentValue}",
                                        CreatedBy = createdBy,
                                        CreatedAt = DateTimeOffset.UtcNow,
                                        ExternalUserId = externalUserId,
                                        ChannelType = channelType
                                    };

                                newPreApplicationApplicantHistories.Add(newPreApplicationApplicantHistory);
                            }
                        }
                    }
                }
            }
            catch
            {
                // ignored
            }

            return newPreApplicationApplicantHistories;
        }

        private bool ShouldSkipVasTypeIdChange(string propertyName, object originalValue, object currentValue)
            => propertyName == "VasTypeId" && $"{originalValue}" == "0" && string.IsNullOrEmpty($"{currentValue}");
    }
}