﻿using System.Collections.Generic;

namespace Portal.Gateway.Contracts.Entities.Dto.Appointment.Application
{
    public class CancelInsuranceOrderListDto
    {
        public ICollection<CancelInsuranceOrderDto> CancelOrders { get; set; }
    }

    public class CancelInsuranceOrderDto
    {
        public int Id { get; set; }
        public int ApplicationId { get; set; }
        public int ApplicationInsuranceId { get; set; }
        public int ProviderId { get; set; }
        public string PolicyNumber { get; set; }
        public int CreatedBy { get; set; }
    }
}
