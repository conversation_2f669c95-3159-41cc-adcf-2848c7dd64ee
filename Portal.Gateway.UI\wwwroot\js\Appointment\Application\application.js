﻿$(function () {

    if ($('#gridApplicationCancellation').val() !== "") {
        $('#gridApplication').data('kendoGrid').dataSource.bind("change", FamilyApplicantDataSourceChange);
    }

    $("#filterGridApplication").click(function (e) {
        refreshGridApplication();
    });

    $("#clearGridApplicationFilter").click(function (e) {
        $("#FilterApplicationNumber").val('');
        $("#FilterCountryId").data("kendoDropDownList").select(null);
        $("#FilterAgencyId").data("kendoDropDownList").select(null);
        $("#FilterApplicantTypeId").data("kendoDropDownList").select(null);
        $("#FilterApplicationTypeId").data("kendoDropDownList").select(null);
        $("#FilterName").val('');
        $("#FilterSurname").val('');
        $("#FilterPassportNumber").val('');
        $("#FilterNationalityId").data("kendoDropDownList").select(null);
        $("#FilterEmail").val('');
        $("#FilterPhoneNumber").val('');
        $("#FilterVisaCategoryId").data("kendoDropDownList").select(null);
        $("#FilterAllowPassiveDeletedApplications").prop('checked', false);
        $("#FilterStartDate").val('');
        $("#FilterEndDate").val('');
        $("#FilterResidenceApplication").data("kendoDropDownList").select(null);
        $("#FilterBirthDate").val('');
        refreshGridApplication();
    });

    $("#filterGridApplicationCancellation").click(function (e) {
        refreshGridApplicationCancellation();
    });

    $("#clearGridApplicationCancellationFilter").click(function (e) {
        $("#FilterCancellationTypeId").data("kendoDropDownList").select(null);
        $("#FilterCancellationReasonId").data("kendoDropDownList").select(null);
        $("#FilterCancellationStatusId").data("kendoDropDownList").select(null);
        refreshGridApplicationCancellation();
    });

    $("#gridApplication").kendoTooltip({
        filter: "td:nth-child(12)",
        position: "right",
        width: 250,
        content: function (e) {
            if (e.target.is("th")) {
                return e.target.text();
            }
            var dataItem = $("#gridApplication").data("kendoGrid").dataItem(e.target.closest("tr")).ApplicationNote;
            if (dataItem == null) {
                return "";
            }
            return dataItem;
        },
        show: function (e) {
            if (this.content.text() != "") {
                $('[role="tooltip"]').css("visibility", "visible");
            } else {
                $('[role="tooltip"]').css("visibility", "hidden");
            }
        },
        hide: function () {
            $('[role="tooltip"]').css("visibility", "hidden");
        }
    }).data("kendoTooltip");

    $("#gridApplication").kendoTooltip({
        filter: "td:nth-child(13)",
        position: "right",
        width: 250,
        content: function (e) {
            if (e.target.is("th")) {
                return e.target.text();
            }
            var dataItem = $("#gridApplication").data("kendoGrid").dataItem(e.target.closest("tr")).Note;
            if (dataItem == null) {
                return "";
            }
            return dataItem;
        },
        show: function (e) {
            if (this.content.text() != "") {
                $('[role="tooltip"]').css("visibility", "visible");
            } else {
                $('[role="tooltip"]').css("visibility", "hidden");
            }
        },
        hide: function () {
            $('[role="tooltip"]').css("visibility", "hidden");
        }
    }).data("kendoTooltip");
});

function refreshGridApplication() {
    $('#gridApplication').data('kendoGrid').dataSource.read();
    $('#gridApplication').data('kendoGrid').refresh();
}

function gridApplicationFilterData() {
    return {
        FilterApplicationNumber: $("#FilterApplicationNumber").val(),
        FilterCountryId: $("#FilterCountryId").val(),
        FilterAgencyId: $("#FilterAgencyId").val(),
        FilterApplicantTypeId: $("#FilterApplicantTypeId").val(),
        FilterApplicationTypeId: $("#FilterApplicationTypeId").val(),
        FilterName: $("#FilterName").val(),
        FilterSurname: $("#FilterSurname").val(),
        FilterPassportNumber: $("#FilterPassportNumber").val(),
        FilterNationalityId: $("#FilterNationalityId").val(),
        FilterEmail: $("#FilterEmail").val(),
        FilterPhoneNumber: $("#FilterPhoneNumber").val(),
        FilterVisaCategoryId: $("#FilterVisaCategoryId").val(),
        FilterAllBranchs: $("#FilterAllBranchs").val(),
        FilterAllowPassiveDeletedApplications: $('#FilterAllowPassiveDeletedApplications').is(":checked"),
        FilterStartDate: $("#FilterStartDate").val(),
        FilterEndDate: $("#FilterEndDate").val(),
        FilterResidenceApplication: $('#FilterResidenceApplication').val(),
        FilterBirthDate: $('#FilterBirthDate').val()
    };
}

function partialAddApplicationCancellation(EncryptedApplicationId, ApplicationCancellationTypeId) {
    $.post('/Appointment/ApplicationCancellation/PartialAddApplicationCancellation', { encryptedApplicationId: EncryptedApplicationId, applicationCancellationTypeId: ApplicationCancellationTypeId },
        function (data) {
            $('#divPartialAddApplicationCancellation').html(data);
        }, 'html');
}

function partialApplicationFiles(encryptedApplicationId) {
    $.ajax({
        type: "GET",
        url: "/Appointment/Application/PartialApplicationFiles?encryptedApplicationId=" + encryptedApplicationId,
        success: function (result) {
            if (result.ResultType !== undefined && (result.ResultType === 2 || result.ResultType === 3)) {
                bootbox.alert({
                    title: result.Type,
                    message: result.Message,
                    callback: function (result) { }
                });
                return;
            }
            else {
                $('#divPartialApplicationFiles').html(result);
                $("#modalPartialApplicationFiles").modal("show");
            }
        },
        error: function (data) {
            showNotification('danger', jsResources.ErrorOccurred);
        }
    });
}

$("#formDownloadApplicationFiles").submit(function (e) {
    e.preventDefault();

    var list = [];

    $("#formDownloadApplicationFiles").find(".checkbox-square").each((key, value) => {
        var checkBox = $(value);
        if (checkBox.prop("checked")) {
            var tr = checkBox.parents("tr").first();
            list.push({
                EncryptedDocumentId: tr.attr("encrypted-document-id")
            });
        }
    });

    if (list.length === 0) {
        bootbox.alert({
            title: jsResources.Warning,
            message: jsResources.PleaseSelectAtLeastOne,
            callback: function (result) { }
        });
        e.preventDefault();
        return false;
    }

    var sendData = {
        EncryptedApplicationId: $("#EncryptedApplicationId").val(),
        DigitalSignatureDocuments: list
    };

    $.ajax({
        type: "POST",
        url: "/Appointment/Application/DownloadApplicationFiles",
        data: sendData,
        success: function (result) {
            if (result.ResultType !== undefined && (result.ResultType === 2 || result.ResultType === 3)) {
                bootbox.alert({
                    title: jsResources.Warning,
                    message: result.Message,
                    callback: function (result) { }
                });
                e.preventDefault();
                return;
            }
            else {
                postForm({ attrs: { action: "/Appointment/Application/DownloadAllFiles", method: "post", target: "_blank" }, data: { model: result } });

                $('#modalPartialApplicationFiles').modal('hide');
            }
        },
        error: function (result) {
            showNotification('danger', jsResources.ErrorOccurred);
        }
    });
});

$("#formAddApplicationCancellation").submit(function (e) {

    e.preventDefault();
    var form = $(this);
    var validator = form.kendoValidator().data("kendoValidator");

    if (!validator.validate()) return;

    $.ajax({
        type: "POST",
        url: "/Appointment/ApplicationCancellation/AddApplicationCancellation",
        data: form.serialize(),
        success: function (data) {
            showNotification(data.Type, data.Message);
            if (data.Type === "success") {
                $('#modalAddApplicationCancellation').modal('hide');
                showNotification("success", jsResources.ApplicationCancellationRequestSaved);
            }
        },
        error: function (data) {
            showNotification('danger', jsResources.ErrorOccurred);
        }
    });
});

$("#formYssCheck").submit(function (e) {
    e.preventDefault();
    var form = $(this);
    var validator = form.kendoValidator().data("kendoValidator");

    if (!validator.validate())
        return;

    bootbox.confirm(jsResources.AreYouSureYouWantToUpdateThisInsurance, function (result) {
        if (result) {
            $.ajax({
                type: "POST",
                url: "/Appointment/Application/AddNonApplicationYss",
                data: form.serialize(),
                success: function (data) {
                    showNotification(data.Type, data.Message);
                    if (data.Type === "success") {
                        $('#modalYssCheck').modal('hide');
                    }
                },
                error: function (data) {
                    showNotification('danger', jsResources.ErrorOccurred);
                }
            });
        }
    });
});

function refreshGridApplicationCancellation() {
    $('#gridApplicationCancellation').data('kendoGrid').dataSource.read();
    $('#gridApplicationCancellation').data('kendoGrid').refresh();
}

function gridApplicationCancellationFilterData() {
    return {
        FilterCancellationTypeId: $("#FilterCancellationTypeId").val(),
        FilterCancellationReasonId: $("#FilterCancellationReasonId").val(),
        FilterCancellationStatusId: $("#FilterCancellationStatusId").val()
    };
}

function updateApplicationCancellationStatus(EncryptedApplicationCancellationId, EncryptedApplicationId, ApplicationCancellationStatusId) {
    bootbox.confirm(jsResources.AreYouSureToDoThisAction, function (result) {
        if (result) {

            $.ajax({
                type: "GET",
                url: "/Appointment/ApplicationCancellation/GetExistingPhotoboothApplicationUsedCheck?encryptedApplicationId=" + EncryptedApplicationId,
                async: false,
                success: function (data) {
                    if (data.Data.Result) {
                        bootbox.confirm(jsResources.CheckUsedPhotoBoothDeletedApplicationSure, function (result) {
                            if (result) {
                                $.ajax({
                                    type: "PUT",
                                    url: "/Appointment/ApplicationCancellation/UpdateApplicationCancellationStatus",
                                    data: { encryptedApplicationCancellationId: EncryptedApplicationCancellationId, applicationCancellationStatusId: ApplicationCancellationStatusId },
                                    success: function (data) {
                                        if (data.Type === jsResources.Info) {
                                            showNotification("success", jsResources.OperationIsSuccessful);
                                            bootbox.dialog({
                                                title: jsResources.RelatedProcessVisaId,
                                                message: '<p><b>' + data.Message + '</b></p>',
                                                size: 'extra-large',
                                                onEscape: true,
                                                backdrop: true
                                            });
                                            $('.modal-content').css("background-color", "#8950FC"); //info
                                        }
                                        else
                                            showNotification(data.Type, data.Message);
                                        refreshGridApplicationCancellation();
                                    },
                                    error: function (data) {
                                        showNotification('danger', jsResources.ErrorOccurred);
                                    }
                                });
                            }
                        });
                    } else {
                        $.ajax({
                            type: "PUT",
                            url: "/Appointment/ApplicationCancellation/UpdateApplicationCancellationStatus",
                            data: { encryptedApplicationCancellationId: EncryptedApplicationCancellationId, applicationCancellationStatusId: ApplicationCancellationStatusId },
                            success: function (data) {
                                if (data.Type === jsResources.Info) {
                                    showNotification("success", jsResources.OperationIsSuccessful);
                                    bootbox.dialog({
                                        title: jsResources.RelatedProcessVisaId,
                                        message: '<p><b>' + data.Message + '</b></p>',
                                        size: 'extra-large',
                                        onEscape: true,
                                        backdrop: true
                                    });
                                    $('.modal-content').css("background-color", "#8950FC"); //info
                                }
                                else
                                    showNotification(data.Type, data.Message);
                                refreshGridApplicationCancellation();
                            },
                            error: function (data) {
                                showNotification('danger', jsResources.ErrorOccurred);
                            }
                        });
                    }
                },
                error: function () {
                    showNotification('danger', jsResources.ErrorOccurred);
                }
            });

        }
    });
}

function partialApplicationEasyUse(EncryptedApplicationId) {
    $.post('/Appointment/Application/EasyUse', { encryptedApplicationId: EncryptedApplicationId },
        function (data) {
            $('#divPartialApplicationEasyUse').html(data);
        }, 'html');
}

function partialYssCheck(encryptedApplicationId) {
    $.ajax({
        type: "Get",
        url: "/Appointment/Application/ForeignHealthInsuranceCheck",
        data: { EncryptedApplicationId: encryptedApplicationId },
        success: function (result) {
            if (typeof (result) == 'object') {
                bootbox.alert({
                    title: jsResources.Warning,
                    message: result.Message,
                    callback: function (result) { }
                });
            }
            else
                $('#modalYssCheck').modalShow({ body: result });
        },
        error: function (result) {
            bootbox.alert({
                title: jsResources.Error,
                message: jsResources.ErrorOccurred,
                callback: function (result) { }
            });
        }
    });
}

function FamilyApplicantDataSourceChange(e) {
    if ($("#FilterStartDate").val() !== '' && $("#FilterEndDate").val() !== '' && $("#FilterApplicantTypeId").val() === '2') {
        $("#report-button-").show();
    }
    else {
        $("#report-button-").hide();
    }
}

$("#formApplicationEasyUse").submit(function (e) {

    e.preventDefault();
    var form = $(this);
    var validator = form.kendoValidator().data("kendoValidator");

    if (!validator.validate()) return;

    $.ajax({
        type: "PUT",
        url: "/Appointment/Application/UpdateApplicationEasyUse",
        data: form.serialize(),
        success: function (data) {
            showNotification(data.Type, data.Message);
            if (data.Type === "success") {
                $('#modalApplicationEasyUse').modal('hide');
                refreshGridApplication();
            }
        },
        error: function (data) {
            showNotification('danger', jsResources.ErrorOccurred);
        }
    });

});

function UpdateInterviewInformation(EncryptedId, IsInterviewRequired, IsInterviewDone) {
    $.ajax({
        type: "PUT",
        url: "/Appointment/Application/UpdateInterviewInformation",
        data: {
            encryptedApplicationId: EncryptedId,
            isInterviewRequired: IsInterviewRequired.checked,
            isInterviewDone: IsInterviewDone.checked
        },
        success: function (data) {
            showNotification(data.Type, data.Message);
        },
        error: function (data) {
            showNotification('danger', jsResources.ErrorOccurred);
        }
    });
}