﻿
using Portal.Gateway.ApiModel;

namespace Portal.Gateway.Contracts.Entities.Dto.Management.BranchApplicationCountryExtraFee.Requests
{
    public class UpdateBranchApplicationCountryExtraFeeRequestDto : BaseRequestDto
    {
        public int Id { get; set; }

        public int BranchApplicationCountryId { get; set; }

        public int ExtraFeeId { get; set; }

        public bool IsAutoChecked { get; set; }

        public decimal Price { get; set; }

        public decimal Tax { get; set; }

        public decimal TaxRatio { get; set; }

        public decimal ServiceTax { get; set; }

        public decimal BasePrice { get; set; }

        public int CurrencyId { get; set; }

        public string SapExtraFeeId { get; set; }

        public bool IsActive { get; set; }

        public bool ShowInICR { get; set; }

        public bool ShowInSummary { get; set; }
        public bool IsShowInReport { get; set; }
        public bool IsGroupInIcr { get; set; }
        public bool IsShowInRejectionList { get; set; }
        public bool IsShowInAllApplicationsReport { get; set; }
        public bool IsShowInApplicationAfterRejection { get; set; }
    }
}
