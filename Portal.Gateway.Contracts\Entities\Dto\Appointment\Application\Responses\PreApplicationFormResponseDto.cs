﻿using System;
using System.Collections.Generic;

namespace Portal.Gateway.Contracts.Entities.Dto.Appointment.Application.Responses
{
    public class PreApplicationFormResponseDto
    {
        public int BranchApplicationCountryId { get; set; }

        public string BranchName { get; set; }

        public int CountryId { get; set; }
        public int BranchCountryId { get; set; }
        public int? CargoProviderId { get; set; }
        public bool IsCargoIntegrationActive { get; set; }
        public bool ShowCityDropdown { get; set; }
        public bool IsPrinterIntegrationActive { get; set; }
        public bool IsPreApplicationConnectionActive { get; set; }
        public bool IsPhotoBoothIntegrationActive { get; set; }
        public bool IsApplicationUpdateStatusCheckActive { get; set; }
        public string CountryName { get; set; }

        public string InformationNotes { get; set; }
        public bool IraqOpsiyonelSmsCheck { get; set; }

        public bool DisableContactInformationVerify { get; set; }
        public bool IsRelatedInsuranceForExemptActive { get; set; }
        public bool ShowPaymentMethods { get; set; }

        //public int? ShowAllowDeniedPassportControl { get; set; }

        public BeforeRelationalApplication BeforeRelationalApplicationDetail { get; set; }
        public class BeforeRelationalApplication
        {
            public int? FamilyIraqSmsSelected { get; set; }
            public int? FamilyIraqOpsiyonelSmsSelected { get; set; }
        }
        public RelationalApplication RelationalApplicationDetail { get; set; }

        public class RelationalApplication
        {
            public int RelationalApplicationId { get; set; }

            public int ApplicantTypeId { get; set; }

            public int? AgencyId { get; set; }

            public string Email { get; set; }

            public string PhoneNumber1 { get; set; }

            public string PhoneNumber2 { get; set; }

            public string Address { get; set; }
            public int? ForeignCityId { get; set; }
            
            //public string City { get; set; }
            public string PostalCode { get; set; }
            public string AreaId { get; set; }
            public string AreaName { get; set; }
            public string GovernorateId { get; set; }
            public string GovernorateName { get; set; }
            public bool? IsCargoIntegrationSelected { get; set; }
			public RelationalApplicationDocument RelationalApplicationDocumentDetail { get; set; }

            public class RelationalApplicationDocument
            {
                public bool HasBankAccount { get; set; }

                public int? BankBalance { get; set; }

                public int? BankBalanceCurrencyId { get; set; }

                public bool HasDeed { get; set; }

                public int VisaCategoryId { get; set; }
                public int? AdditionalServiceTypeId { get; set; }

                public DateTime EntryDate { get; set; }

                public DateTime ExitDate { get; set; }

                public int? MonthlySalary { get; set; }
                public int? MonthlySalaryCurrencyId { get; set; }
                public string Job { get; set; }
                public int? OccupationId { get; set; }
                public string CompanyName { get; set; }
                public int? TotalYearInCompany { get; set; }
                public string ReimbursementSponsorDetail { get; set; }


            }
        }

        public IList<ApplicationFormElement> ApplicationFormElements { get; set; }

        public IList<ExtraFee> ExtraFees { get; set; }

        public class ApplicationFormElement
        {
            public int Id { get; set; }

            public int FormElementId { get; set; }

            public bool IsRequired { get; set; }
        }

        public class ExtraFee
        {
            public int? ApplicationExtraFeeId { get; set; }

            public int ExtraFeeId { get; set; }

            public int TypeId { get; set; }

            public int? MinimumItem { get; set; }

            public int CategoryTypeId { get; set; }

            public string ExtraFeeName { get; set; }

            public bool IsAutoChecked { get; set; }

            public decimal Price { get; set; }

            public decimal Tax { get; set; }

            public decimal TaxRatio { get; set; }

            public decimal ServiceTax { get; set; }

            public decimal BasePrice { get; set; }

            public int CurrencyId { get; set; }
            
            public int Quantity { get; set; }
            public bool IsForRejectedApplications { get; set; }
            public int? PolicyPeriod { get; set; }


            public byte? AgeRange { get; set; }
            public bool IsMainFee { get; set; }
            public bool IsSubFee { get; set; }
            public int? MainFeeId { get; set; }
            public int? RelatedMainFeeId { get; set; }

        }
    }
}
