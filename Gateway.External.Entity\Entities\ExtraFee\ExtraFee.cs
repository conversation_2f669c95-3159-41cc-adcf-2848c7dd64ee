﻿using System;

namespace Gateway.External.Entity.Entities.ExtraFee
{
    public class ExtraFee
    {
        public int Id { get; set; }
        public int Type { get; set; }
        public int Category { get; set; }
        public int? MinimumItem { get; set; }
        public int? PolicyPeriod { get; set; }
        public bool IsStatic { get; set; }
        public byte? AgeRange { get; set; }
        public byte? ApplicationSaleType { get; set; }
        public bool IsApplicationSale { get; set; }
        public bool IsMainExtraFeeCategory { get; set; }
        public bool IsSubExtraFeeCategory { get; set; }
        public int? MainExtraFeeCategoryId { get; set; }
        public int? RelatedMainExtraFeeCategoryId { get; set; }
        public Guid FlagId { get; set; }
        public int? CreatedBy { get; set; }

        public DateTime? CreatedAt { get; set; }

        public int? UpdatedBy { get; set; }

        public DateTime? UpdatedAt { get; set; }
        public bool IsActive { get; set; }

        public bool IsDeleted { get; set; }

        public int? DeletedBy { get; set; }

        public DateTime? DeletedAt { get; set; }
    }
}
