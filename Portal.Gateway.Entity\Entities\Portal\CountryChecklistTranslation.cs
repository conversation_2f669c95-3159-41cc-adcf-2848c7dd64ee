﻿namespace Portal.Gateway.Entity.Entities.Portal
{
    public class CountryChecklistTranslation : AuditableEntity
    {
        public CountryChecklistTranslation()
        {
            IsActive = true;
            IsDeleted = false;
        }
        public int Id { get; set; }
        public int CountryChecklistId { get; set; }
        public int LanguageId { get; set; }
        public string Name { get; set; }
        public CountryChecklist CountryChecklist { get; set; }
    }
}
