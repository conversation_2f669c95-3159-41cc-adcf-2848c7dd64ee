﻿using Gateway.Core.Event;
using Gateway.QMS.Application.Action.Dto;
using Gateway.QMS.Application.Action.Event;
using Gateway.QMS.Application.Action.EventHandler;
using Gateway.QMS.Application.Ticket.Dto;
using Gateway.QMS.Entity.Entities.Lines;
using Gateway.QMS.Entity.Entities.Tokens;
using Gateway.QMS.Persistence;
using Gateway.QMS.Resources;
using Gateway.Validation;
using Microsoft.EntityFrameworkCore;
using Serilog;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Threading.Tasks;
using Gateway.Extensions;
using static Gateway.QMS.Application.Enums.Enums;

namespace Gateway.QMS.Application.Action.TokenAction.CallNext
{
    public class CallNextFromHoldOnAction : BaseCallNextAction<ActionCallNextRequest>, IAction<ActionCallNextRequest, ActionCallNextFromHoldOnResult>
    {
        private List<RedisUserToken> _waitingUserTokens;
        private RedisUserToken _selectedToken;
        private LineDepartment _lineDepartment;
        private List<RedisToken.TokenApplicant> _activeApplicants;
        private RedisNumberOfWaitingModel _numberOfWaitingModel;
        private RedisUserToken _userToken;
        private int _userHoldOnWaitingCount;

        protected static readonly ILogger Logger = Log.ForContext<CallNextFromHoldOnAction>();

        public CallNextFromHoldOnAction(QMSDbContext dbContext, IValidationService validationService, IEventService eventService, IRedisRepository redisRepository) : 
            base(dbContext, validationService, eventService, redisRepository) { }

        public async Task<ActionCallNextFromHoldOnResult> DoAction(ActionCallNextRequest request)
        {
            Logger.Information($"Request => BranchId:{request.Context.Identity.BranchId}, LineId:{request.LineId}, CounterId:{request.CounterId}, TraceId:{request.Context.TraceId}, RequestId:{request.Context.RequestId}, UserId:{request.Context.Identity.UserId}, Class:{GetType().Name}");

            ActionCallNextRequest = request;

            var validationResult = Validate(request);
            if (!validationResult.IsValid)
                return new ActionCallNextFromHoldOnResult
                {
                    Status = CallNextFromHoldOnStatus.InvalidInput,
                    Message = ServiceResources.INVALID_INPUT_ERROR,
                    ValidationMessages = validationResult.ErrorMessages
                };

            await using var transaction = await DbContext.Database.BeginTransactionAsync();

            try
            {
                var key = RedisKeysFactory.GetKey(RedisKeyType.InternalLockKey,
                new RedisActionEvent
                {
                    BranchId = ActionCallNextRequest.BranchId,
                    LineId = ActionCallNextRequest.LineId
                });

                await using var redLock = await RedisRepository.LockInstance(key);
                if (!redLock.IsAcquired)
                    return new ActionCallNextFromHoldOnResult
                    {
                        Status = CallNextFromHoldOnStatus.OngoingProcess,
                        Message = ServiceResources.OngoingProcess + " / " + ServiceResources.PleaseTryAgain
                    };

                await Initialize();

                var actionValidateResult = await ValidateAction();
                if (actionValidateResult.Status != CallNextFromHoldOnStatus.Successful)
                    return new ActionCallNextFromHoldOnResult
                    {
                        Status = actionValidateResult.Status,
                        Message = actionValidateResult.Message
                    };

                _selectedToken.IsCalled = true;
                ActiveDepartment.Applicants.Where(r => r.State == (byte)TokenStatus.Assign || r.State == (byte)TokenStatus.HoldOn).ToList().ForEach(r => r.State = (byte)TokenStatus.InProgress);

                GeneratedTokens = await DbContext.GeneratedToken
                    .Where(p => _activeApplicants.Select(q => q.Id).Contains(p.ApplicantId) && p.OwnerToken == _selectedToken.Token &&
                                p.CreatedAt.Date == DateTime.UtcNow.Date)
                    .ToListAsync();

                var generatedTokenHistories = new List<GeneratedTokenHistory>();

                foreach (var applicant in _activeApplicants)
                {
                    var generatedToken = GeneratedTokens?.Find(p => p.ApplicantId == applicant.Id);
                    if (generatedToken is null)
                        continue;

                    var generatedTokenHistory = new GeneratedTokenHistory
                    {
                        AppointmentId = Convert.ToInt32(generatedToken.AppointmentId),
                        ApplicantId = applicant.Id,
                        GeneratedTokenId = generatedToken.Id,
                        OwnerToken = generatedToken.OwnerToken,
                        Token = generatedToken.Token,
                        DepartmentId = ActiveDepartment.DepartmentId,
                        BranchId = request.BranchId,
                        LineId = request.LineId,
                        CounterId = request.CounterId,
                        Status = (byte)TokenStatus.InProgress,
                        AgentId = request.UserId,
                        CreatedBy = request.UserId,
                        CreatedAt = DateTime.UtcNow
                    };
                    generatedTokenHistories.Add(generatedTokenHistory);
                }

                await DbContext.GeneratedTokenHistory.AddRangeAsync(generatedTokenHistories);

                await DbContext.SaveChangesAsync();

                var hubConsumer = await EventService.RaiseWithResult<CallNextFromHoldOnActionChangedEvent, HubConsumer>(new CallNextFromHoldOnActionChangedEvent
                {
                    Context = ActionCallNextRequest.Context,
                    CachedModel = CacheToken,
                    Notes = CacheToken.Notes,
                    BranchId = ActionCallNextRequest.BranchId,
                    LineId = ActionCallNextRequest.LineId,
                    WaitingCachedModels = _waitingUserTokens,
                    OwnerToken = CacheToken.OwnerToken,
                    TokenNumber = _lineDepartment.IsProcessSameCounter
                        ? CacheToken.OwnerToken
                        : ActiveDepartment.Applicants.FirstOrDefault()?.Token,
                    RedisNumberOfWaitingModel = _numberOfWaitingModel,
                    RedisTokenDepartment = Department,
                    LineDepartmentId = _lineDepartment.Id,
                    Priority = ActiveDepartment.Priority,
                    ActiveTokenId = _selectedToken.Id,
                    CounterId = ActionCallNextRequest.CounterId,
                    CounterName = CounterName,
                    UserId = ActionCallNextRequest.UserId
                });

                await transaction.CommitAsync();

                return new ActionCallNextFromHoldOnResult
                {
                    Status = CallNextFromHoldOnStatus.Successful,
                    Message = ServiceResources.RESOURCE_CREATED,
                    ActiveTokenId = _selectedToken.Id,

                    AppointmentId = Convert.ToInt32(CacheToken.AppointmentId),
                    QmsFilterAppointmentNumber = string.Empty,
                    QmsBranchApplicationCountryId = CacheToken.QmsBranchApplicationCountryId,

                    Screen = CacheToken.Token,
                    TokenNumber = _lineDepartment.IsProcessSameCounter
                        ? CacheToken.OwnerToken
                        : ActiveDepartment.Applicants.FirstOrDefault()?.Token,
                    OwnerToken = CacheToken.OwnerToken,
                    SearchByPassportNumber = ActiveDepartment.SearchByPassportNumber,
                    IsLineDepartmentCreateApplication = _lineDepartment.IsLineDepartmentCreateApplication,
                    LineDepartmentId = _lineDepartment.Id,
                    IsSameProcessCounter = _lineDepartment.IsProcessSameCounter,
                    Interval = _lineDepartment.Interval,
                    Applicants = _activeApplicants.Select(p => new ActionCallNextFromHoldOnResult.Applicant
                    {
                        Id = p.Id,
                        GeneratedTokenId = p.GeneratedTokenId,
                        PassportNumber = p.PassportNumber,
                        ApplicationTime = p.ApplicationTime,
                        NameSurname = p.NameSurname,
                        IsIndividual = p.IsIndividual,
                        IsFamily = p.IsFamily,
                        IsGroup = p.IsGroup,
                        IsApplicationCompleted = p.IsApplicationCompleted,
                        ApplicationCompletedCounterId = p.ApplicationCompletedCounterId,
                        ApplicationCompletedLineDepartmentId = p.ApplicationCompletedLineDepartmentId,
                        ApplicantState = (byte)TokenStatus.InProgress,
                        IsAddedToAnotherToken = p.IsAddedToAnotherToken,
                        InsuranceTypeIds = p.InsuranceTypeIds,
                        IsBlackListApplicant = p.IsBlackListApplicant,
                        IsWhiteListApplicant = p.IsWhiteListApplicant,
                        HasNotCompletedReason = p.HasNotCompletedReason,
                        BlackListModel = p.BlackListModel != null ? new BlackListModel
                        {
                            BlackListNote = p.BlackListModel.BlackListNote,
                            BlackListNoteCreatedBy = p.BlackListModel.BlackListNoteCreatedBy,
                            BlackListNoteUpdatedBy = p.BlackListModel.BlackListNoteUpdatedBy
                        } : null,
                        WhiteListInformation = p.IsWhiteListApplicant ? new RedisWhiteListInformation
                        {
                            Id = p.WhitelistModel?.Id ?? 0,
                            DocumentExemption = p.WhitelistModel?.DocumentExemption,
                            RelevantInstitutionPerson = p.WhitelistModel?.RelevantInstitutionPerson,
                            MissionNotes = p.WhitelistModel?.MissionNotes,
                            BiometricData = p.WhitelistModel?.BiometricData
                        } : null,
                        NotCompletedReasons = p.NotCompletedReasons
                    }).ToList(),
                    HubConsumer = hubConsumer,
                    Notes = CacheToken.Notes
                };
            }
            catch (Exception ex)
            {
                Logger.Error($"BranchId: {ActionCallNextRequest.BranchId}, LineId: {ActionCallNextRequest.LineId}, UserId:{ActionCallNextRequest.Context.Identity.UserId}, Class: {GetType().Name}, Exception: {ex.Message}");

                await transaction.RollbackAsync();

                return new ActionCallNextFromHoldOnResult
                {
                    Status = CallNextFromHoldOnStatus.InternalServerError,
                    Message = ServiceResources.INTERNAL_SERVER_ERROR
                };
            }
        }

        private async Task Initialize()
        {
            _userToken = await RedisRepository.GetUserCache(ActionCallNextRequest.BranchId, ActionCallNextRequest.Context.Identity.UserId);

            _waitingUserTokens = await RedisRepository.GetUserWaitingCache(ActionCallNextRequest.BranchId, ActionCallNextRequest.UserId);

            _lineDepartment = await GetLineDepartment();

            _selectedToken =
                _waitingUserTokens?.Find(p => p.Token == ActionCallNextRequest.Token && p.Id == ActionCallNextRequest.TokenId && !p.IsCalled && p.LineDepartmentId == _lineDepartment.Id);

            if (_selectedToken == null)
                return;

            #region User Waiting List Applicant Count

            var userWaitingList = _waitingUserTokens?.Where(r => r.State == (byte)TokenStatus.Assign || r.State == (byte)TokenStatus.HoldOn).ToList();

            for (var i = 0; i < userWaitingList?.Count; i++)
            {
                _userHoldOnWaitingCount += userWaitingList[i].ApplicantCount;
            }

            _numberOfWaitingModel = new RedisNumberOfWaitingModel { Count = _userHoldOnWaitingCount - _selectedToken.ApplicantCount };

            #endregion

            Department = await RedisRepository.GetDepartments(ActionCallNextRequest.BranchId, _selectedToken.LineId, _selectedToken.Token, _selectedToken.Id);
            ActiveDepartment = Department?.TokenDepartments?.Find(p => p.IsActive);
            CacheToken = await RedisRepository.GetTokenCache(ActionCallNextRequest.BranchId, _selectedToken.LineId, _selectedToken.Token);

            CounterName = await GetCounterName();

            var applicants = ActiveDepartment?.Applicants?
                    .Where(p => _selectedToken.State == (byte)TokenStatus.Assign 
                        ? p.State == (byte)TokenStatus.Assign && !p.IndividualActionApplied
                        : p.State == (byte)TokenStatus.HoldOn && !p.IndividualActionApplied)
                .ToList();

            _activeApplicants = CacheToken?.Applicants?
                    .Where(r => applicants != null && applicants.Select(p => p.ApplicantId).Contains(r.Id) && r.ApplicantAssignee == ActionCallNextRequest.UserId)
                .ToList();
        }

        private async Task<ActionCallNextFromHoldOnResult> ValidateAction()
        {
            if (_userToken?.State == (byte)TokenStatus.InProgress)
            {
                Logger.Warning($"User busy state from holdon. BranchId:{ActionCallNextRequest.BranchId}, LineId:{ActionCallNextRequest.LineId}, UserId:{ActionCallNextRequest.Context.Identity.UserId}, Class:{GetType().Name}");

                await RedisRepository.DeleteUserCache(ActionCallNextRequest.BranchId, ActionCallNextRequest.Context.Identity.UserId);

                return new ActionCallNextFromHoldOnResult
                {
                    Status = CallNextFromHoldOnStatus.ResourceExists,
                    Message = ServiceResources.USER_STATE_REFRESH_MESSAGE
                };
            }

            var isCounterBusy = await RedisRepository.IsCounterBusy(ActionCallNextRequest.BranchId, ActionCallNextRequest.LineId, ActionCallNextRequest.CounterId, _lineDepartment.Id);
            if (isCounterBusy)
                return new ActionCallNextFromHoldOnResult
                {
                    Status = CallNextFromHoldOnStatus.ResourceExists,
                    Message = ServiceResources.COUNTER_BUSY
                };

            if (_selectedToken?.LineId != ActionCallNextRequest.LineId)
                return new ActionCallNextFromHoldOnResult
                {
                    Status = CallNextFromHoldOnStatus.NotFound,
                    Message = ServiceResources.ThereIsNoPersonWaitingOnTheSelectedLine
                };

            if (ActiveDepartment is null)
                throw new Exception("active department not found");

            if (CacheToken is null)
                throw new Exception("cached token not found");

            if (_activeApplicants?.Count == 0 || _activeApplicants == null) 
            {
                Logger.Warning($"ACTIVE_TOKEN_NOT_FOUND => BranchId:{ActionCallNextRequest.BranchId}, LineId:{ActionCallNextRequest.LineId}, UserId:{ActionCallNextRequest.Context.Identity.UserId}, CacheToken:{CacheToken.ToJson()}, Class:{GetType().Name}");

                var userWaitToken =  await RedisRepository.GetUserWaitingCache(ActionCallNextRequest.BranchId, ActionCallNextRequest.UserId);

                if (userWaitToken != null && userWaitToken.Any())
                {
                    var selectedApplicant = userWaitToken.Find(p =>
                        p.Token == ActionCallNextRequest.Token && p.Id == ActionCallNextRequest.TokenId &&
                        p.LineDepartmentId == _lineDepartment.Id);

                    userWaitToken.Remove(selectedApplicant);

                    var redisTransaction = RedisRepository.BeginTransaction();
                    await RedisRepository.AddUserWaitingCache(redisTransaction, userWaitToken, ActionCallNextRequest.BranchId, ActionCallNextRequest.UserId);
                    await RedisRepository.ExecuteTransactionAsync(redisTransaction);
                }

                return new ActionCallNextFromHoldOnResult
                {
                    Status = CallNextFromHoldOnStatus.NotFound,
                    Message = ServiceResources.TOKEN_ALREADY_ADDED_ON_ANOTHER_TOKEN
                };
            }

            return new ActionCallNextFromHoldOnResult
            {
                Status = CallNextFromHoldOnStatus.Successful,
                Message = ServiceResources.SUCCESS
            };
        }

        private async Task<LineDepartment> GetLineDepartment()
        {
            return await DbContext.LineDepartment
                .Join(DbContext.Counter, lineDepartment => lineDepartment.Id,
                    counter => counter.LineDepartmentId, (lineDepartment, counter) => new { lineDepartment, counter })
                .Where(p => !p.counter.IsDeleted && p.counter.Id == ActionCallNextRequest.CounterId &&
                            !p.lineDepartment.IsDeleted && p.lineDepartment.IsActive)
                .Select(r => r.lineDepartment)
                .FirstOrDefaultAsync();
        }
    }
}
