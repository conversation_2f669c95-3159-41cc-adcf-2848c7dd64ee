﻿using System;
using System.Collections.Generic;

namespace Portal.Gateway.Contracts.Entities.Dto.Report.Common.Report_16
{
    public class Report_Cash_2_Response
    {
        public IEnumerable<Branch> Branches { get; set; }

        public class Branch
        {
            public string BranchName { get; set; }

            public IEnumerable<DailyStats> DailyReports { get; set; }

            public class DailyStats
            {
                public DateTime Date { get; set; }

                public IEnumerable<PriceStatsResponse> Totals { get; set; }

                public IEnumerable<ExtraFee> ApplicationInsurances { get; set; }

                public IEnumerable<ExtraFee> ApplicationPcrs { get; set; }

                public IEnumerable<ExtraFee> NonApplicationInsurances { get; set; }

                public IEnumerable<ExtraFee> NonApplicationPcrs { get; set; }

                public IEnumerable<ExtraFee> ExtraFees { get; set; }

                public IEnumerable<UpdatedPriceData> UpdatedPriceDatas { get; set; }

                public IEnumerable<UpdatedPriceDataTotal> UpdatedPriceDataTotals { get; set; }

                public IEnumerable<UpdatedPriceExtraFee> UpdatedPriceExtraFees { get; set; }

                public IEnumerable<PhotoBooth> PhotoBoothsWithReference { get; set; }

                public IEnumerable<PhotoBooth> PhotoBoothsWithoutReference { get; set; }

                public class ExtraFee
                {
                    public string Name { get; set; }

                    public decimal UnitPrice { get; set; }

                    public decimal TotalAvailablePrice { get; set; }

                    public decimal TotalAvailableAfterChangePrice { get; set; }

                    public decimal TotalCancelledPrice { get; set; }

                    public decimal TotalCancelledAfterChangePrice { get; set; }

                    public decimal Tax { get; set; }

                    public int CurrencyId { get; set; }

                    public int Quantity { get; set; }

                    public int CancelledQuantity { get; set; }

                    public int UpdatedQuantity { get; set; }

                    public int UpdatedCancelledQuantity { get; set; }
                    public int CancelleationOfRejected { get; set; }


                }

                public class UpdatedPriceData
                {
                    public string Id { get; set; }

                    public int ExtraFeeId { get; set; }

                    public string Name { get; set; }

                    public decimal UnitPrice { get; set; }
                }

                public class UpdatedPriceDataTotal
                {
                    public int ExtraFeeId { get; set; }

                    public string Name { get; set; }

                    public decimal UnitPrice { get; set; }
                }

                public class UpdatedPriceExtraFee
                {
                    public int ExtraFeeId { get; set; }

                    public string Name { get; set; }

                    public decimal UnitPrice { get; set; }

                    public decimal TotalAvailablePrice { get; set; }

                    public decimal TotalCancelledPrice { get; set; }

                    public int Quantity { get; set; }

                    public int CancelledQuantity { get; set; }

                    public int UpdatedQuantity { get; set; }

                    public int UpdatedCancelledQuantity { get; set; }
                }

                public class PhotoBooth
                {
                    public string Name { get; set; }

                    public decimal UnitPrice { get; set; }

                    public int CurrencyId { get; set; }

                    public decimal TotalAvailablePrice { get; set; }

                    public decimal TotalCancelledPrice { get; set; }

                    public int Quantity { get; set; }

                    public int CancelledQuantity { get; set; }

                }

                public class PriceStatsResponse
                {
                    public int CurrencyId { get; set; }

                    public decimal Total { get; set; }

                    public decimal CanceledTotal { get; set; }
                }
            }
        }
    }
}
