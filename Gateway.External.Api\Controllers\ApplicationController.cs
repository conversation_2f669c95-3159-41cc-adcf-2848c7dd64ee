﻿using Gateway.Extensions;
using Gateway.External.Api.Models;
using Gateway.External.Api.Models.Application;
using Gateway.External.Application.Application;
using Gateway.External.Application.Application.Dto;
using Gateway.External.Core.Context;
using Gateway.External.Resources;
using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Mvc;
using Swashbuckle.AspNetCore.Annotations;
using System.Threading.Tasks;
using Gateway.External.Api.Factories.ResponseFactory;
using Gateway.External.Application.Application.Dto.Request;

namespace Gateway.External.Api.Controllers
{
    [Authorize]
    [Route("api")]
    [ApiController]
    public class ApplicationController:Controller
    {
        private readonly IContext _context;
        private readonly IApplicationService _applicationService;

        #region ctor

        public ApplicationController(IContext context, IApplicationService applicationService)
        {
            _context = context;
            _applicationService = applicationService;
        }

        #endregion

        #region Public Methods

        /// <summary>
        /// Gets Application Statuses
        /// </summary>
        /// <param name="applicationNumber"></param>  
        /// <param name="birthDate"></param>  
        [SwaggerOperation(Summary = "Gets Application Status", Description = "Gets Application Status")]
        [HttpGet]
        [Route("applications")]
        public async Task<IActionResult> GetApplicationStatus([FromQuery] int applicationNumber, [FromQuery] string birthDate)
        {
            if (!applicationNumber.IsNumericAndGreaterThenZero())
                return BadRequest(ApiResponseFactory.BuildBaseApiResponse(ServiceResources.INPUT_ERROR,
                    Resource.GetKey(ServiceResources.INVALID_REQUEST),
                    ServiceResources.INVALID_REQUEST));

            var serviceRequest = new GetApplicationStatusRequest
            {
                ApplicationNumber = applicationNumber,
                BirthDate = birthDate,
                Context = _context
            };

            var result = await _applicationService.GetApplicationStatus(serviceRequest);

            return ApplicationResponseFactory.GetApplicationStatusResponse(result);
        }

        /// <summary>
        /// Gets Application Status by Application Number
        /// </summary>
        /// <param name="applicationNumber"></param>  
        [SwaggerOperation(Summary = "Get Application Status By Id", Description = "Get Application Status By Id")]
        [HttpGet]
        [Route("applications/{applicationNumber?}/status")]
        public async Task<IActionResult> GetApplicationStatusById(int applicationNumber)
        {
            var serviceRequest = new GetApplicationStatusByIdRequest
            {
                ApplicationNumber = applicationNumber,
                Context = _context
            };

            var result = await _applicationService.GetApplicationStatusById(serviceRequest);

            return ApplicationResponseFactory.GetApplicationStatusResponse(result);
        }

        /// <summary>
        /// Gets Application Statuses from using appointmentId coming from appointment
        /// </summary>
        /// <param name="appointmentId"></param>  
        [SwaggerOperation(Summary = "Gets application statuses with appointmentId", Description = "Gets application statuses with appointmentId")]
        [HttpGet]
        [Route("applicationstatus")]
        public async Task<IActionResult> GetApplicationStatusByAppointment([FromQuery] int appointmentId)
        {
            if (!appointmentId.IsNumericAndGreaterThenZero())
                return BadRequest(ApiResponseFactory.BuildBaseApiResponse(ServiceResources.INPUT_ERROR,
                    Resource.GetKey(ServiceResources.INVALID_REQUEST),
                    ServiceResources.INVALID_REQUEST));

            var serviceRequest = new GetApplicationStatusByAppointmentRequest
            {
                AppointmentId = appointmentId,
                Context = _context
            };

            var result = await _applicationService.GetApplicationStatusByAppointment(serviceRequest);

            return ApplicationResponseFactory.GetApplicationStatusByAppointmentResponse(result);
        }

        /// <summary>
        /// Gets Application which have yss insurance by passport number and birth date
        /// </summary>
        [SwaggerOperation(Summary = " Gets Application which have yss insurance by passport number and birth date", Description = " Gets Application which have yss insurance by passport number and birth date")]
        [HttpGet]
        [Route("applications/yss")]
        public async Task<IActionResult> GetYssApplications([FromQuery] string passportNumber, [FromQuery] string birthDate)
        {
            var serviceRequest = new GetYssApplicationRequest
            {
                PassportNumber = passportNumber,
                BirthDate = birthDate,
                Context = _context
            };

            var result = await _applicationService.GetYssApplication(serviceRequest);

            return BaseResponseFactory.CreateResponse(result, result?.Data);
        }
        #endregion
    }
}
