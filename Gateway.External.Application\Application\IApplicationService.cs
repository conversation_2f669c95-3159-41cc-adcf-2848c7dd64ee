﻿using Gateway.External.Application.Application.Dto;
using Gateway.External.Application.Application.Dto.Request;
using Gateway.External.Application.Application.Dto.Result;
using System.Threading.Tasks;

namespace Gateway.External.Application.Application
{
    public interface IApplicationService
    {
        Task<GetApplicationStatusResult> GetApplicationStatus(GetApplicationStatusRequest request);
        Task<GetApplicationStatusResult> GetApplicationStatusById(GetApplicationStatusByIdRequest request);
        Task<GetApplicationStatusByAppointmentResult> GetApplicationStatusByAppointment(GetApplicationStatusByAppointmentRequest request);
        Task<GetYssApplicationResult> GetYssApplication(GetYssApplicationRequest request);
    }
}
