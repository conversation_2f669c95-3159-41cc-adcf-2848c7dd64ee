using Gateway.EventBus;
using Gateway.ObjectStoring;
using Gateway.ObjectStoring.Minio;
using Microsoft.AspNetCore.Authentication.JwtBearer;
using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Builder;
using Microsoft.AspNetCore.Hosting;
using Microsoft.AspNetCore.ResponseCompression;
using Microsoft.EntityFrameworkCore;
using Microsoft.Extensions.Configuration;
using Microsoft.Extensions.DependencyInjection;
using Microsoft.IdentityModel.Tokens;
using Microsoft.OpenApi.Models;
using Portal.Gateway.Api.ActionFilters;
using Portal.Gateway.Api.Config;
using Portal.Gateway.Api.Context;
using Portal.Gateway.Api.Helpers;
using Portal.Gateway.Api.Hubs;
using Portal.Gateway.Contracts.Entities.Constants;
using Portal.Gateway.Contracts.Repository;
using Portal.Gateway.Contracts.Settings.AppSettings;
using Portal.Gateway.Contracts.UnitOfWork;
using Portal.Gateway.Entity.Context;
using Portal.Gateway.Infrastructure.Dapper;
using Portal.Gateway.Infrastructure.Repository;
using Portal.Gateway.Infrastructure.UnitOfWork;
using Serilog;
using StackExchange.Redis;
using System;
using System.Collections.Generic;
using System.Diagnostics;
using System.IO;
using System.IO.Compression;
using System.Linq;
using System.Reflection;
using System.Text;


namespace Portal.Gateway.Api
{
    public class Startup
    {
        public Startup(IConfiguration configuration)
        {
            Configuration = configuration;
        }

        public IConfiguration Configuration { get; }

        public void ConfigureServices(IServiceCollection services)
        {
            services.Configure<GzipCompressionProviderOptions>(options =>
            {
                options.Level = CompressionLevel.Fastest;
            });

            services.AddResponseCompression(options =>
            {
                options.EnableForHttps = true;
                options.Providers.Add<GzipCompressionProvider>();
            });

            var redisUrl = Configuration["Redis:Url"];
            var redisPort = Configuration["Redis:Port"];
            var defaultDatabase = Configuration["Redis:PrinterServiceDefaultDatabase"];

            services.AddSignalR().AddStackExchangeRedis(redisUrl + ":" + redisPort, options =>
            {
                options.Configuration.ChannelPrefix = "PrinterService";
                options.Configuration.AbortOnConnectFail = false;
            });

            services.AddStackExchangeRedisCache(options =>
            {
                //options.Configuration = redisUrl + ":" + redisPort;
                //options.InstanceName = "CacheInstance";
                options.ConfigurationOptions = new ConfigurationOptions
                {
                    DefaultDatabase = int.Parse(defaultDatabase),
                    Ssl = false,
                    ClientName = "Portal.Gateway.Api",
                    ConnectRetry = 5,
                    EndPoints = { redisUrl, redisPort },
                    AbortOnConnectFail = false,
                };
            });

            services.AddControllers();

            services.AddHttpContextAccessor();

            services.AddPortalLogger(Configuration);

            services.AddSwaggerGen(
                c =>
                {
                    c.EnableAnnotations();
                    c.SwaggerDoc(
                        "v1",
                        new OpenApiInfo
                        {
                            Version = "v1",
                            Title = "Portal Gateway Api",
                            Description =
                                $"<b>Env:</b> {Environment.GetEnvironmentVariable("ASPNETCORE_ENVIRONMENT")}<br/>"
                                + $"<b>Upd:</b> {File.GetLastWriteTimeUtc(Assembly.GetExecutingAssembly().Location):dd.MM.yyyy HH:mm:ss}"
                        });

                    c.OperationFilter<CustomHeaderSwagger>();
                    c.CustomSchemaIds(type => type.FullName);
                });

            services.AddAutoMapper(expression => { expression.AddProfile<MapperConfig>(); }, typeof(Startup).Assembly);

            services.AddAuthentication(PublicTokenAuthOptions.DefaultSchemeName)
                .AddScheme<PublicTokenAuthOptions, PublicTokenAuthHandler>(PublicTokenAuthOptions.DefaultSchemeName, opts => { })
                .AddJwtBearer("Bearer", jwtBearerOptions =>
                {
                    jwtBearerOptions.TokenValidationParameters = new TokenValidationParameters()
                    {
                        //ValidateActor = false,
                        ValidateAudience = false,
                        ValidateIssuer = false,
                        ValidateLifetime = true,
                        ValidateIssuerSigningKey = true,
                        ValidIssuer = "Issuer",
                        ValidAudience = "Audience",
                        IssuerSigningKey = new SymmetricSecurityKey(Encoding.UTF8.GetBytes("D9D35D60-DCE3-4651-86EB-E071D674BC6D")),
                        NameClaimType = "UserName",

                    };
                });

            services.AddAuthorization(options =>
            {
                options.DefaultPolicy = new AuthorizationPolicyBuilder(JwtBearerDefaults.AuthenticationScheme)
                .RequireAuthenticatedUser()
                .Build();
            });

            #region AppSettings

            services.Configure<AppSettings>(Configuration.GetSection("AppSettings"));
            services.Configure<IntegrationSettings>(Configuration.GetSection("IntegrationSettings"));
            services.Configure<LdapSettings>(Configuration.GetSection("AppSettings:Ldap"));

            #endregion

            #region DbContext

			services.AddDbContext<PortalDbContext>(options =>
				options.UseNpgsql(Configuration.GetConnectionString(ProjectConst.DbConnectionStringName.PortalDb)).EnableSensitiveDataLogging().LogTo(Console.WriteLine));
			services.AddScoped<IDapperRepository>(s =>
				new DapperRepository(Configuration.GetConnectionString(ProjectConst.DbConnectionStringName.PortalDb)));

            #endregion

            #region UnitOfWork | Repository | Services

            services.AddScoped(typeof(IUnitOfWork<>), typeof(UnitOfWork<>));
            services.AddScoped(typeof(IRepository<>), typeof(Repository<>));
            services.AddScoped<IFileStorage, MinioFileStorage>();
            services.AddScoped<IBucketNamingNormalizer, MinioBucketNamingNormalizer>();

            var serviceList = Assembly.Load("Portal.Gateway.Services").GetTypes()
                .Where(t => t.IsClass && t.Name.EndsWith("Service"))
                .Concat(Assembly.Load("Portal.Gateway.ExternalServices").GetTypes()
                    .Where(t => t.IsClass && t.Name.EndsWith("Service")));

            foreach (var item in serviceList)
            {
                if (item.GetInterface("I" + item.Name) != null)
                {
                    services.AddScoped(item.GetInterface("I" + item.Name), item);
                }
            }

            #endregion

            #region FileStorage

            //services.AddScoped<IFileStorage, MinioFileStorage>();
            //services.AddScoped<IBucketNamingNormalizer, MinioBucketNamingNormalizer>();

            #endregion

            #region Ministry Service

            //services.AddScoped<IMinistryService, MinistryService>();

            #endregion

            #region RabbitMq           

            services.AddRabbitMq(Configuration);

            #endregion

            RegisterHttpContext(services);
        }

        public void Configure(IApplicationBuilder app, IWebHostEnvironment env)
        {
            if (Debugger.IsAttached)
                app.UseDeveloperExceptionPage();

            app.UseRouting();

            app.UseAuthentication();
            app.UseAuthorization();

            app.UseEndpoints(endpoints =>
            {
                endpoints.MapControllers();
                endpoints.MapHub<PrinterHub>("/printerHub");
            });

            app.UseHsts();
            app.UseHttpsRedirection();

            app.UseResponseCompression();

            app.UseSwagger(c =>
            {
                c.PreSerializeFilters.Add((swagger, httpReq) =>
                {
                    swagger.Servers = new List<OpenApiServer>
                    {
                        new OpenApiServer { Url = $"https://{httpReq.Host.Value}" },
                        new OpenApiServer { Url = $"{httpReq.Scheme}://{httpReq.Host.Value}" }
                    };
                });
            });

            app.UseSwaggerUI(
                c =>
                {
                    c.SwaggerEndpoint("/swagger/v1/swagger.json", "v1");
                    c.DefaultModelsExpandDepth(-1);
                });

            app.UseSerilogRequestLogging(opts => opts.EnrichDiagnosticContext = ElasticLogHelper.EnrichFromRequest);
        }

        private IServiceCollection RegisterHttpContext(IServiceCollection services)
        {
            services.AddSingleton<Microsoft.AspNetCore.Http.IHttpContextAccessor, Microsoft.AspNetCore.Http.HttpContextAccessor>();
            services.AddScoped<IContextFactory, ContextFactory>();
            services.AddTransient(p => p.GetRequiredService<IContextFactory>().Create());
            return services;
        }
    }
}
