﻿using System.Collections.Generic;

namespace Gateway.External.Application.Application.Dto.Result
{
    public class GetApplicationStatusResult : BaseServiceResult<GetApplicationStatus>
    {
        public IList<TrackingView> TrackingView { get; set; }
    }

    public class TrackingView
    {
        public string Status { get; set; }
        public bool IsActive { get; set; }
    }
    public enum GetApplicationStatus
    {
        Successful,
        InvalidInput,
        ApplicationNotFound,
        AppointmentNotFound,
        ApplicationStatusNotFound,
        AppointmentNotConvertedToApplication,
        InternalServerError
    }
}
