﻿using System;
using System.Collections.Generic;

namespace Portal.Gateway.Contracts.Entities.Dto.Report.Common.Report_80
{
	public class Report_FreeInsuranceResponse
	{
		public IEnumerable<Branch> Branches { get; set; }
		public class Branch
		{
			public string BranchName { get; set; }
			public IEnumerable<Insurance> Insurances { get; set; }

			public class Insurance
			{
				//public string CountryName { get; set; }

				//public string BranchName { get; set; }

				public int ApplicationTypeId { get; set; }

				public string ReferenceNumber { get; set; }

				public string PassportNumber { get; set; }

				public string PolicyNumber { get; set; }

				public string ApplicantName { get; set; }

				public string ApplicantSurname { get; set; }

				public DateTimeOffset ApplicationDate { get; set; }

				public DateTimeOffset InsuranceStartDate { get; set; }

				public DateTimeOffset InsuranceEndDate { get; set; }

				public int PolicyDays { get; set; }

				public string? CompanyPrice { get; set; }
				public string? ProvicerPrice { get; set; }

				public string CompanyPriceCurrency { get; set; }
				public string ProvicerPriceCurrency { get; set; }

				public string PolicyStatus { get; set; }
                public bool IsValid { get; set; }
                
                public string FirstPolicyNumber { get; set; }

                public DateTimeOffset? FirstInsuranceStartDate { get; set; }

                public DateTimeOffset? FirstInsuranceEndDate { get; set; }
                public string FirstPolicyStatus { get; set; }
                public int FirstReferenceNumber { get; set; }
                public DateTimeOffset? FirstApplicationDate { get; set; }
			}
		}
	}
}
