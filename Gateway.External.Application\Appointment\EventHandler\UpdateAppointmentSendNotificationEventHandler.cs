﻿using Gateway.EventBus.Publishers;
using Gateway.Extensions;
using Gateway.External.Application.Notification;
using Gateway.External.Application.Notification.Events;
using Gateway.External.Application.Notification.Requests;
using Microsoft.Extensions.Configuration;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Threading.Tasks;

namespace Gateway.External.Application.Appointment.EventHandler
{
    public class UpdateAppointmentSendNotificationEventHandler : BaseAppointmentSendNotificationEventHandler, INotificationEventHandler<UpdateAppointmentSendNotificationEvent>
    {
        private readonly IMessagePublisher _messagePublisher;
        private readonly string _queueName;

        public UpdateAppointmentSendNotificationEventHandler(IMessagePublisher messagePublisher, IConfiguration configuration)
        {
            _messagePublisher = messagePublisher;
            _queueName = configuration["RabbitMq:EmailQueue"].AddEnvironmentSuffix();
        }

        public async Task Handle(UpdateAppointmentSendNotificationEvent args)
        {
	        var notification = new NotificationServiceRequest.Notification
            {
                Text = DefineEmailTextByBranch(args.LanguageId,args, 2 , args.HtmlText),
                ApplicantList = args.Applicants.Where(r => !r.Email.IsNullOrWhitespace()).Select(r => new NotificationServiceRequest.Applicant()
                {
                    Contact = r.Email
                }).ToList(),
                Subject = $"{Resources.ServiceResources.APPOINTMENT_UPDATE_CONFIRMATION_FILE_NAME}",
                ProviderId = 1, //sendgrid
                IsPriorityNotification = false,
                NotificationType = 8,
                TransactionId = args.AppointmentNumber.ToString(),
                Attaches = new List<Attachment> { args.AppointmentLetter },
                FooterLanguageId = args.LanguageId,
            };

            var mailTo = notification.ApplicantList.Select(n => n.Contact).Distinct().ToList();

            foreach (var message in mailTo.Select(t => new SendEmailRequest
                     {
                         TransactionId = notification.TransactionId,
                         To = t,
                         Content = DefineTextWithFooter(notification.Text, (int)notification.FooterLanguageId),
                         From = "<EMAIL>",
                         Subject = notification.Subject,
                         Attachments = notification.Attaches,
                         MailType = notification.NotificationType,
                         SendDate = DateTime.Now,
                         ProviderId = notification.ProviderId //sendGrid
                     }))
            {
                await _messagePublisher.PublishAsync(_queueName, message, false, 0);
            }
        }
    }
}
