using Microsoft.AspNetCore.Mvc;
using Microsoft.Extensions.Caching.Memory;
using Microsoft.Extensions.Options;
using Portal.Gateway.UI.Config;
using Portal.Gateway.UI.Controllers;

namespace Portal.Gateway.UI.Areas.Admin.Controllers
{
    [Area("Admin")]
    public class DashboardController : BaseController<DashboardController>
    {
        public DashboardController(
                IOptions<AppSettings> appSettings,
                IMemoryCache memoryCache)
            : base(appSettings, memoryCache)
        {

        }

        public IActionResult Index()
        {
            return View();
        }
    }
}