﻿@using Microsoft.AspNetCore.Builder
@using Microsoft.Extensions.Options
@using Microsoft.AspNetCore.Http
@using Portal.Gateway.UI.Constants
@using Portal.Gateway.UI.Models
@inject IHttpContextAccessor HttpContextAccessor
@inject IOptions<RequestLocalizationOptions> RequestLocalizationOptions
@{
    var currentUser = HttpContextAccessor.HttpContext.Session.Get<UserModel>(SessionKeys.UserSession);
    var languageId = Html.CurrentLanguageId().ToString().ToInt();
    var roleListForTurkmenistanLocal = new int[] { 28 };
    var isLocalAuthorized = currentUser.RoleIds.Any(p => roleListForTurkmenistanLocal.Contains(p));
    var hasNoEmail = Model.Email.Trim().Equals("<EMAIL>", StringComparison.OrdinalIgnoreCase);
}

@model ApplicationViewModel

<div class="card card-custom card-stretch">
    <div class="card-header">
        <div class="card-title">
            <h3 class="card-label">
                @SiteResources.ApplicationSummary.ToTitleCase()
            </h3>
        </div>
        <div class="card-toolbar">
        </div>
    </div>
    <div class="card-body">
        <div class="row">
            <div class="col-xl-12">
                @if (isLocalAuthorized)
                {
                    <div class="form-group row mb-10">
                        <div class="col-lg-3 col-md-6">
                            <label class="font-weight-bolder">@SiteResources.ApplicationNumber.ToTitleCase()</label>
                            <span class="d-block font-italic">@Model.ApplicationNumber</span>
                        </div>
                        <div class="col-lg-3 col-md-6">
                            <label class="font-weight-bolder">@SiteResources.ApplicationTime.ToTitleCase()</label>
                            <span class="d-block font-italic">@Model.ApplicationTime</span>
                        </div>
                        <div class="col-lg-3 col-md-6">
                            <label class="font-weight-bolder">@SiteResources.PassportNumber.ToTitleCase()</label>
                            <span class="d-block font-italic">@Model.PassportNumber</span>
                        </div>
                    </div>
                }
                else
                {
                    <div class="form-group row mb-10">
                        <div class="col-lg-3 col-md-6">
                            <label class="font-weight-bolder">@SiteResources.ApplicationNumber.ToTitleCase()</label>
                            <span class="d-block font-italic">@Model.ApplicationNumber</span>
                        </div>
                        <div class="col-lg-3 col-md-6">
                            <label class="font-weight-bolder">@SiteResources.ApplicationTime.ToTitleCase()</label>
                            <span class="d-block font-italic">@Model.ApplicationTime</span>
                        </div>
                        <div class="col-lg-3 col-md-6">
                            <label class="font-weight-bolder">@SiteResources.CreatedBy.ToTitleCase()</label>
                            <span class="d-block font-italic">@Model.CreatedByNameSurname</span>
                        </div>
                        <div class="col-lg-3 col-md-6">
                            <label class="font-weight-bolder">@SiteResources.Status.ToTitleCase()</label>
                            <span class="d-block font-italic">@Model.Status</span>
                        </div>
                    </div>
                    <div class="form-group row mb-10">
                        <div class="col-lg-3 col-md-6">
                            <label class="font-weight-bolder">@SiteResources.CountryName.ToTitleCase()</label>
                            <span class="d-block font-italic">@Model.CountryName</span>
                        </div>
                        <div class="col-lg-3 col-md-6">
                            <label class="font-weight-bolder">@SiteResources.ApplicantType.ToTitleCase()</label>
                            <span class="d-block font-italic">@Model.ApplicantType</span>
                        </div>
                        <div class="col-lg-3 col-md-6">
                            <label class="font-weight-bolder">@SiteResources.ApplicationType.ToTitleCase()</label>
                            <span class="d-block font-italic">@Model.ApplicationType</span>
                        </div>
                        <div class="col-lg-3 col-md-6">
                            <label class="font-weight-bolder">@SiteResources.Agency.ToTitleCase()</label>
                            <span class="d-block font-italic">@Model.AgencyName</span>
                        </div>
                    </div>
                    <div class="form-group row mb-10">
                        <div class="col-lg-3 col-md-6">
                            <label class="font-weight-bolder">@SiteResources.PassportNumber.ToTitleCase()</label>
                            <span class="d-block font-italic">@Model.PassportNumber</span>
                        </div>
                        <div class="col-lg-3 col-md-6">
                            <label class="font-weight-bolder">@SiteResources.PassportExpireDate.ToTitleCase()</label>
                            @if ((@Model.PassportExpireDate == @DateTime.MinValue) || (@Model.PassportExpireDate == null))
                            {
                                <span class="d-block font-italic">"N/A"</span>
                            }
                            else
                            {
                                <span class="d-block font-italic">@Model.PassportExpireDate.Value.Date.ToString("dd/MM/yyyy")</span>
                            }
                        </div>
                        <div class="col-lg-3 col-md-6">
                            <label class="font-weight-bolder">@SiteResources.ApplicationPassportStatus.ToTitleCase()</label>
                            <span class="d-block font-italic">@Model.ApplicationPassportStatus</span>
                        </div>
                        @if (Model.BranchCountryIso3 == "SAU")
                        {
                            <div class="col-lg-3 col-md-6">
                                <label class="font-weight-bolder">@SiteResources.ResidenceNumber.ToTitleCase()</label>
                                <span class="d-block font-italic">@Model.ResidenceNumber</span>
                            </div>
                        }
                    </div>
                    @if (@Model.IsAllowDeniedPassport == true)
                    {
                        <div class="form-group row mb-10">
                            <div class="col-lg-3 col-md-6">
                                <label class="font-weight-bolder">@SiteResources.AllowDeniedPassport.ToTitleCase()</label>
                                <span id="#IsAllowDeniedPassport" class="d-block font-italic">@Model.IsAllowDeniedPassport</span>
                            </div>
                            <div class="col-lg-3 col-md-6">
                                <label id="#Reason" class="font-weight-bolder">@SiteResources.Reason.ToTitleCase()</label>
                                <span class="d-block font-italic">@Model.Reason</span>
                            </div>
                        </div>
                    }
                }
            </div>
        </div>
        <div class="separator separator-dashed my-5"></div>
        <div class="row">
            <div class="col-xl-12">
                @if (isLocalAuthorized)
                {
                    <div class="form-group row mb-10">
                        <div class="col-lg-3 col-md-6">
                            <label class="font-weight-bolder">@SiteResources.Title.ToTitleCase()</label>
                            <span class="d-block font-italic">@Model.Title</span>
                        </div>
                        <div class="col-lg-3 col-md-6">
                            <label class="font-weight-bolder">@SiteResources.Name.ToTitleCase()</label>
                            <span class="d-block font-italic">@Model.Name</span>
                        </div>
                        <div class="col-lg-3 col-md-6">
                            <label class="font-weight-bolder">@SiteResources.Surname.ToTitleCase()</label>
                            <span class="d-block font-italic">@Model.Surname</span>
                        </div>
                    </div>
                    <div class="form-group row mb-10">
                        <div class="col-lg-3 col-md-6">
                            <label class="font-weight-bolder">@SiteResources.BirthDate.ToTitleCase()</label>
                            <span class="d-block font-italic">@Model.BirthDate.ToString("dd/MM/yyyy")</span>
                        </div>
                        <div class="col-lg-3 col-md-6">
                            <label class="font-weight-bolder">@SiteResources.FatherName.ToTitleCase()</label>
                            <span class="d-block font-italic">@Model.FatherName</span>
                        </div>
                    </div>
                }
                else
                {
                    <div class="form-group row mb-10">
                        <div class="col-lg-3 col-md-6">
                            <label class="font-weight-bolder">@SiteResources.Title.ToTitleCase()</label>
                            <span class="d-block font-italic">@Model.Title</span>
                        </div>
                        <div class="col-lg-3 col-md-6">
                            <label class="font-weight-bolder">@SiteResources.Name.ToTitleCase()</label>
                            <span class="d-block font-italic">@Model.Name</span>
                        </div>
                        <div class="col-lg-3 col-md-6">
                            <label class="font-weight-bolder">@SiteResources.Surname.ToTitleCase()</label>
                            <span class="d-block font-italic">@Model.Surname</span>
                        </div>
                    </div>
                    <div class="form-group row mb-10">
                        <div class="col-lg-3 col-md-6">
                            <label class="font-weight-bolder">@SiteResources.BirthDate.ToTitleCase()</label>
                            <span class="d-block font-italic">@Model.BirthDate.ToString("dd/MM/yyyy")</span>
                        </div>
                        <div class="col-lg-3 col-md-6">
                            <label class="font-weight-bolder">@SiteResources.Gender.ToTitleCase()</label>
                            <span class="d-block font-italic">@Model.Gender</span>
                        </div>
                        <div class="col-lg-3 col-md-6">
                            <label class="font-weight-bolder">@SiteResources.MaritalStatus.ToTitleCase()</label>
                            @if (@Model.MaritalStatus == "")
                            {
                                <span class="d-block font-italic">"N/A"</span>
                            }
                            else
                            {
                                <span class="d-block font-italic">@Model.MaritalStatus</span>
                            }
                        </div>
                    </div>
                    <div class="form-group row mb-10">
                        <div class="col-lg-3 col-md-6">
                            <label class="font-weight-bolder">@SiteResources.Nationality.ToTitleCase()</label>
                            <span class="d-block font-italic">@Model.Nationality</span>
                        </div>
                        <div class="col-lg-3 col-md-6">
                            <label class="font-weight-bolder">@SiteResources.MaidenName.ToTitleCase()</label>
                            <span class="d-block font-italic">@Model.MaidenName</span>
                        </div>
                        <div class="col-lg-3 col-md-6">
                            <label class="font-weight-bolder">@SiteResources.FatherName.ToTitleCase()</label>
                            <span class="d-block font-italic">@Model.FatherName</span>
                        </div>
                        <div class="col-lg-3 col-md-6">
                            <label class="font-weight-bolder">@SiteResources.MotherName.ToTitleCase()</label>
                            <span class="d-block font-italic">@Model.MotherName</span>
                        </div>
                    </div>
                    <div class="form-group row mb-10">
                        <div class="col-lg-3 col-md-6">
                            <label class="font-weight-bolder">@SiteResources.Email.ToTitleCase()</label>
                            <span class="d-block font-italic">@Model.Email</span>
                        </div>
                        <div class="col-lg-3 col-md-6">
                            <label class="font-weight-bolder">@SiteResources.NoEmailLabelName.ToTitleCase()</label>
                            <span class="d-block font-italic">@Html.YesNo(hasNoEmail)</span>
                        </div>
                        <div class="col-lg-3 col-md-6">
                            <label class="font-weight-bolder">@SiteResources.PhoneNumber.ToTitleCase() #1</label>
                            <span class="d-block font-italic">@Model.PhoneNumber1</span>
                        </div>
                        <div class="col-lg-3 col-md-6">
                            <label class="font-weight-bolder">@SiteResources.PhoneNumber.ToTitleCase() #2</label>
                            <span class="d-block font-italic">@Model.PhoneNumber2</span>
                        </div>
                    </div>
                    <div class="form-group row mb-10">
                        <div class="col-lg-12">
                            <label class="font-weight-bolder">@SiteResources.Address.ToTitleCase()</label>
                            <span class="d-block font-italic">@Model.Address</span>
                        </div>
                    </div>
                    <div class="form-group row mb-10">
                        @if(Model.IsCargoIntegrationActive && Model.CargoProviderId is (int)CargoProviderType.LastMile)
                        {
                            <div class="col-lg-3 col-md-6">
                                <label class="font-weight-bolder">@SiteResources.AreaName.ToTitleCase()</label>
                                <span class="d-block font-italic">@Model.AreaName</span>
                            </div>
                        }
                        @if (Model.ShowCityDropdown)
                        {
                            <div class="col-lg-3 col-md-6">
                                <label class="font-weight-bolder">@SiteResources.City.ToTitleCase()</label>
                                <span class="d-block font-italic">@Model.City</span>
                            </div>
                        }
                        @if (Model.IsCargoIntegrationActive && Model.CargoProviderId is (int)CargoProviderType.UPS)
                        {
                            <div class="col-lg-3 col-md-6">
                                <label class="font-weight-bolder">@SiteResources.PostalCode.ToTitleCase()</label>
                                <span class="d-block font-italic">@Model.PostalCode</span>
                            </div>
                            <div class="col-lg-3 col-md-6">
                                <label class="font-weight-bolder">@SiteResources.NameOfSecondContactPerson.ToTitleCase()</label>
                                <span class="d-block font-italic">@Model.NameOfSecondContactPerson</span>
                            </div>
                        }
                    </div>
                    <div class="form-group row mb-10">
                        <div class="col-lg-12">
                            <label class="font-weight-bolder">@SiteResources.Notes.ToTitleCase()</label>
                            @if (!string.IsNullOrEmpty(Model.Note))
                            {
                                <p class="d-block font-italic">
                                    @Html.Raw(Model.Note.Replace("\r\n", "<br />"))
                                </p>
                            }
                        </div>
                    </div>
                }
            </div>
        </div>
    </div>
</div>