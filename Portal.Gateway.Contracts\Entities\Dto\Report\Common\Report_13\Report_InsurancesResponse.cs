﻿using System;
using System.Collections.Generic;

namespace Portal.Gateway.Contracts.Entities.Dto.Report.Common.Report_13
{
    public class Report_InsurancesResponse
    {
        public IEnumerable<Insurance> Insurances { get; set; }

        public class Insurance
        {
            public string ApplicationId { get; set; }

            public string PassportNumber { get; set; }

            public string PolicyNumber { get; set; }

            public string ApplicantName { get; set; }

            public string ProviderName { get; set; }

            public DateTimeOffset ApplicationDate { get; set; }

            public DateTimeOffset InsuranceStartDate { get; set; }

            public DateTimeOffset InsuranceEndDate { get; set; }

            public int InsuranceRemainingDays { get; set; }

            public decimal Price { get; set; }

            public int CurrencyId { get; set; }

            public bool IsValid { get; set; }
            public decimal? CompanyPrice { get; set; }
            public string? CompanyPriceCurrency { get; set; }
            public decimal? Price2 { get; set; }
            public string? Price2Currency { get; set; }
            public int? ApplicationInsuranceCategory { get; set; }
        }
    }
}
