﻿using Gateway.Extensions;
using Gateway.External.Application.Application.Dto;
using Gateway.External.Application.Application.Dto.Request;
using Gateway.External.Application.Application.Dto.Result;
using Gateway.External.Application.Application.Validators;
using Gateway.External.Application.Enums;
using Gateway.External.Application.Lookup;
using Gateway.External.Entity.Entities.Application;
using Gateway.External.Persistence;
using Gateway.External.Resources;
using Gateway.Validation;
using Microsoft.EntityFrameworkCore;
using Portal.Gateway.Common.Utility.Extensions;
using Serilog;
using System;
using System.Collections.Generic;
using System.Globalization;
using System.Linq;
using System.Threading.Tasks;
using EnumExtensions = Gateway.Extensions.EnumExtensions;

namespace Gateway.External.Application.Application
{
    public class ApplicationService : IApplicationService
	{
		private static readonly ILogger Logger = Log.ForContext<ApplicationService>();
		private readonly ApiDbContext _dbContext;
		private readonly IValidationService _validationService;

		public ApplicationService(IValidationService validationService, ApiDbContext dbContext)
		{
			_validationService = validationService;
			_dbContext = dbContext;
		}

		public async Task<GetApplicationStatusResult> GetApplicationStatus(GetApplicationStatusRequest request)
		{
			var validationResult = _validationService.Validate(typeof(GetApplicationStatusValidator), request);

			if (!validationResult.IsValid)
				return new GetApplicationStatusResult
				{
					Status = Dto.Result.GetApplicationStatus.InvalidInput,
					Message = ServiceResources.INVALID_INPUT_ERROR,
					ValidationMessages = validationResult.ErrorMessages
				};

			try
			{
				var birthDate = request.BirthDate.StringToDateTime("ddMMyyyy", CultureInfo.InvariantCulture);

				var existingApplication = await _dbContext.Application
					.Include(p => p.ApplicationStatusHistories)
					.Include(p => p.BranchApplicationCountry)
						.ThenInclude(p => p.Branch)
					.Where(p => p.IsActive && !p.IsDeleted && p.Id == request.ApplicationNumber &&
								p.BirthDate.Year == birthDate.Year && p.BirthDate.Month == birthDate.Month && p.BirthDate.Day == birthDate.Day).FirstOrDefaultAsync();

				if (existingApplication == null)
					return new GetApplicationStatusResult
					{
						Status = Dto.Result.GetApplicationStatus.ApplicationNotFound,
						Message = ServiceResources.APPLICATION_NOT_FOUND
					};

				var existingApplicationStatusHistories = existingApplication.ApplicationStatusHistories
					.Where(s => s.IsActive && !s.IsDeleted).OrderBy(c => c.Id).ToList();

				bool referanceNumberForApplicationCheck = false;
				int referanceNumberNotificationType = 0;
				var existingApplicationsForReferanceNumber = await _dbContext.ApplicationReferenceNumber
                    .Where(p => p.IsActive && p.ApplicationId == existingApplication.Id).OrderByDescending(q => q.Id).ToListAsync();

                if (existingApplicationsForReferanceNumber.Any() && existingApplication.BranchApplicationCountry.Branch.CountryId == 144)// Russia
				{
					referanceNumberForApplicationCheck = true;
					referanceNumberNotificationType = existingApplicationsForReferanceNumber.Select(q => q.NotificationTypeId).FirstOrDefault();
				}

				if (!existingApplicationStatusHistories.Any())
					return new GetApplicationStatusResult
					{
						Status = Dto.Result.GetApplicationStatus.ApplicationStatusNotFound,
						Message = ServiceResources.APPLICATION_STATUS_NOT_FOUND
					};

				var skippedStatusValue = existingApplicationStatusHistories.SkipLast(1).ToList();
				var lastStatusRecord = existingApplicationStatusHistories.LastOrDefault();
				var previousStatusRecordId = 0;

				if (lastStatusRecord == null)
					return new GetApplicationStatusResult
					{
						Status = Dto.Result.GetApplicationStatus.ApplicationStatusNotFound,
						Message = ServiceResources.APPLICATION_STATUS_NOT_FOUND
					};

				if (lastStatusRecord.ApplicationStatusId == 17 ||
					lastStatusRecord.ApplicationStatusId == 29) // Refund is Done or Cancelled By Applicant
				{
					lastStatusRecord = skippedStatusValue.LastOrDefault();

					if (lastStatusRecord is { ApplicationStatusId: (int)Enums.Enums.ApplicationStatusType.ReceivedAtOC })
						previousStatusRecordId = skippedStatusValue.SkipLast(1).LastOrDefault()!.ApplicationStatusId;

				}

				if (lastStatusRecord is { ApplicationStatusId: (int)Enums.Enums.ApplicationStatusType.ReceivedAtOC })
					previousStatusRecordId = skippedStatusValue.LastOrDefault()!.ApplicationStatusId;

				if (lastStatusRecord == null)
					return new GetApplicationStatusResult
					{
						Status = Dto.Result.GetApplicationStatus.ApplicationStatusNotFound,
						Message = ServiceResources.APPLICATION_STATUS_NOT_FOUND
					};

				var isCargoType = IsCargoType(existingApplicationStatusHistories, lastStatusRecord);

				var resultTrackingView = GetTrackingViewByStatus(lastStatusRecord.ApplicationStatusId, previousStatusRecordId, isCargoType, referanceNumberForApplicationCheck, referanceNumberNotificationType);

				var resultTrackingViews = GetTrackingView(
					isCargoType
					? ApplicationCargoType.Cargo
					: ApplicationCargoType.NonCargo,
					IsOcType(existingApplicationStatusHistories), resultTrackingView, referanceNumberNotificationType);

				return new GetApplicationStatusResult
				{
					TrackingView = resultTrackingViews,
					Status = Dto.Result.GetApplicationStatus.Successful,
					Message = ServiceResources.RESOURCE_RETRIEVED
				};
			}
			catch (Exception exc)
			{
				Logger.Error($@"Exception:{exc.Message}:{DateTime.Now}");

				return new GetApplicationStatusResult
				{
					Status = Dto.Result.GetApplicationStatus.InternalServerError,
					Message = ServiceResources.INTERNAL_SERVER_ERROR,
				};
			}
		}

		public async Task<GetApplicationStatusByAppointmentResult> GetApplicationStatusByAppointment(GetApplicationStatusByAppointmentRequest request)
		{
			var validationResult = _validationService.Validate(typeof(GetApplicationStatusByAppointmentApplicantValidator), request);

			if (!validationResult.IsValid)
				return new GetApplicationStatusByAppointmentResult
				{
					Status = Dto.Result.GetApplicationStatus.InvalidInput,
					Message = ServiceResources.INVALID_INPUT_ERROR,
					ValidationMessages = validationResult.ErrorMessages
				};

			try
			{
				var appointmentApplicantIds = await _dbContext.PreApplicationApplicant
					.Include(i => i.PreApplication)
			   .Where(r => r.IsActive && !r.IsDeleted && r.PreApplication.Id == request.AppointmentId &&
			   r.PreApplication.ExternalUserId == request.Context.Identity.ExternalUserId)
			   .Select(s => s.Id).ToListAsync();

				if (!appointmentApplicantIds.Any())
					return new GetApplicationStatusByAppointmentResult
					{
						Status = Dto.Result.GetApplicationStatus.AppointmentNotFound,
						Message = ServiceResources.APPOINTMENT_NOT_FOUND
					};

				var existingApplications = await _dbContext.Application
					.Include(p => p.ApplicationStatusHistories)
					.Where(p => p.IsActive && !p.IsDeleted && appointmentApplicantIds.Contains((int)p.PreApplicationApplicantId)).ToListAsync();

				if (!existingApplications.Any())
					return new GetApplicationStatusByAppointmentResult
					{
						Status = Dto.Result.GetApplicationStatus.AppointmentNotConvertedToApplication,
						Message = ServiceResources.APPOINTMENT_NOT_CONVERTED_APPLICATION
					};

				var trackingViewResultList = new List<TrackingViews>();

				foreach (var application in existingApplications)
				{
					var trackingViewResult = new TrackingViews();

					var existingApplicationStatusHistories = application.ApplicationStatusHistories
						.Where(s => s.IsActive && !s.IsDeleted).OrderBy(c => c.Id).ToList();

					bool referanceNumberForApplicationCheck = false;
					int referanceNumberNotificationType = 0;
                    var existingApplicationsForReferanceNumber = await _dbContext.ApplicationReferenceNumber
                        .Where(p => p.IsActive && p.ApplicationId == application.Id)
                        .OrderByDescending(q => q.Id).ToListAsync();

                    if (existingApplicationsForReferanceNumber.Any() && application.BranchApplicationCountry.Branch.CountryId == 144)// Russia
                    {
                        referanceNumberForApplicationCheck = true;
                        referanceNumberNotificationType = existingApplicationsForReferanceNumber.Select(q => q.NotificationTypeId).FirstOrDefault();
                    }

                    if (!existingApplicationStatusHistories.Any())
						return new GetApplicationStatusByAppointmentResult
						{
							Status = Dto.Result.GetApplicationStatus.ApplicationStatusNotFound,
							Message = ServiceResources.APPLICATION_STATUS_NOT_FOUND
						};

					var skippedStatusValue = existingApplicationStatusHistories.SkipLast(1).ToList();
					var lastStatusRecord = existingApplicationStatusHistories.LastOrDefault();
					var previousStatusRecordId = 0;

					if (lastStatusRecord == null)
						return new GetApplicationStatusByAppointmentResult
						{
							Status = Dto.Result.GetApplicationStatus.ApplicationStatusNotFound,
							Message = ServiceResources.APPLICATION_STATUS_NOT_FOUND
						};

					if (lastStatusRecord.ApplicationStatusId == 17 ||
						lastStatusRecord.ApplicationStatusId == 29) // Refund is Done or Cancelled By Applicant
					{
						lastStatusRecord = existingApplicationStatusHistories.SkipLast(1).LastOrDefault();

						if (lastStatusRecord is { ApplicationStatusId: (int)Enums.Enums.ApplicationStatusType.ReceivedAtOC })
							previousStatusRecordId = skippedStatusValue.SkipLast(1).LastOrDefault()!.ApplicationStatusId;
					}

					if (lastStatusRecord is { ApplicationStatusId: (int)Enums.Enums.ApplicationStatusType.ReceivedAtOC })
						previousStatusRecordId = skippedStatusValue.LastOrDefault()!.ApplicationStatusId;

					if (lastStatusRecord == null)
						return new GetApplicationStatusByAppointmentResult
						{
							Status = Dto.Result.GetApplicationStatus.ApplicationStatusNotFound,
							Message = ServiceResources.APPLICATION_STATUS_NOT_FOUND
						};

					var isCargoType = IsCargoType(existingApplicationStatusHistories, lastStatusRecord);

					var resultTrackingView = GetTrackingViewByStatus(lastStatusRecord.ApplicationStatusId, previousStatusRecordId, isCargoType, referanceNumberForApplicationCheck, referanceNumberNotificationType);

					var resultTrackingViews = GetTrackingView(
						isCargoType
							? ApplicationCargoType.Cargo
							: ApplicationCargoType.NonCargo,
						IsOcType(existingApplicationStatusHistories), resultTrackingView, referanceNumberNotificationType);

					trackingViewResult.TrackingView = resultTrackingViews;
					trackingViewResult.NameSurname = $"{application.Name} {application.Surname}";
					trackingViewResult.ApplicationTime = application.ApplicationTime;
					trackingViewResult.ApplicationTimeText = application.ApplicationTime.ToString("dd/MM/yyyy HH:mm");

					trackingViewResultList.Add(trackingViewResult);
				}

				return new GetApplicationStatusByAppointmentResult
				{
					TrackingViewsList = trackingViewResultList,
					Status = Dto.Result.GetApplicationStatus.Successful,
					Message = ServiceResources.RESOURCE_RETRIEVED
				};
			}
			catch (Exception exc)
			{
				Logger.Error($@"Exception:{exc.Message}:{DateTime.Now}");

				return new GetApplicationStatusByAppointmentResult
				{
					Status = Dto.Result.GetApplicationStatus.InternalServerError,
					Message = ServiceResources.INTERNAL_SERVER_ERROR,
				};
			}
		}

        public async Task<GetYssApplicationResult> GetYssApplication(GetYssApplicationRequest request)
        {
            var validationResult = _validationService.Validate(typeof(GetYssApplicationValidator), request);

            if (!validationResult.IsValid)
                return new GetYssApplicationResult
                {
                    Status = Dto.Result.GetYssApplicationStatus.InvalidInput,
                    Message = ServiceResources.INVALID_INPUT_ERROR,
                    ValidationMessages = validationResult.ErrorMessages
                };

            try
            {
                var birthDate = request.BirthDate.StringToDateTime("ddMMyyyy", CultureInfo.InvariantCulture);

                var existingApplication = await _dbContext.Application
                    .Include(i => i.ApplicationExtraFees)
                    .ThenInclude(i => i.ExtraFee)
                    .Include(p => p.ApplicationInsurances)
                    .Include(i => i.ApplicationDocument)
                    .Where(p => p.IsActive && !p.IsDeleted && p.PassportNumber == request.PassportNumber &&
                                p.BirthDate.Year == birthDate.Year && p.BirthDate.Month == birthDate.Month && p.BirthDate.Day == birthDate.Day 
								 &&
                                p.ApplicationExtraFees.Any(s => s.ExtraFee.Category == (int)Enums.Enums.ExtraFeeCategoryType.YSS && s.IsActive && !s.IsDeleted))
                    .OrderByDescending(s => s.Id)
                    .FirstOrDefaultAsync();

                if (existingApplication == null)
                    return new GetYssApplicationResult
                    {
                        Status = Dto.Result.GetYssApplicationStatus.NotFound,
                        Message = ServiceResources.APPLICATION_NOT_FOUND
                    };

                var foreignData =
                    await _dbContext.ForeignHealthInsurance.AnyAsync(s => s.ApplicationId == existingApplication.Id);

				if(!foreignData)
                    return new GetYssApplicationResult
                    {
                        Status = Dto.Result.GetYssApplicationStatus.NotFound,
                        Message = ServiceResources.RESOURCE_NOT_FOUND
                    };

                var random = new Random();
                var isPolicyCreated = random.Next(2) == 1;  //Todo Add check is yss policy created in later

                return new GetYssApplicationResult()
                {
                    Status = GetYssApplicationStatus.Successful,
                    Message = ServiceResources.RESOURCE_RETRIEVED,
                    Data = new YssApplicationDto()
                    {
                        Name = existingApplication.Name,
                        Surname = existingApplication.Surname,
						VisaCategoryId = existingApplication.ApplicationDocument.VisaCategoryId,
                        IsPolicyCreated = isPolicyCreated,
						VisaCategory = GetLookupValue((int)LookupType.VisaCategoryType, existingApplication.ApplicationDocument.VisaCategoryId)
                    }
                };
            }
            catch (Exception exc)
            {
                Logger.Error($@"Exception:{exc.Message}:{DateTime.Now}");

                return new GetYssApplicationResult
                {
                    Status = Dto.Result.GetYssApplicationStatus.InternalServerError,
                    Message = ServiceResources.INTERNAL_SERVER_ERROR,
                };
            }
        }

        public async Task<GetApplicationStatusResult> GetApplicationStatusById(GetApplicationStatusByIdRequest request)
        {
            var validationResult = _validationService.Validate(typeof(GetApplicationStatusByIdValidator), request);

            if (!validationResult.IsValid)
                return new GetApplicationStatusResult
                {
                    Status = Dto.Result.GetApplicationStatus.InvalidInput,
                    Message = ServiceResources.INVALID_INPUT_ERROR,
                    ValidationMessages = validationResult.ErrorMessages
                };

            try
            {
                var existingApplication = await _dbContext.Application
                    .Include(p => p.ApplicationStatusHistories)
                    .Include(p => p.BranchApplicationCountry)
                        .ThenInclude(p => p.Branch)
                    .Where(p => p.IsActive && !p.IsDeleted && p.Id == request.ApplicationNumber).FirstOrDefaultAsync();

                if (existingApplication == null)
                    return new GetApplicationStatusResult
                    {
                        Status = Dto.Result.GetApplicationStatus.ApplicationNotFound,
                        Message = ServiceResources.APPLICATION_NOT_FOUND
                    };

                var existingApplicationStatusHistories = existingApplication.ApplicationStatusHistories
                    .Where(s => s.IsActive && !s.IsDeleted).OrderBy(c => c.Id).ToList();

                bool referanceNumberForApplicationCheck = false;
                int referanceNumberNotificationType = 0;
                var existingApplicationsForReferanceNumber = await _dbContext.ApplicationReferenceNumber
                    .Where(p => p.IsActive && p.ApplicationId == existingApplication.Id).OrderByDescending(q => q.Id).ToListAsync();

                if (existingApplicationsForReferanceNumber.Any() && existingApplication.BranchApplicationCountry.Branch.CountryId == 144)// Russia
                {
                    referanceNumberForApplicationCheck = true;
                    referanceNumberNotificationType = existingApplicationsForReferanceNumber.Select(q => q.NotificationTypeId).FirstOrDefault();
                }

                if (!existingApplicationStatusHistories.Any())
                    return new GetApplicationStatusResult
                    {
                        Status = Dto.Result.GetApplicationStatus.ApplicationStatusNotFound,
                        Message = ServiceResources.APPLICATION_STATUS_NOT_FOUND
                    };

                var skippedStatusValue = existingApplicationStatusHistories.SkipLast(1).ToList();
                var lastStatusRecord = existingApplicationStatusHistories.LastOrDefault();
                var previousStatusRecordId = 0;

                if (lastStatusRecord == null)
                    return new GetApplicationStatusResult
                    {
                        Status = Dto.Result.GetApplicationStatus.ApplicationStatusNotFound,
                        Message = ServiceResources.APPLICATION_STATUS_NOT_FOUND
                    };

                if (lastStatusRecord.ApplicationStatusId == 17 ||
                    lastStatusRecord.ApplicationStatusId == 29) // Refund is Done or Cancelled By Applicant
                {
                    lastStatusRecord = skippedStatusValue.LastOrDefault();

                    if (lastStatusRecord is { ApplicationStatusId: (int)Enums.Enums.ApplicationStatusType.ReceivedAtOC })
                        previousStatusRecordId = skippedStatusValue.SkipLast(1).LastOrDefault()!.ApplicationStatusId;

                }

                if (lastStatusRecord is { ApplicationStatusId: (int)Enums.Enums.ApplicationStatusType.ReceivedAtOC })
                    previousStatusRecordId = skippedStatusValue.LastOrDefault()!.ApplicationStatusId;

                if (lastStatusRecord == null)
                    return new GetApplicationStatusResult
                    {
                        Status = Dto.Result.GetApplicationStatus.ApplicationStatusNotFound,
                        Message = ServiceResources.APPLICATION_STATUS_NOT_FOUND
                    };

                var isCargoType = IsCargoType(existingApplicationStatusHistories, lastStatusRecord);

                var resultTrackingView = GetTrackingViewByStatus(lastStatusRecord.ApplicationStatusId, previousStatusRecordId, isCargoType, referanceNumberForApplicationCheck, referanceNumberNotificationType);

                var resultTrackingViews = GetTrackingView(
                    isCargoType
                    ? ApplicationCargoType.Cargo
                    : ApplicationCargoType.NonCargo,
                    IsOcType(existingApplicationStatusHistories), resultTrackingView, referanceNumberNotificationType);

                return new GetApplicationStatusResult
                {
                    TrackingView = resultTrackingViews,
                    Status = Dto.Result.GetApplicationStatus.Successful,
                    Message = ServiceResources.RESOURCE_RETRIEVED
                };
            }
            catch (Exception exc)
            {
                Logger.Error($@"Exception:{exc.Message}:{DateTime.Now}");

                return new GetApplicationStatusResult
                {
                    Status = Dto.Result.GetApplicationStatus.InternalServerError,
                    Message = ServiceResources.INTERNAL_SERVER_ERROR,
                };
            }
        }

        #region Private Methods

        private enum ApplicationCargoType
		{
			Cargo,
			NonCargo
		}

		private static List<TrackingView> GetTrackingView(ApplicationCargoType applicationCargoType, bool isOcType, string selectedItem, int referanceNumberNotificationType)
		{
			var result = new List<TrackingView>();

			var cargoList = isOcType ? new List<string>
			{
				EnumExtensions.GetEnumDescription(typeof(Enums.Enums.ApplicationStatusTrackingView),
					((int)Enums.Enums.ApplicationStatusTrackingView.ApplicationTaken).ToString()),
				EnumExtensions.GetEnumDescription(typeof(Enums.Enums.ApplicationStatusTrackingView),
					((int)Enums.Enums.ApplicationStatusTrackingView.SentToMainVisaApplicationCenter).ToString()),
				EnumExtensions.GetEnumDescription(typeof(Enums.Enums.ApplicationStatusTrackingView),
					((int)Enums.Enums.ApplicationStatusTrackingView.ApplicationUnderEvaluation).ToString()),
				EnumExtensions.GetEnumDescription(typeof(Enums.Enums.ApplicationStatusTrackingView),
					((int)Enums.Enums.ApplicationStatusTrackingView.SentFromEmbassyToMainVisaApplicationCenter).ToString()),
				EnumExtensions.GetEnumDescription(typeof(Enums.Enums.ApplicationStatusTrackingView),
					((int)Enums.Enums.ApplicationStatusTrackingView.ReceivedAtMainVisaApplicationCenter).ToString()),
				EnumExtensions.GetEnumDescription(typeof(Enums.Enums.ApplicationStatusTrackingView),
					((int)Enums.Enums.ApplicationStatusTrackingView.DeliveredToCargo).ToString()),
                EnumExtensions.GetEnumDescription(typeof(Enums.Enums.ApplicationStatusTrackingView),
                    ((int)Enums.Enums.ApplicationStatusTrackingView.CourierReturnedPassportToOffice).ToString()),
            } : new List<string>
			{
				EnumExtensions.GetEnumDescription(typeof(Enums.Enums.ApplicationStatusTrackingView),
					((int)Enums.Enums.ApplicationStatusTrackingView.ApplicationTaken).ToString()),
				EnumExtensions.GetEnumDescription(typeof(Enums.Enums.ApplicationStatusTrackingView),
					((int)Enums.Enums.ApplicationStatusTrackingView.ApplicationUnderEvaluation).ToString()),
				EnumExtensions.GetEnumDescription(typeof(Enums.Enums.ApplicationStatusTrackingView),
					((int)Enums.Enums.ApplicationStatusTrackingView.DeliveredToCargo).ToString()),
                EnumExtensions.GetEnumDescription(typeof(Enums.Enums.ApplicationStatusTrackingView),
                    ((int)Enums.Enums.ApplicationStatusTrackingView.CourierReturnedPassportToOffice).ToString()),
                EnumExtensions.GetEnumDescription(typeof(Enums.Enums.ApplicationStatusTrackingView),
					((int)Enums.Enums.ApplicationStatusTrackingView.FileWithdrewAccordingtoCustomerRequest).ToString()),
			};

			String referanceNumberNotificationStatus = "";
			if (referanceNumberNotificationType == 1)
				referanceNumberNotificationStatus = EnumExtensions.GetEnumDescription(typeof(Enums.Enums.ApplicationStatusTrackingView),
					((int)Enums.Enums.ApplicationStatusTrackingView.WorkPermitReferanceNumberSendSms).ToString());
			if (referanceNumberNotificationType == 2)
				referanceNumberNotificationStatus = EnumExtensions.GetEnumDescription(typeof(Enums.Enums.ApplicationStatusTrackingView),
					((int)Enums.Enums.ApplicationStatusTrackingView.WorkPermitReferanceNumberSendMail).ToString());

			var nonCargoList = isOcType ? new List<string>
			{
				EnumExtensions.GetEnumDescription(typeof(Enums.Enums.ApplicationStatusTrackingView),
				((int)Enums.Enums.ApplicationStatusTrackingView.ApplicationTaken).ToString()),
				EnumExtensions.GetEnumDescription(typeof(Enums.Enums.ApplicationStatusTrackingView),
					((int)Enums.Enums.ApplicationStatusTrackingView.SentToMainVisaApplicationCenter).ToString()),
				EnumExtensions.GetEnumDescription(typeof(Enums.Enums.ApplicationStatusTrackingView),
					((int)Enums.Enums.ApplicationStatusTrackingView.ApplicationUnderEvaluation).ToString()),
				EnumExtensions.GetEnumDescription(typeof(Enums.Enums.ApplicationStatusTrackingView),
					((int)Enums.Enums.ApplicationStatusTrackingView.SentFromEmbassyToMainVisaApplicationCenter).ToString()),
				EnumExtensions.GetEnumDescription(typeof(Enums.Enums.ApplicationStatusTrackingView),
					((int)Enums.Enums.ApplicationStatusTrackingView.ReceivedAtMainVisaApplicationCenter).ToString()),
				EnumExtensions.GetEnumDescription(typeof(Enums.Enums.ApplicationStatusTrackingView),
					((int)Enums.Enums.ApplicationStatusTrackingView.SentFromMainVisaApplicationCenterToVisaApplicationCenter).ToString()),
				EnumExtensions.GetEnumDescription(typeof(Enums.Enums.ApplicationStatusTrackingView),
					((int)Enums.Enums.ApplicationStatusTrackingView.ReceivedAtVisaCenter).ToString()),
			} : new List<string>
			{
				EnumExtensions.GetEnumDescription(typeof(Enums.Enums.ApplicationStatusTrackingView),
					((int)Enums.Enums.ApplicationStatusTrackingView.ApplicationTaken).ToString()),
				EnumExtensions.GetEnumDescription(typeof(Enums.Enums.ApplicationStatusTrackingView),
					((int)Enums.Enums.ApplicationStatusTrackingView.ApplicationUnderEvaluation).ToString()),
				referanceNumberNotificationStatus,
				EnumExtensions.GetEnumDescription(typeof(Enums.Enums.ApplicationStatusTrackingView),
					((int)Enums.Enums.ApplicationStatusTrackingView.ReceivedAtVisaCenter).ToString()),
				EnumExtensions.GetEnumDescription(typeof(Enums.Enums.ApplicationStatusTrackingView),
					((int)Enums.Enums.ApplicationStatusTrackingView.FileWithdrewAccordingtoCustomerRequest).ToString()),

			};

			RemoveEmptyStringsToList(nonCargoList);

			switch (applicationCargoType)
			{
				case ApplicationCargoType.Cargo:
					result.AddRange(cargoList.Select(item => new
						TrackingView
					{
						Status = item,
						IsActive = IsApplicationStatusActive(item, selectedItem)
					}));
					break;
				case ApplicationCargoType.NonCargo:
					result.AddRange(nonCargoList.Select(item => new
						TrackingView
					{
						Status = item,
						IsActive = IsApplicationStatusActive(item, selectedItem)
					}));
					break;
			}

			var index = 0;
			var resultCount = result.Count;
			var removedCount = 0;
			for (var i = 0; i < resultCount; i++)
			{
				if (result[i].IsActive)
				{
					index = i;
				}
			}
			for (var i = 0; i < resultCount; i++)
			{
				if (index == 0)
				{
					result.RemoveRange(i + 1, resultCount - 1);
					break;
				}
				if (i <= index) continue;
				result.RemoveAt(i - removedCount);
				removedCount += 1;
			}

			return result;
		}

		private static bool IsApplicationStatusActive(string source, string destination)
		{
			return source == destination;
		}

		private static void RemoveEmptyStringsToList(List<string> statusList)
		{
			statusList.RemoveAll(s => s == string.Empty);
        }

        private static bool IsCargoType(IEnumerable<ApplicationStatusHistory> histories, ApplicationStatusHistory lastHistory)
		{
			var isCargoType = histories.Any(p => p.ApplicationStatusId == 18 && p.IsActive && !p.IsDeleted);

			return isCargoType && !(lastHistory.ApplicationStatusId == (int)Enums.Enums.ApplicationStatusType.HandDeliveredToApplicant);
        }

		private static bool IsOcType(IEnumerable<ApplicationStatusHistory> histories)
		{
			return histories.Any(p => (p.ApplicationStatusId == (int)Enums.Enums.ApplicationStatusType.ReceivedAtOC ||
									  p.ApplicationStatusId == (int)Enums.Enums.ApplicationStatusType.OutScanFromOCToVAC ||
									  p.ApplicationStatusId == (int)Enums.Enums.ApplicationStatusType.OutScanFromVACToOC ||
									  p.ApplicationStatusId == (int)Enums.Enums.ApplicationStatusType.OutScanFromEmbassyToOC ||
									  p.ApplicationStatusId == (int)Enums.Enums.ApplicationStatusType.OutScanFromOCToEmbassy) && p.IsActive && !p.IsDeleted);
		}

		private static string GetTrackingViewByStatus(int statusId, int previousStatusId, bool isCargoType, bool referanceNumberForApplicationCheck, int referanceNumberNotificationType)
		{
			String referanceNumberNotificationStatus = EnumExtensions.GetEnumDescription(typeof(Enums.Enums.ApplicationStatusTrackingView),
					((int)Enums.Enums.ApplicationStatusTrackingView.ApplicationTaken).ToString());
			if (referanceNumberForApplicationCheck)
			{
				if (referanceNumberNotificationType == 1)
					referanceNumberNotificationStatus = EnumExtensions.GetEnumDescription(typeof(Enums.Enums.ApplicationStatusTrackingView),
						((int)Enums.Enums.ApplicationStatusTrackingView.WorkPermitReferanceNumberSendSms).ToString());
				if (referanceNumberNotificationType == 2)
					referanceNumberNotificationStatus = EnumExtensions.GetEnumDescription(typeof(Enums.Enums.ApplicationStatusTrackingView),
						((int)Enums.Enums.ApplicationStatusTrackingView.WorkPermitReferanceNumberSendMail).ToString());
			}

			return statusId switch
			{
				(int)Enums.Enums.ApplicationStatusType.ApplicationTaken => EnumExtensions.GetEnumDescription(typeof(Enums.Enums.ApplicationStatusTrackingView),
					((int)Enums.Enums.ApplicationStatusTrackingView.ApplicationTaken).ToString()),
				(int)Enums.Enums.ApplicationStatusType.DataDone => EnumExtensions.GetEnumDescription(typeof(Enums.Enums.ApplicationStatusTrackingView),
					((int)Enums.Enums.ApplicationStatusTrackingView.ApplicationTaken).ToString()),
				(int)Enums.Enums.ApplicationStatusType.SendToEmbassy => referanceNumberNotificationStatus,
				(int)Enums.Enums.ApplicationStatusType.ReceivedAtEmbassy => EnumExtensions.GetEnumDescription(typeof(Enums.Enums.ApplicationStatusTrackingView),
					((int)Enums.Enums.ApplicationStatusTrackingView.ApplicationUnderEvaluation).ToString()),
				(int)Enums.Enums.ApplicationStatusType.SentToVACFromEmbassy => EnumExtensions.GetEnumDescription(typeof(Enums.Enums.ApplicationStatusTrackingView),
					((int)Enums.Enums.ApplicationStatusTrackingView.ApplicationUnderEvaluation).ToString()),
				(int)Enums.Enums.ApplicationStatusType.HandDeliveredToApplicant => EnumExtensions.GetEnumDescription(typeof(Enums.Enums.ApplicationStatusTrackingView),
					((int)Enums.Enums.ApplicationStatusTrackingView.ReceivedAtVisaCenter).ToString()),
				(int)Enums.Enums.ApplicationStatusType.OutscanToCourier => EnumExtensions.GetEnumDescription(typeof(Enums.Enums.ApplicationStatusTrackingView),
					((int)Enums.Enums.ApplicationStatusTrackingView.DeliveredToCargo).ToString()),
				(int)Enums.Enums.ApplicationStatusType.BiometricEnrollmentDone => EnumExtensions.GetEnumDescription(typeof(Enums.Enums.ApplicationStatusTrackingView),
					((int)Enums.Enums.ApplicationStatusTrackingView.ApplicationTaken).ToString()),
				(int)Enums.Enums.ApplicationStatusType.OutscanPassportWithRejectionToCourier => EnumExtensions.GetEnumDescription(typeof(Enums.Enums.ApplicationStatusTrackingView),
					((int)Enums.Enums.ApplicationStatusTrackingView.DeliveredToCargo).ToString()),
				(int)Enums.Enums.ApplicationStatusType.ReceivedAtVisaCenter => EnumExtensions.GetEnumDescription(typeof(Enums.Enums.ApplicationStatusTrackingView),
					((int)Enums.Enums.ApplicationStatusTrackingView.ReceivedAtVisaCenter).ToString()),
				(int)Enums.Enums.ApplicationStatusType.WaitingApproval => EnumExtensions.GetEnumDescription(typeof(Enums.Enums.ApplicationStatusTrackingView),
					((int)Enums.Enums.ApplicationStatusTrackingView.ApplicationTaken).ToString()),
				(int)Enums.Enums.ApplicationStatusType.FileWithdrewAccordingtoCustomerRequest => EnumExtensions.GetEnumDescription(typeof(Enums.Enums.ApplicationStatusTrackingView),
					((int)Enums.Enums.ApplicationStatusTrackingView.FileWithdrewAccordingtoCustomerRequest).ToString()),
				(int)Enums.Enums.ApplicationStatusType.IhbWaiting => EnumExtensions.GetEnumDescription(typeof(Enums.Enums.ApplicationStatusTrackingView),
					((int)Enums.Enums.ApplicationStatusTrackingView.ApplicationTaken).ToString()),
				(int)Enums.Enums.ApplicationStatusType.IhbUploaded => EnumExtensions.GetEnumDescription(typeof(Enums.Enums.ApplicationStatusTrackingView),
					((int)Enums.Enums.ApplicationStatusTrackingView.ApplicationTaken).ToString()),
				(int)Enums.Enums.ApplicationStatusType.OutScanFromOCToVAC => EnumExtensions.GetEnumDescription(typeof(Enums.Enums.ApplicationStatusTrackingView),
					((int)Enums.Enums.ApplicationStatusTrackingView.SentFromMainVisaApplicationCenterToVisaApplicationCenter).ToString()),
				(int)Enums.Enums.ApplicationStatusType.OutScanFromVACToOC => EnumExtensions.GetEnumDescription(typeof(Enums.Enums.ApplicationStatusTrackingView),
					((int)Enums.Enums.ApplicationStatusTrackingView.SentToMainVisaApplicationCenter).ToString()),
				(int)Enums.Enums.ApplicationStatusType.OutScanFromEmbassyToOC => EnumExtensions.GetEnumDescription(typeof(Enums.Enums.ApplicationStatusTrackingView),
					((int)Enums.Enums.ApplicationStatusTrackingView.SentFromEmbassyToMainVisaApplicationCenter).ToString()),
				(int)Enums.Enums.ApplicationStatusType.OutScanFromOCToEmbassy => EnumExtensions.GetEnumDescription(typeof(Enums.Enums.ApplicationStatusTrackingView),
					((int)Enums.Enums.ApplicationStatusTrackingView.ReceivedAtMainVisaApplicationCenter).ToString()),
                (int)Enums.Enums.ApplicationStatusType.RecievedAtVAC => isCargoType ? EnumExtensions.GetEnumDescription(typeof(Enums.Enums.ApplicationStatusTrackingView),
					((int)Enums.Enums.ApplicationStatusTrackingView.ApplicationUnderEvaluation).ToString()) :
						EnumExtensions.GetEnumDescription(typeof(Enums.Enums.ApplicationStatusTrackingView),
							((int)Enums.Enums.ApplicationStatusTrackingView.ReceivedAtVisaCenter).ToString()),
				(int)Enums.Enums.ApplicationStatusType.ReceivedAtVac => isCargoType ? EnumExtensions.GetEnumDescription(typeof(Enums.Enums.ApplicationStatusTrackingView),
					((int)Enums.Enums.ApplicationStatusTrackingView.ApplicationUnderEvaluation).ToString()) :
						EnumExtensions.GetEnumDescription(typeof(Enums.Enums.ApplicationStatusTrackingView),
							((int)Enums.Enums.ApplicationStatusTrackingView.ReceivedAtVisaCenter).ToString()),
				(int)Enums.Enums.ApplicationStatusType.ReceivedAtOC => previousStatusId == (int)Enums.Enums.ApplicationStatusType.OutScanFromEmbassyToOC ?
					EnumExtensions.GetEnumDescription(typeof(Enums.Enums.ApplicationStatusTrackingView),
					((int)Enums.Enums.ApplicationStatusTrackingView.ReceivedAtMainVisaApplicationCenter).ToString()) :
					EnumExtensions.GetEnumDescription(typeof(Enums.Enums.ApplicationStatusTrackingView),
					((int)Enums.Enums.ApplicationStatusTrackingView.SentToMainVisaApplicationCenter).ToString()),
                (int)Enums.Enums.ApplicationStatusType.CourierReturnedPassportToOffice => EnumExtensions.GetEnumDescription(typeof(Enums.Enums.ApplicationStatusTrackingView),
                    ((int)Enums.Enums.ApplicationStatusTrackingView.CourierReturnedPassportToOffice).ToString()),
                _ => EnumExtensions.GetEnumDescription(typeof(Enums.Enums.ApplicationStatusTrackingView),
					((int)Enums.Enums.ApplicationStatusTrackingView.ApplicationUnderEvaluation).ToString()),
			};
		}

        private static LookupValue GetLookupValue(int enumType, int? value)
        {
            return EnumExtensions.GetEnumAsDictionary(LookupTypeFactory.GetInstance(enumType))
                .Select(x => new LookupValue { Id = x.Key.ToString(), DisplayValue = x.Value })
                .FirstOrDefault(p => p.Id == value.ToString());
        }

        #endregion
    }
}